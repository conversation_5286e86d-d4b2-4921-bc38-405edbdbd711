import { Modal, Tag, Spin, Empty, Avatar } from 'antd';
import { BlockOutlined, UserOutlined } from '@ant-design/icons';
import { PermissionTemplateDisplay } from '../../types';

interface TemplateStudent {
  studentId: number;
  studentName: string;
  avatarUrl?: string;
  className?: string;
}

interface TemplateUsageModalProps {
  visible: boolean;
  onCancel: () => void;
  template: PermissionTemplateDisplay | null;
  students: TemplateStudent[];
  loading: boolean;
}

export const TemplateUsageModal: React.FC<TemplateUsageModalProps> = ({
  visible,
  onCancel,
  template,
  students,
  loading,
}) => {
  return (
    <Modal
      title={
        <div className="flex items-center gap-2">
          <BlockOutlined className="text-blue-500" />
          <span>模板使用情况</span>
          {template && (
            <Tag color={template.isOfficial ? 'gold' : 'success'} className="ml-2 rounded-full">
              {template.isOfficial ? '官方' : '自定义'}
            </Tag>
          )}
        </div>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      centered
      width={700}
      style={{
        maxWidth: '90vw',
        margin: '10vh auto',
        padding: 0,
        top: 0
      }}
      bodyStyle={{
        height: 'calc(80vh - 110px)',
        padding: '20px',
        overflow: 'auto'
      }}
    >
      {template && (
        <div className="h-full flex flex-col">
          <div className="flex-none bg-gray-50 p-4 rounded-lg mb-4">
            <h3 className="text-lg font-medium mb-2">{template.templateName}</h3>
            <p className="text-gray-600 text-sm">{template.templateDescription}</p>
          </div>

          <div className="flex-1 min-h-0 border-t border-gray-100 pt-4">
            <h4 className="text-base font-medium mb-3">使用此模板的学生</h4>
            {loading ? (
              <div className="flex justify-center py-8">
                <Spin />
              </div>
            ) : students.length > 0 ? (
              <div className="h-[calc(100%-40px)] overflow-y-auto pr-2">
                <div className="space-y-2">
                  {students.map(student => (
                    <div 
                      key={student.studentId}
                      className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100 hover:border-blue-200 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <Avatar
                          size={32}
                          src={student.avatarUrl}
                          icon={!student.avatarUrl && <UserOutlined />}
                        />
                        <div>
                          <div className="font-medium">{student.studentName}</div>
                          <div className="text-xs text-gray-500">{student.studentId}</div>
                        </div>
                      </div>
                      <div className="text-sm text-gray-500">
                        {student.className && (
                          <Tag color="blue">{student.className}</Tag>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <Empty description="暂无学生使用此模板" />
            )}
          </div>
        </div>
      )}
    </Modal>
  );
}; 