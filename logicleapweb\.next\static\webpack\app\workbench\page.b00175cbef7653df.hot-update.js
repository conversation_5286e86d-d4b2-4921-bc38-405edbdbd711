"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/ClassDetail.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/ClassDetail.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/blocks.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _lib_api_student__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/api/student */ \"(app-pages-browser)/./lib/api/student.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/TemplateContext */ \"(app-pages-browser)/./app/workbench/contexts/TemplateContext.tsx\");\n/* harmony import */ var _AddStudentModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AddStudentModal */ \"(app-pages-browser)/./app/workbench/components/AddStudentModal.tsx\");\n/* harmony import */ var _teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../teacher-space/components/modals */ \"(app-pages-browser)/./app/teacher-space/components/modals/index.ts\");\n/* harmony import */ var _AssignBlocksModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AssignBlocksModal */ \"(app-pages-browser)/./app/workbench/components/AssignBlocksModal.tsx\");\n/* harmony import */ var _TransferClassModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TransferClassModal */ \"(app-pages-browser)/./app/workbench/components/TransferClassModal.tsx\");\n/* harmony import */ var _AssignPointsModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./AssignPointsModal */ \"(app-pages-browser)/./app/workbench/components/AssignPointsModal.tsx\");\n/* harmony import */ var _BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./BatchUseKeyPackageModal */ \"(app-pages-browser)/./app/workbench/components/BatchUseKeyPackageModal.tsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./app/workbench/components/StudentList/index.tsx\");\n/* harmony import */ var _ClassDetail_css__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ClassDetail.css */ \"(app-pages-browser)/./app/workbench/components/ClassDetail.css\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 类型定义已移至 ./types/index.ts\nconst ClassDetail = (param)=>{\n    let { classInfo: initialClassInfo, selectedSchool, onBack, onClassInfoUpdate, onClassDeleted } = param;\n    var _selectedStudent_nickName, _selectedStudent_currentTemplate;\n    _s();\n    // 创建内部状态来管理班级信息，这样可以在编辑后更新显示\n    const [classInfo, setClassInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialClassInfo);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 同步外部传入的classInfo变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setClassInfo(initialClassInfo);\n    }, [\n        initialClassInfo\n    ]);\n    const [isAddStudentModalVisible, setIsAddStudentModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSettingsDropdownOpen, setIsSettingsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchActionsDropdownOpen, setIsBatchActionsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const settingsDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const batchActionsDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 模态框状态\n    const [isEditClassModalVisible, setIsEditClassModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isImportStudentModalVisible, setIsImportStudentModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTransferClassModalVisible, setIsTransferClassModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isInviteCodeModalVisible, setIsInviteCodeModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAssignBlocksModalVisible, setIsAssignBlocksModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [inviteCode, setInviteCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 新增功能模态框状态\n    const [isPublishTaskModalVisible, setIsPublishTaskModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResetPasswordModalVisible, setIsResetPasswordModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAssignPointsModalVisible, setIsAssignPointsModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchUseKeyPackageModalVisible, setIsBatchUseKeyPackageModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [isRedeemKeyModalVisible, setIsRedeemKeyModalVisible] = useState(false); // 未使用，已移除\n    // 转让管理相关状态\n    const [searchedTeacher, setSearchedTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [transferLoading, setTransferLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // PublishTaskModal 相关状态\n    const [fileList, setFileList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [officialTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 保留以避免引用错误\n    const [selectedTemplateId, setSelectedTemplateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // AssignBlocksModal 相关状态\n    const [loadingTemplates, setLoadingTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [studentTemplateUsage, setStudentTemplateUsage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [teacherTemplate, setTeacherTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedStudentId, setSelectedStudentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 添加userRoles状态，与teacher-space保持一致\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 多选状态\n    const [selectedStudentIds, setSelectedStudentIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSelectAll, setIsSelectAll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 使用全局模板状态\n    const { currentTemplate, globalTemplateChangeVersion, refreshCurrentTemplate } = (0,_contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__.useTemplate)();\n    // 全局当前模板信息（用于没有个人模板的学生）\n    const [globalCurrentTemplate, setGlobalCurrentTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 获取教师当前模板（从班级接口获取）\n    const fetchTeacherCurrentTemplate = async ()=>{\n        try {\n            if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id) || !(classInfo === null || classInfo === void 0 ? void 0 : classInfo.schoolId)) return null;\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.getTeacherClasses(classInfo.schoolId, userId);\n            if (response.data.code === 200 && response.data.data) {\n                // 找到当前班级的模板信息\n                const currentClass = response.data.data.find((cls)=>cls.id === classInfo.id);\n                if (currentClass && currentClass.templateName) {\n                    const templateInfo = {\n                        templateId: currentClass.templateId || 0,\n                        templateName: currentClass.templateName,\n                        isOfficial: currentClass.isOfficial || false\n                    };\n                    setGlobalCurrentTemplate(templateInfo);\n                    console.log(\"获取到教师当前模板:\", templateInfo);\n                    return templateInfo;\n                }\n            }\n        } catch (error) {\n            console.error(\"获取教师当前模板失败:\", error);\n        }\n        return null;\n    };\n    // 同步教师模板到学生（当教师模板更新时调用）\n    const syncTeacherTemplateToStudents = async ()=>{\n        try {\n            const newTemplate = await fetchTeacherCurrentTemplate();\n            if (newTemplate && students.length > 0) {\n                console.log(\"同步教师模板到学生:\", newTemplate);\n                // 使用 ref 获取最新的 personalTemplateAssignments\n                const currentPersonalAssignments = personalTemplateAssignmentsRef.current;\n                // 更新所有没有个人分配模板的学生\n                setStudents((prevStudents)=>prevStudents.map((student)=>{\n                        // 如果学生有个人分配的模板，保持不变\n                        if (currentPersonalAssignments.has(student.userId)) {\n                            console.log(\"保持学生 \".concat(student.nickName, \" 的个人模板 (syncTeacherTemplateToStudents)\"));\n                            return student;\n                        }\n                        // 否则更新为教师当前模板\n                        console.log(\"更新学生 \".concat(student.nickName, \" 为教师模板 (syncTeacherTemplateToStudents)\"));\n                        return {\n                            ...student,\n                            currentTemplate: newTemplate\n                        };\n                    }));\n            }\n        } catch (error) {\n            console.error(\"同步教师模板失败:\", error);\n        }\n    };\n    // 存储个人分配的模板信息，避免被fetchStudents覆盖\n    const [personalTemplateAssignments, setPersonalTemplateAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    // 添加强制重新渲染的状态\n    const [renderVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // setRenderVersion暂时未使用\n    // 获取用户信息\n    const userId = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector)((state)=>state.user.userState.userId);\n    const roleId = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector)((state)=>state.user.userState.roleId);\n    // 获取班级学生数据\n    const fetchStudents = async ()=>{\n        if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) return;\n        try {\n            setLoading(true);\n            setError(null);\n            // 并行获取班级学生基础数据和教师当前模板\n            const [response] = await Promise.all([\n                _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.getClassStudents(classInfo.id),\n                fetchTeacherCurrentTemplate()\n            ]);\n            if (response.data.code === 200) {\n                const studentsData = response.data.data || [];\n                if (studentsData.length === 0) {\n                    setStudents([]);\n                    setSelectedStudent(null);\n                    // 如果学生数量变为0，也要更新班级信息\n                    if (classInfo.studentCount !== 0) {\n                        const updatedClassInfo = {\n                            ...classInfo,\n                            studentCount: 0\n                        };\n                        setClassInfo(updatedClassInfo);\n                        // 通知父组件更新班级列表中的学生数\n                        onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n                    }\n                    return;\n                }\n                // 提取学生ID列表\n                const userIds = studentsData.map((s)=>s.userId);\n                // 批量获取学生详细信息（包括模板和能量信息）\n                const studentInfoMap = await (userIds.length > 20 ? Promise.all(Array.from({\n                    length: Math.ceil(userIds.length / 20)\n                }, (_, i)=>userIds.slice(i * 20, (i + 1) * 20)).map((batchId)=>_lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.getStudentsBatchInfo(batchId).then((param)=>{\n                        let { data: { data } } = param;\n                        return data;\n                    }).catch((error)=>{\n                        console.error(\"获取学生批量信息失败:\", error);\n                        return {};\n                    }))).then((results)=>Object.assign({}, ...results)) : _lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.getStudentsBatchInfo(userIds).then((param)=>{\n                    let { data: { data } } = param;\n                    return data;\n                }).catch((error)=>{\n                    console.error(\"获取学生批量信息失败:\", error);\n                    return {};\n                }));\n                // 合并学生数据\n                const completeStudents = studentsData.map((s)=>{\n                    const studentInfo = studentInfoMap[s.userId];\n                    // 检查是否有个人分配的模板\n                    const personalTemplate = personalTemplateAssignments.get(s.userId);\n                    return {\n                        ...s,\n                        ...studentInfo,\n                        id: s.userId,\n                        nickName: s.nickName || \"学生\".concat(s.studentNumber || s.userId),\n                        totalPoints: (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.totalPoints) || 0,\n                        availablePoints: (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.availablePoints) || 0,\n                        avatarUrl: s.avatarUrl || \"/default-avatar.png\",\n                        // 优先级：个人分配的模板 > 学生API返回的模板 > 教师当前模板 > null\n                        currentTemplate: (()=>{\n                            var _studentsData_;\n                            const result = personalTemplate || (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.currentTemplate) || globalCurrentTemplate;\n                            // 调试信息\n                            if (s.userId === ((_studentsData_ = studentsData[0]) === null || _studentsData_ === void 0 ? void 0 : _studentsData_.userId)) {\n                                console.log(\"学生模板分配逻辑:\", {\n                                    studentId: s.userId,\n                                    studentName: s.nickName,\n                                    personalTemplate,\n                                    studentApiTemplate: studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.currentTemplate,\n                                    globalCurrentTemplate,\n                                    finalTemplate: result\n                                });\n                            }\n                            return result;\n                        })()\n                    };\n                });\n                setStudents(completeStudents);\n                // 如果学生数量发生变化，更新班级信息并通知父组件\n                if (completeStudents.length !== classInfo.studentCount) {\n                    const updatedClassInfo = {\n                        ...classInfo,\n                        studentCount: completeStudents.length\n                    };\n                    setClassInfo(updatedClassInfo);\n                    // 通知父组件更新班级列表中的学生数\n                    onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n                }\n                // 设置userRoles，与teacher-space保持一致\n                const newUserRoles = completeStudents.map((student)=>({\n                        userId: student.userId,\n                        roleId: 1 // 学生角色ID为1\n                    }));\n                // 添加教师自己的角色\n                newUserRoles.push({\n                    userId: userId,\n                    roleId: 2 // 教师角色ID为2\n                });\n                setUserRoles(newUserRoles);\n                // 默认选择第一个学生\n                if (completeStudents.length > 0) {\n                    setSelectedStudent(completeStudents[0]);\n                }\n            } else {\n                setError(response.data.message || \"获取学生列表失败\");\n            }\n        } catch (err) {\n            console.error(\"获取学生列表失败:\", err);\n            console.error(\"错误详情:\", {\n                message: err instanceof Error ? err.message : \"未知错误\",\n                stack: err instanceof Error ? err.stack : undefined,\n                classId: classInfo.id\n            });\n            setError(\"获取学生列表失败，请稍后重试\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 获取权限模板列表\n    const fetchTemplates = async ()=>{\n        setLoadingTemplates(true);\n        try {\n            const { getRoleTemplateList, getOfficialTemplates } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            // 同时获取教师自定义模板和官方模板\n            const [customResponse, officialResponse] = await Promise.all([\n                getRoleTemplateList(userId),\n                getOfficialTemplates()\n            ]);\n            if (customResponse.data.code === 200 && officialResponse.data.code === 200) {\n                const customTemplates = customResponse.data.data || [];\n                const officialTemplates = officialResponse.data.data || [];\n                // 为官方模板添加标记\n                const markedOfficialTemplates = officialTemplates.map((template)=>({\n                        ...template,\n                        isOfficial: true\n                    }));\n                // 合并所有模板\n                const allTemplates = [\n                    ...customTemplates,\n                    ...markedOfficialTemplates\n                ];\n                setTemplates(allTemplates);\n            }\n        } catch (error) {\n            console.error(\"获取权限模板列表失败:\", error);\n        } finally{\n            setLoadingTemplates(false);\n        }\n    };\n    // 获取模板使用情况\n    const fetchTemplateUsage = async ()=>{\n        try {\n            const { getUserCurrentTemplate, getStudentTemplates } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            // 获取教师使用的模板\n            const teacherResponse = await getUserCurrentTemplate(userId);\n            if (teacherResponse.data.code === 200) {\n                const teacherTemplateData = teacherResponse.data.data;\n                setTeacherTemplate(teacherTemplateData);\n            // 同时更新当前模板状态，确保与模板管理同步\n            // 暂时注释掉以停止循环\n            /*\r\n        if (teacherTemplateData) {\r\n          // 使用refreshCurrentTemplate来更新全局模板状态\r\n          await refreshCurrentTemplate();\r\n        }\r\n        */ }\n            // 获取学生使用的模板\n            const studentResponse = await getStudentTemplates({\n                teacherId: userId,\n                page: 1,\n                size: 200\n            });\n            if (studentResponse.data.code === 200) {\n                // 统计每个模板被使用的次数\n                const usage = {};\n                studentResponse.data.data.list.forEach((item)=>{\n                    if (item.templateId) {\n                        usage[item.templateId] = (usage[item.templateId] || 0) + 1;\n                    }\n                });\n                setStudentTemplateUsage(usage);\n            }\n        } catch (error) {\n            console.error(\"获取模板使用情况失败:\", error);\n        }\n    };\n    // 组件挂载时获取学生数据和模板\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStudents();\n        fetchTemplates();\n        fetchTemplateUsage();\n    }, [\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n        userId\n    ]);\n    // 当组件重新挂载或班级变化时，确保获取最新的当前模板\n    // 暂时注释掉以停止循环\n    /*\r\n  useEffect(() => {\r\n    if (classInfo?.id && userId) {\r\n      refreshCurrentTemplate();\r\n    }\r\n  }, [classInfo?.id, userId]);\r\n  */ // 移除这个useEffect，避免在currentTemplate变化时覆盖个人分配的模板\n    // 使用 useRef 来保存最新的 personalTemplateAssignments\n    const personalTemplateAssignmentsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(personalTemplateAssignments);\n    personalTemplateAssignmentsRef.current = personalTemplateAssignments;\n    // 监听全局模板变化，重新获取教师当前模板并更新学生数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (globalTemplateChangeVersion > 0 && (classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) {\n            console.log(\"检测到模板变化，重新获取教师当前模板\");\n            // 重新获取教师当前模板\n            fetchTeacherCurrentTemplate().then((newTemplate)=>{\n                if (newTemplate && students.length > 0) {\n                    // 不清除个人分配的模板记录，保留学生的个人模板数据\n                    // setPersonalTemplateAssignments(new Map()); // 注释掉这行\n                    // 使用 ref 获取最新的 personalTemplateAssignments\n                    const currentPersonalAssignments = personalTemplateAssignmentsRef.current;\n                    // 只更新没有个人模板的学生为新的教师当前模板\n                    console.log(\"全局模板变化，开始更新学生模板:\", {\n                        newTemplate,\n                        personalTemplateAssignments: Array.from(currentPersonalAssignments.entries()),\n                        studentsCount: students.length\n                    });\n                    setStudents((prevStudents)=>prevStudents.map((student)=>{\n                            const hasPersonalTemplate = currentPersonalAssignments.has(student.userId);\n                            console.log(\"学生 \".concat(student.nickName, \" (\").concat(student.userId, \"):\"), {\n                                hasPersonalTemplate,\n                                currentTemplate: student.currentTemplate,\n                                willUpdate: !hasPersonalTemplate\n                            });\n                            // 如果学生有个人分配的模板，保持不变\n                            if (hasPersonalTemplate) {\n                                console.log(\"保持学生 \".concat(student.nickName, \" 的个人模板\"));\n                                return student;\n                            }\n                            // 否则更新为新的教师当前模板\n                            console.log(\"更新学生 \".concat(student.nickName, \" 为教师模板\"));\n                            return {\n                                ...student,\n                                currentTemplate: newTemplate\n                            };\n                        }));\n                }\n            });\n        }\n    }, [\n        globalTemplateChangeVersion,\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id\n    ]);\n    // 定期检查教师模板更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) return;\n        // 立即检查一次\n        syncTeacherTemplateToStudents();\n        // 设置定时器，每30秒检查一次模板更新\n        const interval = setInterval(()=>{\n            syncTeacherTemplateToStudents();\n        }, 30000); // 30秒\n        return ()=>clearInterval(interval);\n    }, [\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n        students.length\n    ]);\n    // 点击外部关闭下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n            // setIsMoreActionsDropdownOpen(false); // 已移除\n            }\n            if (settingsDropdownRef.current && !settingsDropdownRef.current.contains(event.target)) {\n                setIsSettingsDropdownOpen(false);\n            }\n            if (batchActionsDropdownRef.current && !batchActionsDropdownRef.current.contains(event.target)) {\n                setIsBatchActionsDropdownOpen(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, []);\n    // 处理学生点击选择（用于右侧显示详情）\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n    };\n    // 处理删除学生\n    const handleDeleteStudent = async ()=>{\n        if (!selectedStudent) {\n            alert(\"请先选择要删除的学生\");\n            return;\n        }\n        // 显示确认对话框\n        const confirmed = window.confirm(\"确定要将 \".concat(selectedStudent.nickName, \" 移出班级吗？\\n\\n此操作不可恢复！\"));\n        if (!confirmed) {\n            return;\n        }\n        try {\n            console.log(\"删除学生:\", selectedStudent);\n            // 调用删除学生的API，传入学生的userId数组\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.removeStudentFromClass([\n                selectedStudent.userId\n            ]);\n            console.log(\"删除学生 API 响应:\", response.data);\n            // 检查响应状态\n            if (response.data.code === 200) {\n                console.log(\"删除学生成功\");\n                alert(\"学生已成功移出班级\");\n                // 清除选中的学生\n                setSelectedStudent(null);\n                // 重新获取学生列表\n                await fetchStudents();\n            } else {\n                console.error(\"删除学生失败:\", response.data.message);\n                alert(response.data.message || \"删除学生失败\");\n            }\n        } catch (error) {\n            console.error(\"删除学生失败:\", error);\n            alert(\"删除学生失败，请稍后重试\");\n        }\n    };\n    // 编辑班级\n    const handleEditClass = async (values)=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        try {\n            console.log(\"开始编辑班级:\", {\n                classId: classInfo.id,\n                values: values,\n                originalClassName: classInfo.className\n            });\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.updateClass(classInfo.id, {\n                className: values.className,\n                grade: classInfo.grade || \"\" // 确保传递grade字段，如果没有则使用空字符串\n            });\n            console.log(\"编辑班级API响应:\", response);\n            if (response.data.code === 200) {\n                console.log(\"编辑班级成功\");\n                notification.success(\"编辑班级成功\");\n                setIsEditClassModalVisible(false);\n                // 更新本地班级信息\n                const updatedClassInfo = {\n                    ...classInfo,\n                    className: values.className\n                };\n                setClassInfo(updatedClassInfo);\n                // 通知父组件更新班级列表中的班级信息\n                onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n            } else {\n                console.error(\"编辑班级失败 - API返回错误:\", {\n                    code: response.data.code,\n                    message: response.data.message,\n                    data: response.data\n                });\n                notification.error(response.data.message || \"编辑班级失败\");\n                throw new Error(response.data.message || \"编辑班级失败\"); // 抛出错误让模态框知道失败了\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"编辑班级失败 - 请求异常:\", {\n                error: error,\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // 只有在没有显示过错误消息的情况下才显示通用错误\n            if (!error.message || error.message === \"编辑班级失败\") {\n                var _error_response_data, _error_response2;\n                notification.error(((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"编辑班级失败，请稍后重试\");\n            }\n            throw error; // 重新抛出错误，让模态框保持打开状态\n        }\n    };\n    // 导入学生\n    const handleImportStudents = async (file)=>{\n        try {\n            console.log(\"导入学生文件:\", file);\n            // 这里需要实现文件解析和导入逻辑\n            alert(\"导入学生功能正在开发中\");\n            setIsImportStudentModalVisible(false);\n            return true;\n        } catch (error) {\n            console.error(\"导入学生失败:\", error);\n            alert(\"导入学生失败，请稍后重试\");\n            return false;\n        }\n    };\n    // 导出学生\n    const handleExportStudents = async ()=>{\n        try {\n            const response = await _lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.exportStudents(classInfo.id);\n            console.log(\"导出学生成功:\", response);\n            alert(\"导出学生成功\");\n        } catch (error) {\n            console.error(\"导出学生失败:\", error);\n            alert(\"导出学生失败，请稍后重试\");\n        }\n    };\n    // 搜索教师\n    const handleSearchTeacher = async (phone)=>{\n        try {\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.searchTeacherByPhone(phone);\n            console.log(\"搜索教师响应:\", response);\n            if (response.data.code === 200) {\n                setSearchedTeacher(response.data.data);\n            } else {\n                const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                const notification = GetNotification();\n                notification.error(response.data.message || \"搜索教师失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"搜索教师失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"搜索教师失败\");\n        }\n    };\n    // 转让班级\n    const handleTransferClass = async (values)=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        setTransferLoading(true);\n        try {\n            let newTeacherId;\n            if (values.transferType === \"search\") {\n                if (!searchedTeacher) {\n                    notification.error(\"请先搜索并选择教师\");\n                    return;\n                }\n                newTeacherId = searchedTeacher.id;\n            } else {\n                // 检查是否有协助教师\n                if (!classInfo.assistantTeacherId) {\n                    notification.error(\"该班级没有协助教师\");\n                    return;\n                }\n                newTeacherId = classInfo.assistantTeacherId;\n            }\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.transferClass(classInfo.id, newTeacherId, values.transferType);\n            if (response.data.code === 200) {\n                notification.success(\"转让班级成功\");\n                setIsTransferClassModalVisible(false);\n                setSearchedTeacher(null);\n                // 转让成功后返回班级管理页面\n                onBack();\n            } else {\n                notification.error(response.data.message || \"转让班级失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"转让班级失败:\", error);\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"转让班级失败\");\n        } finally{\n            setTransferLoading(false);\n        }\n    };\n    // 移出协助教师\n    const handleRemoveAssistant = async ()=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        try {\n            // 这里需要调用移出协助教师的API\n            // 暂时使用转让API，将assistantTeacherId设为0\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.updateClass(classInfo.id, {\n                assistantTeacherId: 0\n            });\n            if (response.data.code === 200) {\n                notification.success(\"移出协助教师成功\");\n                setIsTransferClassModalVisible(false);\n                // 更新本地班级信息\n                const updatedClassInfo = {\n                    ...classInfo,\n                    assistantTeacherId: 0\n                };\n                setClassInfo(updatedClassInfo);\n                onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n            } else {\n                notification.error(response.data.message || \"移出协助教师失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"移出协助教师失败:\", error);\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"移出协助教师失败\");\n        }\n    };\n    // 生成邀请码\n    const handleGenerateInviteCode = async ()=>{\n        try {\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.generateInviteCode(classInfo.id);\n            if (response.data.code === 200) {\n                setInviteCode(response.data.data.inviteCode);\n                setIsInviteCodeModalVisible(true);\n            } else {\n                console.error(\"生成邀请码失败:\", response.data.message);\n                alert(response.data.message || \"生成邀请码失败\");\n            }\n        } catch (error) {\n            console.error(\"生成邀请码失败:\", error);\n            alert(\"生成邀请码失败，请稍后重试\");\n        }\n    };\n    // 分配积木（批量或选中学生）\n    const handleAssignBlocks = async ()=>{\n        console.log(\"=== 分配积木开始 ===\");\n        console.log(\"selectedStudentIds:\", selectedStudentIds);\n        console.log(\"selectedStudentIds.length:\", selectedStudentIds.length);\n        // 如果有选中的学生，设置为单个学生分配模式\n        if (selectedStudentIds.length === 1) {\n            console.log(\"单个学生分配模式，studentId:\", selectedStudentIds[0]);\n            setSelectedStudentId(selectedStudentIds[0]);\n        } else {\n            console.log(\"批量分配模式，学生数量:\", selectedStudentIds.length);\n            setSelectedStudentId(null);\n        }\n        await fetchTemplates();\n        await fetchTemplateUsage();\n        setIsAssignBlocksModalVisible(true);\n    };\n    // 单独为学生分配积木\n    const handleIndividualAssignBlocks = async (studentId)=>{\n        console.log(\"=== 单独为学生分配积木 ===\");\n        console.log(\"studentId:\", studentId);\n        // 设置为单个学生分配模式\n        setSelectedStudentId(studentId);\n        setSelectedStudentIds([]); // 清空批量选择\n        await fetchTemplates();\n        await fetchTemplateUsage();\n        setIsAssignBlocksModalVisible(true);\n    };\n    // 处理模板分配（从模板卡片点击）\n    const handleTemplateAssignment = async ()=>{\n        if (!selectedStudent) return;\n        console.log(\"=== 从模板卡片分配模板 ===\");\n        console.log(\"selectedStudent:\", selectedStudent);\n        await handleIndividualAssignBlocks(selectedStudent.userId);\n    };\n    // 删除班级\n    const handleDeleteClass = async ()=>{\n        const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        // 先检查是否有学生\n        if (students.length > 0) {\n            Modal.warning({\n                title: \"无法删除班级\",\n                centered: true,\n                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"班级中还有 \",\n                                students.length,\n                                \" 名学生，请先移除所有学生后再删除班级。\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 805,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-gray-500 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"删除步骤：\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 807,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    className: \"list-decimal ml-4 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"选择要移除的学生\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"使用批量操作移除所有学生\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"再次尝试删除班级\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 808,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 806,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 804,\n                    columnNumber: 11\n                }, undefined),\n                okText: \"知道了\"\n            });\n            return;\n        }\n        // 如果没有学生，显示删除确认对话框\n        Modal.confirm({\n            title: \"确认删除班级\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"您确定要删除 \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: classInfo.className\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 826,\n                                columnNumber: 21\n                            }, undefined),\n                            \" 吗？\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 826,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-3 bg-red-50 rounded-lg border border-red-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 830,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"此操作不可恢复！\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 832,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 828,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-500\",\n                                children: \"删除班级将永久移除班级信息，包括班级设置、模板配置等数据。\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 834,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 827,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 825,\n                columnNumber: 9\n            }, undefined),\n            okText: \"确定删除\",\n            cancelText: \"取消\",\n            okButtonProps: {\n                danger: true\n            },\n            centered: true,\n            onOk: async ()=>{\n                try {\n                    const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.deleteClass(classInfo.id);\n                    if (response.data.code === 200) {\n                        notification.success(\"删除班级成功\");\n                        // 通知父组件班级已被删除\n                        onClassDeleted === null || onClassDeleted === void 0 ? void 0 : onClassDeleted(classInfo.id);\n                        // 返回到班级管理页面\n                        onBack();\n                    } else {\n                        notification.error(response.data.message || \"删除班级失败\");\n                    }\n                } catch (error) {\n                    var _error_response_data, _error_response;\n                    console.error(\"删除班级失败:\", error);\n                    if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                        notification.error(error.response.data.message);\n                    } else {\n                        notification.error(\"删除班级失败，请稍后重试\");\n                    }\n                }\n            }\n        });\n    };\n    // 下拉菜单项处理函数\n    const handleMenuItemClick = (action)=>{\n        // setIsMoreActionsDropdownOpen(false); // 已移除\n        switch(action){\n            case \"edit\":\n                setIsEditClassModalVisible(true);\n                break;\n            case \"addStudent\":\n                setIsAddStudentModalVisible(true);\n                break;\n            case \"importStudent\":\n                setIsImportStudentModalVisible(true);\n                break;\n            case \"exportStudent\":\n                handleExportStudents();\n                break;\n            case \"transfer\":\n                setIsTransferClassModalVisible(true);\n                break;\n            case \"invite\":\n                handleGenerateInviteCode();\n                break;\n            case \"batchRedeem\":\n                console.log(\"批量兑换密令\");\n                setIsBatchUseKeyPackageModalVisible(true);\n                break;\n            case \"assignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"deleteClass\":\n                handleDeleteClass();\n                break;\n            default:\n                break;\n        }\n    };\n    // 设置下拉菜单项处理函数\n    const handleSettingsMenuItemClick = (action)=>{\n        setIsSettingsDropdownOpen(false);\n        switch(action){\n            case \"edit\":\n                setIsEditClassModalVisible(true);\n                break;\n            case \"addStudent\":\n                setIsAddStudentModalVisible(true);\n                break;\n            case \"importStudent\":\n                setIsImportStudentModalVisible(true);\n                break;\n            case \"exportStudent\":\n                handleExportStudents();\n                break;\n            case \"transfer\":\n                setIsTransferClassModalVisible(true);\n                break;\n            case \"invite\":\n                handleGenerateInviteCode();\n                break;\n            case \"batchRedeem\":\n                console.log(\"批量兑换密令\");\n                setIsBatchUseKeyPackageModalVisible(true);\n                break;\n            case \"assignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"deleteClass\":\n                handleDeleteClass();\n                break;\n            default:\n                break;\n        }\n    };\n    // 确保选中状态同步\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const shouldBeSelectAll = selectedStudentIds.length > 0 && selectedStudentIds.length === students.length;\n        if (isSelectAll !== shouldBeSelectAll) {\n            console.log(\"修复选中状态同步:\", {\n                isSelectAll,\n                shouldBeSelectAll,\n                selectedStudentIds: selectedStudentIds.length,\n                totalStudents: students.length\n            });\n            setIsSelectAll(shouldBeSelectAll);\n        }\n    }, [\n        selectedStudentIds,\n        students.length,\n        isSelectAll\n    ]);\n    // 全选/取消全选处理函数\n    const handleSelectAll = ()=>{\n        if (isSelectAll) {\n            // 取消全选\n            setSelectedStudentIds([]);\n            setIsSelectAll(false);\n        } else {\n            // 全选\n            const allStudentIds = students.map((student)=>student.userId);\n            setSelectedStudentIds(allStudentIds);\n            setIsSelectAll(true);\n        }\n    };\n    // 单个学生选择处理函数\n    const handleStudentSelect = (studentId)=>{\n        if (selectedStudentIds.includes(studentId)) {\n            // 取消选择\n            const newSelectedIds = selectedStudentIds.filter((id)=>id !== studentId);\n            setSelectedStudentIds(newSelectedIds);\n            setIsSelectAll(false);\n        } else {\n            // 选择\n            const newSelectedIds = [\n                ...selectedStudentIds,\n                studentId\n            ];\n            setSelectedStudentIds(newSelectedIds);\n            // 检查是否全选\n            if (newSelectedIds.length === students.length) {\n                setIsSelectAll(true);\n            }\n        }\n    };\n    // 批量操作处理函数\n    const handleBatchAction = (action)=>{\n        setIsBatchActionsDropdownOpen(false);\n        if (selectedStudentIds.length === 0) {\n            Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                let { GetNotification } = param;\n                GetNotification().warning(\"请先选择要操作的学生\");\n            });\n            return;\n        }\n        switch(action){\n            case \"batchDelete\":\n                handleBatchRemoveStudents(selectedStudentIds);\n                break;\n            case \"batchAssignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"batchAssignPoints\":\n                // 清除单个学生选择，确保进入批量模式\n                setSelectedStudent(null);\n                setIsAssignPointsModalVisible(true);\n                break;\n            case \"batchUseKeyPackage\":\n                handleBatchUseKeyPackage();\n                break;\n            case \"batchExport\":\n                console.log(\"批量导出学生:\", selectedStudentIds);\n                Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                    let { GetNotification } = param;\n                    GetNotification().info(\"批量导出学生功能正在开发中\");\n                });\n                break;\n            default:\n                break;\n        }\n    };\n    // 批量移出班级的改进版本\n    const handleBatchRemoveStudents = async (studentIds)=>{\n        try {\n            // 获取选中的学生信息\n            const selectedStudentsInfo = students.filter((s)=>studentIds.includes(s.userId));\n            // 计算总可用积分\n            const totalAvailablePoints = selectedStudentsInfo.reduce((sum, student)=>sum + (student.availablePoints || 0), 0);\n            const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n            const { InfoCircleOutlined } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_ant-design_icons_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/index.js\"));\n            Modal.confirm({\n                title: \"确认批量移出班级\",\n                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"确定要将选中的 \",\n                                studentIds.length,\n                                \" 名学生移出班级吗？\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1047,\n                            columnNumber: 13\n                        }, undefined),\n                        totalAvailablePoints > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 p-3 bg-yellow-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-yellow-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoCircleOutlined, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1051,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"选中的学生共有 \",\n                                                totalAvailablePoints,\n                                                \" 点可用能量\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1052,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1050,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-sm text-yellow-500\",\n                                    children: \"移出班级后，可用能量将返还到各自的套餐积分中\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1054,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1049,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1046,\n                    columnNumber: 11\n                }, undefined),\n                okText: \"确定移出\",\n                cancelText: \"取消\",\n                centered: true,\n                okButtonProps: {\n                    danger: true\n                },\n                onOk: async ()=>{\n                    try {\n                        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                        const notification = GetNotification();\n                        const hideLoading = notification.loading(\"正在移出学生...\");\n                        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.removeStudentFromClass(studentIds);\n                        if (hideLoading) {\n                            hideLoading.close();\n                        }\n                        if (response.data.code === 200) {\n                            notification.success(\"成功移出 \".concat(studentIds.length, \" 名学生\"));\n                            // 清除选择状态\n                            setSelectedStudentIds([]);\n                            setIsSelectAll(false);\n                            setSelectedStudent(null);\n                            // 重新获取学生列表\n                            await fetchStudents();\n                        } else {\n                            notification.error(response.data.message || \"批量移出学生失败\");\n                        }\n                    } catch (error) {\n                        var _error_response_data, _error_response;\n                        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                        const notification = GetNotification();\n                        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"批量移出学生失败\");\n                        console.error(\"批量移出学生失败:\", error);\n                    }\n                }\n            });\n        } catch (error) {\n            console.error(\"批量移出学生失败:\", error);\n        }\n    };\n    // 处理添加学生\n    const handleAddStudent = async (values)=>{\n        try {\n            console.log(\"添加学生:\", values);\n            // 添加默认密码\n            const studentData = {\n                ...values,\n                password: \"123456\"\n            };\n            // 调用添加学生的API\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.addStudentToClass(classInfo.id, studentData);\n            console.log(\"添加学生 API 响应:\", response.data);\n            // 检查响应状态\n            if (response.data.code === 200) {\n                console.log(\"添加学生成功\");\n                setIsAddStudentModalVisible(false);\n                // 重新获取学生列表\n                await fetchStudents();\n            } else {\n                console.error(\"添加学生失败:\", response.data.message);\n                alert(response.data.message || \"添加学生失败\");\n            }\n        } catch (error) {\n            console.error(\"添加学生失败:\", error);\n            alert(\"添加学生失败，请稍后重试\");\n        }\n    };\n    // 处理文件上传\n    const handleUpload = async (file)=>{\n        setUploading(true);\n        try {\n            // 这里可以添加文件上传逻辑\n            console.log(\"上传文件:\", file);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error(\"文件上传失败:\", error);\n            return {\n                success: false\n            };\n        } finally{\n            setUploading(false);\n        }\n    };\n    // 处理文件删除\n    const handleRemoveFile = async (file)=>{\n        try {\n            setFileList((prev)=>prev.filter((f)=>f.uid !== file.uid));\n            return true;\n        } catch (error) {\n            console.error(\"删除文件失败:\", error);\n            return false;\n        }\n    };\n    // 处理发布任务\n    const handlePublishTask = async (values)=>{\n        try {\n            console.log(\"发布任务:\", values);\n            // 导入taskApi\n            const { default: taskApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/task */ \"(app-pages-browser)/./lib/api/task.ts\"));\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 构建任务参数\n            const params = {\n                taskName: values.taskName,\n                taskDescription: values.taskDescription || \"\",\n                taskType: 1,\n                startDate: values.startDate ? new Date(values.startDate) : undefined,\n                endDate: values.endDate ? new Date(values.endDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n                taskContent: values.taskContent || \"\",\n                attachments: fileList.map((file)=>{\n                    var _file_response;\n                    return file.url || ((_file_response = file.response) === null || _file_response === void 0 ? void 0 : _file_response.url);\n                }).filter(Boolean),\n                teacherId: userId,\n                classId: classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n                studentIds: selectedStudentIds.length > 0 ? selectedStudentIds : students.map((s)=>s.userId),\n                selfAssessmentItems: values.selfAssessmentItems || [],\n                priority: 1,\n                isPublic: 0,\n                allowLateSubmission: values.allowLateSubmission || false\n            };\n            console.log(\"发布任务参数:\", params);\n            // 调用发布任务API\n            const response = await taskApi.publishTask(params);\n            if (response.data.code === 200) {\n                // 显示详细的成功提示\n                const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n                const studentCount = selectedStudentIds.length > 0 ? selectedStudentIds.length : students.length;\n                const className = (classInfo === null || classInfo === void 0 ? void 0 : classInfo.className) || \"当前班级\";\n                Modal.success({\n                    title: \"任务发布成功\",\n                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"任务 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: params.taskName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1204,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    \" 已成功发布到 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: className\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1204,\n                                        columnNumber: 63\n                                    }, undefined),\n                                    \"。\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1204,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"共有 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: studentCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1205,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    \" 名学生将收到此任务。\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1205,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"学生可以在班级空间查看和提交任务。\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1206,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1203,\n                        columnNumber: 13\n                    }, undefined),\n                    okText: \"确定\",\n                    onOk: ()=>{\n                    // 可以在这里添加导航到任务管理页面的逻辑\n                    }\n                });\n                setIsPublishTaskModalVisible(false);\n                // 清理表单数据\n                setFileList([]);\n                setSelectedStudentIds([]);\n                setSelectedTemplateId(null);\n                // 刷新学生列表（如果需要显示任务相关信息）\n                await fetchStudents();\n            } else {\n                notification.error(response.data.message || \"任务发布失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"发布任务失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"发布任务失败，请稍后重试\");\n        }\n    };\n    // 处理重置密码\n    const handleResetPassword = async ()=>{\n        try {\n            if (!selectedStudent) {\n                alert(\"请先选择要重置密码的学生\");\n                return;\n            }\n            console.log(\"重置密码:\", selectedStudent);\n            // 这里可以添加重置密码的API调用\n            setIsResetPasswordModalVisible(false);\n            alert(\"密码重置成功，新密码为：123456\");\n        } catch (error) {\n            console.error(\"重置密码失败:\", error);\n            alert(\"重置密码失败，请稍后重试\");\n        }\n    };\n    // 处理选择模板\n    const handleSelectTemplate = async (templateId)=>{\n        try {\n            const { addUserJoinRole, batchAddUserJoinRole } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 确定要分配的学生\n            const targetStudents = selectedStudentId !== null ? [\n                selectedStudentId\n            ] : selectedStudentIds;\n            console.log(\"=== 模板分配详情 ===\");\n            console.log(\"selectedStudentId:\", selectedStudentId);\n            console.log(\"selectedStudentIds:\", selectedStudentIds);\n            console.log(\"targetStudents:\", targetStudents);\n            console.log(\"templateId:\", templateId);\n            if (targetStudents.length === 0) {\n                console.log(\"❌ 没有选中任何学生\");\n                notification.warning(\"请先选择学生\");\n                return;\n            }\n            const hideLoading = notification.loading(\"正在分配积木...\");\n            const userRolesMap = userRoles || [];\n            try {\n                // 准备用户数据 - 与teacher-space保持一致的逻辑\n                const usersData = targetStudents.map((userId)=>{\n                    const userInfo = userRolesMap.find((u)=>u.userId === userId);\n                    if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.roleId)) return null;\n                    return {\n                        userId: userId,\n                        roleId: userInfo.roleId,\n                        templateId: templateId,\n                        originalTemplateId: templateId\n                    };\n                }).filter((item)=>item !== null);\n                console.log(\"准备分配模板:\", {\n                    templateId,\n                    targetStudents,\n                    userRolesMap,\n                    usersData\n                });\n                if (usersData.length === 0) {\n                    notification.error(\"无有效用户可分配\");\n                    return;\n                }\n                // 分批并发处理\n                const results = await (usersData.length > 20 ? Promise.all(Array.from({\n                    length: Math.ceil(usersData.length / 20)\n                }, (_, i)=>usersData.slice(i * 20, (i + 1) * 20)).map((batchUsers)=>batchAddUserJoinRole({\n                        users: batchUsers\n                    }).then((param)=>{\n                        let { data } = param;\n                        return data;\n                    }).catch(()=>({\n                            code: 500,\n                            data: {\n                                successCount: 0,\n                                failCount: batchUsers.length\n                            }\n                        })))).then((results)=>({\n                        code: results.some((r)=>r.code !== 200) ? 500 : 200,\n                        data: {\n                            successCount: results.reduce((sum, r)=>{\n                                var _r_data;\n                                return sum + (((_r_data = r.data) === null || _r_data === void 0 ? void 0 : _r_data.successCount) || 0);\n                            }, 0),\n                            failCount: results.reduce((sum, r)=>{\n                                var _r_data;\n                                return sum + (((_r_data = r.data) === null || _r_data === void 0 ? void 0 : _r_data.failCount) || 0);\n                            }, 0)\n                        }\n                    })) : batchAddUserJoinRole({\n                    users: usersData\n                }).then((param)=>{\n                    let { data } = param;\n                    return data;\n                }).catch(()=>({\n                        code: 500,\n                        data: {\n                            successCount: 0,\n                            failCount: usersData.length\n                        }\n                    })));\n                if (hideLoading) {\n                    hideLoading.close();\n                }\n                // 显示结果\n                if (results.code === 200) {\n                    const { successCount = 0, failCount = 0 } = results.data || {};\n                    if (successCount > 0 && failCount === 0) {\n                        notification.success(\"成功为 \".concat(successCount, \" 名学生分配积木\"));\n                    } else if (successCount > 0 && failCount > 0) {\n                        notification.warning(\"成功为 \".concat(successCount, \" 名学生分配积木，\").concat(failCount, \" 名学生分配失败\"));\n                    } else {\n                        notification.error(\"积木分配失败\");\n                    }\n                } else {\n                    notification.error(\"积木分配失败\");\n                }\n                // 立即更新已分配学生的模板信息，无需等待API刷新\n                const selectedTemplate = templates.find((t)=>t.id === templateId);\n                if (selectedTemplate) {\n                    const templateData = {\n                        templateId: templateId,\n                        templateName: selectedTemplate.templateName || selectedTemplate.name,\n                        isOfficial: selectedTemplate.isOfficial || false\n                    };\n                    // 更新personalTemplateAssignments Map，确保数据持久化\n                    setPersonalTemplateAssignments((prev)=>{\n                        const newMap = new Map(prev);\n                        targetStudents.forEach((studentId)=>{\n                            newMap.set(studentId, templateData);\n                        });\n                        return newMap;\n                    });\n                    // 更新学生状态\n                    const updatedStudents = students.map((student)=>{\n                        if (targetStudents.includes(student.userId)) {\n                            // 被选中的学生：设置为新分配的模板\n                            return {\n                                ...student,\n                                currentTemplate: templateData\n                            };\n                        }\n                        // 未选中的学生：保持原有状态\n                        return student;\n                    });\n                    setStudents(updatedStudents);\n                    console.log(\"模板分配成功，已更新personalTemplateAssignments:\", {\n                        targetStudents,\n                        templateData,\n                        personalTemplateAssignmentsBefore: Array.from(personalTemplateAssignments.entries()),\n                        personalTemplateAssignmentsAfter: \"will be updated\"\n                    });\n                    // 延迟打印更新后的状态\n                    setTimeout(()=>{\n                        console.log(\"personalTemplateAssignments更新后:\", Array.from(personalTemplateAssignments.entries()));\n                    }, 100);\n                }\n                // 关闭弹窗并清理状态\n                setIsAssignBlocksModalVisible(false);\n                setSelectedStudentId(null);\n                setSelectedStudentIds([]); // 清空选中的学生\n                setIsSelectAll(false); // 取消全选状态\n                // 刷新相关数据\n                await fetchTemplateUsage(); // 刷新模板使用情况\n            // 当前模板信息由全局状态管理，无需手动刷新\n            } catch (error) {\n                if (hideLoading) {\n                    hideLoading.close();\n                }\n                throw error;\n            }\n        } catch (error) {\n            console.error(\"分配模板失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"分配模板失败，请稍后重试\");\n        }\n    };\n    // 处理模板使用情况点击\n    const handleTemplateUsageClick = (e, template)=>{\n        e.stopPropagation();\n        console.log(\"查看模板使用情况:\", template);\n    };\n    // 处理单个学生分配能量\n    const handleAssignPoints = async (values)=>{\n        var _values_studentExpiries;\n        if (!selectedStudent) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"请先选择学生\");\n            return;\n        }\n        // 从 studentExpiries 中提取单个学生的过期时间\n        const expireTime = (_values_studentExpiries = values.studentExpiries) === null || _values_studentExpiries === void 0 ? void 0 : _values_studentExpiries[selectedStudent.userId];\n        try {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            const hideLoading = notification.loading(\"正在分配能量...\");\n            const { pointsApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\"));\n            await pointsApi.assignPermission({\n                studentUserId: selectedStudent.userId,\n                availablePoints: values.availablePoints,\n                expireTime: expireTime,\n                remark: values.remark\n            });\n            if (hideLoading) {\n                hideLoading.close();\n            }\n            notification.success(\"分配能量成功\");\n            setIsAssignPointsModalVisible(false);\n            // 刷新学生列表\n            await refreshStudentList();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 增加更具体的错误提示\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"分配能量失败\");\n        }\n    };\n    // 刷新学生列表\n    const refreshStudentList = async ()=>{\n        await fetchStudents();\n    };\n    // 更新教师的当前模板（通过UserJoinRole表）\n    const updateClassCurrentTemplate = async (templateId, templateName, isOfficial)=>{\n        try {\n            console.log(\"更新教师当前模板:\", {\n                userId,\n                roleId,\n                templateId,\n                templateName,\n                isOfficial\n            });\n            // 使用addUserJoinRole API来更新教师的模板\n            const { addUserJoinRole } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            const response = await addUserJoinRole({\n                userId: userId,\n                roleId: roleId || 2,\n                templateId: templateId\n            });\n            if (response.data.code === 200) {\n                // 更新本地的全局当前模板状态\n                const newTemplate = {\n                    templateId: templateId,\n                    templateName: templateName,\n                    isOfficial: isOfficial\n                };\n                setGlobalCurrentTemplate(newTemplate);\n                console.log(\"教师当前模板更新成功:\", newTemplate);\n            } else {\n                console.error(\"更新教师当前模板失败:\", response.data);\n            }\n        } catch (error) {\n            console.error(\"更新教师当前模板失败:\", error);\n        }\n    };\n    // 更新学生的当前模板信息\n    const handleUpdateStudentTemplate = (studentIds, templateInfo)=>{\n        const templateData = {\n            templateId: templateInfo.templateId,\n            templateName: templateInfo.templateName,\n            isOfficial: templateInfo.isOfficial || false\n        };\n        // 保存个人分配的模板信息\n        setPersonalTemplateAssignments((prev)=>{\n            const newMap = new Map(prev);\n            studentIds.forEach((studentId)=>{\n                newMap.set(studentId, templateData);\n            });\n            return newMap;\n        });\n        // 更新学生状态\n        setStudents((prevStudents)=>prevStudents.map((student)=>{\n                if (studentIds.includes(student.userId)) {\n                    return {\n                        ...student,\n                        currentTemplate: templateData\n                    };\n                }\n                return student;\n            }));\n    // 不再自动更新教师模板，只分配给学生\n    // updateClassCurrentTemplate(templateData.templateId, templateData.templateName, templateData.isOfficial);\n    };\n    // 处理批量/单个兑换密钥\n    const handleBatchUseKeyPackage = (studentId)=>{\n        if (studentId) {\n            // 单个学生触发\n            setSelectedStudentIds([\n                studentId\n            ]);\n            setIsBatchUseKeyPackageModalVisible(true);\n        } else {\n            // 批量操作触发\n            if (selectedStudentIds.length === 0) {\n                Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                    let { GetNotification } = param;\n                    GetNotification().warning(\"请先选择学生\");\n                });\n                return;\n            }\n            setIsBatchUseKeyPackageModalVisible(true);\n        }\n    };\n    // 处理兑换密令成功\n    const handleBatchUseKeyPackageSuccess = ()=>{\n        refreshStudentList();\n        setIsBatchUseKeyPackageModalVisible(false);\n    };\n    // 处理从兑换密令跳转到分配能量\n    const handleGoToAssignPointsFromRedeem = (studentIds)=>{\n        setIsBatchUseKeyPackageModalVisible(false);\n        // 设置选中的学生\n        setSelectedStudentIds(studentIds);\n        // 打开分配能量弹窗\n        setIsAssignPointsModalVisible(true);\n    };\n    // 批量分配能量处理函数\n    const handleBatchAssignPoints = async (values)=>{\n        if (selectedStudentIds.length === 0) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"请先选择学生\");\n            return;\n        }\n        if (!values.studentExpiries) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"未能获取学生过期时间信息\");\n            return;\n        }\n        try {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            const hideLoading = notification.loading(\"正在批量分配能量...\");\n            // 调用新的批量分配 API\n            const { pointsApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\"));\n            const response = await pointsApi.batchAssignPermission({\n                availablePoints: values.availablePoints,\n                studentExpiries: values.studentExpiries,\n                remark: values.remark\n            });\n            console.log(\"批量分配积分res\", response);\n            if (hideLoading) {\n                hideLoading.close();\n            }\n            if (response.data.code === 200) {\n                // 后端现在返回处理结果数组，可以根据需要处理\n                const results = response.data.data;\n                // 可以根据 results 中的信息给出更详细的成功/失败提示，\n                // 但为了简单起见，我们仍然使用之前的逻辑\n                notification.success(\"成功为 \".concat(results.success, \" 名学生分配能量\"));\n                setIsAssignPointsModalVisible(false);\n                // 刷新学生列表\n                await refreshStudentList(); // 确保使用 await\n                setSelectedStudentIds([]); // 清空选择\n                setIsSelectAll(false);\n            } else {\n                // 处理 API 返回的错误信息\n                notification.error(response.data.message || \"批量分配能量失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"批量分配能量失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 处理请求级别的错误\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"批量分配能量失败，请检查网络连接或稍后重试\");\n        }\n    };\n    if (!selectedSchool) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"class-detail-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-content\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"error-message\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"学校信息不存在\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1634,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onBack,\n                            className: \"back-button\",\n                            children: \"返回班级管理\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1635,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1633,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1632,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n            lineNumber: 1631,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 生成随机头像颜色\n    const getAvatarColor = (index)=>{\n        const colors = [\n            \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n            \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n            \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n            \"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)\",\n            \"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\",\n            \"linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)\"\n        ];\n        // 确保index是有效的正数\n        const safeIndex = Math.max(0, index);\n        return colors[safeIndex % colors.length];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"class-detail-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"back-button\",\n                    onClick: onBack,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1667,\n                            columnNumber: 11\n                        }, undefined),\n                        \"返回班级管理\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1663,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1662,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"left-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"left-section-header\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"settings-container\",\n                                    ref: settingsDropdownRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"settings-btn \".concat(isSettingsDropdownOpen ? \"active\" : \"\"),\n                                            onClick: ()=>setIsSettingsDropdownOpen(!isSettingsDropdownOpen),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1682,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1678,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isSettingsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"dropdown-menu\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-menu-items\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"edit\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#8b5cf6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1693,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1692,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"编辑班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1695,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1688,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"addStudent\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#10b981\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1703,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1702,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"添加学生\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1705,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1698,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"importStudent\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#3b82f6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1713,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1712,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"导入学生\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1715,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1708,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"exportStudent\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#10b981\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1723,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1722,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"导出学生\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1725,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1718,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"transfer\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#f59e0b\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1733,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1732,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"转让管理\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1735,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1728,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"invite\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#3b82f6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1743,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1742,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"生成邀请码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1745,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1738,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"batchRedeem\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#f59e0b\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1753,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1752,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"批量兑换密令\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1755,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1748,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"assignBlocks\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#3b82f6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1763,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1762,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"分配积木\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1765,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1758,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-divider\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1768,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item danger\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"deleteClass\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1775,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1774,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"删除班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1777,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1770,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1687,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1686,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1677,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1676,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"class-info-card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"class-card-header\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"class-card-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"class-card-icon\",\n                                                    children: \"\\uD83D\\uDCCB\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1789,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"class-card-info\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"class-card-title\",\n                                                            children: classInfo.className\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1793,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"class-card-subtitle\",\n                                                            children: selectedSchool.schoolName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1794,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1792,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1788,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"class-card-badge\",\n                                            children: \"当前模板\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1797,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1787,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1786,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"search-section\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"search-box\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        size: 16,\n                                        className: \"search-icon\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1806,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1805,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1804,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"students-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"students-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"学生\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1813,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"students-actions\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"add-student-btn\",\n                                                        onClick: ()=>setIsAddStudentModalVisible(true),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1819,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1815,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"select-all-btn \".concat(isSelectAll ? \"active\" : \"\"),\n                                                        onClick: ()=>{\n                                                            console.log(\"点击全选按钮，当前状态:\", {\n                                                                isSelectAll,\n                                                                selectedStudentIds: selectedStudentIds.length,\n                                                                totalStudents: students.length\n                                                            });\n                                                            handleSelectAll();\n                                                        },\n                                                        title: isSelectAll ? \"取消全选\" : \"全选\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: isSelectAll ? \"取消全选\" : \"全选\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1833,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1821,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    selectedStudentIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"batch-actions-container\",\n                                                        ref: batchActionsDropdownRef,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"batch-actions-btn\",\n                                                                onClick: ()=>setIsBatchActionsDropdownOpen(!isBatchActionsDropdownOpen),\n                                                                title: \"批量操作\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"批量操作(\",\n                                                                            selectedStudentIds.length,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1844,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1845,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1839,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            isBatchActionsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"batch-actions-dropdown\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchAssignBlocks\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1855,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量分配积木\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1856,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1851,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchAssignPoints\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1862,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量分配能量\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1863,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1858,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchUseKeyPackage\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1869,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量兑换密令\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1870,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1865,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchDelete\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1876,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量移出班级\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1877,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1872,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchExport\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1883,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量导出\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1884,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1879,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1850,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1838,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1814,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1812,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"students-list\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            students: students,\n                                            loading: loading,\n                                            error: error,\n                                            selectedStudent: selectedStudent,\n                                            selectedStudentIds: selectedStudentIds,\n                                            currentTemplate: globalCurrentTemplate || currentTemplate,\n                                            renderVersion: renderVersion,\n                                            onStudentClick: handleStudentClick,\n                                            onStudentSelect: handleStudentSelect,\n                                            onRetry: fetchStudents,\n                                            onIndividualAssignBlocks: handleIndividualAssignBlocks,\n                                            onAssignPoints: (studentId)=>{\n                                                const student = students.find((s)=>s.userId === studentId);\n                                                if (student) {\n                                                    setSelectedStudent(student);\n                                                    setIsAssignPointsModalVisible(true);\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1894,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1893,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1811,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1674,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"right-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"student-info-header\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"student-avatar-large\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"student-avatar-circle\",\n                                            style: {\n                                                background: selectedStudent ? getAvatarColor(Math.max(0, students.findIndex((s)=>s.userId === selectedStudent.userId))) : getAvatarColor(0)\n                                            },\n                                            children: selectedStudent ? ((_selectedStudent_nickName = selectedStudent.nickName) === null || _selectedStudent_nickName === void 0 ? void 0 : _selectedStudent_nickName.charAt(0)) || \"S\" : \"S\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1923,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1922,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"student-details\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"student-name-large\",\n                                                children: selectedStudent ? selectedStudent.nickName || \"学生\".concat(selectedStudent.studentNumber || selectedStudent.userId) : \"请选择学生\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1935,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"student-id-large\",\n                                                children: selectedStudent ? selectedStudent.studentNumber || \"无学号\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1938,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1934,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"delete-student-btn\",\n                                        onClick: handleDeleteStudent,\n                                        disabled: !selectedStudent,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1947,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1942,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1921,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"functions-container\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"functions-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"更多功能\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1955,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"function-buttons\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn publish-task\",\n                                                        onClick: ()=>setIsPublishTaskModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83D\\uDCDD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1961,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"发布任务\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1962,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1957,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn distribute-blocks\",\n                                                        onClick: handleAssignBlocks,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83E\\uDDE9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1968,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"分配积木\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1969,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1964,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn distribute-energy\",\n                                                        onClick: ()=>setIsAssignPointsModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"⚡\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1975,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"分配能量\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1976,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1971,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn exchange-tokens\",\n                                                        onClick: ()=>setIsBatchUseKeyPackageModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83C\\uDF81\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1982,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"兑换密令\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1983,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1978,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn reset-password\",\n                                                        onClick: ()=>setIsResetPasswordModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83D\\uDD11\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1989,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"重置密码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1990,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1985,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1956,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1954,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"learning-status\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"课程学习情况\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1997,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"status-placeholder\",\n                                                children: \"该区域功能正在等待开放\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1998,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1996,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1952,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bottom-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"energy-progress-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"energy-progress-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"可用能量/总能量\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2010,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.availablePoints) || 0,\n                                                            \"/\",\n                                                            (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.totalPoints) || 0\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2011,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2009,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"energy-progress-bar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"energy-progress-fill\",\n                                                    style: {\n                                                        width: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.totalPoints) ? \"\".concat(Number(selectedStudent.availablePoints || 0) / Number(selectedStudent.totalPoints || 0) * 100, \"%\") : \"0%\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2016,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2015,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2008,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"template-card-section\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"template-card\",\n                                            onClick: ()=>selectedStudent && handleTemplateAssignment(),\n                                            style: {\n                                                cursor: selectedStudent ? \"pointer\" : \"default\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"template-card-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"template-card-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"template-card-icon\",\n                                                                children: \"\\uD83D\\uDCCB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 2035,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"template-card-info\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"template-card-title\",\n                                                                        children: (()=>{\n                                                                            if (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.currentTemplate) {\n                                                                                return selectedStudent.currentTemplate.templateName;\n                                                                            } else if (globalCurrentTemplate) {\n                                                                                return globalCurrentTemplate.templateName;\n                                                                            } else if (currentTemplate) {\n                                                                                return currentTemplate.templateName;\n                                                                            } else if (selectedStudent) {\n                                                                                return \"加载中...\";\n                                                                            } else {\n                                                                                return \"请选择学生\";\n                                                                            }\n                                                                        })()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 2039,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"template-card-subtitle\",\n                                                                        children: selectedStudent ? ((_selectedStudent_currentTemplate = selectedStudent.currentTemplate) === null || _selectedStudent_currentTemplate === void 0 ? void 0 : _selectedStudent_currentTemplate.isOfficial) ? \"官方模板\" : \"自定义模板\" : \"点击选择学生查看模板\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 2054,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 2038,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2034,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"template-card-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"template-card-badge\",\n                                                                children: \"当前模板\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 2063,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"template-card-swap-icon\",\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    selectedStudent && handleTemplateAssignment();\n                                                                },\n                                                                title: \"更换模板\",\n                                                                children: \"⇄\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 2066,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2062,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2033,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2028,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2027,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 2007,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1919,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1672,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddStudentModal__WEBPACK_IMPORTED_MODULE_5__.AddStudentModal, {\n                visible: isAddStudentModalVisible,\n                onCancel: ()=>setIsAddStudentModalVisible(false),\n                onOk: handleAddStudent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2085,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.EditClassModal, {\n                visible: isEditClassModalVisible,\n                onCancel: ()=>setIsEditClassModalVisible(false),\n                onOk: handleEditClass,\n                initialValues: {\n                    className: classInfo.className\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2092,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.ImportStudentModal, {\n                visible: isImportStudentModalVisible,\n                onCancel: ()=>setIsImportStudentModalVisible(false),\n                onImport: handleImportStudents,\n                classId: classInfo.id\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransferClassModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                visible: isTransferClassModalVisible,\n                onCancel: ()=>{\n                    setIsTransferClassModalVisible(false);\n                    setSearchedTeacher(null);\n                },\n                onOk: handleTransferClass,\n                onSearchTeacher: handleSearchTeacher,\n                searchedTeacher: searchedTeacher,\n                hasAssistantTeacher: !!classInfo.assistantTeacherId,\n                onRemoveAssistant: handleRemoveAssistant,\n                loading: transferLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2110,\n                columnNumber: 7\n            }, undefined),\n            isInviteCodeModalVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold\",\n                                    children: \"邀请码生成成功\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2129,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-gray-500 hover:text-gray-700\",\n                                    onClick: ()=>setIsInviteCodeModalVisible(false),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-6 w-6\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2135,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2134,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2130,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2128,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between bg-gray-50 p-3 rounded-lg mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-mono text-lg text-blue-600 mr-4\",\n                                            children: inviteCode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2141,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                            onClick: ()=>{\n                                                navigator.clipboard.writeText(inviteCode).then(()=>alert(\"邀请码已复制\")).catch(()=>alert(\"复制失败，请手动复制\"));\n                                            },\n                                            children: \"复制\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2142,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2140,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-sm space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"您可以:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2154,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-disc list-inside space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"将邀请码分享给学生，让他们加入班级\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2156,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"邀请其他老师作为协助教师加入班级\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2157,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2155,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-500\",\n                                            children: \"⏰ 邀请码有效期为24小时\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2159,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2153,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2139,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                onClick: ()=>setIsInviteCodeModalVisible(false),\n                                children: \"关闭\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 2165,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2164,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 2127,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2126,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.PublishTaskModal, {\n                visible: isPublishTaskModalVisible,\n                onCancel: ()=>setIsPublishTaskModalVisible(false),\n                onOk: handlePublishTask,\n                students: students,\n                selectedStudents: selectedStudentIds,\n                setSelectedStudents: setSelectedStudentIds,\n                fileList: fileList,\n                uploading: uploading,\n                handleUpload: handleUpload,\n                handleRemoveFile: handleRemoveFile,\n                displayTemplates: templates,\n                officialTemplates: officialTemplates,\n                selectedTemplateId: selectedTemplateId,\n                setSelectedTemplateId: setSelectedTemplateId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2179,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.ResetPasswordModal, {\n                visible: isResetPasswordModalVisible,\n                onCancel: ()=>setIsResetPasswordModalVisible(false),\n                onOk: handleResetPassword,\n                isBatch: false,\n                count: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2197,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AssignBlocksModal__WEBPACK_IMPORTED_MODULE_7__.AssignBlocksModal, {\n                visible: isAssignBlocksModalVisible,\n                onCancel: ()=>{\n                    setIsAssignBlocksModalVisible(false);\n                    setSelectedStudentId(null);\n                    setSelectedStudentIds([]);\n                },\n                isClassCardAssign: selectedStudentId !== null,\n                loadingTemplates: loadingTemplates,\n                templates: templates,\n                studentTemplateUsage: studentTemplateUsage,\n                teacherTemplate: teacherTemplate,\n                onSelectTemplate: handleSelectTemplate,\n                onTemplateUsageClick: handleTemplateUsageClick,\n                userId: userId,\n                onRefreshTemplates: fetchTemplates,\n                students: students,\n                selectedStudentIds: selectedStudentIds,\n                userRoles: userRoles,\n                onSuccess: ()=>{\n                    refreshStudentList();\n                    fetchTemplateUsage();\n                },\n                onUpdateStudentTemplate: handleUpdateStudentTemplate\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2206,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AssignPointsModal__WEBPACK_IMPORTED_MODULE_9__.AssignPointsModal, {\n                visible: isAssignPointsModalVisible,\n                onCancel: ()=>{\n                    setIsAssignPointsModalVisible(false);\n                    setSelectedStudent(null);\n                },\n                // 根据 selectedStudent 是否存在来决定调用哪个处理函数\n                onOk: selectedStudent ? handleAssignPoints : handleBatchAssignPoints,\n                studentName: selectedStudent ? \"\".concat(selectedStudent.nickName) : \"已选择 \".concat(selectedStudentIds.length, \" 名学生\"),\n                studentId: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.id) || 0,\n                userId: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.userId) || 0,\n                student: selectedStudent,\n                isBatch: !selectedStudent,\n                selectedStudents: selectedStudentIds,\n                students: students,\n                onSuccess: ()=>{\n                // 移除这里的刷新，因为 onOk 内部已经处理了\n                },\n                refreshStudentList: refreshStudentList,\n                onGoToRedeemKey: (studentIds)=>{\n                    console.log(\"前往兑换密钥:\", studentIds);\n                    setIsBatchUseKeyPackageModalVisible(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2233,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_10__.BatchUseKeyPackageModal, {\n                open: isBatchUseKeyPackageModalVisible,\n                selectedStudentIds: selectedStudentIds,\n                students: students,\n                onClose: ()=>setIsBatchUseKeyPackageModalVisible(false),\n                onSuccess: handleBatchUseKeyPackageSuccess,\n                onGoToAssignPoints: handleGoToAssignPointsFromRedeem\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2259,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n        lineNumber: 1660,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClassDetail, \"rOoOqxdnOob2DP168DTJvyWgCZI=\", false, function() {\n    return [\n        _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__.useTemplate,\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector\n    ];\n});\n_c = ClassDetail;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClassDetail);\nvar _c;\n$RefreshReg$(_c, \"ClassDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvY29tcG9uZW50cy9DbGFzc0RldGFpbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTJEO0FBQzZGO0FBQ3RHO0FBQ0k7QUFDWjtBQUVnQjtBQUNOO0FBTUw7QUFDUztBQUNGO0FBRUU7QUFDWTtBQUM1QjtBQUViO0FBRTNCLDJCQUEyQjtBQUUzQixNQUFNaUMsY0FBMEM7UUFBQyxFQUMvQ0MsV0FBV0MsZ0JBQWdCLEVBQzNCQyxjQUFjLEVBQ2RDLE1BQU0sRUFDTkMsaUJBQWlCLEVBQ2pCQyxjQUFjLEVBQ2Y7UUEyMkRrQ0MsMkJBNkhSQTs7SUF2K0R6Qiw2QkFBNkI7SUFDN0IsTUFBTSxDQUFDTixXQUFXTyxhQUFhLEdBQUd4QywrQ0FBUUEsQ0FBWWtDO0lBQ3RELE1BQU0sQ0FBQ08sVUFBVUMsWUFBWSxHQUFHMUMsK0NBQVFBLENBQVksRUFBRTtJQUN0RCxNQUFNLENBQUMyQyxTQUFTQyxXQUFXLEdBQUc1QywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUM2QyxPQUFPQyxTQUFTLEdBQUc5QywrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDdUMsaUJBQWlCUSxtQkFBbUIsR0FBRy9DLCtDQUFRQSxDQUFpQjtJQUV2RSxxQkFBcUI7SUFDckJDLGdEQUFTQSxDQUFDO1FBQ1J1QyxhQUFhTjtJQUNmLEdBQUc7UUFBQ0E7S0FBaUI7SUFDckIsTUFBTSxDQUFDYywwQkFBMEJDLDRCQUE0QixHQUFHakQsK0NBQVFBLENBQUM7SUFDekUsTUFBTSxDQUFDa0Qsd0JBQXdCQywwQkFBMEIsR0FBR25ELCtDQUFRQSxDQUFDO0lBQ3JFLE1BQU0sQ0FBQ29ELDRCQUE0QkMsOEJBQThCLEdBQUdyRCwrQ0FBUUEsQ0FBQztJQUM3RSxNQUFNc0QsY0FBY3BELDZDQUFNQSxDQUFpQjtJQUMzQyxNQUFNcUQsc0JBQXNCckQsNkNBQU1BLENBQWlCO0lBQ25ELE1BQU1zRCwwQkFBMEJ0RCw2Q0FBTUEsQ0FBaUI7SUFFdkQsUUFBUTtJQUNSLE1BQU0sQ0FBQ3VELHlCQUF5QkMsMkJBQTJCLEdBQUcxRCwrQ0FBUUEsQ0FBQztJQUN2RSxNQUFNLENBQUMyRCw2QkFBNkJDLCtCQUErQixHQUFHNUQsK0NBQVFBLENBQUM7SUFDL0UsTUFBTSxDQUFDNkQsNkJBQTZCQywrQkFBK0IsR0FBRzlELCtDQUFRQSxDQUFDO0lBQy9FLE1BQU0sQ0FBQytELDBCQUEwQkMsNEJBQTRCLEdBQUdoRSwrQ0FBUUEsQ0FBQztJQUN6RSxNQUFNLENBQUNpRSw0QkFBNEJDLDhCQUE4QixHQUFHbEUsK0NBQVFBLENBQUM7SUFDN0UsTUFBTSxDQUFDbUUsWUFBWUMsY0FBYyxHQUFHcEUsK0NBQVFBLENBQVM7SUFFckQsWUFBWTtJQUNaLE1BQU0sQ0FBQ3FFLDJCQUEyQkMsNkJBQTZCLEdBQUd0RSwrQ0FBUUEsQ0FBQztJQUMzRSxNQUFNLENBQUN1RSw2QkFBNkJDLCtCQUErQixHQUFHeEUsK0NBQVFBLENBQUM7SUFDL0UsTUFBTSxDQUFDeUUsNEJBQTRCQyw4QkFBOEIsR0FBRzFFLCtDQUFRQSxDQUFDO0lBQzdFLE1BQU0sQ0FBQzJFLGtDQUFrQ0Msb0NBQW9DLEdBQUc1RSwrQ0FBUUEsQ0FBQztJQUN6Riw0RkFBNEY7SUFFNUYsV0FBVztJQUNYLE1BQU0sQ0FBQzZFLGlCQUFpQkMsbUJBQW1CLEdBQUc5RSwrQ0FBUUEsQ0FBTTtJQUM1RCxNQUFNLENBQUMrRSxpQkFBaUJDLG1CQUFtQixHQUFHaEYsK0NBQVFBLENBQUM7SUFFdkQsd0JBQXdCO0lBQ3hCLE1BQU0sQ0FBQ2lGLFVBQVVDLFlBQVksR0FBR2xGLCtDQUFRQSxDQUFRLEVBQUU7SUFDbEQsTUFBTSxDQUFDbUYsV0FBV0MsYUFBYSxHQUFHcEYsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDcUYsV0FBV0MsYUFBYSxHQUFHdEYsK0NBQVFBLENBQVEsRUFBRTtJQUNwRCxNQUFNLENBQUN1RixrQkFBa0IsR0FBR3ZGLCtDQUFRQSxDQUFRLEVBQUUsR0FBRyxZQUFZO0lBQzdELE1BQU0sQ0FBQ3dGLG9CQUFvQkMsc0JBQXNCLEdBQUd6RiwrQ0FBUUEsQ0FBZ0I7SUFFNUUseUJBQXlCO0lBQ3pCLE1BQU0sQ0FBQzBGLGtCQUFrQkMsb0JBQW9CLEdBQUczRiwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUM0RixzQkFBc0JDLHdCQUF3QixHQUFHN0YsK0NBQVFBLENBQXlCLENBQUM7SUFDMUYsTUFBTSxDQUFDOEYsaUJBQWlCQyxtQkFBbUIsR0FBRy9GLCtDQUFRQTtJQUN0RCxNQUFNLENBQUNnRyxtQkFBbUJDLHFCQUFxQixHQUFHakcsK0NBQVFBLENBQWdCO0lBRTFFLG1DQUFtQztJQUNuQyxNQUFNLENBQUNrRyxXQUFXQyxhQUFhLEdBQUduRywrQ0FBUUEsQ0FBNEMsRUFBRTtJQUV4RixPQUFPO0lBQ1AsTUFBTSxDQUFDb0csb0JBQW9CQyxzQkFBc0IsR0FBR3JHLCtDQUFRQSxDQUFXLEVBQUU7SUFDekUsTUFBTSxDQUFDc0csYUFBYUMsZUFBZSxHQUFHdkcsK0NBQVFBLENBQUM7SUFFL0MsV0FBVztJQUNYLE1BQU0sRUFBRXdHLGVBQWUsRUFBRUMsMkJBQTJCLEVBQUVDLHNCQUFzQixFQUFFLEdBQUdyRixzRUFBV0E7SUFFNUYsd0JBQXdCO0lBQ3hCLE1BQU0sQ0FBQ3NGLHVCQUF1QkMseUJBQXlCLEdBQUc1RywrQ0FBUUEsQ0FBTTtJQUV4RSxvQkFBb0I7SUFDcEIsTUFBTTZHLDhCQUE4QjtRQUNsQyxJQUFJO1lBQ0YsSUFBSSxFQUFDNUUsc0JBQUFBLGdDQUFBQSxVQUFXNkUsRUFBRSxLQUFJLEVBQUM3RSxzQkFBQUEsZ0NBQUFBLFVBQVc4RSxRQUFRLEdBQUUsT0FBTztZQUVuRCxNQUFNQyxXQUFXLE1BQU05RixvREFBUUEsQ0FBQytGLGlCQUFpQixDQUFDaEYsVUFBVThFLFFBQVEsRUFBRUc7WUFDdEUsSUFBSUYsU0FBU0csSUFBSSxDQUFDQyxJQUFJLEtBQUssT0FBT0osU0FBU0csSUFBSSxDQUFDQSxJQUFJLEVBQUU7Z0JBQ3BELGNBQWM7Z0JBQ2QsTUFBTUUsZUFBZUwsU0FBU0csSUFBSSxDQUFDQSxJQUFJLENBQUNHLElBQUksQ0FBQyxDQUFDQyxNQUFhQSxJQUFJVCxFQUFFLEtBQUs3RSxVQUFVNkUsRUFBRTtnQkFDbEYsSUFBSU8sZ0JBQWdCQSxhQUFhRyxZQUFZLEVBQUU7b0JBQzdDLE1BQU1DLGVBQWU7d0JBQ25CQyxZQUFZTCxhQUFhSyxVQUFVLElBQUk7d0JBQ3ZDRixjQUFjSCxhQUFhRyxZQUFZO3dCQUN2Q0csWUFBWU4sYUFBYU0sVUFBVSxJQUFJO29CQUN6QztvQkFDQWYseUJBQXlCYTtvQkFDekJHLFFBQVFDLEdBQUcsQ0FBQyxjQUFjSjtvQkFDMUIsT0FBT0E7Z0JBQ1Q7WUFDRjtRQUNGLEVBQUUsT0FBTzVFLE9BQU87WUFDZCtFLFFBQVEvRSxLQUFLLENBQUMsZUFBZUE7UUFDL0I7UUFDQSxPQUFPO0lBQ1Q7SUFFQSx3QkFBd0I7SUFDeEIsTUFBTWlGLGdDQUFnQztRQUNwQyxJQUFJO1lBQ0YsTUFBTUMsY0FBYyxNQUFNbEI7WUFDMUIsSUFBSWtCLGVBQWV0RixTQUFTdUYsTUFBTSxHQUFHLEdBQUc7Z0JBQ3RDSixRQUFRQyxHQUFHLENBQUMsY0FBY0U7Z0JBRTFCLDJDQUEyQztnQkFDM0MsTUFBTUUsNkJBQTZCQywrQkFBK0JDLE9BQU87Z0JBRXpFLGtCQUFrQjtnQkFDbEJ6RixZQUFZMEYsQ0FBQUEsZUFBZ0JBLGFBQWFDLEdBQUcsQ0FBQ0MsQ0FBQUE7d0JBQzNDLG9CQUFvQjt3QkFDcEIsSUFBSUwsMkJBQTJCTSxHQUFHLENBQUNELFFBQVFwQixNQUFNLEdBQUc7NEJBQ2xEVSxRQUFRQyxHQUFHLENBQUMsUUFBeUIsT0FBakJTLFFBQVFFLFFBQVEsRUFBQzs0QkFDckMsT0FBT0Y7d0JBQ1Q7d0JBRUEsY0FBYzt3QkFDZFYsUUFBUUMsR0FBRyxDQUFDLFFBQXlCLE9BQWpCUyxRQUFRRSxRQUFRLEVBQUM7d0JBQ3JDLE9BQU87NEJBQ0wsR0FBR0YsT0FBTzs0QkFDVjlCLGlCQUFpQnVCO3dCQUNuQjtvQkFDRjtZQUNGO1FBQ0YsRUFBRSxPQUFPbEYsT0FBTztZQUNkK0UsUUFBUS9FLEtBQUssQ0FBQyxhQUFhQTtRQUM3QjtJQUNGO0lBRUEsaUNBQWlDO0lBQ2pDLE1BQU0sQ0FBQzRGLDZCQUE2QkMsK0JBQStCLEdBQUcxSSwrQ0FBUUEsQ0FBaUYsSUFBSTJJO0lBRW5LLGNBQWM7SUFDZCxNQUFNLENBQUNDLGNBQWMsR0FBRzVJLCtDQUFRQSxDQUFDLElBQUksd0JBQXdCO0lBRTdELFNBQVM7SUFDVCxNQUFNa0gsU0FBUzlGLHlEQUFXQSxDQUFDLENBQUN5SCxRQUFxQkEsTUFBTUMsSUFBSSxDQUFDQyxTQUFTLENBQUM3QixNQUFNO0lBQzVFLE1BQU04QixTQUFTNUgseURBQVdBLENBQUMsQ0FBQ3lILFFBQXFCQSxNQUFNQyxJQUFJLENBQUNDLFNBQVMsQ0FBQ0MsTUFBTTtJQUU1RSxXQUFXO0lBQ1gsTUFBTUMsZ0JBQWdCO1FBQ3BCLElBQUksRUFBQ2hILHNCQUFBQSxnQ0FBQUEsVUFBVzZFLEVBQUUsR0FBRTtRQUVwQixJQUFJO1lBQ0ZsRSxXQUFXO1lBQ1hFLFNBQVM7WUFFVCxzQkFBc0I7WUFDdEIsTUFBTSxDQUFDa0UsU0FBUyxHQUFHLE1BQU1rQyxRQUFRQyxHQUFHLENBQUM7Z0JBQ25Dakksb0RBQVFBLENBQUNrSSxnQkFBZ0IsQ0FBQ25ILFVBQVU2RSxFQUFFO2dCQUN0Q0Q7YUFDRDtZQUNELElBQUlHLFNBQVNHLElBQUksQ0FBQ0MsSUFBSSxLQUFLLEtBQUs7Z0JBQzlCLE1BQU1pQyxlQUFlckMsU0FBU0csSUFBSSxDQUFDQSxJQUFJLElBQUksRUFBRTtnQkFFN0MsSUFBSWtDLGFBQWFyQixNQUFNLEtBQUssR0FBRztvQkFDN0J0RixZQUFZLEVBQUU7b0JBQ2RLLG1CQUFtQjtvQkFFbkIscUJBQXFCO29CQUNyQixJQUFJZCxVQUFVcUgsWUFBWSxLQUFLLEdBQUc7d0JBQ2hDLE1BQU1DLG1CQUFtQjs0QkFDdkIsR0FBR3RILFNBQVM7NEJBQ1pxSCxjQUFjO3dCQUNoQjt3QkFDQTlHLGFBQWErRzt3QkFFYixtQkFBbUI7d0JBQ25CbEgsOEJBQUFBLHdDQUFBQSxrQkFBb0JrSDtvQkFFdEI7b0JBQ0E7Z0JBQ0Y7Z0JBRUEsV0FBVztnQkFDWCxNQUFNQyxVQUFVSCxhQUFhaEIsR0FBRyxDQUFDLENBQUNvQixJQUFlQSxFQUFFdkMsTUFBTTtnQkFFekQsd0JBQXdCO2dCQUN4QixNQUFNd0MsaUJBQWlCLE1BQU9GLENBQUFBLFFBQVF4QixNQUFNLEdBQUcsS0FDM0NrQixRQUFRQyxHQUFHLENBQ1hRLE1BQU1DLElBQUksQ0FBQztvQkFBRTVCLFFBQVE2QixLQUFLQyxJQUFJLENBQUNOLFFBQVF4QixNQUFNLEdBQUc7Z0JBQUksR0FDbEQsQ0FBQytCLEdBQUdDLElBQU1SLFFBQVFTLEtBQUssQ0FBQ0QsSUFBSSxJQUFJLENBQUNBLElBQUksS0FBSyxLQUMxQzNCLEdBQUcsQ0FBQzZCLENBQUFBLFVBQ0ovSSx3REFBVUEsQ0FBQ2dKLG9CQUFvQixDQUFDRCxTQUM3QkUsSUFBSSxDQUFDOzRCQUFDLEVBQUVqRCxNQUFNLEVBQUVBLElBQUksRUFBRSxFQUFFO3dCQUN2QixPQUFPQTtvQkFDVCxHQUNDa0QsS0FBSyxDQUFDLENBQUN4SDt3QkFDTitFLFFBQVEvRSxLQUFLLENBQUMsZUFBZUE7d0JBQzdCLE9BQU8sQ0FBQztvQkFDVixLQUVKdUgsSUFBSSxDQUFDRSxDQUFBQSxVQUFXQyxPQUFPQyxNQUFNLENBQUMsQ0FBQyxNQUFNRixZQUNyQ25KLHdEQUFVQSxDQUFDZ0osb0JBQW9CLENBQUNYLFNBQy9CWSxJQUFJLENBQUM7d0JBQUMsRUFBRWpELE1BQU0sRUFBRUEsSUFBSSxFQUFFLEVBQUU7b0JBQ3ZCLE9BQU9BO2dCQUNULEdBQ0NrRCxLQUFLLENBQUMsQ0FBQ3hIO29CQUNOK0UsUUFBUS9FLEtBQUssQ0FBQyxlQUFlQTtvQkFDN0IsT0FBTyxDQUFDO2dCQUNWLEVBQUM7Z0JBS0wsU0FBUztnQkFDVCxNQUFNNEgsbUJBQW1CcEIsYUFBYWhCLEdBQUcsQ0FBQyxDQUFDb0I7b0JBQ3pDLE1BQU1pQixjQUFjaEIsY0FBYyxDQUFDRCxFQUFFdkMsTUFBTSxDQUFDO29CQUM1QyxlQUFlO29CQUNmLE1BQU15RCxtQkFBbUJsQyw0QkFBNEJtQyxHQUFHLENBQUNuQixFQUFFdkMsTUFBTTtvQkFFakUsT0FBTzt3QkFDTCxHQUFHdUMsQ0FBQzt3QkFDSixHQUFHaUIsV0FBVzt3QkFDZDVELElBQUkyQyxFQUFFdkMsTUFBTTt3QkFDWnNCLFVBQVVpQixFQUFFakIsUUFBUSxJQUFJLEtBQWlDLE9BQTVCaUIsRUFBRW9CLGFBQWEsSUFBSXBCLEVBQUV2QyxNQUFNO3dCQUN4RDRELGFBQWFKLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYUksV0FBVyxLQUFJO3dCQUN6Q0MsaUJBQWlCTCxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFLLGVBQWUsS0FBSTt3QkFDakRDLFdBQVd2QixFQUFFdUIsU0FBUyxJQUFJO3dCQUMxQiwyQ0FBMkM7d0JBQzNDeEUsaUJBQWlCLENBQUM7Z0NBTUM2Qzs0QkFMakIsTUFBTTRCLFNBQVNOLHFCQUNiRCx3QkFBQUEsa0NBQUFBLFlBQWFsRSxlQUFlLEtBQzVCRzs0QkFFRixPQUFPOzRCQUNQLElBQUk4QyxFQUFFdkMsTUFBTSxPQUFLbUMsaUJBQUFBLFlBQVksQ0FBQyxFQUFFLGNBQWZBLHFDQUFBQSxlQUFpQm5DLE1BQU0sR0FBRTtnQ0FDeENVLFFBQVFDLEdBQUcsQ0FBQyxhQUFhO29DQUN2QnFELFdBQVd6QixFQUFFdkMsTUFBTTtvQ0FDbkJpRSxhQUFhMUIsRUFBRWpCLFFBQVE7b0NBQ3ZCbUM7b0NBQ0FTLGtCQUFrQixFQUFFVix3QkFBQUEsa0NBQUFBLFlBQWFsRSxlQUFlO29DQUNoREc7b0NBQ0EwRSxlQUFlSjtnQ0FDakI7NEJBQ0Y7NEJBRUEsT0FBT0E7d0JBQ1Q7b0JBQ0Y7Z0JBQ0Y7Z0JBSUF2SSxZQUFZK0g7Z0JBRVosMEJBQTBCO2dCQUMxQixJQUFJQSxpQkFBaUJ6QyxNQUFNLEtBQUsvRixVQUFVcUgsWUFBWSxFQUFFO29CQUN0RCxNQUFNQyxtQkFBbUI7d0JBQ3ZCLEdBQUd0SCxTQUFTO3dCQUNacUgsY0FBY21CLGlCQUFpQnpDLE1BQU07b0JBQ3ZDO29CQUNBeEYsYUFBYStHO29CQUViLG1CQUFtQjtvQkFDbkJsSCw4QkFBQUEsd0NBQUFBLGtCQUFvQmtIO2dCQUV0QjtnQkFFQSxpQ0FBaUM7Z0JBQ2pDLE1BQU0rQixlQUEyQmIsaUJBQWlCcEMsR0FBRyxDQUFDLENBQUNDLFVBQXNCO3dCQUMzRXBCLFFBQVFvQixRQUFRcEIsTUFBTTt3QkFDdEI4QixRQUFRLEVBQUUsV0FBVztvQkFDdkI7Z0JBRUEsWUFBWTtnQkFDWnNDLGFBQWFDLElBQUksQ0FBQztvQkFDaEJyRSxRQUFRQTtvQkFDUjhCLFFBQVEsRUFBRSxXQUFXO2dCQUN2QjtnQkFFQTdDLGFBQWFtRjtnQkFFYixZQUFZO2dCQUNaLElBQUliLGlCQUFpQnpDLE1BQU0sR0FBRyxHQUFHO29CQUMvQmpGLG1CQUFtQjBILGdCQUFnQixDQUFDLEVBQUU7Z0JBQ3hDO1lBQ0YsT0FBTztnQkFDTDNILFNBQVNrRSxTQUFTRyxJQUFJLENBQUNxRSxPQUFPLElBQUk7WUFDcEM7UUFDRixFQUFFLE9BQU9DLEtBQUs7WUFDWjdELFFBQVEvRSxLQUFLLENBQUMsYUFBYTRJO1lBQzNCN0QsUUFBUS9FLEtBQUssQ0FBQyxTQUFTO2dCQUNyQjJJLFNBQVNDLGVBQWVDLFFBQVFELElBQUlELE9BQU8sR0FBRztnQkFDOUNHLE9BQU9GLGVBQWVDLFFBQVFELElBQUlFLEtBQUssR0FBR0M7Z0JBQzFDQyxTQUFTNUosVUFBVTZFLEVBQUU7WUFDdkI7WUFDQWhFLFNBQVM7UUFDWCxTQUFVO1lBQ1JGLFdBQVc7UUFDYjtJQUNGO0lBSUEsV0FBVztJQUNYLE1BQU1rSixpQkFBaUI7UUFDckJuRyxvQkFBb0I7UUFDcEIsSUFBSTtZQUNGLE1BQU0sRUFBRW9HLG1CQUFtQixFQUFFQyxvQkFBb0IsRUFBRSxHQUFHLE1BQU0sMEpBQU87WUFFbkUsbUJBQW1CO1lBQ25CLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxpQkFBaUIsR0FBRyxNQUFNaEQsUUFBUUMsR0FBRyxDQUFDO2dCQUMzRDRDLG9CQUFvQjdFO2dCQUNwQjhFO2FBQ0Q7WUFFRCxJQUFJQyxlQUFlOUUsSUFBSSxDQUFDQyxJQUFJLEtBQUssT0FBTzhFLGlCQUFpQi9FLElBQUksQ0FBQ0MsSUFBSSxLQUFLLEtBQUs7Z0JBQzFFLE1BQU0rRSxrQkFBa0JGLGVBQWU5RSxJQUFJLENBQUNBLElBQUksSUFBSSxFQUFFO2dCQUN0RCxNQUFNNUIsb0JBQW9CMkcsaUJBQWlCL0UsSUFBSSxDQUFDQSxJQUFJLElBQUksRUFBRTtnQkFFMUQsWUFBWTtnQkFDWixNQUFNaUYsMEJBQTBCN0csa0JBQWtCOEMsR0FBRyxDQUFDLENBQUNnRSxXQUFtQjt3QkFDeEUsR0FBR0EsUUFBUTt3QkFDWDFFLFlBQVk7b0JBQ2Q7Z0JBRUEsU0FBUztnQkFDVCxNQUFNMkUsZUFBZTt1QkFBSUg7dUJBQW9CQztpQkFBd0I7Z0JBQ3JFOUcsYUFBYWdIO1lBQ2Y7UUFDRixFQUFFLE9BQU96SixPQUFPO1lBQ2QrRSxRQUFRL0UsS0FBSyxDQUFDLGVBQWVBO1FBQy9CLFNBQVU7WUFDUjhDLG9CQUFvQjtRQUN0QjtJQUNGO0lBRUEsV0FBVztJQUNYLE1BQU00RyxxQkFBcUI7UUFDekIsSUFBSTtZQUNGLE1BQU0sRUFBRUMsc0JBQXNCLEVBQUVDLG1CQUFtQixFQUFFLEdBQUcsTUFBTSwwSkFBTztZQUVyRSxZQUFZO1lBQ1osTUFBTUMsa0JBQWtCLE1BQU1GLHVCQUF1QnRGO1lBQ3JELElBQUl3RixnQkFBZ0J2RixJQUFJLENBQUNDLElBQUksS0FBSyxLQUFLO2dCQUNyQyxNQUFNdUYsc0JBQXNCRCxnQkFBZ0J2RixJQUFJLENBQUNBLElBQUk7Z0JBRXJEcEIsbUJBQW1CNEc7WUFFbkIsdUJBQXVCO1lBQ3ZCLGFBQWE7WUFDYjs7Ozs7UUFLQSxHQUNGO1lBRUEsWUFBWTtZQUNaLE1BQU1DLGtCQUFrQixNQUFNSCxvQkFBb0I7Z0JBQ2hESSxXQUFXM0Y7Z0JBQ1g0RixNQUFNO2dCQUNOQyxNQUFNO1lBQ1I7WUFDQSxJQUFJSCxnQkFBZ0J6RixJQUFJLENBQUNDLElBQUksS0FBSyxLQUFLO2dCQUNyQyxlQUFlO2dCQUNmLE1BQU00RixRQUFtQyxDQUFDO2dCQUMxQ0osZ0JBQWdCekYsSUFBSSxDQUFDQSxJQUFJLENBQUM4RixJQUFJLENBQUNDLE9BQU8sQ0FBQyxDQUFDQztvQkFDdEMsSUFBSUEsS0FBS3pGLFVBQVUsRUFBRTt3QkFDbkJzRixLQUFLLENBQUNHLEtBQUt6RixVQUFVLENBQUMsR0FBRyxDQUFDc0YsS0FBSyxDQUFDRyxLQUFLekYsVUFBVSxDQUFDLElBQUksS0FBSztvQkFDM0Q7Z0JBQ0Y7Z0JBQ0E3Qix3QkFBd0JtSDtZQUMxQjtRQUNGLEVBQUUsT0FBT25LLE9BQU87WUFDZCtFLFFBQVEvRSxLQUFLLENBQUMsZUFBZUE7UUFDL0I7SUFDRjtJQUVBLGlCQUFpQjtJQUNqQjVDLGdEQUFTQSxDQUFDO1FBQ1JnSjtRQUNBNkM7UUFDQVM7SUFDRixHQUFHO1FBQUN0SyxzQkFBQUEsZ0NBQUFBLFVBQVc2RSxFQUFFO1FBQUVJO0tBQU87SUFFMUIsNEJBQTRCO0lBQzVCLGFBQWE7SUFDYjs7Ozs7O0VBTUEsR0FFQSwrQ0FBK0M7SUFFL0MsK0NBQStDO0lBQy9DLE1BQU1nQixpQ0FBaUNoSSw2Q0FBTUEsQ0FBQ3VJO0lBQzlDUCwrQkFBK0JDLE9BQU8sR0FBR007SUFFekMsNkJBQTZCO0lBQzdCeEksZ0RBQVNBLENBQUM7UUFDUixJQUFJd0csOEJBQThCLE1BQUt4RSxzQkFBQUEsZ0NBQUFBLFVBQVc2RSxFQUFFLEdBQUU7WUFDcERjLFFBQVFDLEdBQUcsQ0FBQztZQUVaLGFBQWE7WUFDYmhCLDhCQUE4QnVELElBQUksQ0FBQyxDQUFDckM7Z0JBQ2xDLElBQUlBLGVBQWV0RixTQUFTdUYsTUFBTSxHQUFHLEdBQUc7b0JBQ3RDLDJCQUEyQjtvQkFDM0Isc0RBQXNEO29CQUV0RCwyQ0FBMkM7b0JBQzNDLE1BQU1DLDZCQUE2QkMsK0JBQStCQyxPQUFPO29CQUV6RSx3QkFBd0I7b0JBQ3hCUCxRQUFRQyxHQUFHLENBQUMsb0JBQW9CO3dCQUM5QkU7d0JBQ0FVLDZCQUE2QmtCLE1BQU1DLElBQUksQ0FBQzNCLDJCQUEyQm1GLE9BQU87d0JBQzFFQyxlQUFlNUssU0FBU3VGLE1BQU07b0JBQ2hDO29CQUVBdEYsWUFBWTBGLENBQUFBLGVBQWdCQSxhQUFhQyxHQUFHLENBQUNDLENBQUFBOzRCQUMzQyxNQUFNZ0Ysc0JBQXNCckYsMkJBQTJCTSxHQUFHLENBQUNELFFBQVFwQixNQUFNOzRCQUN6RVUsUUFBUUMsR0FBRyxDQUFDLE1BQTJCUyxPQUFyQkEsUUFBUUUsUUFBUSxFQUFDLE1BQW1CLE9BQWZGLFFBQVFwQixNQUFNLEVBQUMsT0FBSztnQ0FDekRvRztnQ0FDQTlHLGlCQUFpQjhCLFFBQVE5QixlQUFlO2dDQUN4QytHLFlBQVksQ0FBQ0Q7NEJBQ2Y7NEJBRUEsb0JBQW9COzRCQUNwQixJQUFJQSxxQkFBcUI7Z0NBQ3ZCMUYsUUFBUUMsR0FBRyxDQUFDLFFBQXlCLE9BQWpCUyxRQUFRRSxRQUFRLEVBQUM7Z0NBQ3JDLE9BQU9GOzRCQUNUOzRCQUNBLGdCQUFnQjs0QkFDaEJWLFFBQVFDLEdBQUcsQ0FBQyxRQUF5QixPQUFqQlMsUUFBUUUsUUFBUSxFQUFDOzRCQUNyQyxPQUFPO2dDQUNMLEdBQUdGLE9BQU87Z0NBQ1Y5QixpQkFBaUJ1Qjs0QkFDbkI7d0JBQ0Y7Z0JBQ0Y7WUFDRjtRQUNGO0lBQ0YsR0FBRztRQUFDdEI7UUFBNkJ4RSxzQkFBQUEsZ0NBQUFBLFVBQVc2RSxFQUFFO0tBQUM7SUFFL0MsYUFBYTtJQUNiN0csZ0RBQVNBLENBQUM7UUFDUixJQUFJLEVBQUNnQyxzQkFBQUEsZ0NBQUFBLFVBQVc2RSxFQUFFLEdBQUU7UUFFcEIsU0FBUztRQUNUZ0I7UUFFQSxxQkFBcUI7UUFDckIsTUFBTTBGLFdBQVdDLFlBQVk7WUFDM0IzRjtRQUNGLEdBQUcsUUFBUSxNQUFNO1FBRWpCLE9BQU8sSUFBTTRGLGNBQWNGO0lBQzdCLEdBQUc7UUFBQ3ZMLHNCQUFBQSxnQ0FBQUEsVUFBVzZFLEVBQUU7UUFBRXJFLFNBQVN1RixNQUFNO0tBQUM7SUFFbkMsYUFBYTtJQUNiL0gsZ0RBQVNBLENBQUM7UUFDUixNQUFNME4scUJBQXFCLENBQUNDO1lBQzFCLElBQUl0SyxZQUFZNkUsT0FBTyxJQUFJLENBQUM3RSxZQUFZNkUsT0FBTyxDQUFDMEYsUUFBUSxDQUFDRCxNQUFNRSxNQUFNLEdBQVc7WUFDOUUsOENBQThDO1lBQ2hEO1lBQ0EsSUFBSXZLLG9CQUFvQjRFLE9BQU8sSUFBSSxDQUFDNUUsb0JBQW9CNEUsT0FBTyxDQUFDMEYsUUFBUSxDQUFDRCxNQUFNRSxNQUFNLEdBQVc7Z0JBQzlGM0ssMEJBQTBCO1lBQzVCO1lBQ0EsSUFBSUssd0JBQXdCMkUsT0FBTyxJQUFJLENBQUMzRSx3QkFBd0IyRSxPQUFPLENBQUMwRixRQUFRLENBQUNELE1BQU1FLE1BQU0sR0FBVztnQkFDdEd6Syw4QkFBOEI7WUFDaEM7UUFDRjtRQUVBMEssU0FBU0MsZ0JBQWdCLENBQUMsYUFBYUw7UUFDdkMsT0FBTztZQUNMSSxTQUFTRSxtQkFBbUIsQ0FBQyxhQUFhTjtRQUM1QztJQUNGLEdBQUcsRUFBRTtJQUVMLHFCQUFxQjtJQUNyQixNQUFNTyxxQkFBcUIsQ0FBQzVGO1FBQzFCdkYsbUJBQW1CdUY7SUFDckI7SUFFQSxTQUFTO0lBQ1QsTUFBTTZGLHNCQUFzQjtRQUMxQixJQUFJLENBQUM1TCxpQkFBaUI7WUFDcEI2TCxNQUFNO1lBQ047UUFDRjtRQUVBLFVBQVU7UUFDVixNQUFNQyxZQUFZQyxPQUFPQyxPQUFPLENBQzlCLFFBQWlDLE9BQXpCaE0sZ0JBQWdCaUcsUUFBUSxFQUFDO1FBR25DLElBQUksQ0FBQzZGLFdBQVc7WUFDZDtRQUNGO1FBRUEsSUFBSTtZQUNGekcsUUFBUUMsR0FBRyxDQUFDLFNBQVN0RjtZQUVyQiwyQkFBMkI7WUFDM0IsTUFBTXlFLFdBQVcsTUFBTTlGLG9EQUFRQSxDQUFDc04sc0JBQXNCLENBQUM7Z0JBQUNqTSxnQkFBZ0IyRSxNQUFNO2FBQUM7WUFFL0VVLFFBQVFDLEdBQUcsQ0FBQyxnQkFBZ0JiLFNBQVNHLElBQUk7WUFFekMsU0FBUztZQUNULElBQUlILFNBQVNHLElBQUksQ0FBQ0MsSUFBSSxLQUFLLEtBQUs7Z0JBQzlCUSxRQUFRQyxHQUFHLENBQUM7Z0JBQ1p1RyxNQUFNO2dCQUVOLFVBQVU7Z0JBQ1ZyTCxtQkFBbUI7Z0JBRW5CLFdBQVc7Z0JBQ1gsTUFBTWtHO1lBQ1IsT0FBTztnQkFDTHJCLFFBQVEvRSxLQUFLLENBQUMsV0FBV21FLFNBQVNHLElBQUksQ0FBQ3FFLE9BQU87Z0JBQzlDNEMsTUFBTXBILFNBQVNHLElBQUksQ0FBQ3FFLE9BQU8sSUFBSTtZQUNqQztRQUNGLEVBQUUsT0FBTzNJLE9BQU87WUFDZCtFLFFBQVEvRSxLQUFLLENBQUMsV0FBV0E7WUFDekJ1TCxNQUFNO1FBQ1I7SUFDRjtJQUVBLE9BQU87SUFDUCxNQUFNSyxrQkFBa0IsT0FBT0M7UUFDN0IsTUFBTSxFQUFFQyxlQUFlLEVBQUUsR0FBRyxNQUFNLDhOQUFPO1FBQ3pDLE1BQU1DLGVBQWVEO1FBRXJCLElBQUk7WUFDRi9HLFFBQVFDLEdBQUcsQ0FBQyxXQUFXO2dCQUNyQmdFLFNBQVM1SixVQUFVNkUsRUFBRTtnQkFDckI0SCxRQUFRQTtnQkFDUkcsbUJBQW1CNU0sVUFBVTZNLFNBQVM7WUFDeEM7WUFFQSxNQUFNOUgsV0FBVyxNQUFNOUYsb0RBQVFBLENBQUM2TixXQUFXLENBQUM5TSxVQUFVNkUsRUFBRSxFQUFFO2dCQUN4RGdJLFdBQVdKLE9BQU9JLFNBQVM7Z0JBQzNCRSxPQUFPL00sVUFBVStNLEtBQUssSUFBSSxHQUFHLDBCQUEwQjtZQUN6RDtZQUVBcEgsUUFBUUMsR0FBRyxDQUFDLGNBQWNiO1lBRTFCLElBQUlBLFNBQVNHLElBQUksQ0FBQ0MsSUFBSSxLQUFLLEtBQUs7Z0JBQzlCUSxRQUFRQyxHQUFHLENBQUM7Z0JBQ1orRyxhQUFhSyxPQUFPLENBQUM7Z0JBQ3JCdkwsMkJBQTJCO2dCQUUzQixXQUFXO2dCQUNYLE1BQU02RixtQkFBbUI7b0JBQ3ZCLEdBQUd0SCxTQUFTO29CQUNaNk0sV0FBV0osT0FBT0ksU0FBUztnQkFDN0I7Z0JBQ0F0TSxhQUFhK0c7Z0JBRWIsb0JBQW9CO2dCQUNwQmxILDhCQUFBQSx3Q0FBQUEsa0JBQW9Ca0g7WUFDdEIsT0FBTztnQkFDTDNCLFFBQVEvRSxLQUFLLENBQUMscUJBQXFCO29CQUNqQ3VFLE1BQU1KLFNBQVNHLElBQUksQ0FBQ0MsSUFBSTtvQkFDeEJvRSxTQUFTeEUsU0FBU0csSUFBSSxDQUFDcUUsT0FBTztvQkFDOUJyRSxNQUFNSCxTQUFTRyxJQUFJO2dCQUNyQjtnQkFDQXlILGFBQWEvTCxLQUFLLENBQUNtRSxTQUFTRyxJQUFJLENBQUNxRSxPQUFPLElBQUk7Z0JBQzVDLE1BQU0sSUFBSUUsTUFBTTFFLFNBQVNHLElBQUksQ0FBQ3FFLE9BQU8sSUFBSSxXQUFXLGdCQUFnQjtZQUN0RTtRQUNGLEVBQUUsT0FBTzNJLE9BQVk7Z0JBSVBBLGlCQUNGQTtZQUpWK0UsUUFBUS9FLEtBQUssQ0FBQyxrQkFBa0I7Z0JBQzlCQSxPQUFPQTtnQkFDUDJJLFNBQVMzSSxNQUFNMkksT0FBTztnQkFDdEJ4RSxRQUFRLEdBQUVuRSxrQkFBQUEsTUFBTW1FLFFBQVEsY0FBZG5FLHNDQUFBQSxnQkFBZ0JzRSxJQUFJO2dCQUM5QitILE1BQU0sR0FBRXJNLG1CQUFBQSxNQUFNbUUsUUFBUSxjQUFkbkUsdUNBQUFBLGlCQUFnQnFNLE1BQU07WUFDaEM7WUFFQSwwQkFBMEI7WUFDMUIsSUFBSSxDQUFDck0sTUFBTTJJLE9BQU8sSUFBSTNJLE1BQU0ySSxPQUFPLEtBQUssVUFBVTtvQkFDN0IzSSxzQkFBQUE7Z0JBQW5CK0wsYUFBYS9MLEtBQUssQ0FBQ0EsRUFBQUEsbUJBQUFBLE1BQU1tRSxRQUFRLGNBQWRuRSx3Q0FBQUEsdUJBQUFBLGlCQUFnQnNFLElBQUksY0FBcEJ0RSwyQ0FBQUEscUJBQXNCMkksT0FBTyxLQUFJO1lBQ3REO1lBRUEsTUFBTTNJLE9BQU8sb0JBQW9CO1FBQ25DO0lBQ0Y7SUFFQSxPQUFPO0lBQ1AsTUFBTXNNLHVCQUF1QixPQUFPQztRQUNsQyxJQUFJO1lBQ0Z4SCxRQUFRQyxHQUFHLENBQUMsV0FBV3VIO1lBQ3ZCLGtCQUFrQjtZQUNsQmhCLE1BQU07WUFDTnhLLCtCQUErQjtZQUMvQixPQUFPO1FBQ1QsRUFBRSxPQUFPZixPQUFPO1lBQ2QrRSxRQUFRL0UsS0FBSyxDQUFDLFdBQVdBO1lBQ3pCdUwsTUFBTTtZQUNOLE9BQU87UUFDVDtJQUNGO0lBRUEsT0FBTztJQUNQLE1BQU1pQix1QkFBdUI7UUFDM0IsSUFBSTtZQUNGLE1BQU1ySSxXQUFXLE1BQU03Rix3REFBVUEsQ0FBQ21PLGNBQWMsQ0FBQ3JOLFVBQVU2RSxFQUFFO1lBQzdEYyxRQUFRQyxHQUFHLENBQUMsV0FBV2I7WUFDdkJvSCxNQUFNO1FBQ1IsRUFBRSxPQUFPdkwsT0FBTztZQUNkK0UsUUFBUS9FLEtBQUssQ0FBQyxXQUFXQTtZQUN6QnVMLE1BQU07UUFDUjtJQUNGO0lBRUEsT0FBTztJQUNQLE1BQU1tQixzQkFBc0IsT0FBT0M7UUFDakMsSUFBSTtZQUNGLE1BQU14SSxXQUFXLE1BQU05RixvREFBUUEsQ0FBQ3VPLG9CQUFvQixDQUFDRDtZQUNyRDVILFFBQVFDLEdBQUcsQ0FBQyxXQUFXYjtZQUN2QixJQUFJQSxTQUFTRyxJQUFJLENBQUNDLElBQUksS0FBSyxLQUFLO2dCQUM5QnRDLG1CQUFtQmtDLFNBQVNHLElBQUksQ0FBQ0EsSUFBSTtZQUN2QyxPQUFPO2dCQUNMLE1BQU0sRUFBRXdILGVBQWUsRUFBRSxHQUFHLE1BQU0sOE5BQU87Z0JBQ3pDLE1BQU1DLGVBQWVEO2dCQUNyQkMsYUFBYS9MLEtBQUssQ0FBQ21FLFNBQVNHLElBQUksQ0FBQ3FFLE9BQU8sSUFBSTtZQUM5QztRQUNGLEVBQUUsT0FBTzNJLE9BQVk7Z0JBSUFBLHNCQUFBQTtZQUhuQitFLFFBQVEvRSxLQUFLLENBQUMsV0FBV0E7WUFDekIsTUFBTSxFQUFFOEwsZUFBZSxFQUFFLEdBQUcsTUFBTSw4TkFBTztZQUN6QyxNQUFNQyxlQUFlRDtZQUNyQkMsYUFBYS9MLEtBQUssQ0FBQ0EsRUFBQUEsa0JBQUFBLE1BQU1tRSxRQUFRLGNBQWRuRSx1Q0FBQUEsdUJBQUFBLGdCQUFnQnNFLElBQUksY0FBcEJ0RSwyQ0FBQUEscUJBQXNCMkksT0FBTyxLQUFJO1FBQ3REO0lBQ0Y7SUFFQSxPQUFPO0lBQ1AsTUFBTWtFLHNCQUFzQixPQUFPaEI7UUFDakMsTUFBTSxFQUFFQyxlQUFlLEVBQUUsR0FBRyxNQUFNLDhOQUFPO1FBQ3pDLE1BQU1DLGVBQWVEO1FBRXJCM0osbUJBQW1CO1FBQ25CLElBQUk7WUFDRixJQUFJMks7WUFFSixJQUFJakIsT0FBT2tCLFlBQVksS0FBSyxVQUFVO2dCQUNwQyxJQUFJLENBQUMvSyxpQkFBaUI7b0JBQ3BCK0osYUFBYS9MLEtBQUssQ0FBQztvQkFDbkI7Z0JBQ0Y7Z0JBQ0E4TSxlQUFlOUssZ0JBQWdCaUMsRUFBRTtZQUNuQyxPQUFPO2dCQUNMLFlBQVk7Z0JBQ1osSUFBSSxDQUFDN0UsVUFBVTROLGtCQUFrQixFQUFFO29CQUNqQ2pCLGFBQWEvTCxLQUFLLENBQUM7b0JBQ25CO2dCQUNGO2dCQUNBOE0sZUFBZTFOLFVBQVU0TixrQkFBa0I7WUFDN0M7WUFFQSxNQUFNN0ksV0FBVyxNQUFNOUYsb0RBQVFBLENBQUM0TyxhQUFhLENBQUM3TixVQUFVNkUsRUFBRSxFQUFFNkksY0FBY2pCLE9BQU9rQixZQUFZO1lBRTdGLElBQUk1SSxTQUFTRyxJQUFJLENBQUNDLElBQUksS0FBSyxLQUFLO2dCQUM5QndILGFBQWFLLE9BQU8sQ0FBQztnQkFDckJuTCwrQkFBK0I7Z0JBQy9CZ0IsbUJBQW1CO2dCQUVuQixnQkFBZ0I7Z0JBQ2hCMUM7WUFDRixPQUFPO2dCQUNMd00sYUFBYS9MLEtBQUssQ0FBQ21FLFNBQVNHLElBQUksQ0FBQ3FFLE9BQU8sSUFBSTtZQUM5QztRQUNGLEVBQUUsT0FBTzNJLE9BQVk7Z0JBRUFBLHNCQUFBQTtZQURuQitFLFFBQVEvRSxLQUFLLENBQUMsV0FBV0E7WUFDekIrTCxhQUFhL0wsS0FBSyxDQUFDQSxFQUFBQSxrQkFBQUEsTUFBTW1FLFFBQVEsY0FBZG5FLHVDQUFBQSx1QkFBQUEsZ0JBQWdCc0UsSUFBSSxjQUFwQnRFLDJDQUFBQSxxQkFBc0IySSxPQUFPLEtBQUk7UUFDdEQsU0FBVTtZQUNSeEcsbUJBQW1CO1FBQ3JCO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTStLLHdCQUF3QjtRQUM1QixNQUFNLEVBQUVwQixlQUFlLEVBQUUsR0FBRyxNQUFNLDhOQUFPO1FBQ3pDLE1BQU1DLGVBQWVEO1FBRXJCLElBQUk7WUFDRixtQkFBbUI7WUFDbkIsbUNBQW1DO1lBQ25DLE1BQU0zSCxXQUFXLE1BQU05RixvREFBUUEsQ0FBQzZOLFdBQVcsQ0FBQzlNLFVBQVU2RSxFQUFFLEVBQUU7Z0JBQ3hEK0ksb0JBQW9CO1lBQ3RCO1lBRUEsSUFBSTdJLFNBQVNHLElBQUksQ0FBQ0MsSUFBSSxLQUFLLEtBQUs7Z0JBQzlCd0gsYUFBYUssT0FBTyxDQUFDO2dCQUNyQm5MLCtCQUErQjtnQkFFL0IsV0FBVztnQkFDWCxNQUFNeUYsbUJBQW1CO29CQUN2QixHQUFHdEgsU0FBUztvQkFDWjROLG9CQUFvQjtnQkFDdEI7Z0JBQ0FyTixhQUFhK0c7Z0JBQ2JsSCw4QkFBQUEsd0NBQUFBLGtCQUFvQmtIO1lBQ3RCLE9BQU87Z0JBQ0xxRixhQUFhL0wsS0FBSyxDQUFDbUUsU0FBU0csSUFBSSxDQUFDcUUsT0FBTyxJQUFJO1lBQzlDO1FBQ0YsRUFBRSxPQUFPM0ksT0FBWTtnQkFFQUEsc0JBQUFBO1lBRG5CK0UsUUFBUS9FLEtBQUssQ0FBQyxhQUFhQTtZQUMzQitMLGFBQWEvTCxLQUFLLENBQUNBLEVBQUFBLGtCQUFBQSxNQUFNbUUsUUFBUSxjQUFkbkUsdUNBQUFBLHVCQUFBQSxnQkFBZ0JzRSxJQUFJLGNBQXBCdEUsMkNBQUFBLHFCQUFzQjJJLE9BQU8sS0FBSTtRQUN0RDtJQUNGO0lBRUEsUUFBUTtJQUNSLE1BQU13RSwyQkFBMkI7UUFDL0IsSUFBSTtZQUNGLE1BQU1oSixXQUFXLE1BQU05RixvREFBUUEsQ0FBQytPLGtCQUFrQixDQUFDaE8sVUFBVTZFLEVBQUU7WUFFL0QsSUFBSUUsU0FBU0csSUFBSSxDQUFDQyxJQUFJLEtBQUssS0FBSztnQkFDOUJoRCxjQUFjNEMsU0FBU0csSUFBSSxDQUFDQSxJQUFJLENBQUNoRCxVQUFVO2dCQUMzQ0gsNEJBQTRCO1lBQzlCLE9BQU87Z0JBQ0w0RCxRQUFRL0UsS0FBSyxDQUFDLFlBQVltRSxTQUFTRyxJQUFJLENBQUNxRSxPQUFPO2dCQUMvQzRDLE1BQU1wSCxTQUFTRyxJQUFJLENBQUNxRSxPQUFPLElBQUk7WUFDakM7UUFDRixFQUFFLE9BQU8zSSxPQUFPO1lBQ2QrRSxRQUFRL0UsS0FBSyxDQUFDLFlBQVlBO1lBQzFCdUwsTUFBTTtRQUNSO0lBQ0Y7SUFFQSxnQkFBZ0I7SUFDaEIsTUFBTThCLHFCQUFxQjtRQUN6QnRJLFFBQVFDLEdBQUcsQ0FBQztRQUNaRCxRQUFRQyxHQUFHLENBQUMsdUJBQXVCekI7UUFDbkN3QixRQUFRQyxHQUFHLENBQUMsOEJBQThCekIsbUJBQW1CNEIsTUFBTTtRQUVuRSx1QkFBdUI7UUFDdkIsSUFBSTVCLG1CQUFtQjRCLE1BQU0sS0FBSyxHQUFHO1lBQ25DSixRQUFRQyxHQUFHLENBQUMsdUJBQXVCekIsa0JBQWtCLENBQUMsRUFBRTtZQUN4REgscUJBQXFCRyxrQkFBa0IsQ0FBQyxFQUFFO1FBQzVDLE9BQU87WUFDTHdCLFFBQVFDLEdBQUcsQ0FBQyxnQkFBZ0J6QixtQkFBbUI0QixNQUFNO1lBQ3JEL0IscUJBQXFCO1FBQ3ZCO1FBRUEsTUFBTTZGO1FBQ04sTUFBTVM7UUFDTnJJLDhCQUE4QjtJQUNoQztJQUVBLFlBQVk7SUFDWixNQUFNaU0sK0JBQStCLE9BQU9qRjtRQUMxQ3RELFFBQVFDLEdBQUcsQ0FBQztRQUNaRCxRQUFRQyxHQUFHLENBQUMsY0FBY3FEO1FBRTFCLGNBQWM7UUFDZGpGLHFCQUFxQmlGO1FBQ3JCN0Usc0JBQXNCLEVBQUUsR0FBRyxTQUFTO1FBRXBDLE1BQU15RjtRQUNOLE1BQU1TO1FBQ05ySSw4QkFBOEI7SUFDaEM7SUFFQSxrQkFBa0I7SUFDbEIsTUFBTWtNLDJCQUEyQjtRQUMvQixJQUFJLENBQUM3TixpQkFBaUI7UUFFdEJxRixRQUFRQyxHQUFHLENBQUM7UUFDWkQsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQnRGO1FBRWhDLE1BQU00Tiw2QkFBNkI1TixnQkFBZ0IyRSxNQUFNO0lBQzNEO0lBRUEsT0FBTztJQUNQLE1BQU1tSixvQkFBb0I7UUFDeEIsTUFBTSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNLGdOQUFPO1FBQy9CLE1BQU0sRUFBRTNCLGVBQWUsRUFBRSxHQUFHLE1BQU0sOE5BQU87UUFDekMsTUFBTUMsZUFBZUQ7UUFFckIsV0FBVztRQUNYLElBQUlsTSxTQUFTdUYsTUFBTSxHQUFHLEdBQUc7WUFDdkJzSSxNQUFNQyxPQUFPLENBQUM7Z0JBQ1pDLE9BQU87Z0JBQ1BDLFVBQVU7Z0JBQ1ZDLHVCQUNFLDhEQUFDQzs7c0NBQ0MsOERBQUNDOztnQ0FBRTtnQ0FBT25PLFNBQVN1RixNQUFNO2dDQUFDOzs7Ozs7O3NDQUMxQiw4REFBQzJJOzRCQUFJN0IsV0FBVTs7OENBQ2IsOERBQUM4Qjs4Q0FBRTs7Ozs7OzhDQUNILDhEQUFDQztvQ0FBRy9CLFdBQVU7O3NEQUNaLDhEQUFDZ0M7c0RBQUc7Ozs7OztzREFDSiw4REFBQ0E7c0RBQUc7Ozs7OztzREFDSiw4REFBQ0E7c0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQkFLWkMsUUFBUTtZQUNWO1lBQ0E7UUFDRjtRQUVBLG1CQUFtQjtRQUNuQlQsTUFBTS9CLE9BQU8sQ0FBQztZQUNaaUMsT0FBTztZQUNQRSx1QkFDRSw4REFBQ0M7O2tDQUNDLDhEQUFDQzs7NEJBQUU7MENBQU8sOERBQUNJOzBDQUFRL08sVUFBVTZNLFNBQVM7Ozs7Ozs0QkFBVTs7Ozs7OztrQ0FDaEQsOERBQUM2Qjt3QkFBSTdCLFdBQVU7OzBDQUNiLDhEQUFDNkI7Z0NBQUk3QixXQUFVOztrREFDYiw4REFBQ21DO3dDQUFJbkMsV0FBVTt3Q0FBVW9DLE1BQUs7d0NBQWVDLFNBQVE7a0RBQ25ELDRFQUFDQzs0Q0FBS0MsVUFBUzs0Q0FBVUMsR0FBRTs0Q0FBb05DLFVBQVM7Ozs7Ozs7Ozs7O2tEQUUxUCw4REFBQ0M7d0NBQUsxQyxXQUFVO2tEQUFjOzs7Ozs7Ozs7Ozs7MENBRWhDLDhEQUFDOEI7Z0NBQUU5QixXQUFVOzBDQUE0Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTS9DaUMsUUFBUTtZQUNSVSxZQUFZO1lBQ1pDLGVBQWU7Z0JBQUVDLFFBQVE7WUFBSztZQUM5QmxCLFVBQVU7WUFDVm1CLE1BQU07Z0JBQ0osSUFBSTtvQkFDRixNQUFNNUssV0FBVyxNQUFNOUYsb0RBQVFBLENBQUMyUSxXQUFXLENBQUM1UCxVQUFVNkUsRUFBRTtvQkFFeEQsSUFBSUUsU0FBU0csSUFBSSxDQUFDQyxJQUFJLEtBQUssS0FBSzt3QkFDOUJ3SCxhQUFhSyxPQUFPLENBQUM7d0JBQ3JCLGNBQWM7d0JBQ2QzTSwyQkFBQUEscUNBQUFBLGVBQWlCTCxVQUFVNkUsRUFBRTt3QkFDN0IsWUFBWTt3QkFDWjFFO29CQUNGLE9BQU87d0JBQ0x3TSxhQUFhL0wsS0FBSyxDQUFDbUUsU0FBU0csSUFBSSxDQUFDcUUsT0FBTyxJQUFJO29CQUM5QztnQkFDRixFQUFFLE9BQU8zSSxPQUFZO3dCQUVmQSxzQkFBQUE7b0JBREorRSxRQUFRL0UsS0FBSyxDQUFDLFdBQVdBO29CQUN6QixLQUFJQSxrQkFBQUEsTUFBTW1FLFFBQVEsY0FBZG5FLHVDQUFBQSx1QkFBQUEsZ0JBQWdCc0UsSUFBSSxjQUFwQnRFLDJDQUFBQSxxQkFBc0IySSxPQUFPLEVBQUU7d0JBQ2pDb0QsYUFBYS9MLEtBQUssQ0FBQ0EsTUFBTW1FLFFBQVEsQ0FBQ0csSUFBSSxDQUFDcUUsT0FBTztvQkFDaEQsT0FBTzt3QkFDTG9ELGFBQWEvTCxLQUFLLENBQUM7b0JBQ3JCO2dCQUNGO1lBQ0Y7UUFDRjtJQUNGO0lBRUEsWUFBWTtJQUNaLE1BQU1pUCxzQkFBc0IsQ0FBQ0M7UUFDM0IsOENBQThDO1FBRTlDLE9BQVFBO1lBQ04sS0FBSztnQkFDSHJPLDJCQUEyQjtnQkFDM0I7WUFDRixLQUFLO2dCQUNIVCw0QkFBNEI7Z0JBQzVCO1lBQ0YsS0FBSztnQkFDSFcsK0JBQStCO2dCQUMvQjtZQUNGLEtBQUs7Z0JBQ0h5TDtnQkFDQTtZQUNGLEtBQUs7Z0JBQ0h2TCwrQkFBK0I7Z0JBQy9CO1lBQ0YsS0FBSztnQkFDSGtNO2dCQUNBO1lBQ0YsS0FBSztnQkFDSHBJLFFBQVFDLEdBQUcsQ0FBQztnQkFDWmpELG9DQUFvQztnQkFDcEM7WUFDRixLQUFLO2dCQUNIc0w7Z0JBQ0E7WUFDRixLQUFLO2dCQUNIRztnQkFDQTtZQUNGO2dCQUNFO1FBQ0o7SUFDRjtJQUVBLGNBQWM7SUFDZCxNQUFNMkIsOEJBQThCLENBQUNEO1FBQ25DNU8sMEJBQTBCO1FBRTFCLE9BQVE0TztZQUNOLEtBQUs7Z0JBQ0hyTywyQkFBMkI7Z0JBQzNCO1lBQ0YsS0FBSztnQkFDSFQsNEJBQTRCO2dCQUM1QjtZQUNGLEtBQUs7Z0JBQ0hXLCtCQUErQjtnQkFDL0I7WUFDRixLQUFLO2dCQUNIeUw7Z0JBQ0E7WUFDRixLQUFLO2dCQUNIdkwsK0JBQStCO2dCQUMvQjtZQUNGLEtBQUs7Z0JBQ0hrTTtnQkFDQTtZQUNGLEtBQUs7Z0JBQ0hwSSxRQUFRQyxHQUFHLENBQUM7Z0JBQ1pqRCxvQ0FBb0M7Z0JBQ3BDO1lBQ0YsS0FBSztnQkFDSHNMO2dCQUNBO1lBQ0YsS0FBSztnQkFDSEc7Z0JBQ0E7WUFDRjtnQkFDRTtRQUNKO0lBQ0Y7SUFFQSxXQUFXO0lBQ1hwUSxnREFBU0EsQ0FBQztRQUNSLE1BQU1nUyxvQkFBb0I3TCxtQkFBbUI0QixNQUFNLEdBQUcsS0FBSzVCLG1CQUFtQjRCLE1BQU0sS0FBS3ZGLFNBQVN1RixNQUFNO1FBQ3hHLElBQUkxQixnQkFBZ0IyTCxtQkFBbUI7WUFDckNySyxRQUFRQyxHQUFHLENBQUMsYUFBYTtnQkFDdkJ2QjtnQkFDQTJMO2dCQUNBN0wsb0JBQW9CQSxtQkFBbUI0QixNQUFNO2dCQUM3Q2tLLGVBQWV6UCxTQUFTdUYsTUFBTTtZQUNoQztZQUNBekIsZUFBZTBMO1FBQ2pCO0lBQ0YsR0FBRztRQUFDN0w7UUFBb0IzRCxTQUFTdUYsTUFBTTtRQUFFMUI7S0FBWTtJQUVyRCxjQUFjO0lBQ2QsTUFBTTZMLGtCQUFrQjtRQUN0QixJQUFJN0wsYUFBYTtZQUNmLE9BQU87WUFDUEQsc0JBQXNCLEVBQUU7WUFDeEJFLGVBQWU7UUFDakIsT0FBTztZQUNMLEtBQUs7WUFDTCxNQUFNNkwsZ0JBQWdCM1AsU0FBUzRGLEdBQUcsQ0FBQ0MsQ0FBQUEsVUFBV0EsUUFBUXBCLE1BQU07WUFDNURiLHNCQUFzQitMO1lBQ3RCN0wsZUFBZTtRQUNqQjtJQUNGO0lBRUEsYUFBYTtJQUNiLE1BQU04TCxzQkFBc0IsQ0FBQ25IO1FBQzNCLElBQUk5RSxtQkFBbUJrTSxRQUFRLENBQUNwSCxZQUFZO1lBQzFDLE9BQU87WUFDUCxNQUFNcUgsaUJBQWlCbk0sbUJBQW1Cb00sTUFBTSxDQUFDMUwsQ0FBQUEsS0FBTUEsT0FBT29FO1lBQzlEN0Usc0JBQXNCa007WUFDdEJoTSxlQUFlO1FBQ2pCLE9BQU87WUFDTCxLQUFLO1lBQ0wsTUFBTWdNLGlCQUFpQjttQkFBSW5NO2dCQUFvQjhFO2FBQVU7WUFDekQ3RSxzQkFBc0JrTTtZQUN0QixTQUFTO1lBQ1QsSUFBSUEsZUFBZXZLLE1BQU0sS0FBS3ZGLFNBQVN1RixNQUFNLEVBQUU7Z0JBQzdDekIsZUFBZTtZQUNqQjtRQUNGO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTWtNLG9CQUFvQixDQUFDVjtRQUN6QjFPLDhCQUE4QjtRQUU5QixJQUFJK0MsbUJBQW1CNEIsTUFBTSxLQUFLLEdBQUc7WUFDbkMsOE5BQU8sQ0FBNkNvQyxJQUFJLENBQUM7b0JBQUMsRUFBRXVFLGVBQWUsRUFBRTtnQkFDM0VBLGtCQUFrQjRCLE9BQU8sQ0FBQztZQUM1QjtZQUNBO1FBQ0Y7UUFFQSxPQUFRd0I7WUFDTixLQUFLO2dCQUNIVywwQkFBMEJ0TTtnQkFDMUI7WUFDRixLQUFLO2dCQUNIOEo7Z0JBQ0E7WUFDRixLQUFLO2dCQUNILG9CQUFvQjtnQkFDcEJuTixtQkFBbUI7Z0JBQ25CMkIsOEJBQThCO2dCQUM5QjtZQUNGLEtBQUs7Z0JBQ0hpTztnQkFDQTtZQUNGLEtBQUs7Z0JBQ0gvSyxRQUFRQyxHQUFHLENBQUMsV0FBV3pCO2dCQUN2Qiw4TkFBTyxDQUE2Q2dFLElBQUksQ0FBQzt3QkFBQyxFQUFFdUUsZUFBZSxFQUFFO29CQUMzRUEsa0JBQWtCaUUsSUFBSSxDQUFDO2dCQUN6QjtnQkFDQTtZQUNGO2dCQUNFO1FBQ0o7SUFDRjtJQUVBLGNBQWM7SUFDZCxNQUFNRiw0QkFBNEIsT0FBT0c7UUFDdkMsSUFBSTtZQUNGLFlBQVk7WUFDWixNQUFNQyx1QkFBdUJyUSxTQUFTK1AsTUFBTSxDQUFDL0ksQ0FBQUEsSUFBS29KLFdBQVdQLFFBQVEsQ0FBQzdJLEVBQUV2QyxNQUFNO1lBRTlFLFVBQVU7WUFDVixNQUFNNkwsdUJBQXVCRCxxQkFBcUJFLE1BQU0sQ0FDdEQsQ0FBQ0MsS0FBSzNLLFVBQVkySyxNQUFPM0ssQ0FBQUEsUUFBUXlDLGVBQWUsSUFBSSxJQUNwRDtZQUdGLE1BQU0sRUFBRXVGLEtBQUssRUFBRSxHQUFHLE1BQU0sZ05BQU87WUFDL0IsTUFBTSxFQUFFNEMsa0JBQWtCLEVBQUUsR0FBRyxNQUFNLHNQQUFPO1lBRTVDNUMsTUFBTS9CLE9BQU8sQ0FBQztnQkFDWmlDLE9BQU87Z0JBQ1BFLHVCQUNFLDhEQUFDQzs7c0NBQ0MsOERBQUNDOztnQ0FBRTtnQ0FBU2lDLFdBQVc3SyxNQUFNO2dDQUFDOzs7Ozs7O3dCQUM3QitLLHVCQUF1QixtQkFDdEIsOERBQUNwQzs0QkFBSTdCLFdBQVU7OzhDQUNiLDhEQUFDNkI7b0NBQUk3QixXQUFVOztzREFDYiw4REFBQ29FOzs7OztzREFDRCw4REFBQzFCOztnREFBSztnREFBU3VCO2dEQUFxQjs7Ozs7Ozs7Ozs7Ozs4Q0FFdEMsOERBQUNuQztvQ0FBRTlCLFdBQVU7OENBQStCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBT3BEaUMsUUFBUTtnQkFDUlUsWUFBWTtnQkFDWmhCLFVBQVU7Z0JBQ1ZpQixlQUFlO29CQUFFQyxRQUFRO2dCQUFLO2dCQUM5QkMsTUFBTTtvQkFDSixJQUFJO3dCQUNGLE1BQU0sRUFBRWpELGVBQWUsRUFBRSxHQUFHLE1BQU0sOE5BQU87d0JBQ3pDLE1BQU1DLGVBQWVEO3dCQUNyQixNQUFNd0UsY0FBY3ZFLGFBQWFqTSxPQUFPLENBQUM7d0JBRXpDLE1BQU1xRSxXQUFXLE1BQU05RixvREFBUUEsQ0FBQ3NOLHNCQUFzQixDQUFDcUU7d0JBRXZELElBQUlNLGFBQWE7NEJBQ2ZBLFlBQVlDLEtBQUs7d0JBQ25CO3dCQUVBLElBQUlwTSxTQUFTRyxJQUFJLENBQUNDLElBQUksS0FBSyxLQUFLOzRCQUM5QndILGFBQWFLLE9BQU8sQ0FBQyxRQUEwQixPQUFsQjRELFdBQVc3SyxNQUFNLEVBQUM7NEJBRS9DLFNBQVM7NEJBQ1QzQixzQkFBc0IsRUFBRTs0QkFDeEJFLGVBQWU7NEJBQ2Z4RCxtQkFBbUI7NEJBRW5CLFdBQVc7NEJBQ1gsTUFBTWtHO3dCQUNSLE9BQU87NEJBQ0wyRixhQUFhL0wsS0FBSyxDQUFDbUUsU0FBU0csSUFBSSxDQUFDcUUsT0FBTyxJQUFJO3dCQUM5QztvQkFDRixFQUFFLE9BQU8zSSxPQUFZOzRCQUdBQSxzQkFBQUE7d0JBRm5CLE1BQU0sRUFBRThMLGVBQWUsRUFBRSxHQUFHLE1BQU0sOE5BQU87d0JBQ3pDLE1BQU1DLGVBQWVEO3dCQUNyQkMsYUFBYS9MLEtBQUssQ0FBQ0EsRUFBQUEsa0JBQUFBLE1BQU1tRSxRQUFRLGNBQWRuRSx1Q0FBQUEsdUJBQUFBLGdCQUFnQnNFLElBQUksY0FBcEJ0RSwyQ0FBQUEscUJBQXNCMkksT0FBTyxLQUFJO3dCQUNwRDVELFFBQVEvRSxLQUFLLENBQUMsYUFBYUE7b0JBQzdCO2dCQUNGO1lBQ0Y7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZCtFLFFBQVEvRSxLQUFLLENBQUMsYUFBYUE7UUFDN0I7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNd1EsbUJBQW1CLE9BQU8zRTtRQUM5QixJQUFJO1lBQ0Y5RyxRQUFRQyxHQUFHLENBQUMsU0FBUzZHO1lBRXJCLFNBQVM7WUFDVCxNQUFNNEUsY0FBYztnQkFDbEIsR0FBRzVFLE1BQU07Z0JBQ1Q2RSxVQUFVO1lBQ1o7WUFFQSxhQUFhO1lBQ2IsTUFBTXZNLFdBQVcsTUFBTTlGLG9EQUFRQSxDQUFDc1MsaUJBQWlCLENBQUN2UixVQUFVNkUsRUFBRSxFQUFFd007WUFFaEUxTCxRQUFRQyxHQUFHLENBQUMsZ0JBQWdCYixTQUFTRyxJQUFJO1lBRXpDLFNBQVM7WUFDVCxJQUFJSCxTQUFTRyxJQUFJLENBQUNDLElBQUksS0FBSyxLQUFLO2dCQUM5QlEsUUFBUUMsR0FBRyxDQUFDO2dCQUNaNUUsNEJBQTRCO2dCQUM1QixXQUFXO2dCQUNYLE1BQU1nRztZQUNSLE9BQU87Z0JBQ0xyQixRQUFRL0UsS0FBSyxDQUFDLFdBQVdtRSxTQUFTRyxJQUFJLENBQUNxRSxPQUFPO2dCQUM5QzRDLE1BQU1wSCxTQUFTRyxJQUFJLENBQUNxRSxPQUFPLElBQUk7WUFDakM7UUFDRixFQUFFLE9BQU8zSSxPQUFPO1lBQ2QrRSxRQUFRL0UsS0FBSyxDQUFDLFdBQVdBO1lBQ3pCdUwsTUFBTTtRQUNSO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTXFGLGVBQWUsT0FBT3JFO1FBQzFCaEssYUFBYTtRQUNiLElBQUk7WUFDRixlQUFlO1lBQ2Z3QyxRQUFRQyxHQUFHLENBQUMsU0FBU3VIO1lBQ3JCLE9BQU87Z0JBQUVILFNBQVM7WUFBSztRQUN6QixFQUFFLE9BQU9wTSxPQUFPO1lBQ2QrRSxRQUFRL0UsS0FBSyxDQUFDLFdBQVdBO1lBQ3pCLE9BQU87Z0JBQUVvTSxTQUFTO1lBQU07UUFDMUIsU0FBVTtZQUNSN0osYUFBYTtRQUNmO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTXNPLG1CQUFtQixPQUFPdEU7UUFDOUIsSUFBSTtZQUNGbEssWUFBWXlPLENBQUFBLE9BQVFBLEtBQUtuQixNQUFNLENBQUNvQixDQUFBQSxJQUFLQSxFQUFFQyxHQUFHLEtBQUt6RSxLQUFLeUUsR0FBRztZQUN2RCxPQUFPO1FBQ1QsRUFBRSxPQUFPaFIsT0FBTztZQUNkK0UsUUFBUS9FLEtBQUssQ0FBQyxXQUFXQTtZQUN6QixPQUFPO1FBQ1Q7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNaVIsb0JBQW9CLE9BQU9wRjtRQUMvQixJQUFJO1lBQ0Y5RyxRQUFRQyxHQUFHLENBQUMsU0FBUzZHO1lBRXJCLFlBQVk7WUFDWixNQUFNLEVBQUVxRixTQUFTQyxPQUFPLEVBQUUsR0FBRyxNQUFNLDBKQUFPO1lBQzFDLE1BQU0sRUFBRXJGLGVBQWUsRUFBRSxHQUFHLE1BQU0sOE5BQU87WUFDekMsTUFBTUMsZUFBZUQ7WUFFckIsU0FBUztZQUNULE1BQU1zRixTQUFTO2dCQUNiQyxVQUFVeEYsT0FBT3dGLFFBQVE7Z0JBQ3pCQyxpQkFBaUJ6RixPQUFPeUYsZUFBZSxJQUFJO2dCQUMzQ0MsVUFBVTtnQkFDVkMsV0FBVzNGLE9BQU8yRixTQUFTLEdBQUcsSUFBSUMsS0FBSzVGLE9BQU8yRixTQUFTLElBQUl6STtnQkFDM0QySSxTQUFTN0YsT0FBTzZGLE9BQU8sR0FBRyxJQUFJRCxLQUFLNUYsT0FBTzZGLE9BQU8sSUFBSSxJQUFJRCxLQUFLQSxLQUFLRSxHQUFHLEtBQUssSUFBSSxLQUFLLEtBQUssS0FBSztnQkFDOUZDLGFBQWEvRixPQUFPK0YsV0FBVyxJQUFJO2dCQUNuQ0MsYUFBYXpQLFNBQVNvRCxHQUFHLENBQUMrRyxDQUFBQTt3QkFBb0JBOzJCQUFaQSxLQUFLdUYsR0FBRyxNQUFJdkYsaUJBQUFBLEtBQUtwSSxRQUFRLGNBQWJvSSxxQ0FBQUEsZUFBZXVGLEdBQUc7bUJBQUVuQyxNQUFNLENBQUNvQztnQkFDekUvSCxXQUFXM0Y7Z0JBQ1gyRSxPQUFPLEVBQUU1SixzQkFBQUEsZ0NBQUFBLFVBQVc2RSxFQUFFO2dCQUN0QitMLFlBQVl6TSxtQkFBbUI0QixNQUFNLEdBQUcsSUFBSTVCLHFCQUFxQjNELFNBQVM0RixHQUFHLENBQUNvQixDQUFBQSxJQUFLQSxFQUFFdkMsTUFBTTtnQkFDM0YyTixxQkFBcUJuRyxPQUFPbUcsbUJBQW1CLElBQUksRUFBRTtnQkFDckRDLFVBQVU7Z0JBQ1ZDLFVBQVU7Z0JBQ1ZDLHFCQUFxQnRHLE9BQU9zRyxtQkFBbUIsSUFBSTtZQUNyRDtZQUVBcE4sUUFBUUMsR0FBRyxDQUFDLFdBQVdvTTtZQUV2QixZQUFZO1lBQ1osTUFBTWpOLFdBQVcsTUFBTWdOLFFBQVFpQixXQUFXLENBQUNoQjtZQUUzQyxJQUFJak4sU0FBU0csSUFBSSxDQUFDQyxJQUFJLEtBQUssS0FBSztnQkFDOUIsWUFBWTtnQkFDWixNQUFNLEVBQUVrSixLQUFLLEVBQUUsR0FBRyxNQUFNLGdOQUFPO2dCQUMvQixNQUFNaEgsZUFBZWxELG1CQUFtQjRCLE1BQU0sR0FBRyxJQUFJNUIsbUJBQW1CNEIsTUFBTSxHQUFHdkYsU0FBU3VGLE1BQU07Z0JBQ2hHLE1BQU04RyxZQUFZN00sQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXNk0sU0FBUyxLQUFJO2dCQUUxQ3dCLE1BQU1yQixPQUFPLENBQUM7b0JBQ1p1QixPQUFPO29CQUNQRSx1QkFDRSw4REFBQ0M7OzBDQUNDLDhEQUFDQzs7b0NBQUU7a0RBQUcsOERBQUNJO2tEQUFRaUQsT0FBT0MsUUFBUTs7Ozs7O29DQUFVO2tEQUFRLDhEQUFDbEQ7a0RBQVFsQzs7Ozs7O29DQUFtQjs7Ozs7OzswQ0FDNUUsOERBQUM4Qjs7b0NBQUU7a0RBQUcsOERBQUNJO2tEQUFRMUg7Ozs7OztvQ0FBc0I7Ozs7Ozs7MENBQ3JDLDhEQUFDc0g7MENBQUU7Ozs7Ozs7Ozs7OztvQkFHUEcsUUFBUTtvQkFDUmEsTUFBTTtvQkFDSixzQkFBc0I7b0JBQ3hCO2dCQUNGO2dCQUVBdE4sNkJBQTZCO2dCQUU3QixTQUFTO2dCQUNUWSxZQUFZLEVBQUU7Z0JBQ2RtQixzQkFBc0IsRUFBRTtnQkFDeEJaLHNCQUFzQjtnQkFFdEIsdUJBQXVCO2dCQUN2QixNQUFNd0Q7WUFDUixPQUFPO2dCQUNMMkYsYUFBYS9MLEtBQUssQ0FBQ21FLFNBQVNHLElBQUksQ0FBQ3FFLE9BQU8sSUFBSTtZQUM5QztRQUNGLEVBQUUsT0FBTzNJLE9BQVk7Z0JBSUFBLHNCQUFBQTtZQUhuQitFLFFBQVEvRSxLQUFLLENBQUMsV0FBV0E7WUFDekIsTUFBTSxFQUFFOEwsZUFBZSxFQUFFLEdBQUcsTUFBTSw4TkFBTztZQUN6QyxNQUFNQyxlQUFlRDtZQUNyQkMsYUFBYS9MLEtBQUssQ0FBQ0EsRUFBQUEsa0JBQUFBLE1BQU1tRSxRQUFRLGNBQWRuRSx1Q0FBQUEsdUJBQUFBLGdCQUFnQnNFLElBQUksY0FBcEJ0RSwyQ0FBQUEscUJBQXNCMkksT0FBTyxLQUFJO1FBQ3REO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTTBKLHNCQUFzQjtRQUMxQixJQUFJO1lBQ0YsSUFBSSxDQUFDM1MsaUJBQWlCO2dCQUNwQjZMLE1BQU07Z0JBQ047WUFDRjtZQUVBeEcsUUFBUUMsR0FBRyxDQUFDLFNBQVN0RjtZQUNyQixtQkFBbUI7WUFDbkJpQywrQkFBK0I7WUFDL0I0SixNQUFNO1FBQ1IsRUFBRSxPQUFPdkwsT0FBTztZQUNkK0UsUUFBUS9FLEtBQUssQ0FBQyxXQUFXQTtZQUN6QnVMLE1BQU07UUFDUjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU0rRyx1QkFBdUIsT0FBT3pOO1FBQ2xDLElBQUk7WUFDRixNQUFNLEVBQUUwTixlQUFlLEVBQUVDLG9CQUFvQixFQUFFLEdBQUcsTUFBTSwwSkFBTztZQUMvRCxNQUFNLEVBQUUxRyxlQUFlLEVBQUUsR0FBRyxNQUFNLDhOQUFPO1lBQ3pDLE1BQU1DLGVBQWVEO1lBRXJCLFdBQVc7WUFDWCxNQUFNMkcsaUJBQWlCdFAsc0JBQXNCLE9BQU87Z0JBQUNBO2FBQWtCLEdBQUdJO1lBRTFFd0IsUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQyxzQkFBc0I3QjtZQUNsQzRCLFFBQVFDLEdBQUcsQ0FBQyx1QkFBdUJ6QjtZQUNuQ3dCLFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUJ5TjtZQUMvQjFOLFFBQVFDLEdBQUcsQ0FBQyxlQUFlSDtZQUUzQixJQUFJNE4sZUFBZXROLE1BQU0sS0FBSyxHQUFHO2dCQUMvQkosUUFBUUMsR0FBRyxDQUFDO2dCQUNaK0csYUFBYTJCLE9BQU8sQ0FBQztnQkFDckI7WUFDRjtZQUVBLE1BQU00QyxjQUFjdkUsYUFBYWpNLE9BQU8sQ0FBQztZQUN6QyxNQUFNNFMsZUFBZXJQLGFBQWEsRUFBRTtZQUVwQyxJQUFJO2dCQUNGLGlDQUFpQztnQkFDakMsTUFBTXNQLFlBQVlGLGVBQ2ZqTixHQUFHLENBQUNuQixDQUFBQTtvQkFDSCxNQUFNdU8sV0FBV0YsYUFBYWpPLElBQUksQ0FBQ29PLENBQUFBLElBQUtBLEVBQUV4TyxNQUFNLEtBQUtBO29CQUNyRCxJQUFJLEVBQUN1TyxxQkFBQUEsK0JBQUFBLFNBQVV6TSxNQUFNLEdBQUUsT0FBTztvQkFDOUIsT0FBTzt3QkFDTDlCLFFBQVFBO3dCQUNSOEIsUUFBUXlNLFNBQVN6TSxNQUFNO3dCQUN2QnRCLFlBQVlBO3dCQUNaaU8sb0JBQW9Cak87b0JBQ3RCO2dCQUNGLEdBQ0M4SyxNQUFNLENBQUMsQ0FBQ3JGLE9BQXFHQSxTQUFTO2dCQUV6SHZGLFFBQVFDLEdBQUcsQ0FBQyxXQUFXO29CQUNyQkg7b0JBQ0E0TjtvQkFDQUM7b0JBQ0FDO2dCQUNGO2dCQUVBLElBQUlBLFVBQVV4TixNQUFNLEtBQUssR0FBRztvQkFDMUI0RyxhQUFhL0wsS0FBSyxDQUFDO29CQUNuQjtnQkFDRjtnQkFFQSxTQUFTO2dCQUNULE1BQU15SCxVQUFVLE1BQU9rTCxDQUFBQSxVQUFVeE4sTUFBTSxHQUFHLEtBQ3RDa0IsUUFBUUMsR0FBRyxDQUNYUSxNQUFNQyxJQUFJLENBQ1I7b0JBQUU1QixRQUFRNkIsS0FBS0MsSUFBSSxDQUFDMEwsVUFBVXhOLE1BQU0sR0FBRztnQkFBSSxHQUMzQyxDQUFDK0IsR0FBR0MsSUFBTXdMLFVBQVV2TCxLQUFLLENBQUNELElBQUksSUFBSSxDQUFDQSxJQUFJLEtBQUssS0FDNUMzQixHQUFHLENBQUN1TixDQUFBQSxhQUNKUCxxQkFBcUI7d0JBQUVRLE9BQU9EO29CQUFXLEdBQ3RDeEwsSUFBSSxDQUFDOzRCQUFDLEVBQUVqRCxJQUFJLEVBQUU7K0JBQUtBO3VCQUNuQmtELEtBQUssQ0FBQyxJQUFPOzRCQUFFakQsTUFBTTs0QkFBS0QsTUFBTTtnQ0FBRTJPLGNBQWM7Z0NBQUdDLFdBQVdILFdBQVc1TixNQUFNOzRCQUFDO3dCQUFFLE1BRXZGb0MsSUFBSSxDQUFDRSxDQUFBQSxVQUFZO3dCQUNqQmxELE1BQU1rRCxRQUFRMEwsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFN08sSUFBSSxLQUFLLE9BQU8sTUFBTTt3QkFDaERELE1BQU07NEJBQ0oyTyxjQUFjeEwsUUFBUTBJLE1BQU0sQ0FBQyxDQUFDQyxLQUFLZ0Q7b0NBQWFBO3VDQUFQaEQsTUFBT2dELENBQUFBLEVBQUFBLFVBQUFBLEVBQUU5TyxJQUFJLGNBQU44Tyw4QkFBQUEsUUFBUUgsWUFBWSxLQUFJOytCQUFJOzRCQUM1RUMsV0FBV3pMLFFBQVEwSSxNQUFNLENBQUMsQ0FBQ0MsS0FBS2dEO29DQUFhQTt1Q0FBUGhELE1BQU9nRCxDQUFBQSxFQUFBQSxVQUFBQSxFQUFFOU8sSUFBSSxjQUFOOE8sOEJBQUFBLFFBQVFGLFNBQVMsS0FBSTsrQkFBSTt3QkFDeEU7b0JBQ0YsTUFDRVYscUJBQXFCO29CQUFFUSxPQUFPTDtnQkFBVSxHQUN2Q3BMLElBQUksQ0FBQzt3QkFBQyxFQUFFakQsSUFBSSxFQUFFOzJCQUFLQTttQkFDbkJrRCxLQUFLLENBQUMsSUFBTzt3QkFBRWpELE1BQU07d0JBQUtELE1BQU07NEJBQUUyTyxjQUFjOzRCQUFHQyxXQUFXUCxVQUFVeE4sTUFBTTt3QkFBQztvQkFBRSxHQUFFO2dCQUd4RixJQUFJbUwsYUFBYTtvQkFDZkEsWUFBWUMsS0FBSztnQkFDbkI7Z0JBRUEsT0FBTztnQkFDUCxJQUFJOUksUUFBUWxELElBQUksS0FBSyxLQUFLO29CQUN4QixNQUFNLEVBQUUwTyxlQUFlLENBQUMsRUFBRUMsWUFBWSxDQUFDLEVBQUUsR0FBR3pMLFFBQVFuRCxJQUFJLElBQUksQ0FBQztvQkFFN0QsSUFBSTJPLGVBQWUsS0FBS0MsY0FBYyxHQUFHO3dCQUN2Q25ILGFBQWFLLE9BQU8sQ0FBQyxPQUFvQixPQUFiNkcsY0FBYTtvQkFDM0MsT0FBTyxJQUFJQSxlQUFlLEtBQUtDLFlBQVksR0FBRzt3QkFDNUNuSCxhQUFhMkIsT0FBTyxDQUFDLE9BQStCd0YsT0FBeEJELGNBQWEsYUFBcUIsT0FBVkMsV0FBVTtvQkFDaEUsT0FBTzt3QkFDTG5ILGFBQWEvTCxLQUFLLENBQUM7b0JBQ3JCO2dCQUNGLE9BQU87b0JBQ0wrTCxhQUFhL0wsS0FBSyxDQUFDO2dCQUNyQjtnQkFFQSwyQkFBMkI7Z0JBQzNCLE1BQU1xVCxtQkFBbUI3USxVQUFVaUMsSUFBSSxDQUFDNk8sQ0FBQUEsSUFBS0EsRUFBRXJQLEVBQUUsS0FBS1k7Z0JBQ3RELElBQUl3TyxrQkFBa0I7b0JBQ3BCLE1BQU1FLGVBQWU7d0JBQ25CMU8sWUFBWUE7d0JBQ1pGLGNBQWMwTyxpQkFBaUIxTyxZQUFZLElBQUkwTyxpQkFBaUJHLElBQUk7d0JBQ3BFMU8sWUFBWXVPLGlCQUFpQnZPLFVBQVUsSUFBSTtvQkFDN0M7b0JBRUEsNENBQTRDO29CQUM1Q2UsK0JBQStCaUwsQ0FBQUE7d0JBQzdCLE1BQU0yQyxTQUFTLElBQUkzTixJQUFJZ0w7d0JBQ3ZCMkIsZUFBZXBJLE9BQU8sQ0FBQ2hDLENBQUFBOzRCQUNyQm9MLE9BQU9DLEdBQUcsQ0FBQ3JMLFdBQVdrTDt3QkFDeEI7d0JBQ0EsT0FBT0U7b0JBQ1Q7b0JBRUEsU0FBUztvQkFDVCxNQUFNRSxrQkFBa0IvVCxTQUFTNEYsR0FBRyxDQUFDQyxDQUFBQTt3QkFDbkMsSUFBSWdOLGVBQWVoRCxRQUFRLENBQUNoSyxRQUFRcEIsTUFBTSxHQUFHOzRCQUMzQyxtQkFBbUI7NEJBQ25CLE9BQU87Z0NBQ0wsR0FBR29CLE9BQU87Z0NBQ1Y5QixpQkFBaUI0UDs0QkFDbkI7d0JBQ0Y7d0JBQ0EsZ0JBQWdCO3dCQUNoQixPQUFPOU47b0JBQ1Q7b0JBQ0E1RixZQUFZOFQ7b0JBRVo1TyxRQUFRQyxHQUFHLENBQUMsMENBQTBDO3dCQUNwRHlOO3dCQUNBYzt3QkFDQUssbUNBQW1DOU0sTUFBTUMsSUFBSSxDQUFDbkIsNEJBQTRCMkUsT0FBTzt3QkFDakZzSixrQ0FBa0M7b0JBQ3BDO29CQUVBLGFBQWE7b0JBQ2JDLFdBQVc7d0JBQ1QvTyxRQUFRQyxHQUFHLENBQUMsbUNBQW1DOEIsTUFBTUMsSUFBSSxDQUFDbkIsNEJBQTRCMkUsT0FBTztvQkFDL0YsR0FBRztnQkFDTDtnQkFFQSxZQUFZO2dCQUNabEosOEJBQThCO2dCQUM5QitCLHFCQUFxQjtnQkFDckJJLHNCQUFzQixFQUFFLEdBQUcsVUFBVTtnQkFDckNFLGVBQWUsUUFBUSxTQUFTO2dCQUVoQyxTQUFTO2dCQUNULE1BQU1nRyxzQkFBc0IsV0FBVztZQUN2Qyx1QkFBdUI7WUFFekIsRUFBRSxPQUFPMUosT0FBTztnQkFDZCxJQUFJc1EsYUFBYTtvQkFDZkEsWUFBWUMsS0FBSztnQkFDbkI7Z0JBQ0EsTUFBTXZRO1lBQ1I7UUFFRixFQUFFLE9BQU9BLE9BQU87WUFDZCtFLFFBQVEvRSxLQUFLLENBQUMsV0FBV0E7WUFDekIsTUFBTSxFQUFFOEwsZUFBZSxFQUFFLEdBQUcsTUFBTSw4TkFBTztZQUN6QyxNQUFNQyxlQUFlRDtZQUNyQkMsYUFBYS9MLEtBQUssQ0FBQztRQUNyQjtJQUNGO0lBRUEsYUFBYTtJQUNiLE1BQU0rVCwyQkFBMkIsQ0FBQ0MsR0FBcUJ4SztRQUNyRHdLLEVBQUVDLGVBQWU7UUFDakJsUCxRQUFRQyxHQUFHLENBQUMsYUFBYXdFO0lBQzNCO0lBRUEsYUFBYTtJQUNiLE1BQU0wSyxxQkFBcUIsT0FBT3JJO1lBV2JBO1FBVm5CLElBQUksQ0FBQ25NLGlCQUFpQjtZQUNwQixNQUFNLEVBQUVvTSxlQUFlLEVBQUUsR0FBRyxNQUFNLDhOQUFPO1lBQ3pDLE1BQU1DLGVBQWVEO1lBQ3JCQyxhQUFhL0wsS0FBSyxDQUFDO1lBQ25CO1FBQ0Y7UUFJQSxpQ0FBaUM7UUFDakMsTUFBTW1VLGNBQWF0SSwwQkFBQUEsT0FBT3VJLGVBQWUsY0FBdEJ2SSw4Q0FBQUEsdUJBQXdCLENBQUNuTSxnQkFBZ0IyRSxNQUFNLENBQUM7UUFFbkUsSUFBSTtZQUNGLE1BQU0sRUFBRXlILGVBQWUsRUFBRSxHQUFHLE1BQU0sOE5BQU87WUFDekMsTUFBTUMsZUFBZUQ7WUFDckIsTUFBTXdFLGNBQWN2RSxhQUFhak0sT0FBTyxDQUFDO1lBRXpDLE1BQU0sRUFBRXVVLFNBQVMsRUFBRSxHQUFHLE1BQU0sOEpBQU87WUFDbkMsTUFBTUEsVUFBVUMsZ0JBQWdCLENBQUM7Z0JBQy9CQyxlQUFlN1UsZ0JBQWdCMkUsTUFBTTtnQkFDckM2RCxpQkFBaUIyRCxPQUFPM0QsZUFBZTtnQkFDdkNpTSxZQUFZQTtnQkFDWkssUUFBUTNJLE9BQU8ySSxNQUFNO1lBQ3ZCO1lBRUEsSUFBSWxFLGFBQWE7Z0JBQ2ZBLFlBQVlDLEtBQUs7WUFDbkI7WUFFQXhFLGFBQWFLLE9BQU8sQ0FBQztZQUNyQnZLLDhCQUE4QjtZQUM5QixTQUFTO1lBQ1QsTUFBTTRTO1FBQ1IsRUFBRSxPQUFPelUsT0FBWTtnQkFJQUEsc0JBQUFBO1lBSG5CLE1BQU0sRUFBRThMLGVBQWUsRUFBRSxHQUFHLE1BQU0sOE5BQU87WUFDekMsTUFBTUMsZUFBZUQ7WUFDckIsYUFBYTtZQUNiQyxhQUFhL0wsS0FBSyxDQUFDQSxFQUFBQSxrQkFBQUEsTUFBTW1FLFFBQVEsY0FBZG5FLHVDQUFBQSx1QkFBQUEsZ0JBQWdCc0UsSUFBSSxjQUFwQnRFLDJDQUFBQSxxQkFBc0IySSxPQUFPLEtBQUk7UUFDdEQ7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNOEwscUJBQXFCO1FBQ3pCLE1BQU1yTztJQUNSO0lBRUEsNkJBQTZCO0lBQzdCLE1BQU1zTyw2QkFBNkIsT0FBTzdQLFlBQW9CRixjQUFzQkc7UUFDbEYsSUFBSTtZQUNGQyxRQUFRQyxHQUFHLENBQUMsYUFBYTtnQkFBRVg7Z0JBQVE4QjtnQkFBUXRCO2dCQUFZRjtnQkFBY0c7WUFBVztZQUVoRixnQ0FBZ0M7WUFDaEMsTUFBTSxFQUFFeU4sZUFBZSxFQUFFLEdBQUcsTUFBTSwwSkFBTztZQUN6QyxNQUFNcE8sV0FBVyxNQUFNb08sZ0JBQWdCO2dCQUNyQ2xPLFFBQVFBO2dCQUNSOEIsUUFBUUEsVUFBVTtnQkFDbEJ0QixZQUFZQTtZQUNkO1lBRUEsSUFBSVYsU0FBU0csSUFBSSxDQUFDQyxJQUFJLEtBQUssS0FBSztnQkFDOUIsZ0JBQWdCO2dCQUNoQixNQUFNVyxjQUFjO29CQUNsQkwsWUFBWUE7b0JBQ1pGLGNBQWNBO29CQUNkRyxZQUFZQTtnQkFDZDtnQkFDQWYseUJBQXlCbUI7Z0JBQ3pCSCxRQUFRQyxHQUFHLENBQUMsZUFBZUU7WUFDN0IsT0FBTztnQkFDTEgsUUFBUS9FLEtBQUssQ0FBQyxlQUFlbUUsU0FBU0csSUFBSTtZQUM1QztRQUNGLEVBQUUsT0FBT3RFLE9BQU87WUFDZCtFLFFBQVEvRSxLQUFLLENBQUMsZUFBZUE7UUFDL0I7SUFDRjtJQUVBLGNBQWM7SUFDZCxNQUFNMlUsOEJBQThCLENBQUMzRSxZQUFzQnBMO1FBQ3pELE1BQU0yTyxlQUFlO1lBQ25CMU8sWUFBWUQsYUFBYUMsVUFBVTtZQUNuQ0YsY0FBY0MsYUFBYUQsWUFBWTtZQUN2Q0csWUFBWUYsYUFBYUUsVUFBVSxJQUFJO1FBQ3pDO1FBRUEsY0FBYztRQUNkZSwrQkFBK0JpTCxDQUFBQTtZQUM3QixNQUFNMkMsU0FBUyxJQUFJM04sSUFBSWdMO1lBQ3ZCZCxXQUFXM0YsT0FBTyxDQUFDaEMsQ0FBQUE7Z0JBQ2pCb0wsT0FBT0MsR0FBRyxDQUFDckwsV0FBV2tMO1lBQ3hCO1lBQ0EsT0FBT0U7UUFDVDtRQUVBLFNBQVM7UUFDVDVULFlBQVkwRixDQUFBQSxlQUNWQSxhQUFhQyxHQUFHLENBQUNDLENBQUFBO2dCQUNmLElBQUl1SyxXQUFXUCxRQUFRLENBQUNoSyxRQUFRcEIsTUFBTSxHQUFHO29CQUN2QyxPQUFPO3dCQUNMLEdBQUdvQixPQUFPO3dCQUNWOUIsaUJBQWlCNFA7b0JBQ25CO2dCQUNGO2dCQUNBLE9BQU85TjtZQUNUO0lBR0Ysb0JBQW9CO0lBQ3BCLDJHQUEyRztJQUM3RztJQUVBLGNBQWM7SUFDZCxNQUFNcUssMkJBQTJCLENBQUN6SDtRQUNoQyxJQUFJQSxXQUFXO1lBQ2IsU0FBUztZQUNUN0Usc0JBQXNCO2dCQUFDNkU7YUFBVTtZQUNqQ3RHLG9DQUFvQztRQUN0QyxPQUFPO1lBQ0wsU0FBUztZQUNULElBQUl3QixtQkFBbUI0QixNQUFNLEtBQUssR0FBRztnQkFDbkMsOE5BQU8sQ0FBNkNvQyxJQUFJLENBQUM7d0JBQUMsRUFBRXVFLGVBQWUsRUFBRTtvQkFDM0VBLGtCQUFrQjRCLE9BQU8sQ0FBQztnQkFDNUI7Z0JBQ0E7WUFDRjtZQUNBM0wsb0NBQW9DO1FBQ3RDO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTTZTLGtDQUFrQztRQUN0Q0g7UUFDQTFTLG9DQUFvQztJQUN0QztJQUVBLGlCQUFpQjtJQUNqQixNQUFNOFMsbUNBQW1DLENBQUM3RTtRQUN4Q2pPLG9DQUFvQztRQUNwQyxVQUFVO1FBQ1Z5QixzQkFBc0J3TTtRQUN0QixXQUFXO1FBQ1huTyw4QkFBOEI7SUFDaEM7SUFFQSxhQUFhO0lBQ2IsTUFBTWlULDBCQUEwQixPQUFPako7UUFDckMsSUFBSXRJLG1CQUFtQjRCLE1BQU0sS0FBSyxHQUFHO1lBQ25DLE1BQU0sRUFBRTJHLGVBQWUsRUFBRSxHQUFHLE1BQU0sOE5BQU87WUFDekMsTUFBTUMsZUFBZUQ7WUFDckJDLGFBQWEvTCxLQUFLLENBQUM7WUFDbkI7UUFDRjtRQUlBLElBQUksQ0FBQzZMLE9BQU91SSxlQUFlLEVBQUU7WUFDM0IsTUFBTSxFQUFFdEksZUFBZSxFQUFFLEdBQUcsTUFBTSw4TkFBTztZQUN6QyxNQUFNQyxlQUFlRDtZQUNyQkMsYUFBYS9MLEtBQUssQ0FBQztZQUNuQjtRQUNGO1FBRUEsSUFBSTtZQUNGLE1BQU0sRUFBRThMLGVBQWUsRUFBRSxHQUFHLE1BQU0sOE5BQU87WUFDekMsTUFBTUMsZUFBZUQ7WUFDckIsTUFBTXdFLGNBQWN2RSxhQUFhak0sT0FBTyxDQUFDO1lBRXpDLGVBQWU7WUFDZixNQUFNLEVBQUV1VSxTQUFTLEVBQUUsR0FBRyxNQUFNLDhKQUFPO1lBQ25DLE1BQU1sUSxXQUFXLE1BQU1rUSxVQUFVVSxxQkFBcUIsQ0FBQztnQkFDckQ3TSxpQkFBaUIyRCxPQUFPM0QsZUFBZTtnQkFDdkNrTSxpQkFBaUJ2SSxPQUFPdUksZUFBZTtnQkFDdkNJLFFBQVEzSSxPQUFPMkksTUFBTTtZQUN2QjtZQUNBelAsUUFBUUMsR0FBRyxDQUFDLGFBQWFiO1lBRXpCLElBQUltTSxhQUFhO2dCQUNmQSxZQUFZQyxLQUFLO1lBQ25CO1lBRUEsSUFBSXBNLFNBQVNHLElBQUksQ0FBQ0MsSUFBSSxLQUFLLEtBQUs7Z0JBQzlCLHdCQUF3QjtnQkFDeEIsTUFBTWtELFVBQVV0RCxTQUFTRyxJQUFJLENBQUNBLElBQUk7Z0JBQ2xDLGtDQUFrQztnQkFDbEMsc0JBQXNCO2dCQUN0QnlILGFBQWFLLE9BQU8sQ0FBQyxPQUF1QixPQUFoQjNFLFFBQVEyRSxPQUFPLEVBQUM7Z0JBRTVDdkssOEJBQThCO2dCQUM5QixTQUFTO2dCQUNULE1BQU00UyxzQkFBc0IsYUFBYTtnQkFDekNqUixzQkFBc0IsRUFBRSxHQUFHLE9BQU87Z0JBQ2xDRSxlQUFlO1lBQ2pCLE9BQU87Z0JBQ0wsaUJBQWlCO2dCQUNqQnFJLGFBQWEvTCxLQUFLLENBQUNtRSxTQUFTRyxJQUFJLENBQUNxRSxPQUFPLElBQUk7WUFDOUM7UUFDRixFQUFFLE9BQU8zSSxPQUFZO2dCQUtBQSxzQkFBQUE7WUFKbkIrRSxRQUFRL0UsS0FBSyxDQUFDLGFBQWFBO1lBQzNCLE1BQU0sRUFBRThMLGVBQWUsRUFBRSxHQUFHLE1BQU0sOE5BQU87WUFDekMsTUFBTUMsZUFBZUQ7WUFDckIsWUFBWTtZQUNaQyxhQUFhL0wsS0FBSyxDQUFDQSxFQUFBQSxrQkFBQUEsTUFBTW1FLFFBQVEsY0FBZG5FLHVDQUFBQSx1QkFBQUEsZ0JBQWdCc0UsSUFBSSxjQUFwQnRFLDJDQUFBQSxxQkFBc0IySSxPQUFPLEtBQUk7UUFDdEQ7SUFDRjtJQUVBLElBQUksQ0FBQ3JKLGdCQUFnQjtRQUNuQixxQkFDRSw4REFBQ3dPO1lBQUk3QixXQUFVO3NCQUNiLDRFQUFDNkI7Z0JBQUk3QixXQUFVOzBCQUNiLDRFQUFDNkI7b0JBQUk3QixXQUFVOztzQ0FDYiw4REFBQzhCO3NDQUFFOzs7Ozs7c0NBQ0gsOERBQUNpSDs0QkFBT0MsU0FBUzFWOzRCQUFRME0sV0FBVTtzQ0FBYzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU8zRDtJQUVBLFdBQVc7SUFDWCxNQUFNaUosaUJBQWlCLENBQUNDO1FBQ3RCLE1BQU1DLFNBQVM7WUFDYjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUNELGdCQUFnQjtRQUNoQixNQUFNQyxZQUFZck8sS0FBS3NPLEdBQUcsQ0FBQyxHQUFHSDtRQUM5QixPQUFPQyxNQUFNLENBQUNDLFlBQVlELE9BQU9qUSxNQUFNLENBQUM7SUFDMUM7SUFFQSxxQkFDRSw4REFBQzJJO1FBQUk3QixXQUFVOzswQkFFYiw4REFBQzZCO2dCQUFJN0IsV0FBVTswQkFDYiw0RUFBQytJO29CQUNDL0ksV0FBVTtvQkFDVmdKLFNBQVMxVjs7c0NBRVQsOERBQUNqQyx1TEFBU0E7NEJBQUM0TSxNQUFNOzs7Ozs7d0JBQU07Ozs7Ozs7Ozs7OzswQkFLM0IsOERBQUM0RDtnQkFBSTdCLFdBQVU7O2tDQUViLDhEQUFDNkI7d0JBQUk3QixXQUFVOzswQ0FFYiw4REFBQzZCO2dDQUFJN0IsV0FBVTswQ0FDYiw0RUFBQzZCO29DQUFJN0IsV0FBVTtvQ0FBcUJzSixLQUFLN1U7O3NEQUN2Qyw4REFBQ3NVOzRDQUNDL0ksV0FBVyxnQkFBdUQsT0FBdkM1TCx5QkFBeUIsV0FBVzs0Q0FDL0Q0VSxTQUFTLElBQU0zVSwwQkFBMEIsQ0FBQ0Q7c0RBRTFDLDRFQUFDNUMsdUxBQVFBO2dEQUFDeU0sTUFBTTs7Ozs7Ozs7Ozs7d0NBR2pCN0osd0NBQ0MsOERBQUN5Tjs0Q0FBSTdCLFdBQVU7c0RBQ2IsNEVBQUM2QjtnREFBSTdCLFdBQVU7O2tFQUNiLDhEQUFDNkI7d0RBQ0M3QixXQUFVO3dEQUNWZ0osU0FBUyxJQUFNOUYsNEJBQTRCOzswRUFFM0MsOERBQUNyQjtnRUFBSTdCLFdBQVU7MEVBQ2IsNEVBQUN0Tyx1TEFBSUE7b0VBQUN1TSxNQUFNO29FQUFJc0wsT0FBTzt3RUFBRUMsT0FBTztvRUFBVTs7Ozs7Ozs7Ozs7MEVBRTVDLDhEQUFDOUc7MEVBQUs7Ozs7Ozs7Ozs7OztrRUFHUiw4REFBQ2I7d0RBQ0M3QixXQUFVO3dEQUNWZ0osU0FBUyxJQUFNOUYsNEJBQTRCOzswRUFFM0MsOERBQUNyQjtnRUFBSTdCLFdBQVU7MEVBQ2IsNEVBQUNyTyx1TEFBUUE7b0VBQUNzTSxNQUFNO29FQUFJc0wsT0FBTzt3RUFBRUMsT0FBTztvRUFBVTs7Ozs7Ozs7Ozs7MEVBRWhELDhEQUFDOUc7MEVBQUs7Ozs7Ozs7Ozs7OztrRUFHUiw4REFBQ2I7d0RBQ0M3QixXQUFVO3dEQUNWZ0osU0FBUyxJQUFNOUYsNEJBQTRCOzswRUFFM0MsOERBQUNyQjtnRUFBSTdCLFdBQVU7MEVBQ2IsNEVBQUNwTyx1TEFBTUE7b0VBQUNxTSxNQUFNO29FQUFJc0wsT0FBTzt3RUFBRUMsT0FBTztvRUFBVTs7Ozs7Ozs7Ozs7MEVBRTlDLDhEQUFDOUc7MEVBQUs7Ozs7Ozs7Ozs7OztrRUFHUiw4REFBQ2I7d0RBQ0M3QixXQUFVO3dEQUNWZ0osU0FBUyxJQUFNOUYsNEJBQTRCOzswRUFFM0MsOERBQUNyQjtnRUFBSTdCLFdBQVU7MEVBQ2IsNEVBQUNuTyx1TEFBUUE7b0VBQUNvTSxNQUFNO29FQUFJc0wsT0FBTzt3RUFBRUMsT0FBTztvRUFBVTs7Ozs7Ozs7Ozs7MEVBRWhELDhEQUFDOUc7MEVBQUs7Ozs7Ozs7Ozs7OztrRUFHUiw4REFBQ2I7d0RBQ0M3QixXQUFVO3dEQUNWZ0osU0FBUyxJQUFNOUYsNEJBQTRCOzswRUFFM0MsOERBQUNyQjtnRUFBSTdCLFdBQVU7MEVBQ2IsNEVBQUNsTyx1TEFBS0E7b0VBQUNtTSxNQUFNO29FQUFJc0wsT0FBTzt3RUFBRUMsT0FBTztvRUFBVTs7Ozs7Ozs7Ozs7MEVBRTdDLDhEQUFDOUc7MEVBQUs7Ozs7Ozs7Ozs7OztrRUFHUiw4REFBQ2I7d0RBQ0M3QixXQUFVO3dEQUNWZ0osU0FBUyxJQUFNOUYsNEJBQTRCOzswRUFFM0MsOERBQUNyQjtnRUFBSTdCLFdBQVU7MEVBQ2IsNEVBQUNqTyx1TEFBSUE7b0VBQUNrTSxNQUFNO29FQUFJc0wsT0FBTzt3RUFBRUMsT0FBTztvRUFBVTs7Ozs7Ozs7Ozs7MEVBRTVDLDhEQUFDOUc7MEVBQUs7Ozs7Ozs7Ozs7OztrRUFHUiw4REFBQ2I7d0RBQ0M3QixXQUFVO3dEQUNWZ0osU0FBUyxJQUFNOUYsNEJBQTRCOzswRUFFM0MsOERBQUNyQjtnRUFBSTdCLFdBQVU7MEVBQ2IsNEVBQUNoTyx1TEFBSUE7b0VBQUNpTSxNQUFNO29FQUFJc0wsT0FBTzt3RUFBRUMsT0FBTztvRUFBVTs7Ozs7Ozs7Ozs7MEVBRTVDLDhEQUFDOUc7MEVBQUs7Ozs7Ozs7Ozs7OztrRUFHUiw4REFBQ2I7d0RBQ0M3QixXQUFVO3dEQUNWZ0osU0FBUyxJQUFNOUYsNEJBQTRCOzswRUFFM0MsOERBQUNyQjtnRUFBSTdCLFdBQVU7MEVBQ2IsNEVBQUMvTix1TEFBTUE7b0VBQUNnTSxNQUFNO29FQUFJc0wsT0FBTzt3RUFBRUMsT0FBTztvRUFBVTs7Ozs7Ozs7Ozs7MEVBRTlDLDhEQUFDOUc7MEVBQUs7Ozs7Ozs7Ozs7OztrRUFHUiw4REFBQ2I7d0RBQUk3QixXQUFVOzs7Ozs7a0VBRWYsOERBQUM2Qjt3REFDQzdCLFdBQVU7d0RBQ1ZnSixTQUFTLElBQU05Riw0QkFBNEI7OzBFQUUzQyw4REFBQ3JCO2dFQUFJN0IsV0FBVTswRUFDYiw0RUFBQ3ZPLHVMQUFNQTtvRUFBQ3dNLE1BQU07Ozs7Ozs7Ozs7OzBFQUVoQiw4REFBQ3lFOzBFQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVNsQiw4REFBQ2I7Z0NBQUk3QixXQUFVOzBDQUNiLDRFQUFDNkI7b0NBQUk3QixXQUFVOztzREFDYiw4REFBQzZCOzRDQUFJN0IsV0FBVTs7OERBQ2IsOERBQUM2QjtvREFBSTdCLFdBQVU7OERBQWtCOzs7Ozs7OERBR2pDLDhEQUFDNkI7b0RBQUk3QixXQUFVOztzRUFDYiw4REFBQ3lKOzREQUFHekosV0FBVTtzRUFBb0I3TSxVQUFVNk0sU0FBUzs7Ozs7O3NFQUNyRCw4REFBQzhCOzREQUFFOUIsV0FBVTtzRUFBdUIzTSxlQUFlcVcsVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUdqRSw4REFBQzdIOzRDQUFJN0IsV0FBVTtzREFBbUI7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU90Qyw4REFBQzZCO2dDQUFJN0IsV0FBVTswQ0FDYiw0RUFBQzZCO29DQUFJN0IsV0FBVTs4Q0FDYiw0RUFBQzFPLHVMQUFNQTt3Q0FBQzJNLE1BQU07d0NBQUkrQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzBDQUtoQyw4REFBQzZCO2dDQUFJN0IsV0FBVTs7a0RBQ2IsOERBQUM2Qjt3Q0FBSTdCLFdBQVU7OzBEQUNiLDhEQUFDeUo7MERBQUc7Ozs7OzswREFDSiw4REFBQzVIO2dEQUFJN0IsV0FBVTs7a0VBQ2IsOERBQUMrSTt3REFDQy9JLFdBQVU7d0RBQ1ZnSixTQUFTLElBQU03VSw0QkFBNEI7a0VBRTNDLDRFQUFDNUMsdUxBQUlBOzREQUFDME0sTUFBTTs7Ozs7Ozs7Ozs7a0VBRWQsOERBQUM4Szt3REFDQy9JLFdBQVcsa0JBQThDLE9BQTVCeEksY0FBYyxXQUFXO3dEQUN0RHdSLFNBQVM7NERBQ1BsUSxRQUFRQyxHQUFHLENBQUMsZ0JBQWdCO2dFQUMxQnZCO2dFQUNBRixvQkFBb0JBLG1CQUFtQjRCLE1BQU07Z0VBQzdDa0ssZUFBZXpQLFNBQVN1RixNQUFNOzREQUNoQzs0REFDQW1LO3dEQUNGO3dEQUNBM0IsT0FBT2xLLGNBQWMsU0FBUztrRUFFOUIsNEVBQUNrTDtzRUFBTWxMLGNBQWMsU0FBUzs7Ozs7Ozs7Ozs7b0RBSS9CRixtQkFBbUI0QixNQUFNLEdBQUcsbUJBQzNCLDhEQUFDMkk7d0RBQUk3QixXQUFVO3dEQUEwQnNKLEtBQUs1VTs7MEVBQzVDLDhEQUFDcVU7Z0VBQ0MvSSxXQUFVO2dFQUNWZ0osU0FBUyxJQUFNelUsOEJBQThCLENBQUNEO2dFQUM5Q29OLE9BQU07O2tGQUVOLDhEQUFDZ0I7OzRFQUFLOzRFQUFNcEwsbUJBQW1CNEIsTUFBTTs0RUFBQzs7Ozs7OztrRkFDdEMsOERBQUNoSCx1TEFBV0E7d0VBQUMrTCxNQUFNOzs7Ozs7Ozs7Ozs7NERBSXBCM0osNENBQ0MsOERBQUN1TjtnRUFBSTdCLFdBQVU7O2tGQUNiLDhEQUFDK0k7d0VBQ0MvSSxXQUFVO3dFQUNWZ0osU0FBUyxJQUFNckYsa0JBQWtCOzswRkFFakMsOERBQUMxUix1TEFBTUE7Z0ZBQUNnTSxNQUFNOzs7Ozs7MEZBQ2QsOERBQUN5RTswRkFBSzs7Ozs7Ozs7Ozs7O2tGQUVSLDhEQUFDcUc7d0VBQ0MvSSxXQUFVO3dFQUNWZ0osU0FBUyxJQUFNckYsa0JBQWtCOzswRkFFakMsOERBQUN4Uix1TEFBR0E7Z0ZBQUM4TCxNQUFNOzs7Ozs7MEZBQ1gsOERBQUN5RTswRkFBSzs7Ozs7Ozs7Ozs7O2tGQUVSLDhEQUFDcUc7d0VBQ0MvSSxXQUFVO3dFQUNWZ0osU0FBUyxJQUFNckYsa0JBQWtCOzswRkFFakMsOERBQUMzUix1TEFBSUE7Z0ZBQUNpTSxNQUFNOzs7Ozs7MEZBQ1osOERBQUN5RTswRkFBSzs7Ozs7Ozs7Ozs7O2tGQUVSLDhEQUFDcUc7d0VBQ0MvSSxXQUFVO3dFQUNWZ0osU0FBUyxJQUFNckYsa0JBQWtCOzswRkFFakMsOERBQUNsUyx1TEFBTUE7Z0ZBQUN3TSxNQUFNOzs7Ozs7MEZBQ2QsOERBQUN5RTswRkFBSzs7Ozs7Ozs7Ozs7O2tGQUVSLDhEQUFDcUc7d0VBQ0MvSSxXQUFVO3dFQUNWZ0osU0FBUyxJQUFNckYsa0JBQWtCOzswRkFFakMsOERBQUM5Uix1TEFBUUE7Z0ZBQUNvTSxNQUFNOzs7Ozs7MEZBQ2hCLDhEQUFDeUU7MEZBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFTcEIsOERBQUNiO3dDQUFJN0IsV0FBVTtrREFDYiw0RUFBQy9NLHFEQUFXQTs0Q0FDVlUsVUFBVUE7NENBQ1ZFLFNBQVNBOzRDQUNURSxPQUFPQTs0Q0FDUE4saUJBQWlCQTs0Q0FDakI2RCxvQkFBb0JBOzRDQUNwQkksaUJBQWlCRyx5QkFBeUJIOzRDQUMxQ29DLGVBQWVBOzRDQUNmNlAsZ0JBQWdCdks7NENBQ2hCd0ssaUJBQWlCckc7NENBQ2pCc0csU0FBUzFQOzRDQUNUMlAsMEJBQTBCekk7NENBQzFCMEksZ0JBQWdCLENBQUMzTjtnREFDZixNQUFNNUMsVUFBVTdGLFNBQVM2RSxJQUFJLENBQUNtQyxDQUFBQSxJQUFLQSxFQUFFdkMsTUFBTSxLQUFLZ0U7Z0RBQ2hELElBQUk1QyxTQUFTO29EQUNYdkYsbUJBQW1CdUY7b0RBQ25CNUQsOEJBQThCO2dEQUNoQzs0Q0FDRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT1IsOERBQUNpTTt3QkFBSTdCLFdBQVU7OzBDQUViLDhEQUFDNkI7Z0NBQUk3QixXQUFVOztrREFDYiw4REFBQzZCO3dDQUFJN0IsV0FBVTtrREFDYiw0RUFBQzZCOzRDQUNDN0IsV0FBVTs0Q0FDVnVKLE9BQU87Z0RBQ0xTLFlBQVl2VyxrQkFDVndWLGVBQWVsTyxLQUFLc08sR0FBRyxDQUFDLEdBQUcxVixTQUFTc1csU0FBUyxDQUFDdFAsQ0FBQUEsSUFBS0EsRUFBRXZDLE1BQU0sS0FBSzNFLGdCQUFnQjJFLE1BQU0sTUFDdEY2USxlQUFlOzRDQUNuQjtzREFFQ3hWLGtCQUFrQkEsRUFBQUEsNEJBQUFBLGdCQUFnQmlHLFFBQVEsY0FBeEJqRyxnREFBQUEsMEJBQTBCeVcsTUFBTSxDQUFDLE9BQU0sTUFBTTs7Ozs7Ozs7Ozs7a0RBR3BFLDhEQUFDckk7d0NBQUk3QixXQUFVOzswREFDYiw4REFBQzZCO2dEQUFJN0IsV0FBVTswREFDWnZNLGtCQUFrQkEsZ0JBQWdCaUcsUUFBUSxJQUFJLEtBQTZELE9BQXhEakcsZ0JBQWdCc0ksYUFBYSxJQUFJdEksZ0JBQWdCMkUsTUFBTSxJQUFLOzs7Ozs7MERBRWxILDhEQUFDeUo7Z0RBQUk3QixXQUFVOzBEQUNadk0sa0JBQWtCQSxnQkFBZ0JzSSxhQUFhLElBQUksUUFBUTs7Ozs7Ozs7Ozs7O2tEQUdoRSw4REFBQ2dOO3dDQUNDL0ksV0FBVTt3Q0FDVmdKLFNBQVMzSjt3Q0FDVDhLLFVBQVUsQ0FBQzFXO2tEQUVYLDRFQUFDaEMsdUxBQU1BOzRDQUFDd00sTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBS2xCLDhEQUFDNEQ7Z0NBQUk3QixXQUFVOztrREFFYiw4REFBQzZCO3dDQUFJN0IsV0FBVTs7MERBQ2IsOERBQUN5SjswREFBRzs7Ozs7OzBEQUNKLDhEQUFDNUg7Z0RBQUk3QixXQUFVOztrRUFDYiw4REFBQytJO3dEQUNDL0ksV0FBVTt3REFDVmdKLFNBQVMsSUFBTXhULDZCQUE2Qjs7MEVBRTVDLDhEQUFDcU07Z0VBQUk3QixXQUFVOzBFQUFnQjs7Ozs7OzBFQUMvQiw4REFBQzBDOzBFQUFLOzs7Ozs7Ozs7Ozs7a0VBRVIsOERBQUNxRzt3REFDQy9JLFdBQVU7d0RBQ1ZnSixTQUFTNUg7OzBFQUVULDhEQUFDUztnRUFBSTdCLFdBQVU7MEVBQWdCOzs7Ozs7MEVBQy9CLDhEQUFDMEM7MEVBQUs7Ozs7Ozs7Ozs7OztrRUFFUiw4REFBQ3FHO3dEQUNDL0ksV0FBVTt3REFDVmdKLFNBQVMsSUFBTXBULDhCQUE4Qjs7MEVBRTdDLDhEQUFDaU07Z0VBQUk3QixXQUFVOzBFQUFnQjs7Ozs7OzBFQUMvQiw4REFBQzBDOzBFQUFLOzs7Ozs7Ozs7Ozs7a0VBRVIsOERBQUNxRzt3REFDQy9JLFdBQVU7d0RBQ1ZnSixTQUFTLElBQU1sVCxvQ0FBb0M7OzBFQUVuRCw4REFBQytMO2dFQUFJN0IsV0FBVTswRUFBZ0I7Ozs7OzswRUFDL0IsOERBQUMwQzswRUFBSzs7Ozs7Ozs7Ozs7O2tFQUVSLDhEQUFDcUc7d0RBQ0MvSSxXQUFVO3dEQUNWZ0osU0FBUyxJQUFNdFQsK0JBQStCOzswRUFFOUMsOERBQUNtTTtnRUFBSTdCLFdBQVU7MEVBQWdCOzs7Ozs7MEVBQy9CLDhEQUFDMEM7MEVBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFNWiw4REFBQ2I7d0NBQUk3QixXQUFVOzswREFDYiw4REFBQ3lKOzBEQUFHOzs7Ozs7MERBQ0osOERBQUM1SDtnREFBSTdCLFdBQVU7MERBQXFCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBU3hDLDhEQUFDNkI7Z0NBQUk3QixXQUFVOztrREFDYiw4REFBQzZCO3dDQUFJN0IsV0FBVTs7MERBQ2IsOERBQUM2QjtnREFBSTdCLFdBQVU7O2tFQUNiLDhEQUFDMEM7a0VBQUs7Ozs7OztrRUFDTiw4REFBQ0E7OzREQUNFalAsQ0FBQUEsNEJBQUFBLHNDQUFBQSxnQkFBaUJ3SSxlQUFlLEtBQUk7NERBQUU7NERBQUV4SSxDQUFBQSw0QkFBQUEsc0NBQUFBLGdCQUFpQnVJLFdBQVcsS0FBSTs7Ozs7Ozs7Ozs7OzswREFHN0UsOERBQUM2RjtnREFBSTdCLFdBQVU7MERBQ2IsNEVBQUM2QjtvREFDQzdCLFdBQVU7b0RBQ1Z1SixPQUFPO3dEQUNMYSxPQUFPM1csQ0FBQUEsNEJBQUFBLHNDQUFBQSxnQkFBaUJ1SSxXQUFXLElBQy9CLEdBQW1HLE9BQWhHLE9BQVF2SSxnQkFBZ0J3SSxlQUFlLElBQUksS0FBS29PLE9BQU81VyxnQkFBZ0J1SSxXQUFXLElBQUksS0FBTSxLQUFJLE9BQ25HO29EQUNOOzs7Ozs7Ozs7Ozs7Ozs7OztrREFLTiw4REFBQzZGO3dDQUFJN0IsV0FBVTtrREFDYiw0RUFBQzZCOzRDQUNDN0IsV0FBVTs0Q0FDVmdKLFNBQVMsSUFBTXZWLG1CQUFtQjZOOzRDQUNsQ2lJLE9BQU87Z0RBQUVlLFFBQVE3VyxrQkFBa0IsWUFBWTs0Q0FBVTtzREFFekQsNEVBQUNvTztnREFBSTdCLFdBQVU7O2tFQUNiLDhEQUFDNkI7d0RBQUk3QixXQUFVOzswRUFDYiw4REFBQzZCO2dFQUFJN0IsV0FBVTswRUFBcUI7Ozs7OzswRUFHcEMsOERBQUM2QjtnRUFBSTdCLFdBQVU7O2tGQUNiLDhEQUFDdUs7d0VBQUd2SyxXQUFVO2tGQUNYLENBQUM7NEVBQ0EsSUFBSXZNLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCaUUsZUFBZSxFQUFFO2dGQUNwQyxPQUFPakUsZ0JBQWdCaUUsZUFBZSxDQUFDZ0IsWUFBWTs0RUFDckQsT0FBTyxJQUFJYix1QkFBdUI7Z0ZBQ2hDLE9BQU9BLHNCQUFzQmEsWUFBWTs0RUFDM0MsT0FBTyxJQUFJaEIsaUJBQWlCO2dGQUMxQixPQUFPQSxnQkFBZ0JnQixZQUFZOzRFQUNyQyxPQUFPLElBQUlqRixpQkFBaUI7Z0ZBQzFCLE9BQU87NEVBQ1QsT0FBTztnRkFDTCxPQUFPOzRFQUNUO3dFQUNGOzs7Ozs7a0ZBRUYsOERBQUNxTzt3RUFBRTlCLFdBQVU7a0ZBQ1Z2TSxrQkFDRUEsRUFBQUEsbUNBQUFBLGdCQUFnQmlFLGVBQWUsY0FBL0JqRSx1REFBQUEsaUNBQWlDb0YsVUFBVSxJQUFHLFNBQVMsVUFDeEQ7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFLUiw4REFBQ2dKO3dEQUFJN0IsV0FBVTs7MEVBQ2IsOERBQUM2QjtnRUFBSTdCLFdBQVU7MEVBQXNCOzs7Ozs7MEVBR3JDLDhEQUFDNkI7Z0VBQ0M3QixXQUFVO2dFQUNWZ0osU0FBUyxDQUFDakI7b0VBQ1JBLEVBQUVDLGVBQWU7b0VBQ2pCdlUsbUJBQW1CNk47Z0VBQ3JCO2dFQUNBSSxPQUFNOzBFQUNQOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVlmLDhEQUFDbFAsNkRBQWVBO2dCQUNkZ1ksU0FBU3RXO2dCQUNUdVcsVUFBVSxJQUFNdFcsNEJBQTRCO2dCQUM1QzJPLE1BQU15Qjs7Ozs7OzBCQUlSLDhEQUFDOVIsNEVBQWNBO2dCQUNiK1gsU0FBUzdWO2dCQUNUOFYsVUFBVSxJQUFNN1YsMkJBQTJCO2dCQUMzQ2tPLE1BQU1uRDtnQkFDTitLLGVBQWU7b0JBQ2IxSyxXQUFXN00sVUFBVTZNLFNBQVM7Z0JBQ2hDOzs7Ozs7MEJBSUYsOERBQUN0TixnRkFBa0JBO2dCQUNqQjhYLFNBQVMzVjtnQkFDVDRWLFVBQVUsSUFBTTNWLCtCQUErQjtnQkFDL0M2VixVQUFVdEs7Z0JBQ1Z0RCxTQUFTNUosVUFBVTZFLEVBQUU7Ozs7OzswQkFJdkIsOERBQUNsRiwyREFBa0JBO2dCQUNqQjBYLFNBQVN6VjtnQkFDVDBWLFVBQVU7b0JBQ1J6ViwrQkFBK0I7b0JBQy9CZ0IsbUJBQW1CO2dCQUNyQjtnQkFDQThNLE1BQU1sQztnQkFDTmdLLGlCQUFpQm5LO2dCQUNqQjFLLGlCQUFpQkE7Z0JBQ2pCOFUscUJBQXFCLENBQUMsQ0FBQzFYLFVBQVU0TixrQkFBa0I7Z0JBQ25EK0osbUJBQW1CN0o7Z0JBQ25CcE4sU0FBU29DOzs7Ozs7WUFJVmhCLDBDQUNDLDhEQUFDNE07Z0JBQUk3QixXQUFVOzBCQUNiLDRFQUFDNkI7b0JBQUk3QixXQUFVOztzQ0FDYiw4REFBQzZCOzRCQUFJN0IsV0FBVTs7OENBQ2IsOERBQUMrSztvQ0FBRy9LLFdBQVU7OENBQXdCOzs7Ozs7OENBQ3RDLDhEQUFDK0k7b0NBQ0MvSSxXQUFVO29DQUNWZ0osU0FBUyxJQUFNOVQsNEJBQTRCOzhDQUUzQyw0RUFBQ2lOO3dDQUFJNkksT0FBTTt3Q0FBNkJoTCxXQUFVO3dDQUFVb0MsTUFBSzt3Q0FBT0MsU0FBUTt3Q0FBWTRJLFFBQU87a0RBQ2pHLDRFQUFDM0k7NENBQUs0SSxlQUFjOzRDQUFRQyxnQkFBZTs0Q0FBUUMsYUFBYTs0Q0FBRzVJLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSTNFLDhEQUFDWDs0QkFBSTdCLFdBQVU7OzhDQUNiLDhEQUFDNkI7b0NBQUk3QixXQUFVOztzREFDYiw4REFBQzBDOzRDQUFLMUMsV0FBVTtzREFBd0MzSzs7Ozs7O3NEQUN4RCw4REFBQzBUOzRDQUNDL0ksV0FBVTs0Q0FDVmdKLFNBQVM7Z0RBQ1BxQyxVQUFVQyxTQUFTLENBQUNDLFNBQVMsQ0FBQ2xXLFlBQzNCaUcsSUFBSSxDQUFDLElBQU1nRSxNQUFNLFdBQ2pCL0QsS0FBSyxDQUFDLElBQU0rRCxNQUFNOzRDQUN2QjtzREFDRDs7Ozs7Ozs7Ozs7OzhDQUlILDhEQUFDdUM7b0NBQUk3QixXQUFVOztzREFDYiw4REFBQzhCO3NEQUFFOzs7Ozs7c0RBQ0gsOERBQUMwSjs0Q0FBR3hMLFdBQVU7OzhEQUNaLDhEQUFDZ0M7OERBQUc7Ozs7Ozs4REFDSiw4REFBQ0E7OERBQUc7Ozs7Ozs7Ozs7OztzREFFTiw4REFBQ0Y7NENBQUU5QixXQUFVO3NEQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUtuQyw4REFBQzZCOzRCQUFJN0IsV0FBVTtzQ0FDYiw0RUFBQytJO2dDQUNDL0ksV0FBVTtnQ0FDVmdKLFNBQVMsSUFBTTlULDRCQUE0QjswQ0FDNUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBV1QsOERBQUN2Qyw4RUFBZ0JBO2dCQUNmNlgsU0FBU2pWO2dCQUNUa1YsVUFBVSxJQUFNalYsNkJBQTZCO2dCQUM3Q3NOLE1BQU1rQztnQkFDTnJSLFVBQVVBO2dCQUNWOFgsa0JBQWtCblU7Z0JBQ2xCb1UscUJBQXFCblU7Z0JBQ3JCcEIsVUFBVUE7Z0JBQ1ZFLFdBQVdBO2dCQUNYc08sY0FBY0E7Z0JBQ2RDLGtCQUFrQkE7Z0JBQ2xCK0csa0JBQWtCcFY7Z0JBQ2xCRSxtQkFBbUJBO2dCQUNuQkMsb0JBQW9CQTtnQkFDcEJDLHVCQUF1QkE7Ozs7OzswQkFJekIsOERBQUMvRCxnRkFBa0JBO2dCQUNqQjRYLFNBQVMvVTtnQkFDVGdWLFVBQVUsSUFBTS9VLCtCQUErQjtnQkFDL0NvTixNQUFNc0Q7Z0JBQ053RixTQUFTO2dCQUNUQyxPQUFPOzs7Ozs7MEJBSVQsOERBQUNoWixpRUFBaUJBO2dCQUNoQjJYLFNBQVNyVjtnQkFDVHNWLFVBQVU7b0JBQ1JyViw4QkFBOEI7b0JBQzlCK0IscUJBQXFCO29CQUNyQkksc0JBQXNCLEVBQUU7Z0JBQzFCO2dCQUNBdVUsbUJBQW1CNVUsc0JBQXNCO2dCQUN6Q04sa0JBQWtCQTtnQkFDbEJMLFdBQVdBO2dCQUNYTyxzQkFBc0JBO2dCQUN0QkUsaUJBQWlCQTtnQkFDakIrVSxrQkFBa0IxRjtnQkFDbEIyRixzQkFBc0JsRTtnQkFDdEIxUCxRQUFRQTtnQkFDUjZULG9CQUFvQmpQO2dCQUNwQnJKLFVBQVVBO2dCQUNWMkQsb0JBQW9CQTtnQkFDcEJGLFdBQVdBO2dCQUNYOFUsV0FBVztvQkFDVDFEO29CQUNBL0s7Z0JBQ0Y7Z0JBQ0EwTyx5QkFBeUJ6RDs7Ozs7OzBCQUkzQiw4REFBQzNWLGlFQUFpQkE7Z0JBQ2hCeVgsU0FBUzdVO2dCQUNUOFUsVUFBVTtvQkFDUjdVLDhCQUE4QjtvQkFDOUIzQixtQkFBbUI7Z0JBQ3JCO2dCQUNBLHFDQUFxQztnQkFDckM2TyxNQUFNclAsa0JBQWtCd1UscUJBQXFCWTtnQkFDN0N4TSxhQUFhNUksa0JBQWtCLEdBQTRCLE9BQXpCQSxnQkFBZ0JpRyxRQUFRLElBQUssT0FBaUMsT0FBMUJwQyxtQkFBbUI0QixNQUFNLEVBQUM7Z0JBQ2hHa0QsV0FBVzNJLENBQUFBLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCdUUsRUFBRSxLQUFJO2dCQUNsQ0ksUUFBUTNFLENBQUFBLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCMkUsTUFBTSxLQUFJO2dCQUNuQ29CLFNBQVMvRjtnQkFDVG1ZLFNBQVMsQ0FBQ25ZO2dCQUNWZ1ksa0JBQWtCblU7Z0JBQ2xCM0QsVUFBVUE7Z0JBQ1Z1WSxXQUFXO2dCQUNULDBCQUEwQjtnQkFDNUI7Z0JBQ0ExRCxvQkFBb0JBO2dCQUNwQjRELGlCQUFpQixDQUFDckk7b0JBQ2hCakwsUUFBUUMsR0FBRyxDQUFDLFdBQVdnTDtvQkFDdkJqTyxvQ0FBb0M7Z0JBQ3RDOzs7Ozs7MEJBSUYsOERBQUM5Qyw4RUFBdUJBO2dCQUN0QnFaLE1BQU14VztnQkFDTnlCLG9CQUFvQkE7Z0JBQ3BCM0QsVUFBVUE7Z0JBQ1YyWSxTQUFTLElBQU14VyxvQ0FBb0M7Z0JBQ25Eb1csV0FBV3ZEO2dCQUNYNEQsb0JBQW9CM0Q7Ozs7Ozs7Ozs7OztBQUk1QjtHQW5zRU0xVjs7UUFpRTZFWCxrRUFBV0E7UUFxRTdFRCxxREFBV0E7UUFDWEEscURBQVdBOzs7S0F2SXRCWTtBQXFzRU4sK0RBQWVBLFdBQVdBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL3dvcmtiZW5jaC9jb21wb25lbnRzL0NsYXNzRGV0YWlsLnRzeD83YzIxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IEFycm93TGVmdCwgU2VhcmNoLCBQbHVzLCBTZXR0aW5ncywgVHJhc2gyLCBFZGl0LCBVc2VyUGx1cywgVXBsb2FkLCBEb3dubG9hZCwgVXNlcnMsIExpbmssIEdpZnQsIEJsb2NrcywgQ2hldnJvbkRvd24sIFphcCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XHJcbmltcG9ydCB7IGNsYXNzQXBpIH0gZnJvbSAnLi4vLi4vLi4vbGliL2FwaS9jbGFzcyc7XHJcbmltcG9ydCB7IHN0dWRlbnRBcGkgfSBmcm9tICcuLi8uLi8uLi9saWIvYXBpL3N0dWRlbnQnO1xyXG5pbXBvcnQgeyB1c2VTZWxlY3RvciB9IGZyb20gJ3JlYWN0LXJlZHV4JztcclxuaW1wb3J0IHsgUm9vdFN0YXRlIH0gZnJvbSAnLi4vLi4vLi4vbGliL3N0b3JlJztcclxuaW1wb3J0IHsgdXNlVGVtcGxhdGUgfSBmcm9tICcuLi9jb250ZXh0cy9UZW1wbGF0ZUNvbnRleHQnO1xyXG5pbXBvcnQgeyBBZGRTdHVkZW50TW9kYWwgfSBmcm9tICcuL0FkZFN0dWRlbnRNb2RhbCc7XHJcbmltcG9ydCB7XHJcbiAgRWRpdENsYXNzTW9kYWwsXHJcbiAgSW1wb3J0U3R1ZGVudE1vZGFsLFxyXG4gIFB1Ymxpc2hUYXNrTW9kYWwsXHJcbiAgUmVzZXRQYXNzd29yZE1vZGFsXHJcbn0gZnJvbSAnLi4vLi4vdGVhY2hlci1zcGFjZS9jb21wb25lbnRzL21vZGFscyc7XHJcbmltcG9ydCB7IEFzc2lnbkJsb2Nrc01vZGFsIH0gZnJvbSAnLi9Bc3NpZ25CbG9ja3NNb2RhbCc7XHJcbmltcG9ydCBUcmFuc2ZlckNsYXNzTW9kYWwgZnJvbSAnLi9UcmFuc2ZlckNsYXNzTW9kYWwnO1xyXG5pbXBvcnQgeyB1cGRhdGVUZWFjaGVyQ2xhc3NDYWNoZSwgY2xlYXJUZWFjaGVyQ2xhc3NDYWNoZSB9IGZyb20gJy4vTGVmdFNpZGViYXInO1xyXG5pbXBvcnQgeyBBc3NpZ25Qb2ludHNNb2RhbCB9IGZyb20gJy4vQXNzaWduUG9pbnRzTW9kYWwnO1xyXG5pbXBvcnQgeyBCYXRjaFVzZUtleVBhY2thZ2VNb2RhbCB9IGZyb20gJy4vQmF0Y2hVc2VLZXlQYWNrYWdlTW9kYWwnO1xyXG5pbXBvcnQgU3R1ZGVudExpc3QgZnJvbSAnLi9TdHVkZW50TGlzdCc7XHJcbmltcG9ydCB7IENsYXNzSW5mbywgU3R1ZGVudCwgVXNlclJvbGUsIENsYXNzRGV0YWlsUHJvcHMgfSBmcm9tICcuL3R5cGVzJztcclxuaW1wb3J0ICcuL0NsYXNzRGV0YWlsLmNzcyc7XHJcblxyXG4vLyDnsbvlnovlrprkuYnlt7Lnp7voh7MgLi90eXBlcy9pbmRleC50c1xyXG5cclxuY29uc3QgQ2xhc3NEZXRhaWw6IFJlYWN0LkZDPENsYXNzRGV0YWlsUHJvcHM+ID0gKHtcclxuICBjbGFzc0luZm86IGluaXRpYWxDbGFzc0luZm8sXHJcbiAgc2VsZWN0ZWRTY2hvb2wsXHJcbiAgb25CYWNrLFxyXG4gIG9uQ2xhc3NJbmZvVXBkYXRlLFxyXG4gIG9uQ2xhc3NEZWxldGVkXHJcbn0pID0+IHtcclxuICAvLyDliJvlu7rlhoXpg6jnirbmgIHmnaXnrqHnkIbnj63nuqfkv6Hmga/vvIzov5nmoLflj6/ku6XlnKjnvJbovpHlkI7mm7TmlrDmmL7npLpcclxuICBjb25zdCBbY2xhc3NJbmZvLCBzZXRDbGFzc0luZm9dID0gdXNlU3RhdGU8Q2xhc3NJbmZvPihpbml0aWFsQ2xhc3NJbmZvKTtcclxuICBjb25zdCBbc3R1ZGVudHMsIHNldFN0dWRlbnRzXSA9IHVzZVN0YXRlPFN0dWRlbnRbXT4oW10pO1xyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW3NlbGVjdGVkU3R1ZGVudCwgc2V0U2VsZWN0ZWRTdHVkZW50XSA9IHVzZVN0YXRlPFN0dWRlbnQgfCBudWxsPihudWxsKTtcclxuXHJcbiAgLy8g5ZCM5q2l5aSW6YOo5Lyg5YWl55qEY2xhc3NJbmZv5Y+Y5YyWXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIHNldENsYXNzSW5mbyhpbml0aWFsQ2xhc3NJbmZvKTtcclxuICB9LCBbaW5pdGlhbENsYXNzSW5mb10pO1xyXG4gIGNvbnN0IFtpc0FkZFN0dWRlbnRNb2RhbFZpc2libGUsIHNldElzQWRkU3R1ZGVudE1vZGFsVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzU2V0dGluZ3NEcm9wZG93bk9wZW4sIHNldElzU2V0dGluZ3NEcm9wZG93bk9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtpc0JhdGNoQWN0aW9uc0Ryb3Bkb3duT3Blbiwgc2V0SXNCYXRjaEFjdGlvbnNEcm9wZG93bk9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IGRyb3Bkb3duUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcclxuICBjb25zdCBzZXR0aW5nc0Ryb3Bkb3duUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcclxuICBjb25zdCBiYXRjaEFjdGlvbnNEcm9wZG93blJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XHJcblxyXG4gIC8vIOaooeaAgeahhueKtuaAgVxyXG4gIGNvbnN0IFtpc0VkaXRDbGFzc01vZGFsVmlzaWJsZSwgc2V0SXNFZGl0Q2xhc3NNb2RhbFZpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtpc0ltcG9ydFN0dWRlbnRNb2RhbFZpc2libGUsIHNldElzSW1wb3J0U3R1ZGVudE1vZGFsVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzVHJhbnNmZXJDbGFzc01vZGFsVmlzaWJsZSwgc2V0SXNUcmFuc2ZlckNsYXNzTW9kYWxWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNJbnZpdGVDb2RlTW9kYWxWaXNpYmxlLCBzZXRJc0ludml0ZUNvZGVNb2RhbFZpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtpc0Fzc2lnbkJsb2Nrc01vZGFsVmlzaWJsZSwgc2V0SXNBc3NpZ25CbG9ja3NNb2RhbFZpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtpbnZpdGVDb2RlLCBzZXRJbnZpdGVDb2RlXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xyXG5cclxuICAvLyDmlrDlop7lip/og73mqKHmgIHmoYbnirbmgIFcclxuICBjb25zdCBbaXNQdWJsaXNoVGFza01vZGFsVmlzaWJsZSwgc2V0SXNQdWJsaXNoVGFza01vZGFsVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzUmVzZXRQYXNzd29yZE1vZGFsVmlzaWJsZSwgc2V0SXNSZXNldFBhc3N3b3JkTW9kYWxWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNBc3NpZ25Qb2ludHNNb2RhbFZpc2libGUsIHNldElzQXNzaWduUG9pbnRzTW9kYWxWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNCYXRjaFVzZUtleVBhY2thZ2VNb2RhbFZpc2libGUsIHNldElzQmF0Y2hVc2VLZXlQYWNrYWdlTW9kYWxWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICAvLyBjb25zdCBbaXNSZWRlZW1LZXlNb2RhbFZpc2libGUsIHNldElzUmVkZWVtS2V5TW9kYWxWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTsgLy8g5pyq5L2/55So77yM5bey56e76ZmkXHJcblxyXG4gIC8vIOi9rOiuqeeuoeeQhuebuOWFs+eKtuaAgVxyXG4gIGNvbnN0IFtzZWFyY2hlZFRlYWNoZXIsIHNldFNlYXJjaGVkVGVhY2hlcl0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpO1xyXG4gIGNvbnN0IFt0cmFuc2ZlckxvYWRpbmcsIHNldFRyYW5zZmVyTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIC8vIFB1Ymxpc2hUYXNrTW9kYWwg55u45YWz54q25oCBXHJcbiAgY29uc3QgW2ZpbGVMaXN0LCBzZXRGaWxlTGlzdF0gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xyXG4gIGNvbnN0IFt1cGxvYWRpbmcsIHNldFVwbG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3RlbXBsYXRlcywgc2V0VGVtcGxhdGVzXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XHJcbiAgY29uc3QgW29mZmljaWFsVGVtcGxhdGVzXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7IC8vIOS/neeVmeS7pemBv+WFjeW8leeUqOmUmeivr1xyXG4gIGNvbnN0IFtzZWxlY3RlZFRlbXBsYXRlSWQsIHNldFNlbGVjdGVkVGVtcGxhdGVJZF0gPSB1c2VTdGF0ZTxudW1iZXIgfCBudWxsPihudWxsKTtcclxuXHJcbiAgLy8gQXNzaWduQmxvY2tzTW9kYWwg55u45YWz54q25oCBXHJcbiAgY29uc3QgW2xvYWRpbmdUZW1wbGF0ZXMsIHNldExvYWRpbmdUZW1wbGF0ZXNdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzdHVkZW50VGVtcGxhdGVVc2FnZSwgc2V0U3R1ZGVudFRlbXBsYXRlVXNhZ2VdID0gdXNlU3RhdGU8UmVjb3JkPG51bWJlciwgbnVtYmVyPj4oe30pO1xyXG4gIGNvbnN0IFt0ZWFjaGVyVGVtcGxhdGUsIHNldFRlYWNoZXJUZW1wbGF0ZV0gPSB1c2VTdGF0ZTx7IHRlbXBsYXRlSWQ6IG51bWJlciB9IHwgdW5kZWZpbmVkPigpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZFN0dWRlbnRJZCwgc2V0U2VsZWN0ZWRTdHVkZW50SWRdID0gdXNlU3RhdGU8bnVtYmVyIHwgbnVsbD4obnVsbCk7XHJcblxyXG4gIC8vIOa3u+WKoHVzZXJSb2xlc+eKtuaAge+8jOS4jnRlYWNoZXItc3BhY2Xkv53mjIHkuIDoh7RcclxuICBjb25zdCBbdXNlclJvbGVzLCBzZXRVc2VyUm9sZXNdID0gdXNlU3RhdGU8QXJyYXk8eyB1c2VySWQ6IG51bWJlcjsgcm9sZUlkOiBudW1iZXIgfT4+KFtdKTtcclxuXHJcbiAgLy8g5aSa6YCJ54q25oCBXHJcbiAgY29uc3QgW3NlbGVjdGVkU3R1ZGVudElkcywgc2V0U2VsZWN0ZWRTdHVkZW50SWRzXSA9IHVzZVN0YXRlPG51bWJlcltdPihbXSk7XHJcbiAgY29uc3QgW2lzU2VsZWN0QWxsLCBzZXRJc1NlbGVjdEFsbF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIC8vIOS9v+eUqOWFqOWxgOaooeadv+eKtuaAgVxyXG4gIGNvbnN0IHsgY3VycmVudFRlbXBsYXRlLCBnbG9iYWxUZW1wbGF0ZUNoYW5nZVZlcnNpb24sIHJlZnJlc2hDdXJyZW50VGVtcGxhdGUgfSA9IHVzZVRlbXBsYXRlKCk7XHJcblxyXG4gIC8vIOWFqOWxgOW9k+WJjeaooeadv+S/oeaBr++8iOeUqOS6juayoeacieS4quS6uuaooeadv+eahOWtpueUn++8iVxyXG4gIGNvbnN0IFtnbG9iYWxDdXJyZW50VGVtcGxhdGUsIHNldEdsb2JhbEN1cnJlbnRUZW1wbGF0ZV0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpO1xyXG5cclxuICAvLyDojrflj5bmlZnluIjlvZPliY3mqKHmnb/vvIjku47nj63nuqfmjqXlj6Pojrflj5bvvIlcclxuICBjb25zdCBmZXRjaFRlYWNoZXJDdXJyZW50VGVtcGxhdGUgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBpZiAoIWNsYXNzSW5mbz8uaWQgfHwgIWNsYXNzSW5mbz8uc2Nob29sSWQpIHJldHVybiBudWxsO1xyXG5cclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjbGFzc0FwaS5nZXRUZWFjaGVyQ2xhc3NlcyhjbGFzc0luZm8uc2Nob29sSWQsIHVzZXJJZCk7XHJcbiAgICAgIGlmIChyZXNwb25zZS5kYXRhLmNvZGUgPT09IDIwMCAmJiByZXNwb25zZS5kYXRhLmRhdGEpIHtcclxuICAgICAgICAvLyDmib7liLDlvZPliY3nj63nuqfnmoTmqKHmnb/kv6Hmga9cclxuICAgICAgICBjb25zdCBjdXJyZW50Q2xhc3MgPSByZXNwb25zZS5kYXRhLmRhdGEuZmluZCgoY2xzOiBhbnkpID0+IGNscy5pZCA9PT0gY2xhc3NJbmZvLmlkKTtcclxuICAgICAgICBpZiAoY3VycmVudENsYXNzICYmIGN1cnJlbnRDbGFzcy50ZW1wbGF0ZU5hbWUpIHtcclxuICAgICAgICAgIGNvbnN0IHRlbXBsYXRlSW5mbyA9IHtcclxuICAgICAgICAgICAgdGVtcGxhdGVJZDogY3VycmVudENsYXNzLnRlbXBsYXRlSWQgfHwgMCxcclxuICAgICAgICAgICAgdGVtcGxhdGVOYW1lOiBjdXJyZW50Q2xhc3MudGVtcGxhdGVOYW1lLFxyXG4gICAgICAgICAgICBpc09mZmljaWFsOiBjdXJyZW50Q2xhc3MuaXNPZmZpY2lhbCB8fCBmYWxzZVxyXG4gICAgICAgICAgfTtcclxuICAgICAgICAgIHNldEdsb2JhbEN1cnJlbnRUZW1wbGF0ZSh0ZW1wbGF0ZUluZm8pO1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ+iOt+WPluWIsOaVmeW4iOW9k+WJjeaooeadvzonLCB0ZW1wbGF0ZUluZm8pO1xyXG4gICAgICAgICAgcmV0dXJuIHRlbXBsYXRlSW5mbztcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluaVmeW4iOW9k+WJjeaooeadv+Wksei0pTonLCBlcnJvcik7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gbnVsbDtcclxuICB9O1xyXG5cclxuICAvLyDlkIzmraXmlZnluIjmqKHmnb/liLDlrabnlJ/vvIjlvZPmlZnluIjmqKHmnb/mm7TmlrDml7bosIPnlKjvvIlcclxuICBjb25zdCBzeW5jVGVhY2hlclRlbXBsYXRlVG9TdHVkZW50cyA9IGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IG5ld1RlbXBsYXRlID0gYXdhaXQgZmV0Y2hUZWFjaGVyQ3VycmVudFRlbXBsYXRlKCk7XHJcbiAgICAgIGlmIChuZXdUZW1wbGF0ZSAmJiBzdHVkZW50cy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ+WQjOatpeaVmeW4iOaooeadv+WIsOWtpueUnzonLCBuZXdUZW1wbGF0ZSk7XHJcblxyXG4gICAgICAgIC8vIOS9v+eUqCByZWYg6I635Y+W5pyA5paw55qEIHBlcnNvbmFsVGVtcGxhdGVBc3NpZ25tZW50c1xyXG4gICAgICAgIGNvbnN0IGN1cnJlbnRQZXJzb25hbEFzc2lnbm1lbnRzID0gcGVyc29uYWxUZW1wbGF0ZUFzc2lnbm1lbnRzUmVmLmN1cnJlbnQ7XHJcblxyXG4gICAgICAgIC8vIOabtOaWsOaJgOacieayoeacieS4quS6uuWIhumFjeaooeadv+eahOWtpueUn1xyXG4gICAgICAgIHNldFN0dWRlbnRzKHByZXZTdHVkZW50cyA9PiBwcmV2U3R1ZGVudHMubWFwKHN0dWRlbnQgPT4ge1xyXG4gICAgICAgICAgLy8g5aaC5p6c5a2m55Sf5pyJ5Liq5Lq65YiG6YWN55qE5qih5p2/77yM5L+d5oyB5LiN5Y+YXHJcbiAgICAgICAgICBpZiAoY3VycmVudFBlcnNvbmFsQXNzaWdubWVudHMuaGFzKHN0dWRlbnQudXNlcklkKSkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg5L+d5oyB5a2m55SfICR7c3R1ZGVudC5uaWNrTmFtZX0g55qE5Liq5Lq65qih5p2/IChzeW5jVGVhY2hlclRlbXBsYXRlVG9TdHVkZW50cylgKTtcclxuICAgICAgICAgICAgcmV0dXJuIHN0dWRlbnQ7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgLy8g5ZCm5YiZ5pu05paw5Li65pWZ5biI5b2T5YmN5qih5p2/XHJcbiAgICAgICAgICBjb25zb2xlLmxvZyhg5pu05paw5a2m55SfICR7c3R1ZGVudC5uaWNrTmFtZX0g5Li65pWZ5biI5qih5p2/IChzeW5jVGVhY2hlclRlbXBsYXRlVG9TdHVkZW50cylgKTtcclxuICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIC4uLnN0dWRlbnQsXHJcbiAgICAgICAgICAgIGN1cnJlbnRUZW1wbGF0ZTogbmV3VGVtcGxhdGVcclxuICAgICAgICAgIH07XHJcbiAgICAgICAgfSkpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCflkIzmraXmlZnluIjmqKHmnb/lpLHotKU6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOWtmOWCqOS4quS6uuWIhumFjeeahOaooeadv+S/oeaBr++8jOmBv+WFjeiiq2ZldGNoU3R1ZGVudHPopobnm5ZcclxuICBjb25zdCBbcGVyc29uYWxUZW1wbGF0ZUFzc2lnbm1lbnRzLCBzZXRQZXJzb25hbFRlbXBsYXRlQXNzaWdubWVudHNdID0gdXNlU3RhdGU8TWFwPG51bWJlciwgeyB0ZW1wbGF0ZUlkOiBudW1iZXI7IHRlbXBsYXRlTmFtZTogc3RyaW5nOyBpc09mZmljaWFsOiBib29sZWFuIH0+PihuZXcgTWFwKCkpO1xyXG5cclxuICAvLyDmt7vliqDlvLrliLbph43mlrDmuLLmn5PnmoTnirbmgIFcclxuICBjb25zdCBbcmVuZGVyVmVyc2lvbl0gPSB1c2VTdGF0ZSgwKTsgLy8gc2V0UmVuZGVyVmVyc2lvbuaaguaXtuacquS9v+eUqFxyXG5cclxuICAvLyDojrflj5bnlKjmiLfkv6Hmga9cclxuICBjb25zdCB1c2VySWQgPSB1c2VTZWxlY3Rvcigoc3RhdGU6IFJvb3RTdGF0ZSkgPT4gc3RhdGUudXNlci51c2VyU3RhdGUudXNlcklkKTtcclxuICBjb25zdCByb2xlSWQgPSB1c2VTZWxlY3Rvcigoc3RhdGU6IFJvb3RTdGF0ZSkgPT4gc3RhdGUudXNlci51c2VyU3RhdGUucm9sZUlkKTtcclxuXHJcbiAgLy8g6I635Y+W54+t57qn5a2m55Sf5pWw5o2uXHJcbiAgY29uc3QgZmV0Y2hTdHVkZW50cyA9IGFzeW5jICgpID0+IHtcclxuICAgIGlmICghY2xhc3NJbmZvPy5pZCkgcmV0dXJuO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XHJcbiAgICAgIHNldEVycm9yKG51bGwpO1xyXG5cclxuICAgICAgLy8g5bm26KGM6I635Y+W54+t57qn5a2m55Sf5Z+656GA5pWw5o2u5ZKM5pWZ5biI5b2T5YmN5qih5p2/XHJcbiAgICAgIGNvbnN0IFtyZXNwb25zZV0gPSBhd2FpdCBQcm9taXNlLmFsbChbXHJcbiAgICAgICAgY2xhc3NBcGkuZ2V0Q2xhc3NTdHVkZW50cyhjbGFzc0luZm8uaWQpLFxyXG4gICAgICAgIGZldGNoVGVhY2hlckN1cnJlbnRUZW1wbGF0ZSgpXHJcbiAgICAgIF0pO1xyXG4gICAgICBpZiAocmVzcG9uc2UuZGF0YS5jb2RlID09PSAyMDApIHtcclxuICAgICAgICBjb25zdCBzdHVkZW50c0RhdGEgPSByZXNwb25zZS5kYXRhLmRhdGEgfHwgW107XHJcblxyXG4gICAgICAgIGlmIChzdHVkZW50c0RhdGEubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgICBzZXRTdHVkZW50cyhbXSk7XHJcbiAgICAgICAgICBzZXRTZWxlY3RlZFN0dWRlbnQobnVsbCk7XHJcblxyXG4gICAgICAgICAgLy8g5aaC5p6c5a2m55Sf5pWw6YeP5Y+Y5Li6MO+8jOS5n+imgeabtOaWsOePree6p+S/oeaBr1xyXG4gICAgICAgICAgaWYgKGNsYXNzSW5mby5zdHVkZW50Q291bnQgIT09IDApIHtcclxuICAgICAgICAgICAgY29uc3QgdXBkYXRlZENsYXNzSW5mbyA9IHtcclxuICAgICAgICAgICAgICAuLi5jbGFzc0luZm8sXHJcbiAgICAgICAgICAgICAgc3R1ZGVudENvdW50OiAwXHJcbiAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgIHNldENsYXNzSW5mbyh1cGRhdGVkQ2xhc3NJbmZvKTtcclxuXHJcbiAgICAgICAgICAgIC8vIOmAmuefpeeItue7hOS7tuabtOaWsOePree6p+WIl+ihqOS4reeahOWtpueUn+aVsFxyXG4gICAgICAgICAgICBvbkNsYXNzSW5mb1VwZGF0ZT8uKHVwZGF0ZWRDbGFzc0luZm8pO1xyXG5cclxuICAgICAgICAgIH1cclxuICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIOaPkOWPluWtpueUn0lE5YiX6KGoXHJcbiAgICAgICAgY29uc3QgdXNlcklkcyA9IHN0dWRlbnRzRGF0YS5tYXAoKHM6IFN0dWRlbnQpID0+IHMudXNlcklkKTtcclxuXHJcbiAgICAgICAgLy8g5om56YeP6I635Y+W5a2m55Sf6K+m57uG5L+h5oGv77yI5YyF5ous5qih5p2/5ZKM6IO96YeP5L+h5oGv77yJXHJcbiAgICAgICAgY29uc3Qgc3R1ZGVudEluZm9NYXAgPSBhd2FpdCAodXNlcklkcy5sZW5ndGggPiAyMFxyXG4gICAgICAgICAgPyBQcm9taXNlLmFsbChcclxuICAgICAgICAgICAgQXJyYXkuZnJvbSh7IGxlbmd0aDogTWF0aC5jZWlsKHVzZXJJZHMubGVuZ3RoIC8gMjApIH0sXHJcbiAgICAgICAgICAgICAgKF8sIGkpID0+IHVzZXJJZHMuc2xpY2UoaSAqIDIwLCAoaSArIDEpICogMjApXHJcbiAgICAgICAgICAgICkubWFwKGJhdGNoSWQgPT5cclxuICAgICAgICAgICAgICBzdHVkZW50QXBpLmdldFN0dWRlbnRzQmF0Y2hJbmZvKGJhdGNoSWQpXHJcbiAgICAgICAgICAgICAgICAudGhlbigoeyBkYXRhOiB7IGRhdGEgfSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIHJldHVybiBkYXRhO1xyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHtcclxuICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5a2m55Sf5om56YeP5L+h5oGv5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgICAgICAgICAgICAgcmV0dXJuIHt9O1xyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgICAgKS50aGVuKHJlc3VsdHMgPT4gT2JqZWN0LmFzc2lnbih7fSwgLi4ucmVzdWx0cykpXHJcbiAgICAgICAgICA6IHN0dWRlbnRBcGkuZ2V0U3R1ZGVudHNCYXRjaEluZm8odXNlcklkcylcclxuICAgICAgICAgICAgLnRoZW4oKHsgZGF0YTogeyBkYXRhIH0gfSkgPT4ge1xyXG4gICAgICAgICAgICAgIHJldHVybiBkYXRhO1xyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5a2m55Sf5om56YeP5L+h5oGv5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgICAgICAgICByZXR1cm4ge307XHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgKTtcclxuXHJcblxyXG5cclxuICAgICAgICAvLyDlkIjlubblrabnlJ/mlbDmja5cclxuICAgICAgICBjb25zdCBjb21wbGV0ZVN0dWRlbnRzID0gc3R1ZGVudHNEYXRhLm1hcCgoczogU3R1ZGVudCkgPT4ge1xyXG4gICAgICAgICAgY29uc3Qgc3R1ZGVudEluZm8gPSBzdHVkZW50SW5mb01hcFtzLnVzZXJJZF07XHJcbiAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbmnInkuKrkurrliIbphY3nmoTmqKHmnb9cclxuICAgICAgICAgIGNvbnN0IHBlcnNvbmFsVGVtcGxhdGUgPSBwZXJzb25hbFRlbXBsYXRlQXNzaWdubWVudHMuZ2V0KHMudXNlcklkKTtcclxuXHJcbiAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAuLi5zLFxyXG4gICAgICAgICAgICAuLi5zdHVkZW50SW5mbyxcclxuICAgICAgICAgICAgaWQ6IHMudXNlcklkLCAvLyDmt7vliqBpZOWxnuaAp+S7peWFvOWuuXRlYWNoZXItc3BhY2XnsbvlnotcclxuICAgICAgICAgICAgbmlja05hbWU6IHMubmlja05hbWUgfHwgYOWtpueUnyR7cy5zdHVkZW50TnVtYmVyIHx8IHMudXNlcklkfWAsIC8vIOehruS/nW5pY2tOYW1l5oC75piv5pyJ5YC8XHJcbiAgICAgICAgICAgIHRvdGFsUG9pbnRzOiBzdHVkZW50SW5mbz8udG90YWxQb2ludHMgfHwgMCxcclxuICAgICAgICAgICAgYXZhaWxhYmxlUG9pbnRzOiBzdHVkZW50SW5mbz8uYXZhaWxhYmxlUG9pbnRzIHx8IDAsXHJcbiAgICAgICAgICAgIGF2YXRhclVybDogcy5hdmF0YXJVcmwgfHwgJy9kZWZhdWx0LWF2YXRhci5wbmcnLCAvLyDnoa7kv51hdmF0YXJVcmzmgLvmmK/mnInlgLxcclxuICAgICAgICAgICAgLy8g5LyY5YWI57qn77ya5Liq5Lq65YiG6YWN55qE5qih5p2/ID4g5a2m55SfQVBJ6L+U5Zue55qE5qih5p2/ID4g5pWZ5biI5b2T5YmN5qih5p2/ID4gbnVsbFxyXG4gICAgICAgICAgICBjdXJyZW50VGVtcGxhdGU6ICgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gcGVyc29uYWxUZW1wbGF0ZSB8fFxyXG4gICAgICAgICAgICAgICAgc3R1ZGVudEluZm8/LmN1cnJlbnRUZW1wbGF0ZSB8fFxyXG4gICAgICAgICAgICAgICAgZ2xvYmFsQ3VycmVudFRlbXBsYXRlO1xyXG5cclxuICAgICAgICAgICAgICAvLyDosIPor5Xkv6Hmga9cclxuICAgICAgICAgICAgICBpZiAocy51c2VySWQgPT09IHN0dWRlbnRzRGF0YVswXT8udXNlcklkKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5a2m55Sf5qih5p2/5YiG6YWN6YC76L6ROicsIHtcclxuICAgICAgICAgICAgICAgICAgc3R1ZGVudElkOiBzLnVzZXJJZCxcclxuICAgICAgICAgICAgICAgICAgc3R1ZGVudE5hbWU6IHMubmlja05hbWUsXHJcbiAgICAgICAgICAgICAgICAgIHBlcnNvbmFsVGVtcGxhdGUsXHJcbiAgICAgICAgICAgICAgICAgIHN0dWRlbnRBcGlUZW1wbGF0ZTogc3R1ZGVudEluZm8/LmN1cnJlbnRUZW1wbGF0ZSxcclxuICAgICAgICAgICAgICAgICAgZ2xvYmFsQ3VycmVudFRlbXBsYXRlLFxyXG4gICAgICAgICAgICAgICAgICBmaW5hbFRlbXBsYXRlOiByZXN1bHRcclxuICAgICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdDtcclxuICAgICAgICAgICAgfSkoKVxyXG4gICAgICAgICAgfTtcclxuICAgICAgICB9KTtcclxuXHJcblxyXG5cclxuICAgICAgICBzZXRTdHVkZW50cyhjb21wbGV0ZVN0dWRlbnRzKTtcclxuXHJcbiAgICAgICAgLy8g5aaC5p6c5a2m55Sf5pWw6YeP5Y+R55Sf5Y+Y5YyW77yM5pu05paw54+t57qn5L+h5oGv5bm26YCa55+l54i257uE5Lu2XHJcbiAgICAgICAgaWYgKGNvbXBsZXRlU3R1ZGVudHMubGVuZ3RoICE9PSBjbGFzc0luZm8uc3R1ZGVudENvdW50KSB7XHJcbiAgICAgICAgICBjb25zdCB1cGRhdGVkQ2xhc3NJbmZvID0ge1xyXG4gICAgICAgICAgICAuLi5jbGFzc0luZm8sXHJcbiAgICAgICAgICAgIHN0dWRlbnRDb3VudDogY29tcGxldGVTdHVkZW50cy5sZW5ndGhcclxuICAgICAgICAgIH07XHJcbiAgICAgICAgICBzZXRDbGFzc0luZm8odXBkYXRlZENsYXNzSW5mbyk7XHJcblxyXG4gICAgICAgICAgLy8g6YCa55+l54i257uE5Lu25pu05paw54+t57qn5YiX6KGo5Lit55qE5a2m55Sf5pWwXHJcbiAgICAgICAgICBvbkNsYXNzSW5mb1VwZGF0ZT8uKHVwZGF0ZWRDbGFzc0luZm8pO1xyXG5cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIOiuvue9rnVzZXJSb2xlc++8jOS4jnRlYWNoZXItc3BhY2Xkv53mjIHkuIDoh7RcclxuICAgICAgICBjb25zdCBuZXdVc2VyUm9sZXM6IFVzZXJSb2xlW10gPSBjb21wbGV0ZVN0dWRlbnRzLm1hcCgoc3R1ZGVudDogU3R1ZGVudCkgPT4gKHtcclxuICAgICAgICAgIHVzZXJJZDogc3R1ZGVudC51c2VySWQsXHJcbiAgICAgICAgICByb2xlSWQ6IDEgLy8g5a2m55Sf6KeS6ImySUTkuLoxXHJcbiAgICAgICAgfSkpO1xyXG5cclxuICAgICAgICAvLyDmt7vliqDmlZnluIjoh6rlt7HnmoTop5LoibJcclxuICAgICAgICBuZXdVc2VyUm9sZXMucHVzaCh7XHJcbiAgICAgICAgICB1c2VySWQ6IHVzZXJJZCxcclxuICAgICAgICAgIHJvbGVJZDogMiAvLyDmlZnluIjop5LoibJJROS4ujJcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgc2V0VXNlclJvbGVzKG5ld1VzZXJSb2xlcyk7XHJcblxyXG4gICAgICAgIC8vIOm7mOiupOmAieaLqeesrOS4gOS4quWtpueUn1xyXG4gICAgICAgIGlmIChjb21wbGV0ZVN0dWRlbnRzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgIHNldFNlbGVjdGVkU3R1ZGVudChjb21wbGV0ZVN0dWRlbnRzWzBdKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2V0RXJyb3IocmVzcG9uc2UuZGF0YS5tZXNzYWdlIHx8ICfojrflj5blrabnlJ/liJfooajlpLHotKUnKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWtpueUn+WIl+ihqOWksei0pTonLCBlcnIpO1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfplJnor6/or6bmg4U6Jywge1xyXG4gICAgICAgIG1lc3NhZ2U6IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAn5pyq55+l6ZSZ6K+vJyxcclxuICAgICAgICBzdGFjazogZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIuc3RhY2sgOiB1bmRlZmluZWQsXHJcbiAgICAgICAgY2xhc3NJZDogY2xhc3NJbmZvLmlkXHJcbiAgICAgIH0pO1xyXG4gICAgICBzZXRFcnJvcign6I635Y+W5a2m55Sf5YiX6KGo5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VJyk7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuXHJcblxyXG4gIC8vIOiOt+WPluadg+mZkOaooeadv+WIl+ihqFxyXG4gIGNvbnN0IGZldGNoVGVtcGxhdGVzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgc2V0TG9hZGluZ1RlbXBsYXRlcyh0cnVlKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHsgZ2V0Um9sZVRlbXBsYXRlTGlzdCwgZ2V0T2ZmaWNpYWxUZW1wbGF0ZXMgfSA9IGF3YWl0IGltcG9ydCgnLi4vLi4vLi4vbGliL2FwaS9yb2xlJyk7XHJcblxyXG4gICAgICAvLyDlkIzml7bojrflj5bmlZnluIjoh6rlrprkuYnmqKHmnb/lkozlrpjmlrnmqKHmnb9cclxuICAgICAgY29uc3QgW2N1c3RvbVJlc3BvbnNlLCBvZmZpY2lhbFJlc3BvbnNlXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcclxuICAgICAgICBnZXRSb2xlVGVtcGxhdGVMaXN0KHVzZXJJZCksXHJcbiAgICAgICAgZ2V0T2ZmaWNpYWxUZW1wbGF0ZXMoKVxyXG4gICAgICBdKTtcclxuXHJcbiAgICAgIGlmIChjdXN0b21SZXNwb25zZS5kYXRhLmNvZGUgPT09IDIwMCAmJiBvZmZpY2lhbFJlc3BvbnNlLmRhdGEuY29kZSA9PT0gMjAwKSB7XHJcbiAgICAgICAgY29uc3QgY3VzdG9tVGVtcGxhdGVzID0gY3VzdG9tUmVzcG9uc2UuZGF0YS5kYXRhIHx8IFtdO1xyXG4gICAgICAgIGNvbnN0IG9mZmljaWFsVGVtcGxhdGVzID0gb2ZmaWNpYWxSZXNwb25zZS5kYXRhLmRhdGEgfHwgW107XHJcblxyXG4gICAgICAgIC8vIOS4uuWumOaWueaooeadv+a3u+WKoOagh+iusFxyXG4gICAgICAgIGNvbnN0IG1hcmtlZE9mZmljaWFsVGVtcGxhdGVzID0gb2ZmaWNpYWxUZW1wbGF0ZXMubWFwKCh0ZW1wbGF0ZTogYW55KSA9PiAoe1xyXG4gICAgICAgICAgLi4udGVtcGxhdGUsXHJcbiAgICAgICAgICBpc09mZmljaWFsOiB0cnVlXHJcbiAgICAgICAgfSkpO1xyXG5cclxuICAgICAgICAvLyDlkIjlubbmiYDmnInmqKHmnb9cclxuICAgICAgICBjb25zdCBhbGxUZW1wbGF0ZXMgPSBbLi4uY3VzdG9tVGVtcGxhdGVzLCAuLi5tYXJrZWRPZmZpY2lhbFRlbXBsYXRlc107XHJcbiAgICAgICAgc2V0VGVtcGxhdGVzKGFsbFRlbXBsYXRlcyk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluadg+mZkOaooeadv+WIl+ihqOWksei0pTonLCBlcnJvcik7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRMb2FkaW5nVGVtcGxhdGVzKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyDojrflj5bmqKHmnb/kvb/nlKjmg4XlhrVcclxuICBjb25zdCBmZXRjaFRlbXBsYXRlVXNhZ2UgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB7IGdldFVzZXJDdXJyZW50VGVtcGxhdGUsIGdldFN0dWRlbnRUZW1wbGF0ZXMgfSA9IGF3YWl0IGltcG9ydCgnLi4vLi4vLi4vbGliL2FwaS9yb2xlJyk7XHJcblxyXG4gICAgICAvLyDojrflj5bmlZnluIjkvb/nlKjnmoTmqKHmnb9cclxuICAgICAgY29uc3QgdGVhY2hlclJlc3BvbnNlID0gYXdhaXQgZ2V0VXNlckN1cnJlbnRUZW1wbGF0ZSh1c2VySWQpO1xyXG4gICAgICBpZiAodGVhY2hlclJlc3BvbnNlLmRhdGEuY29kZSA9PT0gMjAwKSB7XHJcbiAgICAgICAgY29uc3QgdGVhY2hlclRlbXBsYXRlRGF0YSA9IHRlYWNoZXJSZXNwb25zZS5kYXRhLmRhdGE7XHJcblxyXG4gICAgICAgIHNldFRlYWNoZXJUZW1wbGF0ZSh0ZWFjaGVyVGVtcGxhdGVEYXRhKTtcclxuXHJcbiAgICAgICAgLy8g5ZCM5pe25pu05paw5b2T5YmN5qih5p2/54q25oCB77yM56Gu5L+d5LiO5qih5p2/566h55CG5ZCM5q2lXHJcbiAgICAgICAgLy8g5pqC5pe25rOo6YeK5o6J5Lul5YGc5q2i5b6q546vXHJcbiAgICAgICAgLypcclxuICAgICAgICBpZiAodGVhY2hlclRlbXBsYXRlRGF0YSkge1xyXG4gICAgICAgICAgLy8g5L2/55SocmVmcmVzaEN1cnJlbnRUZW1wbGF0ZeadpeabtOaWsOWFqOWxgOaooeadv+eKtuaAgVxyXG4gICAgICAgICAgYXdhaXQgcmVmcmVzaEN1cnJlbnRUZW1wbGF0ZSgpO1xyXG4gICAgICAgIH1cclxuICAgICAgICAqL1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyDojrflj5blrabnlJ/kvb/nlKjnmoTmqKHmnb9cclxuICAgICAgY29uc3Qgc3R1ZGVudFJlc3BvbnNlID0gYXdhaXQgZ2V0U3R1ZGVudFRlbXBsYXRlcyh7XHJcbiAgICAgICAgdGVhY2hlcklkOiB1c2VySWQsXHJcbiAgICAgICAgcGFnZTogMSxcclxuICAgICAgICBzaXplOiAyMDBcclxuICAgICAgfSk7XHJcbiAgICAgIGlmIChzdHVkZW50UmVzcG9uc2UuZGF0YS5jb2RlID09PSAyMDApIHtcclxuICAgICAgICAvLyDnu5/orqHmr4/kuKrmqKHmnb/ooqvkvb/nlKjnmoTmrKHmlbBcclxuICAgICAgICBjb25zdCB1c2FnZTogeyBba2V5OiBudW1iZXJdOiBudW1iZXIgfSA9IHt9O1xyXG4gICAgICAgIHN0dWRlbnRSZXNwb25zZS5kYXRhLmRhdGEubGlzdC5mb3JFYWNoKChpdGVtOiBhbnkpID0+IHtcclxuICAgICAgICAgIGlmIChpdGVtLnRlbXBsYXRlSWQpIHtcclxuICAgICAgICAgICAgdXNhZ2VbaXRlbS50ZW1wbGF0ZUlkXSA9ICh1c2FnZVtpdGVtLnRlbXBsYXRlSWRdIHx8IDApICsgMTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuICAgICAgICBzZXRTdHVkZW50VGVtcGxhdGVVc2FnZSh1c2FnZSk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluaooeadv+S9v+eUqOaDheWGteWksei0pTonLCBlcnJvcik7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8g57uE5Lu25oyC6L295pe26I635Y+W5a2m55Sf5pWw5o2u5ZKM5qih5p2/XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGZldGNoU3R1ZGVudHMoKTtcclxuICAgIGZldGNoVGVtcGxhdGVzKCk7XHJcbiAgICBmZXRjaFRlbXBsYXRlVXNhZ2UoKTtcclxuICB9LCBbY2xhc3NJbmZvPy5pZCwgdXNlcklkXSk7XHJcblxyXG4gIC8vIOW9k+e7hOS7tumHjeaWsOaMgui9veaIluePree6p+WPmOWMluaXtu+8jOehruS/neiOt+WPluacgOaWsOeahOW9k+WJjeaooeadv1xyXG4gIC8vIOaaguaXtuazqOmHiuaOieS7peWBnOatouW+queOr1xyXG4gIC8qXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChjbGFzc0luZm8/LmlkICYmIHVzZXJJZCkge1xyXG4gICAgICByZWZyZXNoQ3VycmVudFRlbXBsYXRlKCk7XHJcbiAgICB9XHJcbiAgfSwgW2NsYXNzSW5mbz8uaWQsIHVzZXJJZF0pO1xyXG4gICovXHJcblxyXG4gIC8vIOenu+mZpOi/meS4qnVzZUVmZmVjdO+8jOmBv+WFjeWcqGN1cnJlbnRUZW1wbGF0ZeWPmOWMluaXtuimhuebluS4quS6uuWIhumFjeeahOaooeadv1xyXG5cclxuICAvLyDkvb/nlKggdXNlUmVmIOadpeS/neWtmOacgOaWsOeahCBwZXJzb25hbFRlbXBsYXRlQXNzaWdubWVudHNcclxuICBjb25zdCBwZXJzb25hbFRlbXBsYXRlQXNzaWdubWVudHNSZWYgPSB1c2VSZWYocGVyc29uYWxUZW1wbGF0ZUFzc2lnbm1lbnRzKTtcclxuICBwZXJzb25hbFRlbXBsYXRlQXNzaWdubWVudHNSZWYuY3VycmVudCA9IHBlcnNvbmFsVGVtcGxhdGVBc3NpZ25tZW50cztcclxuXHJcbiAgLy8g55uR5ZCs5YWo5bGA5qih5p2/5Y+Y5YyW77yM6YeN5paw6I635Y+W5pWZ5biI5b2T5YmN5qih5p2/5bm25pu05paw5a2m55Sf5pWw5o2uXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChnbG9iYWxUZW1wbGF0ZUNoYW5nZVZlcnNpb24gPiAwICYmIGNsYXNzSW5mbz8uaWQpIHtcclxuICAgICAgY29uc29sZS5sb2coJ+ajgOa1i+WIsOaooeadv+WPmOWMlu+8jOmHjeaWsOiOt+WPluaVmeW4iOW9k+WJjeaooeadvycpO1xyXG5cclxuICAgICAgLy8g6YeN5paw6I635Y+W5pWZ5biI5b2T5YmN5qih5p2/XHJcbiAgICAgIGZldGNoVGVhY2hlckN1cnJlbnRUZW1wbGF0ZSgpLnRoZW4oKG5ld1RlbXBsYXRlKSA9PiB7XHJcbiAgICAgICAgaWYgKG5ld1RlbXBsYXRlICYmIHN0dWRlbnRzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgIC8vIOS4jea4hemZpOS4quS6uuWIhumFjeeahOaooeadv+iusOW9le+8jOS/neeVmeWtpueUn+eahOS4quS6uuaooeadv+aVsOaNrlxyXG4gICAgICAgICAgLy8gc2V0UGVyc29uYWxUZW1wbGF0ZUFzc2lnbm1lbnRzKG5ldyBNYXAoKSk7IC8vIOazqOmHiuaOiei/meihjFxyXG5cclxuICAgICAgICAgIC8vIOS9v+eUqCByZWYg6I635Y+W5pyA5paw55qEIHBlcnNvbmFsVGVtcGxhdGVBc3NpZ25tZW50c1xyXG4gICAgICAgICAgY29uc3QgY3VycmVudFBlcnNvbmFsQXNzaWdubWVudHMgPSBwZXJzb25hbFRlbXBsYXRlQXNzaWdubWVudHNSZWYuY3VycmVudDtcclxuXHJcbiAgICAgICAgICAvLyDlj6rmm7TmlrDmsqHmnInkuKrkurrmqKHmnb/nmoTlrabnlJ/kuLrmlrDnmoTmlZnluIjlvZPliY3mqKHmnb9cclxuICAgICAgICAgIGNvbnNvbGUubG9nKCflhajlsYDmqKHmnb/lj5jljJbvvIzlvIDlp4vmm7TmlrDlrabnlJ/mqKHmnb86Jywge1xyXG4gICAgICAgICAgICBuZXdUZW1wbGF0ZSxcclxuICAgICAgICAgICAgcGVyc29uYWxUZW1wbGF0ZUFzc2lnbm1lbnRzOiBBcnJheS5mcm9tKGN1cnJlbnRQZXJzb25hbEFzc2lnbm1lbnRzLmVudHJpZXMoKSksXHJcbiAgICAgICAgICAgIHN0dWRlbnRzQ291bnQ6IHN0dWRlbnRzLmxlbmd0aFxyXG4gICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgc2V0U3R1ZGVudHMocHJldlN0dWRlbnRzID0+IHByZXZTdHVkZW50cy5tYXAoc3R1ZGVudCA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGhhc1BlcnNvbmFsVGVtcGxhdGUgPSBjdXJyZW50UGVyc29uYWxBc3NpZ25tZW50cy5oYXMoc3R1ZGVudC51c2VySWQpO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg5a2m55SfICR7c3R1ZGVudC5uaWNrTmFtZX0gKCR7c3R1ZGVudC51c2VySWR9KTpgLCB7XHJcbiAgICAgICAgICAgICAgaGFzUGVyc29uYWxUZW1wbGF0ZSxcclxuICAgICAgICAgICAgICBjdXJyZW50VGVtcGxhdGU6IHN0dWRlbnQuY3VycmVudFRlbXBsYXRlLFxyXG4gICAgICAgICAgICAgIHdpbGxVcGRhdGU6ICFoYXNQZXJzb25hbFRlbXBsYXRlXHJcbiAgICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgICAgLy8g5aaC5p6c5a2m55Sf5pyJ5Liq5Lq65YiG6YWN55qE5qih5p2/77yM5L+d5oyB5LiN5Y+YXHJcbiAgICAgICAgICAgIGlmIChoYXNQZXJzb25hbFRlbXBsYXRlKSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coYOS/neaMgeWtpueUnyAke3N0dWRlbnQubmlja05hbWV9IOeahOS4quS6uuaooeadv2ApO1xyXG4gICAgICAgICAgICAgIHJldHVybiBzdHVkZW50O1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIC8vIOWQpuWImeabtOaWsOS4uuaWsOeahOaVmeW4iOW9k+WJjeaooeadv1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg5pu05paw5a2m55SfICR7c3R1ZGVudC5uaWNrTmFtZX0g5Li65pWZ5biI5qih5p2/YCk7XHJcbiAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgLi4uc3R1ZGVudCxcclxuICAgICAgICAgICAgICBjdXJyZW50VGVtcGxhdGU6IG5ld1RlbXBsYXRlXHJcbiAgICAgICAgICAgIH07XHJcbiAgICAgICAgICB9KSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICB9LCBbZ2xvYmFsVGVtcGxhdGVDaGFuZ2VWZXJzaW9uLCBjbGFzc0luZm8/LmlkXSk7XHJcblxyXG4gIC8vIOWumuacn+ajgOafpeaVmeW4iOaooeadv+abtOaWsFxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoIWNsYXNzSW5mbz8uaWQpIHJldHVybjtcclxuXHJcbiAgICAvLyDnq4vljbPmo4Dmn6XkuIDmrKFcclxuICAgIHN5bmNUZWFjaGVyVGVtcGxhdGVUb1N0dWRlbnRzKCk7XHJcblxyXG4gICAgLy8g6K6+572u5a6a5pe25Zmo77yM5q+PMzDnp5Lmo4Dmn6XkuIDmrKHmqKHmnb/mm7TmlrBcclxuICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xyXG4gICAgICBzeW5jVGVhY2hlclRlbXBsYXRlVG9TdHVkZW50cygpO1xyXG4gICAgfSwgMzAwMDApOyAvLyAzMOenklxyXG5cclxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKTtcclxuICB9LCBbY2xhc3NJbmZvPy5pZCwgc3R1ZGVudHMubGVuZ3RoXSk7XHJcblxyXG4gIC8vIOeCueWHu+WklumDqOWFs+mXreS4i+aLieiPnOWNlVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBoYW5kbGVDbGlja091dHNpZGUgPSAoZXZlbnQ6IE1vdXNlRXZlbnQpID0+IHtcclxuICAgICAgaWYgKGRyb3Bkb3duUmVmLmN1cnJlbnQgJiYgIWRyb3Bkb3duUmVmLmN1cnJlbnQuY29udGFpbnMoZXZlbnQudGFyZ2V0IGFzIE5vZGUpKSB7XHJcbiAgICAgICAgLy8gc2V0SXNNb3JlQWN0aW9uc0Ryb3Bkb3duT3BlbihmYWxzZSk7IC8vIOW3suenu+mZpFxyXG4gICAgICB9XHJcbiAgICAgIGlmIChzZXR0aW5nc0Ryb3Bkb3duUmVmLmN1cnJlbnQgJiYgIXNldHRpbmdzRHJvcGRvd25SZWYuY3VycmVudC5jb250YWlucyhldmVudC50YXJnZXQgYXMgTm9kZSkpIHtcclxuICAgICAgICBzZXRJc1NldHRpbmdzRHJvcGRvd25PcGVuKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgICBpZiAoYmF0Y2hBY3Rpb25zRHJvcGRvd25SZWYuY3VycmVudCAmJiAhYmF0Y2hBY3Rpb25zRHJvcGRvd25SZWYuY3VycmVudC5jb250YWlucyhldmVudC50YXJnZXQgYXMgTm9kZSkpIHtcclxuICAgICAgICBzZXRJc0JhdGNoQWN0aW9uc0Ryb3Bkb3duT3BlbihmYWxzZSk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2Vkb3duJywgaGFuZGxlQ2xpY2tPdXRzaWRlKTtcclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlZG93bicsIGhhbmRsZUNsaWNrT3V0c2lkZSk7XHJcbiAgICB9O1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8g5aSE55CG5a2m55Sf54K55Ye76YCJ5oup77yI55So5LqO5Y+z5L6n5pi+56S66K+m5oOF77yJXHJcbiAgY29uc3QgaGFuZGxlU3R1ZGVudENsaWNrID0gKHN0dWRlbnQ6IFN0dWRlbnQpID0+IHtcclxuICAgIHNldFNlbGVjdGVkU3R1ZGVudChzdHVkZW50KTtcclxuICB9O1xyXG5cclxuICAvLyDlpITnkIbliKDpmaTlrabnlJ9cclxuICBjb25zdCBoYW5kbGVEZWxldGVTdHVkZW50ID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKCFzZWxlY3RlZFN0dWRlbnQpIHtcclxuICAgICAgYWxlcnQoJ+ivt+WFiOmAieaLqeimgeWIoOmZpOeahOWtpueUnycpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgLy8g5pi+56S656Gu6K6k5a+56K+d5qGGXHJcbiAgICBjb25zdCBjb25maXJtZWQgPSB3aW5kb3cuY29uZmlybShcclxuICAgICAgYOehruWumuimgeWwhiAke3NlbGVjdGVkU3R1ZGVudC5uaWNrTmFtZX0g56e75Ye654+t57qn5ZCX77yfXFxuXFxu5q2k5pON5L2c5LiN5Y+v5oGi5aSN77yBYFxyXG4gICAgKTtcclxuXHJcbiAgICBpZiAoIWNvbmZpcm1lZCkge1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc29sZS5sb2coJ+WIoOmZpOWtpueUnzonLCBzZWxlY3RlZFN0dWRlbnQpO1xyXG5cclxuICAgICAgLy8g6LCD55So5Yig6Zmk5a2m55Sf55qEQVBJ77yM5Lyg5YWl5a2m55Sf55qEdXNlcklk5pWw57uEXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgY2xhc3NBcGkucmVtb3ZlU3R1ZGVudEZyb21DbGFzcyhbc2VsZWN0ZWRTdHVkZW50LnVzZXJJZF0pO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ+WIoOmZpOWtpueUnyBBUEkg5ZON5bqUOicsIHJlc3BvbnNlLmRhdGEpO1xyXG5cclxuICAgICAgLy8g5qOA5p+l5ZON5bqU54q25oCBXHJcbiAgICAgIGlmIChyZXNwb25zZS5kYXRhLmNvZGUgPT09IDIwMCkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCfliKDpmaTlrabnlJ/miJDlip8nKTtcclxuICAgICAgICBhbGVydCgn5a2m55Sf5bey5oiQ5Yqf56e75Ye654+t57qnJyk7XHJcblxyXG4gICAgICAgIC8vIOa4hemZpOmAieS4reeahOWtpueUn1xyXG4gICAgICAgIHNldFNlbGVjdGVkU3R1ZGVudChudWxsKTtcclxuXHJcbiAgICAgICAgLy8g6YeN5paw6I635Y+W5a2m55Sf5YiX6KGoXHJcbiAgICAgICAgYXdhaXQgZmV0Y2hTdHVkZW50cygpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIoOmZpOWtpueUn+Wksei0pTonLCByZXNwb25zZS5kYXRhLm1lc3NhZ2UpO1xyXG4gICAgICAgIGFsZXJ0KHJlc3BvbnNlLmRhdGEubWVzc2FnZSB8fCAn5Yig6Zmk5a2m55Sf5aSx6LSlJyk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIoOmZpOWtpueUn+Wksei0pTonLCBlcnJvcik7XHJcbiAgICAgIGFsZXJ0KCfliKDpmaTlrabnlJ/lpLHotKXvvIzor7fnqI3lkI7ph43or5UnKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyDnvJbovpHnj63nuqdcclxuICBjb25zdCBoYW5kbGVFZGl0Q2xhc3MgPSBhc3luYyAodmFsdWVzOiB7IGNsYXNzTmFtZTogc3RyaW5nIH0pID0+IHtcclxuICAgIGNvbnN0IHsgR2V0Tm90aWZpY2F0aW9uIH0gPSBhd2FpdCBpbXBvcnQoJ2xvZ2ljLWNvbW1vbi9kaXN0L2NvbXBvbmVudHMvTm90aWZpY2F0aW9uJyk7XHJcbiAgICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zb2xlLmxvZygn5byA5aeL57yW6L6R54+t57qnOicsIHtcclxuICAgICAgICBjbGFzc0lkOiBjbGFzc0luZm8uaWQsXHJcbiAgICAgICAgdmFsdWVzOiB2YWx1ZXMsXHJcbiAgICAgICAgb3JpZ2luYWxDbGFzc05hbWU6IGNsYXNzSW5mby5jbGFzc05hbWVcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNsYXNzQXBpLnVwZGF0ZUNsYXNzKGNsYXNzSW5mby5pZCwge1xyXG4gICAgICAgIGNsYXNzTmFtZTogdmFsdWVzLmNsYXNzTmFtZSxcclxuICAgICAgICBncmFkZTogY2xhc3NJbmZvLmdyYWRlIHx8ICcnIC8vIOehruS/neS8oOmAkmdyYWRl5a2X5q6177yM5aaC5p6c5rKh5pyJ5YiZ5L2/55So56m65a2X56ym5LiyXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ+e8lui+keePree6p0FQSeWTjeW6lDonLCByZXNwb25zZSk7XHJcblxyXG4gICAgICBpZiAocmVzcG9uc2UuZGF0YS5jb2RlID09PSAyMDApIHtcclxuICAgICAgICBjb25zb2xlLmxvZygn57yW6L6R54+t57qn5oiQ5YqfJyk7XHJcbiAgICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+e8lui+keePree6p+aIkOWKnycpO1xyXG4gICAgICAgIHNldElzRWRpdENsYXNzTW9kYWxWaXNpYmxlKGZhbHNlKTtcclxuXHJcbiAgICAgICAgLy8g5pu05paw5pys5Zyw54+t57qn5L+h5oGvXHJcbiAgICAgICAgY29uc3QgdXBkYXRlZENsYXNzSW5mbyA9IHtcclxuICAgICAgICAgIC4uLmNsYXNzSW5mbyxcclxuICAgICAgICAgIGNsYXNzTmFtZTogdmFsdWVzLmNsYXNzTmFtZVxyXG4gICAgICAgIH07XHJcbiAgICAgICAgc2V0Q2xhc3NJbmZvKHVwZGF0ZWRDbGFzc0luZm8pO1xyXG5cclxuICAgICAgICAvLyDpgJrnn6XniLbnu4Tku7bmm7TmlrDnj63nuqfliJfooajkuK3nmoTnj63nuqfkv6Hmga9cclxuICAgICAgICBvbkNsYXNzSW5mb1VwZGF0ZT8uKHVwZGF0ZWRDbGFzc0luZm8pO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+e8lui+keePree6p+Wksei0pSAtIEFQSei/lOWbnumUmeivrzonLCB7XHJcbiAgICAgICAgICBjb2RlOiByZXNwb25zZS5kYXRhLmNvZGUsXHJcbiAgICAgICAgICBtZXNzYWdlOiByZXNwb25zZS5kYXRhLm1lc3NhZ2UsXHJcbiAgICAgICAgICBkYXRhOiByZXNwb25zZS5kYXRhXHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKHJlc3BvbnNlLmRhdGEubWVzc2FnZSB8fCAn57yW6L6R54+t57qn5aSx6LSlJyk7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLmRhdGEubWVzc2FnZSB8fCAn57yW6L6R54+t57qn5aSx6LSlJyk7IC8vIOaKm+WHuumUmeivr+iuqeaooeaAgeahhuefpemBk+Wksei0peS6hlxyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+e8lui+keePree6p+Wksei0pSAtIOivt+axguW8guW4uDonLCB7XHJcbiAgICAgICAgZXJyb3I6IGVycm9yLFxyXG4gICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsXHJcbiAgICAgICAgcmVzcG9uc2U6IGVycm9yLnJlc3BvbnNlPy5kYXRhLFxyXG4gICAgICAgIHN0YXR1czogZXJyb3IucmVzcG9uc2U/LnN0YXR1c1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIC8vIOWPquacieWcqOayoeacieaYvuekuui/h+mUmeivr+a2iOaBr+eahOaDheWGteS4i+aJjeaYvuekuumAmueUqOmUmeivr1xyXG4gICAgICBpZiAoIWVycm9yLm1lc3NhZ2UgfHwgZXJyb3IubWVzc2FnZSA9PT0gJ+e8lui+keePree6p+Wksei0pScpIHtcclxuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ+e8lui+keePree6p+Wksei0pe+8jOivt+eojeWQjumHjeivlScpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICB0aHJvdyBlcnJvcjsgLy8g6YeN5paw5oqb5Ye66ZSZ6K+v77yM6K6p5qih5oCB5qGG5L+d5oyB5omT5byA54q25oCBXHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8g5a+85YWl5a2m55SfXHJcbiAgY29uc3QgaGFuZGxlSW1wb3J0U3R1ZGVudHMgPSBhc3luYyAoZmlsZTogRmlsZSk6IFByb21pc2U8Ym9vbGVhbj4gPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc29sZS5sb2coJ+WvvOWFpeWtpueUn+aWh+S7tjonLCBmaWxlKTtcclxuICAgICAgLy8g6L+Z6YeM6ZyA6KaB5a6e546w5paH5Lu26Kej5p6Q5ZKM5a+85YWl6YC76L6RXHJcbiAgICAgIGFsZXJ0KCflr7zlhaXlrabnlJ/lip/og73mraPlnKjlvIDlj5HkuK0nKTtcclxuICAgICAgc2V0SXNJbXBvcnRTdHVkZW50TW9kYWxWaXNpYmxlKGZhbHNlKTtcclxuICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCflr7zlhaXlrabnlJ/lpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICBhbGVydCgn5a+85YWl5a2m55Sf5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VJyk7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyDlr7zlh7rlrabnlJ9cclxuICBjb25zdCBoYW5kbGVFeHBvcnRTdHVkZW50cyA9IGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgc3R1ZGVudEFwaS5leHBvcnRTdHVkZW50cyhjbGFzc0luZm8uaWQpO1xyXG4gICAgICBjb25zb2xlLmxvZygn5a+85Ye65a2m55Sf5oiQ5YqfOicsIHJlc3BvbnNlKTtcclxuICAgICAgYWxlcnQoJ+WvvOWHuuWtpueUn+aIkOWKnycpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign5a+85Ye65a2m55Sf5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgYWxlcnQoJ+WvvOWHuuWtpueUn+Wksei0pe+8jOivt+eojeWQjumHjeivlScpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOaQnOe0ouaVmeW4iFxyXG4gIGNvbnN0IGhhbmRsZVNlYXJjaFRlYWNoZXIgPSBhc3luYyAocGhvbmU6IHN0cmluZykgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjbGFzc0FwaS5zZWFyY2hUZWFjaGVyQnlQaG9uZShwaG9uZSk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfmkJzntKLmlZnluIjlk43lupQ6JywgcmVzcG9uc2UpO1xyXG4gICAgICBpZiAocmVzcG9uc2UuZGF0YS5jb2RlID09PSAyMDApIHtcclxuICAgICAgICBzZXRTZWFyY2hlZFRlYWNoZXIocmVzcG9uc2UuZGF0YS5kYXRhKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zdCB7IEdldE5vdGlmaWNhdGlvbiB9ID0gYXdhaXQgaW1wb3J0KCdsb2dpYy1jb21tb24vZGlzdC9jb21wb25lbnRzL05vdGlmaWNhdGlvbicpO1xyXG4gICAgICAgIGNvbnN0IG5vdGlmaWNhdGlvbiA9IEdldE5vdGlmaWNhdGlvbigpO1xyXG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihyZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgJ+aQnOe0ouaVmeW4iOWksei0pScpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+aQnOe0ouaVmeW4iOWksei0pTonLCBlcnJvcik7XHJcbiAgICAgIGNvbnN0IHsgR2V0Tm90aWZpY2F0aW9uIH0gPSBhd2FpdCBpbXBvcnQoJ2xvZ2ljLWNvbW1vbi9kaXN0L2NvbXBvbmVudHMvTm90aWZpY2F0aW9uJyk7XHJcbiAgICAgIGNvbnN0IG5vdGlmaWNhdGlvbiA9IEdldE5vdGlmaWNhdGlvbigpO1xyXG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ+aQnOe0ouaVmeW4iOWksei0pScpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOi9rOiuqeePree6p1xyXG4gIGNvbnN0IGhhbmRsZVRyYW5zZmVyQ2xhc3MgPSBhc3luYyAodmFsdWVzOiB7IHRyYW5zZmVyVHlwZTogJ3NlYXJjaCcgfCAnYXNzaXN0YW50JyB8ICdyZW1vdmUnOyBwaG9uZT86IHN0cmluZyB9KSA9PiB7XHJcbiAgICBjb25zdCB7IEdldE5vdGlmaWNhdGlvbiB9ID0gYXdhaXQgaW1wb3J0KCdsb2dpYy1jb21tb24vZGlzdC9jb21wb25lbnRzL05vdGlmaWNhdGlvbicpO1xyXG4gICAgY29uc3Qgbm90aWZpY2F0aW9uID0gR2V0Tm90aWZpY2F0aW9uKCk7XHJcblxyXG4gICAgc2V0VHJhbnNmZXJMb2FkaW5nKHRydWUpO1xyXG4gICAgdHJ5IHtcclxuICAgICAgbGV0IG5ld1RlYWNoZXJJZDogbnVtYmVyO1xyXG5cclxuICAgICAgaWYgKHZhbHVlcy50cmFuc2ZlclR5cGUgPT09ICdzZWFyY2gnKSB7XHJcbiAgICAgICAgaWYgKCFzZWFyY2hlZFRlYWNoZXIpIHtcclxuICAgICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign6K+35YWI5pCc57Si5bm26YCJ5oup5pWZ5biIJyk7XHJcbiAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG4gICAgICAgIG5ld1RlYWNoZXJJZCA9IHNlYXJjaGVkVGVhY2hlci5pZDtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICAvLyDmo4Dmn6XmmK/lkKbmnInljY/liqnmlZnluIhcclxuICAgICAgICBpZiAoIWNsYXNzSW5mby5hc3Npc3RhbnRUZWFjaGVySWQpIHtcclxuICAgICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign6K+l54+t57qn5rKh5pyJ5Y2P5Yqp5pWZ5biIJyk7XHJcbiAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG4gICAgICAgIG5ld1RlYWNoZXJJZCA9IGNsYXNzSW5mby5hc3Npc3RhbnRUZWFjaGVySWQ7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgY2xhc3NBcGkudHJhbnNmZXJDbGFzcyhjbGFzc0luZm8uaWQsIG5ld1RlYWNoZXJJZCwgdmFsdWVzLnRyYW5zZmVyVHlwZSk7XHJcblxyXG4gICAgICBpZiAocmVzcG9uc2UuZGF0YS5jb2RlID09PSAyMDApIHtcclxuICAgICAgICBub3RpZmljYXRpb24uc3VjY2Vzcygn6L2s6K6p54+t57qn5oiQ5YqfJyk7XHJcbiAgICAgICAgc2V0SXNUcmFuc2ZlckNsYXNzTW9kYWxWaXNpYmxlKGZhbHNlKTtcclxuICAgICAgICBzZXRTZWFyY2hlZFRlYWNoZXIobnVsbCk7XHJcblxyXG4gICAgICAgIC8vIOi9rOiuqeaIkOWKn+WQjui/lOWbnuePree6p+euoeeQhumhtemdolxyXG4gICAgICAgIG9uQmFjaygpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihyZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgJ+i9rOiuqeePree6p+Wksei0pScpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+i9rOiuqeePree6p+Wksei0pTonLCBlcnJvcik7XHJcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAn6L2s6K6p54+t57qn5aSx6LSlJyk7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRUcmFuc2ZlckxvYWRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOenu+WHuuWNj+WKqeaVmeW4iFxyXG4gIGNvbnN0IGhhbmRsZVJlbW92ZUFzc2lzdGFudCA9IGFzeW5jICgpID0+IHtcclxuICAgIGNvbnN0IHsgR2V0Tm90aWZpY2F0aW9uIH0gPSBhd2FpdCBpbXBvcnQoJ2xvZ2ljLWNvbW1vbi9kaXN0L2NvbXBvbmVudHMvTm90aWZpY2F0aW9uJyk7XHJcbiAgICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICAvLyDov5nph4zpnIDopoHosIPnlKjnp7vlh7rljY/liqnmlZnluIjnmoRBUElcclxuICAgICAgLy8g5pqC5pe25L2/55So6L2s6K6pQVBJ77yM5bCGYXNzaXN0YW50VGVhY2hlcklk6K6+5Li6MFxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNsYXNzQXBpLnVwZGF0ZUNsYXNzKGNsYXNzSW5mby5pZCwge1xyXG4gICAgICAgIGFzc2lzdGFudFRlYWNoZXJJZDogMFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGlmIChyZXNwb25zZS5kYXRhLmNvZGUgPT09IDIwMCkge1xyXG4gICAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKCfnp7vlh7rljY/liqnmlZnluIjmiJDlip8nKTtcclxuICAgICAgICBzZXRJc1RyYW5zZmVyQ2xhc3NNb2RhbFZpc2libGUoZmFsc2UpO1xyXG5cclxuICAgICAgICAvLyDmm7TmlrDmnKzlnLDnj63nuqfkv6Hmga9cclxuICAgICAgICBjb25zdCB1cGRhdGVkQ2xhc3NJbmZvID0ge1xyXG4gICAgICAgICAgLi4uY2xhc3NJbmZvLFxyXG4gICAgICAgICAgYXNzaXN0YW50VGVhY2hlcklkOiAwXHJcbiAgICAgICAgfTtcclxuICAgICAgICBzZXRDbGFzc0luZm8odXBkYXRlZENsYXNzSW5mbyk7XHJcbiAgICAgICAgb25DbGFzc0luZm9VcGRhdGU/Lih1cGRhdGVkQ2xhc3NJbmZvKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IocmVzcG9uc2UuZGF0YS5tZXNzYWdlIHx8ICfnp7vlh7rljY/liqnmlZnluIjlpLHotKUnKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfnp7vlh7rljY/liqnmlZnluIjlpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ+enu+WHuuWNj+WKqeaVmeW4iOWksei0pScpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOeUn+aIkOmCgOivt+eggVxyXG4gIGNvbnN0IGhhbmRsZUdlbmVyYXRlSW52aXRlQ29kZSA9IGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgY2xhc3NBcGkuZ2VuZXJhdGVJbnZpdGVDb2RlKGNsYXNzSW5mby5pZCk7XHJcblxyXG4gICAgICBpZiAocmVzcG9uc2UuZGF0YS5jb2RlID09PSAyMDApIHtcclxuICAgICAgICBzZXRJbnZpdGVDb2RlKHJlc3BvbnNlLmRhdGEuZGF0YS5pbnZpdGVDb2RlKTtcclxuICAgICAgICBzZXRJc0ludml0ZUNvZGVNb2RhbFZpc2libGUodHJ1ZSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcign55Sf5oiQ6YKA6K+356CB5aSx6LSlOicsIHJlc3BvbnNlLmRhdGEubWVzc2FnZSk7XHJcbiAgICAgICAgYWxlcnQocmVzcG9uc2UuZGF0YS5tZXNzYWdlIHx8ICfnlJ/miJDpgoDor7fnoIHlpLHotKUnKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign55Sf5oiQ6YKA6K+356CB5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgYWxlcnQoJ+eUn+aIkOmCgOivt+eggeWksei0pe+8jOivt+eojeWQjumHjeivlScpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOWIhumFjeenr+acqO+8iOaJuemHj+aIlumAieS4reWtpueUn++8iVxyXG4gIGNvbnN0IGhhbmRsZUFzc2lnbkJsb2NrcyA9IGFzeW5jICgpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKCc9PT0g5YiG6YWN56ev5pyo5byA5aeLID09PScpO1xyXG4gICAgY29uc29sZS5sb2coJ3NlbGVjdGVkU3R1ZGVudElkczonLCBzZWxlY3RlZFN0dWRlbnRJZHMpO1xyXG4gICAgY29uc29sZS5sb2coJ3NlbGVjdGVkU3R1ZGVudElkcy5sZW5ndGg6Jywgc2VsZWN0ZWRTdHVkZW50SWRzLmxlbmd0aCk7XHJcblxyXG4gICAgLy8g5aaC5p6c5pyJ6YCJ5Lit55qE5a2m55Sf77yM6K6+572u5Li65Y2V5Liq5a2m55Sf5YiG6YWN5qih5byPXHJcbiAgICBpZiAoc2VsZWN0ZWRTdHVkZW50SWRzLmxlbmd0aCA9PT0gMSkge1xyXG4gICAgICBjb25zb2xlLmxvZygn5Y2V5Liq5a2m55Sf5YiG6YWN5qih5byP77yMc3R1ZGVudElkOicsIHNlbGVjdGVkU3R1ZGVudElkc1swXSk7XHJcbiAgICAgIHNldFNlbGVjdGVkU3R1ZGVudElkKHNlbGVjdGVkU3R1ZGVudElkc1swXSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBjb25zb2xlLmxvZygn5om56YeP5YiG6YWN5qih5byP77yM5a2m55Sf5pWw6YePOicsIHNlbGVjdGVkU3R1ZGVudElkcy5sZW5ndGgpO1xyXG4gICAgICBzZXRTZWxlY3RlZFN0dWRlbnRJZChudWxsKTtcclxuICAgIH1cclxuXHJcbiAgICBhd2FpdCBmZXRjaFRlbXBsYXRlcygpO1xyXG4gICAgYXdhaXQgZmV0Y2hUZW1wbGF0ZVVzYWdlKCk7XHJcbiAgICBzZXRJc0Fzc2lnbkJsb2Nrc01vZGFsVmlzaWJsZSh0cnVlKTtcclxuICB9O1xyXG5cclxuICAvLyDljZXni6zkuLrlrabnlJ/liIbphY3np6/mnKhcclxuICBjb25zdCBoYW5kbGVJbmRpdmlkdWFsQXNzaWduQmxvY2tzID0gYXN5bmMgKHN0dWRlbnRJZDogbnVtYmVyKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZygnPT09IOWNleeLrOS4uuWtpueUn+WIhumFjeenr+acqCA9PT0nKTtcclxuICAgIGNvbnNvbGUubG9nKCdzdHVkZW50SWQ6Jywgc3R1ZGVudElkKTtcclxuXHJcbiAgICAvLyDorr7nva7kuLrljZXkuKrlrabnlJ/liIbphY3mqKHlvI9cclxuICAgIHNldFNlbGVjdGVkU3R1ZGVudElkKHN0dWRlbnRJZCk7XHJcbiAgICBzZXRTZWxlY3RlZFN0dWRlbnRJZHMoW10pOyAvLyDmuIXnqbrmibnph4/pgInmi6lcclxuXHJcbiAgICBhd2FpdCBmZXRjaFRlbXBsYXRlcygpO1xyXG4gICAgYXdhaXQgZmV0Y2hUZW1wbGF0ZVVzYWdlKCk7XHJcbiAgICBzZXRJc0Fzc2lnbkJsb2Nrc01vZGFsVmlzaWJsZSh0cnVlKTtcclxuICB9O1xyXG5cclxuICAvLyDlpITnkIbmqKHmnb/liIbphY3vvIjku47mqKHmnb/ljaHniYfngrnlh7vvvIlcclxuICBjb25zdCBoYW5kbGVUZW1wbGF0ZUFzc2lnbm1lbnQgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoIXNlbGVjdGVkU3R1ZGVudCkgcmV0dXJuO1xyXG5cclxuICAgIGNvbnNvbGUubG9nKCc9PT0g5LuO5qih5p2/5Y2h54mH5YiG6YWN5qih5p2/ID09PScpO1xyXG4gICAgY29uc29sZS5sb2coJ3NlbGVjdGVkU3R1ZGVudDonLCBzZWxlY3RlZFN0dWRlbnQpO1xyXG5cclxuICAgIGF3YWl0IGhhbmRsZUluZGl2aWR1YWxBc3NpZ25CbG9ja3Moc2VsZWN0ZWRTdHVkZW50LnVzZXJJZCk7XHJcbiAgfTtcclxuXHJcbiAgLy8g5Yig6Zmk54+t57qnXHJcbiAgY29uc3QgaGFuZGxlRGVsZXRlQ2xhc3MgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBjb25zdCB7IE1vZGFsIH0gPSBhd2FpdCBpbXBvcnQoJ2FudGQnKTtcclxuICAgIGNvbnN0IHsgR2V0Tm90aWZpY2F0aW9uIH0gPSBhd2FpdCBpbXBvcnQoJ2xvZ2ljLWNvbW1vbi9kaXN0L2NvbXBvbmVudHMvTm90aWZpY2F0aW9uJyk7XHJcbiAgICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcclxuXHJcbiAgICAvLyDlhYjmo4Dmn6XmmK/lkKbmnInlrabnlJ9cclxuICAgIGlmIChzdHVkZW50cy5sZW5ndGggPiAwKSB7XHJcbiAgICAgIE1vZGFsLndhcm5pbmcoe1xyXG4gICAgICAgIHRpdGxlOiAn5peg5rOV5Yig6Zmk54+t57qnJyxcclxuICAgICAgICBjZW50ZXJlZDogdHJ1ZSxcclxuICAgICAgICBjb250ZW50OiAoXHJcbiAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICA8cD7nj63nuqfkuK3ov5jmnIkge3N0dWRlbnRzLmxlbmd0aH0g5ZCN5a2m55Sf77yM6K+35YWI56e76Zmk5omA5pyJ5a2m55Sf5ZCO5YaN5Yig6Zmk54+t57qn44CCPC9wPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgdGV4dC1ncmF5LTUwMCB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgPHA+5Yig6Zmk5q2l6aqk77yaPC9wPlxyXG4gICAgICAgICAgICAgIDxvbCBjbGFzc05hbWU9XCJsaXN0LWRlY2ltYWwgbWwtNCBtdC0yXCI+XHJcbiAgICAgICAgICAgICAgICA8bGk+6YCJ5oup6KaB56e76Zmk55qE5a2m55SfPC9saT5cclxuICAgICAgICAgICAgICAgIDxsaT7kvb/nlKjmibnph4/mk43kvZznp7vpmaTmiYDmnInlrabnlJ88L2xpPlxyXG4gICAgICAgICAgICAgICAgPGxpPuWGjeasoeWwneivleWIoOmZpOePree6pzwvbGk+XHJcbiAgICAgICAgICAgICAgPC9vbD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApLFxyXG4gICAgICAgIG9rVGV4dDogJ+efpemBk+S6hidcclxuICAgICAgfSk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICAvLyDlpoLmnpzmsqHmnInlrabnlJ/vvIzmmL7npLrliKDpmaTnoa7orqTlr7nor53moYZcclxuICAgIE1vZGFsLmNvbmZpcm0oe1xyXG4gICAgICB0aXRsZTogJ+ehruiupOWIoOmZpOePree6pycsXHJcbiAgICAgIGNvbnRlbnQ6IChcclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPHA+5oKo56Gu5a6a6KaB5Yig6ZmkIDxzdHJvbmc+e2NsYXNzSW5mby5jbGFzc05hbWV9PC9zdHJvbmc+IOWQl++8nzwvcD5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMyBwLTMgYmctcmVkLTUwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1yZWQtMjAwXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC1yZWQtNjAwXCI+XHJcbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cclxuICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNOC4yNTcgMy4wOTljLjc2NS0xLjM2IDIuNzIyLTEuMzYgMy40ODYgMGw1LjU4IDkuOTJjLjc1IDEuMzM0LS4yMTMgMi45OC0xLjc0MiAyLjk4SDQuNDJjLTEuNTMgMC0yLjQ5My0xLjY0Ni0xLjc0My0yLjk4bDUuNTgtOS45MnpNMTEgMTNhMSAxIDAgMTEtMiAwIDEgMSAwIDAxMiAwem0tMS04YTEgMSAwIDAwLTEgMXYzYTEgMSAwIDAwMiAwVjZhMSAxIDAgMDAtMS0xelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XHJcbiAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj7mraTmk43kvZzkuI3lj6/mgaLlpI3vvIE8L3NwYW4+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNTAwXCI+XHJcbiAgICAgICAgICAgICAg5Yig6Zmk54+t57qn5bCG5rC45LmF56e76Zmk54+t57qn5L+h5oGv77yM5YyF5ous54+t57qn6K6+572u44CB5qih5p2/6YWN572u562J5pWw5o2u44CCXHJcbiAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApLFxyXG4gICAgICBva1RleHQ6ICfnoa7lrprliKDpmaQnLFxyXG4gICAgICBjYW5jZWxUZXh0OiAn5Y+W5raIJyxcclxuICAgICAgb2tCdXR0b25Qcm9wczogeyBkYW5nZXI6IHRydWUgfSxcclxuICAgICAgY2VudGVyZWQ6IHRydWUsXHJcbiAgICAgIG9uT2s6IGFzeW5jICgpID0+IHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjbGFzc0FwaS5kZWxldGVDbGFzcyhjbGFzc0luZm8uaWQpO1xyXG5cclxuICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmNvZGUgPT09IDIwMCkge1xyXG4gICAgICAgICAgICBub3RpZmljYXRpb24uc3VjY2Vzcygn5Yig6Zmk54+t57qn5oiQ5YqfJyk7XHJcbiAgICAgICAgICAgIC8vIOmAmuefpeeItue7hOS7tuePree6p+W3suiiq+WIoOmZpFxyXG4gICAgICAgICAgICBvbkNsYXNzRGVsZXRlZD8uKGNsYXNzSW5mby5pZCk7XHJcbiAgICAgICAgICAgIC8vIOi/lOWbnuWIsOePree6p+euoeeQhumhtemdolxyXG4gICAgICAgICAgICBvbkJhY2soKTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihyZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgJ+WIoOmZpOePree6p+Wksei0pScpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIoOmZpOePree6p+Wksei0pTonLCBlcnJvcik7XHJcbiAgICAgICAgICBpZiAoZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UpIHtcclxuICAgICAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKGVycm9yLnJlc3BvbnNlLmRhdGEubWVzc2FnZSk7XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+WIoOmZpOePree6p+Wksei0pe+8jOivt+eojeWQjumHjeivlScpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfSk7XHJcbiAgfTtcclxuXHJcbiAgLy8g5LiL5ouJ6I+c5Y2V6aG55aSE55CG5Ye95pWwXHJcbiAgY29uc3QgaGFuZGxlTWVudUl0ZW1DbGljayA9IChhY3Rpb246IHN0cmluZykgPT4ge1xyXG4gICAgLy8gc2V0SXNNb3JlQWN0aW9uc0Ryb3Bkb3duT3BlbihmYWxzZSk7IC8vIOW3suenu+mZpFxyXG5cclxuICAgIHN3aXRjaCAoYWN0aW9uKSB7XHJcbiAgICAgIGNhc2UgJ2VkaXQnOlxyXG4gICAgICAgIHNldElzRWRpdENsYXNzTW9kYWxWaXNpYmxlKHRydWUpO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICBjYXNlICdhZGRTdHVkZW50JzpcclxuICAgICAgICBzZXRJc0FkZFN0dWRlbnRNb2RhbFZpc2libGUodHJ1ZSk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIGNhc2UgJ2ltcG9ydFN0dWRlbnQnOlxyXG4gICAgICAgIHNldElzSW1wb3J0U3R1ZGVudE1vZGFsVmlzaWJsZSh0cnVlKTtcclxuICAgICAgICBicmVhaztcclxuICAgICAgY2FzZSAnZXhwb3J0U3R1ZGVudCc6XHJcbiAgICAgICAgaGFuZGxlRXhwb3J0U3R1ZGVudHMoKTtcclxuICAgICAgICBicmVhaztcclxuICAgICAgY2FzZSAndHJhbnNmZXInOlxyXG4gICAgICAgIHNldElzVHJhbnNmZXJDbGFzc01vZGFsVmlzaWJsZSh0cnVlKTtcclxuICAgICAgICBicmVhaztcclxuICAgICAgY2FzZSAnaW52aXRlJzpcclxuICAgICAgICBoYW5kbGVHZW5lcmF0ZUludml0ZUNvZGUoKTtcclxuICAgICAgICBicmVhaztcclxuICAgICAgY2FzZSAnYmF0Y2hSZWRlZW0nOlxyXG4gICAgICAgIGNvbnNvbGUubG9nKCfmibnph4/lhZHmjaLlr4bku6QnKTtcclxuICAgICAgICBzZXRJc0JhdGNoVXNlS2V5UGFja2FnZU1vZGFsVmlzaWJsZSh0cnVlKTtcclxuICAgICAgICBicmVhaztcclxuICAgICAgY2FzZSAnYXNzaWduQmxvY2tzJzpcclxuICAgICAgICBoYW5kbGVBc3NpZ25CbG9ja3MoKTtcclxuICAgICAgICBicmVhaztcclxuICAgICAgY2FzZSAnZGVsZXRlQ2xhc3MnOlxyXG4gICAgICAgIGhhbmRsZURlbGV0ZUNsYXNzKCk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8g6K6+572u5LiL5ouJ6I+c5Y2V6aG55aSE55CG5Ye95pWwXHJcbiAgY29uc3QgaGFuZGxlU2V0dGluZ3NNZW51SXRlbUNsaWNrID0gKGFjdGlvbjogc3RyaW5nKSA9PiB7XHJcbiAgICBzZXRJc1NldHRpbmdzRHJvcGRvd25PcGVuKGZhbHNlKTtcclxuXHJcbiAgICBzd2l0Y2ggKGFjdGlvbikge1xyXG4gICAgICBjYXNlICdlZGl0JzpcclxuICAgICAgICBzZXRJc0VkaXRDbGFzc01vZGFsVmlzaWJsZSh0cnVlKTtcclxuICAgICAgICBicmVhaztcclxuICAgICAgY2FzZSAnYWRkU3R1ZGVudCc6XHJcbiAgICAgICAgc2V0SXNBZGRTdHVkZW50TW9kYWxWaXNpYmxlKHRydWUpO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICBjYXNlICdpbXBvcnRTdHVkZW50JzpcclxuICAgICAgICBzZXRJc0ltcG9ydFN0dWRlbnRNb2RhbFZpc2libGUodHJ1ZSk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIGNhc2UgJ2V4cG9ydFN0dWRlbnQnOlxyXG4gICAgICAgIGhhbmRsZUV4cG9ydFN0dWRlbnRzKCk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIGNhc2UgJ3RyYW5zZmVyJzpcclxuICAgICAgICBzZXRJc1RyYW5zZmVyQ2xhc3NNb2RhbFZpc2libGUodHJ1ZSk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIGNhc2UgJ2ludml0ZSc6XHJcbiAgICAgICAgaGFuZGxlR2VuZXJhdGVJbnZpdGVDb2RlKCk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIGNhc2UgJ2JhdGNoUmVkZWVtJzpcclxuICAgICAgICBjb25zb2xlLmxvZygn5om56YeP5YWR5o2i5a+G5LukJyk7XHJcbiAgICAgICAgc2V0SXNCYXRjaFVzZUtleVBhY2thZ2VNb2RhbFZpc2libGUodHJ1ZSk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIGNhc2UgJ2Fzc2lnbkJsb2Nrcyc6XHJcbiAgICAgICAgaGFuZGxlQXNzaWduQmxvY2tzKCk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIGNhc2UgJ2RlbGV0ZUNsYXNzJzpcclxuICAgICAgICBoYW5kbGVEZWxldGVDbGFzcygpO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICBkZWZhdWx0OlxyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOehruS/nemAieS4reeKtuaAgeWQjOatpVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBzaG91bGRCZVNlbGVjdEFsbCA9IHNlbGVjdGVkU3R1ZGVudElkcy5sZW5ndGggPiAwICYmIHNlbGVjdGVkU3R1ZGVudElkcy5sZW5ndGggPT09IHN0dWRlbnRzLmxlbmd0aDtcclxuICAgIGlmIChpc1NlbGVjdEFsbCAhPT0gc2hvdWxkQmVTZWxlY3RBbGwpIHtcclxuICAgICAgY29uc29sZS5sb2coJ+S/ruWkjemAieS4reeKtuaAgeWQjOatpTonLCB7XHJcbiAgICAgICAgaXNTZWxlY3RBbGwsXHJcbiAgICAgICAgc2hvdWxkQmVTZWxlY3RBbGwsXHJcbiAgICAgICAgc2VsZWN0ZWRTdHVkZW50SWRzOiBzZWxlY3RlZFN0dWRlbnRJZHMubGVuZ3RoLFxyXG4gICAgICAgIHRvdGFsU3R1ZGVudHM6IHN0dWRlbnRzLmxlbmd0aFxyXG4gICAgICB9KTtcclxuICAgICAgc2V0SXNTZWxlY3RBbGwoc2hvdWxkQmVTZWxlY3RBbGwpO1xyXG4gICAgfVxyXG4gIH0sIFtzZWxlY3RlZFN0dWRlbnRJZHMsIHN0dWRlbnRzLmxlbmd0aCwgaXNTZWxlY3RBbGxdKTtcclxuXHJcbiAgLy8g5YWo6YCJL+WPlua2iOWFqOmAieWkhOeQhuWHveaVsFxyXG4gIGNvbnN0IGhhbmRsZVNlbGVjdEFsbCA9ICgpID0+IHtcclxuICAgIGlmIChpc1NlbGVjdEFsbCkge1xyXG4gICAgICAvLyDlj5bmtojlhajpgIlcclxuICAgICAgc2V0U2VsZWN0ZWRTdHVkZW50SWRzKFtdKTtcclxuICAgICAgc2V0SXNTZWxlY3RBbGwoZmFsc2UpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgLy8g5YWo6YCJXHJcbiAgICAgIGNvbnN0IGFsbFN0dWRlbnRJZHMgPSBzdHVkZW50cy5tYXAoc3R1ZGVudCA9PiBzdHVkZW50LnVzZXJJZCk7XHJcbiAgICAgIHNldFNlbGVjdGVkU3R1ZGVudElkcyhhbGxTdHVkZW50SWRzKTtcclxuICAgICAgc2V0SXNTZWxlY3RBbGwodHJ1ZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8g5Y2V5Liq5a2m55Sf6YCJ5oup5aSE55CG5Ye95pWwXHJcbiAgY29uc3QgaGFuZGxlU3R1ZGVudFNlbGVjdCA9IChzdHVkZW50SWQ6IG51bWJlcikgPT4ge1xyXG4gICAgaWYgKHNlbGVjdGVkU3R1ZGVudElkcy5pbmNsdWRlcyhzdHVkZW50SWQpKSB7XHJcbiAgICAgIC8vIOWPlua2iOmAieaLqVxyXG4gICAgICBjb25zdCBuZXdTZWxlY3RlZElkcyA9IHNlbGVjdGVkU3R1ZGVudElkcy5maWx0ZXIoaWQgPT4gaWQgIT09IHN0dWRlbnRJZCk7XHJcbiAgICAgIHNldFNlbGVjdGVkU3R1ZGVudElkcyhuZXdTZWxlY3RlZElkcyk7XHJcbiAgICAgIHNldElzU2VsZWN0QWxsKGZhbHNlKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIC8vIOmAieaLqVxyXG4gICAgICBjb25zdCBuZXdTZWxlY3RlZElkcyA9IFsuLi5zZWxlY3RlZFN0dWRlbnRJZHMsIHN0dWRlbnRJZF07XHJcbiAgICAgIHNldFNlbGVjdGVkU3R1ZGVudElkcyhuZXdTZWxlY3RlZElkcyk7XHJcbiAgICAgIC8vIOajgOafpeaYr+WQpuWFqOmAiVxyXG4gICAgICBpZiAobmV3U2VsZWN0ZWRJZHMubGVuZ3RoID09PSBzdHVkZW50cy5sZW5ndGgpIHtcclxuICAgICAgICBzZXRJc1NlbGVjdEFsbCh0cnVlKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOaJuemHj+aTjeS9nOWkhOeQhuWHveaVsFxyXG4gIGNvbnN0IGhhbmRsZUJhdGNoQWN0aW9uID0gKGFjdGlvbjogc3RyaW5nKSA9PiB7XHJcbiAgICBzZXRJc0JhdGNoQWN0aW9uc0Ryb3Bkb3duT3BlbihmYWxzZSk7XHJcblxyXG4gICAgaWYgKHNlbGVjdGVkU3R1ZGVudElkcy5sZW5ndGggPT09IDApIHtcclxuICAgICAgaW1wb3J0KCdsb2dpYy1jb21tb24vZGlzdC9jb21wb25lbnRzL05vdGlmaWNhdGlvbicpLnRoZW4oKHsgR2V0Tm90aWZpY2F0aW9uIH0pID0+IHtcclxuICAgICAgICBHZXROb3RpZmljYXRpb24oKS53YXJuaW5nKCfor7flhYjpgInmi6nopoHmk43kvZznmoTlrabnlJ8nKTtcclxuICAgICAgfSk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICBzd2l0Y2ggKGFjdGlvbikge1xyXG4gICAgICBjYXNlICdiYXRjaERlbGV0ZSc6XHJcbiAgICAgICAgaGFuZGxlQmF0Y2hSZW1vdmVTdHVkZW50cyhzZWxlY3RlZFN0dWRlbnRJZHMpO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICBjYXNlICdiYXRjaEFzc2lnbkJsb2Nrcyc6XHJcbiAgICAgICAgaGFuZGxlQXNzaWduQmxvY2tzKCk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIGNhc2UgJ2JhdGNoQXNzaWduUG9pbnRzJzpcclxuICAgICAgICAvLyDmuIXpmaTljZXkuKrlrabnlJ/pgInmi6nvvIznoa7kv53ov5vlhaXmibnph4/mqKHlvI9cclxuICAgICAgICBzZXRTZWxlY3RlZFN0dWRlbnQobnVsbCk7XHJcbiAgICAgICAgc2V0SXNBc3NpZ25Qb2ludHNNb2RhbFZpc2libGUodHJ1ZSk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIGNhc2UgJ2JhdGNoVXNlS2V5UGFja2FnZSc6XHJcbiAgICAgICAgaGFuZGxlQmF0Y2hVc2VLZXlQYWNrYWdlKCk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIGNhc2UgJ2JhdGNoRXhwb3J0JzpcclxuICAgICAgICBjb25zb2xlLmxvZygn5om56YeP5a+85Ye65a2m55SfOicsIHNlbGVjdGVkU3R1ZGVudElkcyk7XHJcbiAgICAgICAgaW1wb3J0KCdsb2dpYy1jb21tb24vZGlzdC9jb21wb25lbnRzL05vdGlmaWNhdGlvbicpLnRoZW4oKHsgR2V0Tm90aWZpY2F0aW9uIH0pID0+IHtcclxuICAgICAgICAgIEdldE5vdGlmaWNhdGlvbigpLmluZm8oJ+aJuemHj+WvvOWHuuWtpueUn+WKn+iDveato+WcqOW8gOWPkeS4rScpO1xyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICBkZWZhdWx0OlxyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOaJuemHj+enu+WHuuePree6p+eahOaUuei/m+eJiOacrFxyXG4gIGNvbnN0IGhhbmRsZUJhdGNoUmVtb3ZlU3R1ZGVudHMgPSBhc3luYyAoc3R1ZGVudElkczogbnVtYmVyW10pID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIOiOt+WPlumAieS4reeahOWtpueUn+S/oeaBr1xyXG4gICAgICBjb25zdCBzZWxlY3RlZFN0dWRlbnRzSW5mbyA9IHN0dWRlbnRzLmZpbHRlcihzID0+IHN0dWRlbnRJZHMuaW5jbHVkZXMocy51c2VySWQpKTtcclxuXHJcbiAgICAgIC8vIOiuoeeul+aAu+WPr+eUqOenr+WIhlxyXG4gICAgICBjb25zdCB0b3RhbEF2YWlsYWJsZVBvaW50cyA9IHNlbGVjdGVkU3R1ZGVudHNJbmZvLnJlZHVjZShcclxuICAgICAgICAoc3VtLCBzdHVkZW50KSA9PiBzdW0gKyAoc3R1ZGVudC5hdmFpbGFibGVQb2ludHMgfHwgMCksXHJcbiAgICAgICAgMFxyXG4gICAgICApO1xyXG5cclxuICAgICAgY29uc3QgeyBNb2RhbCB9ID0gYXdhaXQgaW1wb3J0KCdhbnRkJyk7XHJcbiAgICAgIGNvbnN0IHsgSW5mb0NpcmNsZU91dGxpbmVkIH0gPSBhd2FpdCBpbXBvcnQoJ0BhbnQtZGVzaWduL2ljb25zJyk7XHJcblxyXG4gICAgICBNb2RhbC5jb25maXJtKHtcclxuICAgICAgICB0aXRsZTogJ+ehruiupOaJuemHj+enu+WHuuePree6pycsXHJcbiAgICAgICAgY29udGVudDogKFxyXG4gICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgPHA+56Gu5a6a6KaB5bCG6YCJ5Lit55qEIHtzdHVkZW50SWRzLmxlbmd0aH0g5ZCN5a2m55Sf56e75Ye654+t57qn5ZCX77yfPC9wPlxyXG4gICAgICAgICAgICB7dG90YWxBdmFpbGFibGVQb2ludHMgPiAwICYmIChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTIgcC0zIGJnLXllbGxvdy01MCByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQteWVsbG93LTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgICA8SW5mb0NpcmNsZU91dGxpbmVkIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuPumAieS4reeahOWtpueUn+WFseaciSB7dG90YWxBdmFpbGFibGVQb2ludHN9IOeCueWPr+eUqOiDvemHjzwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQteWVsbG93LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICDnp7vlh7rnj63nuqflkI7vvIzlj6/nlKjog73ph4/lsIbov5Tov5jliLDlkIToh6rnmoTlpZfppJDnp6/liIbkuK1cclxuICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICksXHJcbiAgICAgICAgb2tUZXh0OiAn56Gu5a6a56e75Ye6JyxcclxuICAgICAgICBjYW5jZWxUZXh0OiAn5Y+W5raIJyxcclxuICAgICAgICBjZW50ZXJlZDogdHJ1ZSxcclxuICAgICAgICBva0J1dHRvblByb3BzOiB7IGRhbmdlcjogdHJ1ZSB9LFxyXG4gICAgICAgIG9uT2s6IGFzeW5jICgpID0+IHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHsgR2V0Tm90aWZpY2F0aW9uIH0gPSBhd2FpdCBpbXBvcnQoJ2xvZ2ljLWNvbW1vbi9kaXN0L2NvbXBvbmVudHMvTm90aWZpY2F0aW9uJyk7XHJcbiAgICAgICAgICAgIGNvbnN0IG5vdGlmaWNhdGlvbiA9IEdldE5vdGlmaWNhdGlvbigpO1xyXG4gICAgICAgICAgICBjb25zdCBoaWRlTG9hZGluZyA9IG5vdGlmaWNhdGlvbi5sb2FkaW5nKCfmraPlnKjnp7vlh7rlrabnlJ8uLi4nKTtcclxuXHJcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgY2xhc3NBcGkucmVtb3ZlU3R1ZGVudEZyb21DbGFzcyhzdHVkZW50SWRzKTtcclxuXHJcbiAgICAgICAgICAgIGlmIChoaWRlTG9hZGluZykge1xyXG4gICAgICAgICAgICAgIGhpZGVMb2FkaW5nLmNsb3NlKCk7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmNvZGUgPT09IDIwMCkge1xyXG4gICAgICAgICAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKGDmiJDlip/np7vlh7ogJHtzdHVkZW50SWRzLmxlbmd0aH0g5ZCN5a2m55SfYCk7XHJcblxyXG4gICAgICAgICAgICAgIC8vIOa4hemZpOmAieaLqeeKtuaAgVxyXG4gICAgICAgICAgICAgIHNldFNlbGVjdGVkU3R1ZGVudElkcyhbXSk7XHJcbiAgICAgICAgICAgICAgc2V0SXNTZWxlY3RBbGwoZmFsc2UpO1xyXG4gICAgICAgICAgICAgIHNldFNlbGVjdGVkU3R1ZGVudChudWxsKTtcclxuXHJcbiAgICAgICAgICAgICAgLy8g6YeN5paw6I635Y+W5a2m55Sf5YiX6KGoXHJcbiAgICAgICAgICAgICAgYXdhaXQgZmV0Y2hTdHVkZW50cygpO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihyZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgJ+aJuemHj+enu+WHuuWtpueUn+Wksei0pScpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHsgR2V0Tm90aWZpY2F0aW9uIH0gPSBhd2FpdCBpbXBvcnQoJ2xvZ2ljLWNvbW1vbi9kaXN0L2NvbXBvbmVudHMvTm90aWZpY2F0aW9uJyk7XHJcbiAgICAgICAgICAgIGNvbnN0IG5vdGlmaWNhdGlvbiA9IEdldE5vdGlmaWNhdGlvbigpO1xyXG4gICAgICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ+aJuemHj+enu+WHuuWtpueUn+Wksei0pScpO1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfmibnph4/np7vlh7rlrabnlJ/lpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfmibnph4/np7vlh7rlrabnlJ/lpLHotKU6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOWkhOeQhua3u+WKoOWtpueUn1xyXG4gIGNvbnN0IGhhbmRsZUFkZFN0dWRlbnQgPSBhc3luYyAodmFsdWVzOiB7IHN0dWRlbnROdW1iZXI6IHN0cmluZzsgbmlja05hbWU6IHN0cmluZyB9KSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zb2xlLmxvZygn5re75Yqg5a2m55SfOicsIHZhbHVlcyk7XHJcblxyXG4gICAgICAvLyDmt7vliqDpu5jorqTlr4bnoIFcclxuICAgICAgY29uc3Qgc3R1ZGVudERhdGEgPSB7XHJcbiAgICAgICAgLi4udmFsdWVzLFxyXG4gICAgICAgIHBhc3N3b3JkOiAnMTIzNDU2J1xyXG4gICAgICB9O1xyXG5cclxuICAgICAgLy8g6LCD55So5re75Yqg5a2m55Sf55qEQVBJXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgY2xhc3NBcGkuYWRkU3R1ZGVudFRvQ2xhc3MoY2xhc3NJbmZvLmlkLCBzdHVkZW50RGF0YSk7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZygn5re75Yqg5a2m55SfIEFQSSDlk43lupQ6JywgcmVzcG9uc2UuZGF0YSk7XHJcblxyXG4gICAgICAvLyDmo4Dmn6Xlk43lupTnirbmgIFcclxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuY29kZSA9PT0gMjAwKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ+a3u+WKoOWtpueUn+aIkOWKnycpO1xyXG4gICAgICAgIHNldElzQWRkU3R1ZGVudE1vZGFsVmlzaWJsZShmYWxzZSk7XHJcbiAgICAgICAgLy8g6YeN5paw6I635Y+W5a2m55Sf5YiX6KGoXHJcbiAgICAgICAgYXdhaXQgZmV0Y2hTdHVkZW50cygpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+a3u+WKoOWtpueUn+Wksei0pTonLCByZXNwb25zZS5kYXRhLm1lc3NhZ2UpO1xyXG4gICAgICAgIGFsZXJ0KHJlc3BvbnNlLmRhdGEubWVzc2FnZSB8fCAn5re75Yqg5a2m55Sf5aSx6LSlJyk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+a3u+WKoOWtpueUn+Wksei0pTonLCBlcnJvcik7XHJcbiAgICAgIGFsZXJ0KCfmt7vliqDlrabnlJ/lpLHotKXvvIzor7fnqI3lkI7ph43or5UnKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyDlpITnkIbmlofku7bkuIrkvKBcclxuICBjb25zdCBoYW5kbGVVcGxvYWQgPSBhc3luYyAoZmlsZTogRmlsZSkgPT4ge1xyXG4gICAgc2V0VXBsb2FkaW5nKHRydWUpO1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8g6L+Z6YeM5Y+v5Lul5re75Yqg5paH5Lu25LiK5Lyg6YC76L6RXHJcbiAgICAgIGNvbnNvbGUubG9nKCfkuIrkvKDmlofku7Y6JywgZmlsZSk7XHJcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUgfTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+aWh+S7tuS4iuS8oOWksei0pTonLCBlcnJvcik7XHJcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlIH07XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRVcGxvYWRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOWkhOeQhuaWh+S7tuWIoOmZpFxyXG4gIGNvbnN0IGhhbmRsZVJlbW92ZUZpbGUgPSBhc3luYyAoZmlsZTogYW55KSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRGaWxlTGlzdChwcmV2ID0+IHByZXYuZmlsdGVyKGYgPT4gZi51aWQgIT09IGZpbGUudWlkKSk7XHJcbiAgICAgIHJldHVybiB0cnVlO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign5Yig6Zmk5paH5Lu25aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOWkhOeQhuWPkeW4g+S7u+WKoVxyXG4gIGNvbnN0IGhhbmRsZVB1Ymxpc2hUYXNrID0gYXN5bmMgKHZhbHVlczogYW55KSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zb2xlLmxvZygn5Y+R5biD5Lu75YqhOicsIHZhbHVlcyk7XHJcblxyXG4gICAgICAvLyDlr7zlhaV0YXNrQXBpXHJcbiAgICAgIGNvbnN0IHsgZGVmYXVsdDogdGFza0FwaSB9ID0gYXdhaXQgaW1wb3J0KCcuLi8uLi8uLi9saWIvYXBpL3Rhc2snKTtcclxuICAgICAgY29uc3QgeyBHZXROb3RpZmljYXRpb24gfSA9IGF3YWl0IGltcG9ydCgnbG9naWMtY29tbW9uL2Rpc3QvY29tcG9uZW50cy9Ob3RpZmljYXRpb24nKTtcclxuICAgICAgY29uc3Qgbm90aWZpY2F0aW9uID0gR2V0Tm90aWZpY2F0aW9uKCk7XHJcblxyXG4gICAgICAvLyDmnoTlu7rku7vliqHlj4LmlbBcclxuICAgICAgY29uc3QgcGFyYW1zID0ge1xyXG4gICAgICAgIHRhc2tOYW1lOiB2YWx1ZXMudGFza05hbWUsXHJcbiAgICAgICAgdGFza0Rlc2NyaXB0aW9uOiB2YWx1ZXMudGFza0Rlc2NyaXB0aW9uIHx8ICcnLFxyXG4gICAgICAgIHRhc2tUeXBlOiAxLCAvLyDpu5jorqTku7vliqHnsbvlnotcclxuICAgICAgICBzdGFydERhdGU6IHZhbHVlcy5zdGFydERhdGUgPyBuZXcgRGF0ZSh2YWx1ZXMuc3RhcnREYXRlKSA6IHVuZGVmaW5lZCxcclxuICAgICAgICBlbmREYXRlOiB2YWx1ZXMuZW5kRGF0ZSA/IG5ldyBEYXRlKHZhbHVlcy5lbmREYXRlKSA6IG5ldyBEYXRlKERhdGUubm93KCkgKyA3ICogMjQgKiA2MCAqIDYwICogMTAwMCksIC8vIOm7mOiupDflpKnlkI5cclxuICAgICAgICB0YXNrQ29udGVudDogdmFsdWVzLnRhc2tDb250ZW50IHx8ICcnLFxyXG4gICAgICAgIGF0dGFjaG1lbnRzOiBmaWxlTGlzdC5tYXAoZmlsZSA9PiBmaWxlLnVybCB8fCBmaWxlLnJlc3BvbnNlPy51cmwpLmZpbHRlcihCb29sZWFuKSxcclxuICAgICAgICB0ZWFjaGVySWQ6IHVzZXJJZCxcclxuICAgICAgICBjbGFzc0lkOiBjbGFzc0luZm8/LmlkLFxyXG4gICAgICAgIHN0dWRlbnRJZHM6IHNlbGVjdGVkU3R1ZGVudElkcy5sZW5ndGggPiAwID8gc2VsZWN0ZWRTdHVkZW50SWRzIDogc3R1ZGVudHMubWFwKHMgPT4gcy51c2VySWQpLFxyXG4gICAgICAgIHNlbGZBc3Nlc3NtZW50SXRlbXM6IHZhbHVlcy5zZWxmQXNzZXNzbWVudEl0ZW1zIHx8IFtdLFxyXG4gICAgICAgIHByaW9yaXR5OiAxLCAvLyDpu5jorqTkvJjlhYjnuqdcclxuICAgICAgICBpc1B1YmxpYzogMCwgLy8g6buY6K6k5LiN5YWs5byAXHJcbiAgICAgICAgYWxsb3dMYXRlU3VibWlzc2lvbjogdmFsdWVzLmFsbG93TGF0ZVN1Ym1pc3Npb24gfHwgZmFsc2VcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKCflj5HluIPku7vliqHlj4LmlbA6JywgcGFyYW1zKTtcclxuXHJcbiAgICAgIC8vIOiwg+eUqOWPkeW4g+S7u+WKoUFQSVxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRhc2tBcGkucHVibGlzaFRhc2socGFyYW1zKTtcclxuXHJcbiAgICAgIGlmIChyZXNwb25zZS5kYXRhLmNvZGUgPT09IDIwMCkge1xyXG4gICAgICAgIC8vIOaYvuekuuivpue7hueahOaIkOWKn+aPkOekulxyXG4gICAgICAgIGNvbnN0IHsgTW9kYWwgfSA9IGF3YWl0IGltcG9ydCgnYW50ZCcpO1xyXG4gICAgICAgIGNvbnN0IHN0dWRlbnRDb3VudCA9IHNlbGVjdGVkU3R1ZGVudElkcy5sZW5ndGggPiAwID8gc2VsZWN0ZWRTdHVkZW50SWRzLmxlbmd0aCA6IHN0dWRlbnRzLmxlbmd0aDtcclxuICAgICAgICBjb25zdCBjbGFzc05hbWUgPSBjbGFzc0luZm8/LmNsYXNzTmFtZSB8fCAn5b2T5YmN54+t57qnJztcclxuXHJcbiAgICAgICAgTW9kYWwuc3VjY2Vzcyh7XHJcbiAgICAgICAgICB0aXRsZTogJ+S7u+WKoeWPkeW4g+aIkOWKnycsXHJcbiAgICAgICAgICBjb250ZW50OiAoXHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgPHA+5Lu75YqhIDxzdHJvbmc+e3BhcmFtcy50YXNrTmFtZX08L3N0cm9uZz4g5bey5oiQ5Yqf5Y+R5biD5YiwIDxzdHJvbmc+e2NsYXNzTmFtZX08L3N0cm9uZz7jgII8L3A+XHJcbiAgICAgICAgICAgICAgPHA+5YWx5pyJIDxzdHJvbmc+e3N0dWRlbnRDb3VudH08L3N0cm9uZz4g5ZCN5a2m55Sf5bCG5pS25Yiw5q2k5Lu75Yqh44CCPC9wPlxyXG4gICAgICAgICAgICAgIDxwPuWtpueUn+WPr+S7peWcqOePree6p+epuumXtOafpeeci+WSjOaPkOS6pOS7u+WKoeOAgjwvcD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApLFxyXG4gICAgICAgICAgb2tUZXh0OiAn56Gu5a6aJyxcclxuICAgICAgICAgIG9uT2s6ICgpID0+IHtcclxuICAgICAgICAgICAgLy8g5Y+v5Lul5Zyo6L+Z6YeM5re75Yqg5a+86Iiq5Yiw5Lu75Yqh566h55CG6aG16Z2i55qE6YC76L6RXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIHNldElzUHVibGlzaFRhc2tNb2RhbFZpc2libGUoZmFsc2UpO1xyXG5cclxuICAgICAgICAvLyDmuIXnkIbooajljZXmlbDmja5cclxuICAgICAgICBzZXRGaWxlTGlzdChbXSk7XHJcbiAgICAgICAgc2V0U2VsZWN0ZWRTdHVkZW50SWRzKFtdKTtcclxuICAgICAgICBzZXRTZWxlY3RlZFRlbXBsYXRlSWQobnVsbCk7XHJcblxyXG4gICAgICAgIC8vIOWIt+aWsOWtpueUn+WIl+ihqO+8iOWmguaenOmcgOimgeaYvuekuuS7u+WKoeebuOWFs+S/oeaBr++8iVxyXG4gICAgICAgIGF3YWl0IGZldGNoU3R1ZGVudHMoKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IocmVzcG9uc2UuZGF0YS5tZXNzYWdlIHx8ICfku7vliqHlj5HluIPlpLHotKUnKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCflj5HluIPku7vliqHlpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICBjb25zdCB7IEdldE5vdGlmaWNhdGlvbiB9ID0gYXdhaXQgaW1wb3J0KCdsb2dpYy1jb21tb24vZGlzdC9jb21wb25lbnRzL05vdGlmaWNhdGlvbicpO1xyXG4gICAgICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcclxuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICflj5HluIPku7vliqHlpLHotKXvvIzor7fnqI3lkI7ph43or5UnKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyDlpITnkIbph43nva7lr4bnoIFcclxuICBjb25zdCBoYW5kbGVSZXNldFBhc3N3b3JkID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgaWYgKCFzZWxlY3RlZFN0dWRlbnQpIHtcclxuICAgICAgICBhbGVydCgn6K+35YWI6YCJ5oup6KaB6YeN572u5a+G56CB55qE5a2m55SfJyk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zb2xlLmxvZygn6YeN572u5a+G56CBOicsIHNlbGVjdGVkU3R1ZGVudCk7XHJcbiAgICAgIC8vIOi/memHjOWPr+S7pea3u+WKoOmHjee9ruWvhueggeeahEFQSeiwg+eUqFxyXG4gICAgICBzZXRJc1Jlc2V0UGFzc3dvcmRNb2RhbFZpc2libGUoZmFsc2UpO1xyXG4gICAgICBhbGVydCgn5a+G56CB6YeN572u5oiQ5Yqf77yM5paw5a+G56CB5Li677yaMTIzNDU2Jyk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfph43nva7lr4bnoIHlpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICBhbGVydCgn6YeN572u5a+G56CB5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VJyk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8g5aSE55CG6YCJ5oup5qih5p2/XHJcbiAgY29uc3QgaGFuZGxlU2VsZWN0VGVtcGxhdGUgPSBhc3luYyAodGVtcGxhdGVJZDogbnVtYmVyKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB7IGFkZFVzZXJKb2luUm9sZSwgYmF0Y2hBZGRVc2VySm9pblJvbGUgfSA9IGF3YWl0IGltcG9ydCgnLi4vLi4vLi4vbGliL2FwaS9yb2xlJyk7XHJcbiAgICAgIGNvbnN0IHsgR2V0Tm90aWZpY2F0aW9uIH0gPSBhd2FpdCBpbXBvcnQoJ2xvZ2ljLWNvbW1vbi9kaXN0L2NvbXBvbmVudHMvTm90aWZpY2F0aW9uJyk7XHJcbiAgICAgIGNvbnN0IG5vdGlmaWNhdGlvbiA9IEdldE5vdGlmaWNhdGlvbigpO1xyXG5cclxuICAgICAgLy8g56Gu5a6a6KaB5YiG6YWN55qE5a2m55SfXHJcbiAgICAgIGNvbnN0IHRhcmdldFN0dWRlbnRzID0gc2VsZWN0ZWRTdHVkZW50SWQgIT09IG51bGwgPyBbc2VsZWN0ZWRTdHVkZW50SWRdIDogc2VsZWN0ZWRTdHVkZW50SWRzO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJz09PSDmqKHmnb/liIbphY3or6bmg4UgPT09Jyk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdzZWxlY3RlZFN0dWRlbnRJZDonLCBzZWxlY3RlZFN0dWRlbnRJZCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdzZWxlY3RlZFN0dWRlbnRJZHM6Jywgc2VsZWN0ZWRTdHVkZW50SWRzKTtcclxuICAgICAgY29uc29sZS5sb2coJ3RhcmdldFN0dWRlbnRzOicsIHRhcmdldFN0dWRlbnRzKTtcclxuICAgICAgY29uc29sZS5sb2coJ3RlbXBsYXRlSWQ6JywgdGVtcGxhdGVJZCk7XHJcblxyXG4gICAgICBpZiAodGFyZ2V0U3R1ZGVudHMubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ+KdjCDmsqHmnInpgInkuK3ku7vkvZXlrabnlJ8nKTtcclxuICAgICAgICBub3RpZmljYXRpb24ud2FybmluZygn6K+35YWI6YCJ5oup5a2m55SfJyk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCBoaWRlTG9hZGluZyA9IG5vdGlmaWNhdGlvbi5sb2FkaW5nKCfmraPlnKjliIbphY3np6/mnKguLi4nKTtcclxuICAgICAgY29uc3QgdXNlclJvbGVzTWFwID0gdXNlclJvbGVzIHx8IFtdO1xyXG5cclxuICAgICAgdHJ5IHtcclxuICAgICAgICAvLyDlh4blpIfnlKjmiLfmlbDmja4gLSDkuI50ZWFjaGVyLXNwYWNl5L+d5oyB5LiA6Ie055qE6YC76L6RXHJcbiAgICAgICAgY29uc3QgdXNlcnNEYXRhID0gdGFyZ2V0U3R1ZGVudHNcclxuICAgICAgICAgIC5tYXAodXNlcklkID0+IHtcclxuICAgICAgICAgICAgY29uc3QgdXNlckluZm8gPSB1c2VyUm9sZXNNYXAuZmluZCh1ID0+IHUudXNlcklkID09PSB1c2VySWQpO1xyXG4gICAgICAgICAgICBpZiAoIXVzZXJJbmZvPy5yb2xlSWQpIHJldHVybiBudWxsO1xyXG4gICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgIHVzZXJJZDogdXNlcklkLFxyXG4gICAgICAgICAgICAgIHJvbGVJZDogdXNlckluZm8ucm9sZUlkLFxyXG4gICAgICAgICAgICAgIHRlbXBsYXRlSWQ6IHRlbXBsYXRlSWQsXHJcbiAgICAgICAgICAgICAgb3JpZ2luYWxUZW1wbGF0ZUlkOiB0ZW1wbGF0ZUlkXHJcbiAgICAgICAgICAgIH0gYXMgY29uc3Q7XHJcbiAgICAgICAgICB9KVxyXG4gICAgICAgICAgLmZpbHRlcigoaXRlbSk6IGl0ZW0gaXMgeyB1c2VySWQ6IG51bWJlcjsgcm9sZUlkOiBudW1iZXI7IHRlbXBsYXRlSWQ6IG51bWJlcjsgb3JpZ2luYWxUZW1wbGF0ZUlkOiBudW1iZXIgfSA9PiBpdGVtICE9PSBudWxsKTtcclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coJ+WHhuWkh+WIhumFjeaooeadvzonLCB7XHJcbiAgICAgICAgICB0ZW1wbGF0ZUlkLFxyXG4gICAgICAgICAgdGFyZ2V0U3R1ZGVudHMsXHJcbiAgICAgICAgICB1c2VyUm9sZXNNYXAsXHJcbiAgICAgICAgICB1c2Vyc0RhdGFcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKHVzZXJzRGF0YS5sZW5ndGggPT09IDApIHtcclxuICAgICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign5peg5pyJ5pWI55So5oi35Y+v5YiG6YWNJyk7XHJcbiAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyDliIbmibnlubblj5HlpITnkIZcclxuICAgICAgICBjb25zdCByZXN1bHRzID0gYXdhaXQgKHVzZXJzRGF0YS5sZW5ndGggPiAyMFxyXG4gICAgICAgICAgPyBQcm9taXNlLmFsbChcclxuICAgICAgICAgICAgQXJyYXkuZnJvbShcclxuICAgICAgICAgICAgICB7IGxlbmd0aDogTWF0aC5jZWlsKHVzZXJzRGF0YS5sZW5ndGggLyAyMCkgfSxcclxuICAgICAgICAgICAgICAoXywgaSkgPT4gdXNlcnNEYXRhLnNsaWNlKGkgKiAyMCwgKGkgKyAxKSAqIDIwKVxyXG4gICAgICAgICAgICApLm1hcChiYXRjaFVzZXJzID0+XHJcbiAgICAgICAgICAgICAgYmF0Y2hBZGRVc2VySm9pblJvbGUoeyB1c2VyczogYmF0Y2hVc2VycyB9KVxyXG4gICAgICAgICAgICAgICAgLnRoZW4oKHsgZGF0YSB9KSA9PiBkYXRhKVxyXG4gICAgICAgICAgICAgICAgLmNhdGNoKCgpID0+ICh7IGNvZGU6IDUwMCwgZGF0YTogeyBzdWNjZXNzQ291bnQ6IDAsIGZhaWxDb3VudDogYmF0Y2hVc2Vycy5sZW5ndGggfSB9KSlcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgICAgKS50aGVuKHJlc3VsdHMgPT4gKHtcclxuICAgICAgICAgICAgY29kZTogcmVzdWx0cy5zb21lKHIgPT4gci5jb2RlICE9PSAyMDApID8gNTAwIDogMjAwLFxyXG4gICAgICAgICAgICBkYXRhOiB7XHJcbiAgICAgICAgICAgICAgc3VjY2Vzc0NvdW50OiByZXN1bHRzLnJlZHVjZSgoc3VtLCByKSA9PiBzdW0gKyAoci5kYXRhPy5zdWNjZXNzQ291bnQgfHwgMCksIDApLFxyXG4gICAgICAgICAgICAgIGZhaWxDb3VudDogcmVzdWx0cy5yZWR1Y2UoKHN1bSwgcikgPT4gc3VtICsgKHIuZGF0YT8uZmFpbENvdW50IHx8IDApLCAwKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9KSlcclxuICAgICAgICAgIDogYmF0Y2hBZGRVc2VySm9pblJvbGUoeyB1c2VyczogdXNlcnNEYXRhIH0pXHJcbiAgICAgICAgICAgIC50aGVuKCh7IGRhdGEgfSkgPT4gZGF0YSlcclxuICAgICAgICAgICAgLmNhdGNoKCgpID0+ICh7IGNvZGU6IDUwMCwgZGF0YTogeyBzdWNjZXNzQ291bnQ6IDAsIGZhaWxDb3VudDogdXNlcnNEYXRhLmxlbmd0aCB9IH0pKVxyXG4gICAgICAgICk7XHJcblxyXG4gICAgICAgIGlmIChoaWRlTG9hZGluZykge1xyXG4gICAgICAgICAgaGlkZUxvYWRpbmcuY2xvc2UoKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIOaYvuekuue7k+aenFxyXG4gICAgICAgIGlmIChyZXN1bHRzLmNvZGUgPT09IDIwMCkge1xyXG4gICAgICAgICAgY29uc3QgeyBzdWNjZXNzQ291bnQgPSAwLCBmYWlsQ291bnQgPSAwIH0gPSByZXN1bHRzLmRhdGEgfHwge307XHJcblxyXG4gICAgICAgICAgaWYgKHN1Y2Nlc3NDb3VudCA+IDAgJiYgZmFpbENvdW50ID09PSAwKSB7XHJcbiAgICAgICAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKGDmiJDlip/kuLogJHtzdWNjZXNzQ291bnR9IOWQjeWtpueUn+WIhumFjeenr+acqGApO1xyXG4gICAgICAgICAgfSBlbHNlIGlmIChzdWNjZXNzQ291bnQgPiAwICYmIGZhaWxDb3VudCA+IDApIHtcclxuICAgICAgICAgICAgbm90aWZpY2F0aW9uLndhcm5pbmcoYOaIkOWKn+S4uiAke3N1Y2Nlc3NDb3VudH0g5ZCN5a2m55Sf5YiG6YWN56ev5pyo77yMJHtmYWlsQ291bnR9IOWQjeWtpueUn+WIhumFjeWksei0pWApO1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfnp6/mnKjliIbphY3lpLHotKUnKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfnp6/mnKjliIbphY3lpLHotKUnKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIOeri+WNs+abtOaWsOW3suWIhumFjeWtpueUn+eahOaooeadv+S/oeaBr++8jOaXoOmcgOetieW+hUFQSeWIt+aWsFxyXG4gICAgICAgIGNvbnN0IHNlbGVjdGVkVGVtcGxhdGUgPSB0ZW1wbGF0ZXMuZmluZCh0ID0+IHQuaWQgPT09IHRlbXBsYXRlSWQpO1xyXG4gICAgICAgIGlmIChzZWxlY3RlZFRlbXBsYXRlKSB7XHJcbiAgICAgICAgICBjb25zdCB0ZW1wbGF0ZURhdGEgPSB7XHJcbiAgICAgICAgICAgIHRlbXBsYXRlSWQ6IHRlbXBsYXRlSWQsXHJcbiAgICAgICAgICAgIHRlbXBsYXRlTmFtZTogc2VsZWN0ZWRUZW1wbGF0ZS50ZW1wbGF0ZU5hbWUgfHwgc2VsZWN0ZWRUZW1wbGF0ZS5uYW1lLFxyXG4gICAgICAgICAgICBpc09mZmljaWFsOiBzZWxlY3RlZFRlbXBsYXRlLmlzT2ZmaWNpYWwgfHwgZmFsc2VcclxuICAgICAgICAgIH07XHJcblxyXG4gICAgICAgICAgLy8g5pu05pawcGVyc29uYWxUZW1wbGF0ZUFzc2lnbm1lbnRzIE1hcO+8jOehruS/neaVsOaNruaMgeS5heWMllxyXG4gICAgICAgICAgc2V0UGVyc29uYWxUZW1wbGF0ZUFzc2lnbm1lbnRzKHByZXYgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBuZXdNYXAgPSBuZXcgTWFwKHByZXYpO1xyXG4gICAgICAgICAgICB0YXJnZXRTdHVkZW50cy5mb3JFYWNoKHN0dWRlbnRJZCA9PiB7XHJcbiAgICAgICAgICAgICAgbmV3TWFwLnNldChzdHVkZW50SWQsIHRlbXBsYXRlRGF0YSk7XHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICByZXR1cm4gbmV3TWFwO1xyXG4gICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgLy8g5pu05paw5a2m55Sf54q25oCBXHJcbiAgICAgICAgICBjb25zdCB1cGRhdGVkU3R1ZGVudHMgPSBzdHVkZW50cy5tYXAoc3R1ZGVudCA9PiB7XHJcbiAgICAgICAgICAgIGlmICh0YXJnZXRTdHVkZW50cy5pbmNsdWRlcyhzdHVkZW50LnVzZXJJZCkpIHtcclxuICAgICAgICAgICAgICAvLyDooqvpgInkuK3nmoTlrabnlJ/vvJrorr7nva7kuLrmlrDliIbphY3nmoTmqKHmnb9cclxuICAgICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgICAgLi4uc3R1ZGVudCxcclxuICAgICAgICAgICAgICAgIGN1cnJlbnRUZW1wbGF0ZTogdGVtcGxhdGVEYXRhXHJcbiAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAvLyDmnKrpgInkuK3nmoTlrabnlJ/vvJrkv53mjIHljp/mnInnirbmgIFcclxuICAgICAgICAgICAgcmV0dXJuIHN0dWRlbnQ7XHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICAgIHNldFN0dWRlbnRzKHVwZGF0ZWRTdHVkZW50cyk7XHJcblxyXG4gICAgICAgICAgY29uc29sZS5sb2coJ+aooeadv+WIhumFjeaIkOWKn++8jOW3suabtOaWsHBlcnNvbmFsVGVtcGxhdGVBc3NpZ25tZW50czonLCB7XHJcbiAgICAgICAgICAgIHRhcmdldFN0dWRlbnRzLFxyXG4gICAgICAgICAgICB0ZW1wbGF0ZURhdGEsXHJcbiAgICAgICAgICAgIHBlcnNvbmFsVGVtcGxhdGVBc3NpZ25tZW50c0JlZm9yZTogQXJyYXkuZnJvbShwZXJzb25hbFRlbXBsYXRlQXNzaWdubWVudHMuZW50cmllcygpKSxcclxuICAgICAgICAgICAgcGVyc29uYWxUZW1wbGF0ZUFzc2lnbm1lbnRzQWZ0ZXI6ICd3aWxsIGJlIHVwZGF0ZWQnXHJcbiAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAvLyDlu7bov5/miZPljbDmm7TmlrDlkI7nmoTnirbmgIFcclxuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygncGVyc29uYWxUZW1wbGF0ZUFzc2lnbm1lbnRz5pu05paw5ZCOOicsIEFycmF5LmZyb20ocGVyc29uYWxUZW1wbGF0ZUFzc2lnbm1lbnRzLmVudHJpZXMoKSkpO1xyXG4gICAgICAgICAgfSwgMTAwKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIOWFs+mXreW8ueeql+W5tua4heeQhueKtuaAgVxyXG4gICAgICAgIHNldElzQXNzaWduQmxvY2tzTW9kYWxWaXNpYmxlKGZhbHNlKTtcclxuICAgICAgICBzZXRTZWxlY3RlZFN0dWRlbnRJZChudWxsKTtcclxuICAgICAgICBzZXRTZWxlY3RlZFN0dWRlbnRJZHMoW10pOyAvLyDmuIXnqbrpgInkuK3nmoTlrabnlJ9cclxuICAgICAgICBzZXRJc1NlbGVjdEFsbChmYWxzZSk7IC8vIOWPlua2iOWFqOmAieeKtuaAgVxyXG5cclxuICAgICAgICAvLyDliLfmlrDnm7jlhbPmlbDmja5cclxuICAgICAgICBhd2FpdCBmZXRjaFRlbXBsYXRlVXNhZ2UoKTsgLy8g5Yi35paw5qih5p2/5L2/55So5oOF5Ya1XHJcbiAgICAgICAgLy8g5b2T5YmN5qih5p2/5L+h5oGv55Sx5YWo5bGA54q25oCB566h55CG77yM5peg6ZyA5omL5Yqo5Yi35pawXHJcblxyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGlmIChoaWRlTG9hZGluZykge1xyXG4gICAgICAgICAgaGlkZUxvYWRpbmcuY2xvc2UoKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICAgIH1cclxuXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfliIbphY3mqKHmnb/lpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICBjb25zdCB7IEdldE5vdGlmaWNhdGlvbiB9ID0gYXdhaXQgaW1wb3J0KCdsb2dpYy1jb21tb24vZGlzdC9jb21wb25lbnRzL05vdGlmaWNhdGlvbicpO1xyXG4gICAgICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcclxuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfliIbphY3mqKHmnb/lpLHotKXvvIzor7fnqI3lkI7ph43or5UnKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyDlpITnkIbmqKHmnb/kvb/nlKjmg4XlhrXngrnlh7tcclxuICBjb25zdCBoYW5kbGVUZW1wbGF0ZVVzYWdlQ2xpY2sgPSAoZTogUmVhY3QuTW91c2VFdmVudCwgdGVtcGxhdGU6IGFueSkgPT4ge1xyXG4gICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcclxuICAgIGNvbnNvbGUubG9nKCfmn6XnnIvmqKHmnb/kvb/nlKjmg4XlhrU6JywgdGVtcGxhdGUpO1xyXG4gIH07XHJcblxyXG4gIC8vIOWkhOeQhuWNleS4quWtpueUn+WIhumFjeiDvemHj1xyXG4gIGNvbnN0IGhhbmRsZUFzc2lnblBvaW50cyA9IGFzeW5jICh2YWx1ZXM6IHsgYXZhaWxhYmxlUG9pbnRzOiBudW1iZXI7IHN0dWRlbnRFeHBpcmllcz86IHsgW2lkOiBudW1iZXJdOiBzdHJpbmcgfCB1bmRlZmluZWQgfTsgcmVtYXJrPzogc3RyaW5nIH0pID0+IHtcclxuICAgIGlmICghc2VsZWN0ZWRTdHVkZW50KSB7XHJcbiAgICAgIGNvbnN0IHsgR2V0Tm90aWZpY2F0aW9uIH0gPSBhd2FpdCBpbXBvcnQoJ2xvZ2ljLWNvbW1vbi9kaXN0L2NvbXBvbmVudHMvTm90aWZpY2F0aW9uJyk7XHJcbiAgICAgIGNvbnN0IG5vdGlmaWNhdGlvbiA9IEdldE5vdGlmaWNhdGlvbigpO1xyXG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+ivt+WFiOmAieaLqeWtpueUnycpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG5cclxuXHJcbiAgICAvLyDku44gc3R1ZGVudEV4cGlyaWVzIOS4reaPkOWPluWNleS4quWtpueUn+eahOi/h+acn+aXtumXtFxyXG4gICAgY29uc3QgZXhwaXJlVGltZSA9IHZhbHVlcy5zdHVkZW50RXhwaXJpZXM/LltzZWxlY3RlZFN0dWRlbnQudXNlcklkXTtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB7IEdldE5vdGlmaWNhdGlvbiB9ID0gYXdhaXQgaW1wb3J0KCdsb2dpYy1jb21tb24vZGlzdC9jb21wb25lbnRzL05vdGlmaWNhdGlvbicpO1xyXG4gICAgICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcclxuICAgICAgY29uc3QgaGlkZUxvYWRpbmcgPSBub3RpZmljYXRpb24ubG9hZGluZygn5q2j5Zyo5YiG6YWN6IO96YePLi4uJyk7XHJcblxyXG4gICAgICBjb25zdCB7IHBvaW50c0FwaSB9ID0gYXdhaXQgaW1wb3J0KCcuLi8uLi8uLi9saWIvYXBpL3BvaW50cycpO1xyXG4gICAgICBhd2FpdCBwb2ludHNBcGkuYXNzaWduUGVybWlzc2lvbih7XHJcbiAgICAgICAgc3R1ZGVudFVzZXJJZDogc2VsZWN0ZWRTdHVkZW50LnVzZXJJZCxcclxuICAgICAgICBhdmFpbGFibGVQb2ludHM6IHZhbHVlcy5hdmFpbGFibGVQb2ludHMsXHJcbiAgICAgICAgZXhwaXJlVGltZTogZXhwaXJlVGltZSwgLy8g5L2/55So5o+Q5Y+W5Ye655qE6L+H5pyf5pe26Ze0XHJcbiAgICAgICAgcmVtYXJrOiB2YWx1ZXMucmVtYXJrXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKGhpZGVMb2FkaW5nKSB7XHJcbiAgICAgICAgaGlkZUxvYWRpbmcuY2xvc2UoKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+WIhumFjeiDvemHj+aIkOWKnycpO1xyXG4gICAgICBzZXRJc0Fzc2lnblBvaW50c01vZGFsVmlzaWJsZShmYWxzZSk7XHJcbiAgICAgIC8vIOWIt+aWsOWtpueUn+WIl+ihqFxyXG4gICAgICBhd2FpdCByZWZyZXNoU3R1ZGVudExpc3QoKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgY29uc3QgeyBHZXROb3RpZmljYXRpb24gfSA9IGF3YWl0IGltcG9ydCgnbG9naWMtY29tbW9uL2Rpc3QvY29tcG9uZW50cy9Ob3RpZmljYXRpb24nKTtcclxuICAgICAgY29uc3Qgbm90aWZpY2F0aW9uID0gR2V0Tm90aWZpY2F0aW9uKCk7XHJcbiAgICAgIC8vIOWinuWKoOabtOWFt+S9k+eahOmUmeivr+aPkOekulxyXG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ+WIhumFjeiDvemHj+Wksei0pScpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOWIt+aWsOWtpueUn+WIl+ihqFxyXG4gIGNvbnN0IHJlZnJlc2hTdHVkZW50TGlzdCA9IGFzeW5jICgpID0+IHtcclxuICAgIGF3YWl0IGZldGNoU3R1ZGVudHMoKTtcclxuICB9O1xyXG5cclxuICAvLyDmm7TmlrDmlZnluIjnmoTlvZPliY3mqKHmnb/vvIjpgJrov4dVc2VySm9pblJvbGXooajvvIlcclxuICBjb25zdCB1cGRhdGVDbGFzc0N1cnJlbnRUZW1wbGF0ZSA9IGFzeW5jICh0ZW1wbGF0ZUlkOiBudW1iZXIsIHRlbXBsYXRlTmFtZTogc3RyaW5nLCBpc09mZmljaWFsOiBib29sZWFuKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zb2xlLmxvZygn5pu05paw5pWZ5biI5b2T5YmN5qih5p2/OicsIHsgdXNlcklkLCByb2xlSWQsIHRlbXBsYXRlSWQsIHRlbXBsYXRlTmFtZSwgaXNPZmZpY2lhbCB9KTtcclxuXHJcbiAgICAgIC8vIOS9v+eUqGFkZFVzZXJKb2luUm9sZSBBUEnmnaXmm7TmlrDmlZnluIjnmoTmqKHmnb9cclxuICAgICAgY29uc3QgeyBhZGRVc2VySm9pblJvbGUgfSA9IGF3YWl0IGltcG9ydCgnLi4vLi4vLi4vbGliL2FwaS9yb2xlJyk7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYWRkVXNlckpvaW5Sb2xlKHtcclxuICAgICAgICB1c2VySWQ6IHVzZXJJZCxcclxuICAgICAgICByb2xlSWQ6IHJvbGVJZCB8fCAyLCAvLyDpu5jorqTkuLrmlZnluIjop5LoibJcclxuICAgICAgICB0ZW1wbGF0ZUlkOiB0ZW1wbGF0ZUlkXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuY29kZSA9PT0gMjAwKSB7XHJcbiAgICAgICAgLy8g5pu05paw5pys5Zyw55qE5YWo5bGA5b2T5YmN5qih5p2/54q25oCBXHJcbiAgICAgICAgY29uc3QgbmV3VGVtcGxhdGUgPSB7XHJcbiAgICAgICAgICB0ZW1wbGF0ZUlkOiB0ZW1wbGF0ZUlkLFxyXG4gICAgICAgICAgdGVtcGxhdGVOYW1lOiB0ZW1wbGF0ZU5hbWUsXHJcbiAgICAgICAgICBpc09mZmljaWFsOiBpc09mZmljaWFsXHJcbiAgICAgICAgfTtcclxuICAgICAgICBzZXRHbG9iYWxDdXJyZW50VGVtcGxhdGUobmV3VGVtcGxhdGUpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCfmlZnluIjlvZPliY3mqKHmnb/mm7TmlrDmiJDlip86JywgbmV3VGVtcGxhdGUpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+abtOaWsOaVmeW4iOW9k+WJjeaooeadv+Wksei0pTonLCByZXNwb25zZS5kYXRhKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign5pu05paw5pWZ5biI5b2T5YmN5qih5p2/5aSx6LSlOicsIGVycm9yKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyDmm7TmlrDlrabnlJ/nmoTlvZPliY3mqKHmnb/kv6Hmga9cclxuICBjb25zdCBoYW5kbGVVcGRhdGVTdHVkZW50VGVtcGxhdGUgPSAoc3R1ZGVudElkczogbnVtYmVyW10sIHRlbXBsYXRlSW5mbzogeyB0ZW1wbGF0ZUlkOiBudW1iZXI7IHRlbXBsYXRlTmFtZTogc3RyaW5nOyBpc09mZmljaWFsPzogYm9vbGVhbiB9KSA9PiB7XHJcbiAgICBjb25zdCB0ZW1wbGF0ZURhdGEgPSB7XHJcbiAgICAgIHRlbXBsYXRlSWQ6IHRlbXBsYXRlSW5mby50ZW1wbGF0ZUlkLFxyXG4gICAgICB0ZW1wbGF0ZU5hbWU6IHRlbXBsYXRlSW5mby50ZW1wbGF0ZU5hbWUsXHJcbiAgICAgIGlzT2ZmaWNpYWw6IHRlbXBsYXRlSW5mby5pc09mZmljaWFsIHx8IGZhbHNlXHJcbiAgICB9O1xyXG5cclxuICAgIC8vIOS/neWtmOS4quS6uuWIhumFjeeahOaooeadv+S/oeaBr1xyXG4gICAgc2V0UGVyc29uYWxUZW1wbGF0ZUFzc2lnbm1lbnRzKHByZXYgPT4ge1xyXG4gICAgICBjb25zdCBuZXdNYXAgPSBuZXcgTWFwKHByZXYpO1xyXG4gICAgICBzdHVkZW50SWRzLmZvckVhY2goc3R1ZGVudElkID0+IHtcclxuICAgICAgICBuZXdNYXAuc2V0KHN0dWRlbnRJZCwgdGVtcGxhdGVEYXRhKTtcclxuICAgICAgfSk7XHJcbiAgICAgIHJldHVybiBuZXdNYXA7XHJcbiAgICB9KTtcclxuXHJcbiAgICAvLyDmm7TmlrDlrabnlJ/nirbmgIFcclxuICAgIHNldFN0dWRlbnRzKHByZXZTdHVkZW50cyA9PlxyXG4gICAgICBwcmV2U3R1ZGVudHMubWFwKHN0dWRlbnQgPT4ge1xyXG4gICAgICAgIGlmIChzdHVkZW50SWRzLmluY2x1ZGVzKHN0dWRlbnQudXNlcklkKSkge1xyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgLi4uc3R1ZGVudCxcclxuICAgICAgICAgICAgY3VycmVudFRlbXBsYXRlOiB0ZW1wbGF0ZURhdGFcclxuICAgICAgICAgIH07XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBzdHVkZW50O1xyXG4gICAgICB9KVxyXG4gICAgKTtcclxuXHJcbiAgICAvLyDkuI3lho3oh6rliqjmm7TmlrDmlZnluIjmqKHmnb/vvIzlj6rliIbphY3nu5nlrabnlJ9cclxuICAgIC8vIHVwZGF0ZUNsYXNzQ3VycmVudFRlbXBsYXRlKHRlbXBsYXRlRGF0YS50ZW1wbGF0ZUlkLCB0ZW1wbGF0ZURhdGEudGVtcGxhdGVOYW1lLCB0ZW1wbGF0ZURhdGEuaXNPZmZpY2lhbCk7XHJcbiAgfTtcclxuXHJcbiAgLy8g5aSE55CG5om56YePL+WNleS4quWFkeaNouWvhumSpVxyXG4gIGNvbnN0IGhhbmRsZUJhdGNoVXNlS2V5UGFja2FnZSA9IChzdHVkZW50SWQ/OiBudW1iZXIpID0+IHtcclxuICAgIGlmIChzdHVkZW50SWQpIHtcclxuICAgICAgLy8g5Y2V5Liq5a2m55Sf6Kem5Y+RXHJcbiAgICAgIHNldFNlbGVjdGVkU3R1ZGVudElkcyhbc3R1ZGVudElkXSk7XHJcbiAgICAgIHNldElzQmF0Y2hVc2VLZXlQYWNrYWdlTW9kYWxWaXNpYmxlKHRydWUpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgLy8g5om56YeP5pON5L2c6Kem5Y+RXHJcbiAgICAgIGlmIChzZWxlY3RlZFN0dWRlbnRJZHMubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgaW1wb3J0KCdsb2dpYy1jb21tb24vZGlzdC9jb21wb25lbnRzL05vdGlmaWNhdGlvbicpLnRoZW4oKHsgR2V0Tm90aWZpY2F0aW9uIH0pID0+IHtcclxuICAgICAgICAgIEdldE5vdGlmaWNhdGlvbigpLndhcm5pbmcoJ+ivt+WFiOmAieaLqeWtpueUnycpO1xyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG4gICAgICBzZXRJc0JhdGNoVXNlS2V5UGFja2FnZU1vZGFsVmlzaWJsZSh0cnVlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyDlpITnkIblhZHmjaLlr4bku6TmiJDlip9cclxuICBjb25zdCBoYW5kbGVCYXRjaFVzZUtleVBhY2thZ2VTdWNjZXNzID0gKCkgPT4ge1xyXG4gICAgcmVmcmVzaFN0dWRlbnRMaXN0KCk7XHJcbiAgICBzZXRJc0JhdGNoVXNlS2V5UGFja2FnZU1vZGFsVmlzaWJsZShmYWxzZSk7XHJcbiAgfTtcclxuXHJcbiAgLy8g5aSE55CG5LuO5YWR5o2i5a+G5Luk6Lez6L2s5Yiw5YiG6YWN6IO96YePXHJcbiAgY29uc3QgaGFuZGxlR29Ub0Fzc2lnblBvaW50c0Zyb21SZWRlZW0gPSAoc3R1ZGVudElkczogbnVtYmVyW10pID0+IHtcclxuICAgIHNldElzQmF0Y2hVc2VLZXlQYWNrYWdlTW9kYWxWaXNpYmxlKGZhbHNlKTtcclxuICAgIC8vIOiuvue9rumAieS4reeahOWtpueUn1xyXG4gICAgc2V0U2VsZWN0ZWRTdHVkZW50SWRzKHN0dWRlbnRJZHMpO1xyXG4gICAgLy8g5omT5byA5YiG6YWN6IO96YeP5by556qXXHJcbiAgICBzZXRJc0Fzc2lnblBvaW50c01vZGFsVmlzaWJsZSh0cnVlKTtcclxuICB9O1xyXG5cclxuICAvLyDmibnph4/liIbphY3og73ph4/lpITnkIblh73mlbBcclxuICBjb25zdCBoYW5kbGVCYXRjaEFzc2lnblBvaW50cyA9IGFzeW5jICh2YWx1ZXM6IHsgYXZhaWxhYmxlUG9pbnRzOiBudW1iZXI7IHN0dWRlbnRFeHBpcmllcz86IHsgW2lkOiBudW1iZXJdOiBzdHJpbmcgfCB1bmRlZmluZWQgfTsgcmVtYXJrPzogc3RyaW5nIH0pID0+IHtcclxuICAgIGlmIChzZWxlY3RlZFN0dWRlbnRJZHMubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgIGNvbnN0IHsgR2V0Tm90aWZpY2F0aW9uIH0gPSBhd2FpdCBpbXBvcnQoJ2xvZ2ljLWNvbW1vbi9kaXN0L2NvbXBvbmVudHMvTm90aWZpY2F0aW9uJyk7XHJcbiAgICAgIGNvbnN0IG5vdGlmaWNhdGlvbiA9IEdldE5vdGlmaWNhdGlvbigpO1xyXG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+ivt+WFiOmAieaLqeWtpueUnycpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG5cclxuXHJcbiAgICBpZiAoIXZhbHVlcy5zdHVkZW50RXhwaXJpZXMpIHtcclxuICAgICAgY29uc3QgeyBHZXROb3RpZmljYXRpb24gfSA9IGF3YWl0IGltcG9ydCgnbG9naWMtY29tbW9uL2Rpc3QvY29tcG9uZW50cy9Ob3RpZmljYXRpb24nKTtcclxuICAgICAgY29uc3Qgbm90aWZpY2F0aW9uID0gR2V0Tm90aWZpY2F0aW9uKCk7XHJcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign5pyq6IO96I635Y+W5a2m55Sf6L+H5pyf5pe26Ze05L+h5oGvJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB7IEdldE5vdGlmaWNhdGlvbiB9ID0gYXdhaXQgaW1wb3J0KCdsb2dpYy1jb21tb24vZGlzdC9jb21wb25lbnRzL05vdGlmaWNhdGlvbicpO1xyXG4gICAgICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcclxuICAgICAgY29uc3QgaGlkZUxvYWRpbmcgPSBub3RpZmljYXRpb24ubG9hZGluZygn5q2j5Zyo5om56YeP5YiG6YWN6IO96YePLi4uJyk7XHJcblxyXG4gICAgICAvLyDosIPnlKjmlrDnmoTmibnph4/liIbphY0gQVBJXHJcbiAgICAgIGNvbnN0IHsgcG9pbnRzQXBpIH0gPSBhd2FpdCBpbXBvcnQoJy4uLy4uLy4uL2xpYi9hcGkvcG9pbnRzJyk7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcG9pbnRzQXBpLmJhdGNoQXNzaWduUGVybWlzc2lvbih7XHJcbiAgICAgICAgYXZhaWxhYmxlUG9pbnRzOiB2YWx1ZXMuYXZhaWxhYmxlUG9pbnRzLFxyXG4gICAgICAgIHN0dWRlbnRFeHBpcmllczogdmFsdWVzLnN0dWRlbnRFeHBpcmllcyxcclxuICAgICAgICByZW1hcms6IHZhbHVlcy5yZW1hcmtcclxuICAgICAgfSk7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwi5om56YeP5YiG6YWN56ev5YiGcmVzXCIsIHJlc3BvbnNlKTtcclxuXHJcbiAgICAgIGlmIChoaWRlTG9hZGluZykge1xyXG4gICAgICAgIGhpZGVMb2FkaW5nLmNsb3NlKCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmIChyZXNwb25zZS5kYXRhLmNvZGUgPT09IDIwMCkge1xyXG4gICAgICAgIC8vIOWQjuerr+eOsOWcqOi/lOWbnuWkhOeQhue7k+aenOaVsOe7hO+8jOWPr+S7peagueaNrumcgOimgeWkhOeQhlxyXG4gICAgICAgIGNvbnN0IHJlc3VsdHMgPSByZXNwb25zZS5kYXRhLmRhdGE7XHJcbiAgICAgICAgLy8g5Y+v5Lul5qC55o2uIHJlc3VsdHMg5Lit55qE5L+h5oGv57uZ5Ye65pu06K+m57uG55qE5oiQ5YqfL+Wksei0peaPkOekuu+8jFxyXG4gICAgICAgIC8vIOS9huS4uuS6hueugOWNlei1t+inge+8jOaIkeS7rOS7jeeEtuS9v+eUqOS5i+WJjeeahOmAu+i+kVxyXG4gICAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKGDmiJDlip/kuLogJHtyZXN1bHRzLnN1Y2Nlc3N9IOWQjeWtpueUn+WIhumFjeiDvemHj2ApO1xyXG5cclxuICAgICAgICBzZXRJc0Fzc2lnblBvaW50c01vZGFsVmlzaWJsZShmYWxzZSk7XHJcbiAgICAgICAgLy8g5Yi35paw5a2m55Sf5YiX6KGoXHJcbiAgICAgICAgYXdhaXQgcmVmcmVzaFN0dWRlbnRMaXN0KCk7IC8vIOehruS/neS9v+eUqCBhd2FpdFxyXG4gICAgICAgIHNldFNlbGVjdGVkU3R1ZGVudElkcyhbXSk7IC8vIOa4heepuumAieaLqVxyXG4gICAgICAgIHNldElzU2VsZWN0QWxsKGZhbHNlKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICAvLyDlpITnkIYgQVBJIOi/lOWbnueahOmUmeivr+S/oeaBr1xyXG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihyZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgJ+aJuemHj+WIhumFjeiDvemHj+Wksei0pScpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+aJuemHj+WIhumFjeiDvemHj+Wksei0pTonLCBlcnJvcik7XHJcbiAgICAgIGNvbnN0IHsgR2V0Tm90aWZpY2F0aW9uIH0gPSBhd2FpdCBpbXBvcnQoJ2xvZ2ljLWNvbW1vbi9kaXN0L2NvbXBvbmVudHMvTm90aWZpY2F0aW9uJyk7XHJcbiAgICAgIGNvbnN0IG5vdGlmaWNhdGlvbiA9IEdldE5vdGlmaWNhdGlvbigpO1xyXG4gICAgICAvLyDlpITnkIbor7fmsYLnuqfliKvnmoTplJnor69cclxuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICfmibnph4/liIbphY3og73ph4/lpLHotKXvvIzor7fmo4Dmn6XnvZHnu5zov57mjqXmiJbnqI3lkI7ph43or5UnKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBpZiAoIXNlbGVjdGVkU2Nob29sKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNsYXNzLWRldGFpbC1jb250YWluZXJcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNsYXNzLWRldGFpbC1jb250ZW50XCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImVycm9yLW1lc3NhZ2VcIj5cclxuICAgICAgICAgICAgPHA+5a2m5qCh5L+h5oGv5LiN5a2Y5ZyoPC9wPlxyXG4gICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e29uQmFja30gY2xhc3NOYW1lPVwiYmFjay1idXR0b25cIj5cclxuICAgICAgICAgICAgICDov5Tlm57nj63nuqfnrqHnkIZcclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgLy8g55Sf5oiQ6ZqP5py65aS05YOP6aKc6ImyXHJcbiAgY29uc3QgZ2V0QXZhdGFyQ29sb3IgPSAoaW5kZXg6IG51bWJlcikgPT4ge1xyXG4gICAgY29uc3QgY29sb3JzID0gW1xyXG4gICAgICAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKScsXHJcbiAgICAgICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjA5M2ZiIDAlLCAjZjU1NzZjIDEwMCUpJyxcclxuICAgICAgJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICM0ZmFjZmUgMCUsICMwMGYyZmUgMTAwJSknLFxyXG4gICAgICAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzQzZTk3YiAwJSwgIzM4ZjlkNyAxMDAlKScsXHJcbiAgICAgICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmE3MDlhIDAlLCAjZmVlMTQwIDEwMCUpJyxcclxuICAgICAgJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICNhOGVkZWEgMCUsICNmZWQ2ZTMgMTAwJSknLFxyXG4gICAgXTtcclxuICAgIC8vIOehruS/nWluZGV45piv5pyJ5pWI55qE5q2j5pWwXHJcbiAgICBjb25zdCBzYWZlSW5kZXggPSBNYXRoLm1heCgwLCBpbmRleCk7XHJcbiAgICByZXR1cm4gY29sb3JzW3NhZmVJbmRleCAlIGNvbG9ycy5sZW5ndGhdO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImNsYXNzLWRldGFpbC1jb250YWluZXJcIj5cclxuICAgICAgey8qIOmhtemdouWktOmDqCAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjbGFzcy1kZXRhaWwtaGVhZGVyXCI+XHJcbiAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwiYmFjay1idXR0b25cIlxyXG4gICAgICAgICAgb25DbGljaz17b25CYWNrfVxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxBcnJvd0xlZnQgc2l6ZT17MjB9IC8+XHJcbiAgICAgICAgICDov5Tlm57nj63nuqfnrqHnkIZcclxuICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNsYXNzLWRldGFpbC1jb250ZW50XCI+XHJcbiAgICAgICAgey8qIOW3puS+p++8muePree6p+S/oeaBr+WSjOWtpueUn+WIl+ihqCAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxlZnQtc2VjdGlvblwiPlxyXG4gICAgICAgICAgey8qIOW3puS+p+WktOmDqO+8muiuvue9ruaMiemSruWSjOePree6p+S/oeaBryAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGVmdC1zZWN0aW9uLWhlYWRlclwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNldHRpbmdzLWNvbnRhaW5lclwiIHJlZj17c2V0dGluZ3NEcm9wZG93blJlZn0+XHJcbiAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgc2V0dGluZ3MtYnRuICR7aXNTZXR0aW5nc0Ryb3Bkb3duT3BlbiA/ICdhY3RpdmUnIDogJyd9YH1cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzU2V0dGluZ3NEcm9wZG93bk9wZW4oIWlzU2V0dGluZ3NEcm9wZG93bk9wZW4pfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxTZXR0aW5ncyBzaXplPXsyMH0gLz5cclxuICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuXHJcbiAgICAgICAgICAgICAge2lzU2V0dGluZ3NEcm9wZG93bk9wZW4gJiYgKFxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkcm9wZG93bi1tZW51XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZHJvcGRvd24tbWVudS1pdGVtc1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImRyb3Bkb3duLW1lbnUtaXRlbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTZXR0aW5nc01lbnVJdGVtQ2xpY2soJ2VkaXQnKX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRyb3Bkb3duLW1lbnUtaXRlbS1pY29uXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxFZGl0IHNpemU9ezE2fSBzdHlsZT17eyBjb2xvcjogJyM4YjVjZjYnIH19IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPue8lui+keePree6pzwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZHJvcGRvd24tbWVudS1pdGVtXCJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVNldHRpbmdzTWVudUl0ZW1DbGljaygnYWRkU3R1ZGVudCcpfVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZHJvcGRvd24tbWVudS1pdGVtLWljb25cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFVzZXJQbHVzIHNpemU9ezE2fSBzdHlsZT17eyBjb2xvcjogJyMxMGI5ODEnIH19IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPua3u+WKoOWtpueUnzwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZHJvcGRvd24tbWVudS1pdGVtXCJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVNldHRpbmdzTWVudUl0ZW1DbGljaygnaW1wb3J0U3R1ZGVudCcpfVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZHJvcGRvd24tbWVudS1pdGVtLWljb25cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFVwbG9hZCBzaXplPXsxNn0gc3R5bGU9e3sgY29sb3I6ICcjM2I4MmY2JyB9fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7lr7zlhaXlrabnlJ88L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImRyb3Bkb3duLW1lbnUtaXRlbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTZXR0aW5nc01lbnVJdGVtQ2xpY2soJ2V4cG9ydFN0dWRlbnQnKX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRyb3Bkb3duLW1lbnUtaXRlbS1pY29uXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxEb3dubG9hZCBzaXplPXsxNn0gc3R5bGU9e3sgY29sb3I6ICcjMTBiOTgxJyB9fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7lr7zlh7rlrabnlJ88L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImRyb3Bkb3duLW1lbnUtaXRlbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTZXR0aW5nc01lbnVJdGVtQ2xpY2soJ3RyYW5zZmVyJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkcm9wZG93bi1tZW51LWl0ZW0taWNvblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VXNlcnMgc2l6ZT17MTZ9IHN0eWxlPXt7IGNvbG9yOiAnI2Y1OWUwYicgfX0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+6L2s6K6p566h55CGPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJkcm9wZG93bi1tZW51LWl0ZW1cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlU2V0dGluZ3NNZW51SXRlbUNsaWNrKCdpbnZpdGUnKX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRyb3Bkb3duLW1lbnUtaXRlbS1pY29uXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rIHNpemU9ezE2fSBzdHlsZT17eyBjb2xvcjogJyMzYjgyZjYnIH19IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPueUn+aIkOmCgOivt+eggTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZHJvcGRvd24tbWVudS1pdGVtXCJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVNldHRpbmdzTWVudUl0ZW1DbGljaygnYmF0Y2hSZWRlZW0nKX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRyb3Bkb3duLW1lbnUtaXRlbS1pY29uXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxHaWZ0IHNpemU9ezE2fSBzdHlsZT17eyBjb2xvcjogJyNmNTllMGInIH19IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuaJuemHj+WFkeaNouWvhuS7pDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZHJvcGRvd24tbWVudS1pdGVtXCJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVNldHRpbmdzTWVudUl0ZW1DbGljaygnYXNzaWduQmxvY2tzJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkcm9wZG93bi1tZW51LWl0ZW0taWNvblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8QmxvY2tzIHNpemU9ezE2fSBzdHlsZT17eyBjb2xvcjogJyMzYjgyZjYnIH19IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuWIhumFjeenr+acqDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkcm9wZG93bi1tZW51LWRpdmlkZXJcIj48L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZHJvcGRvd24tbWVudS1pdGVtIGRhbmdlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTZXR0aW5nc01lbnVJdGVtQ2xpY2soJ2RlbGV0ZUNsYXNzJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkcm9wZG93bi1tZW51LWl0ZW0taWNvblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VHJhc2gyIHNpemU9ezE2fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7liKDpmaTnj63nuqc8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICB7Lyog54+t57qn5L+h5oGv5Y2h54mHICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjbGFzcy1pbmZvLWNhcmRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjbGFzcy1jYXJkLWhlYWRlclwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2xhc3MtY2FyZC1sZWZ0XCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNsYXNzLWNhcmQtaWNvblwiPlxyXG4gICAgICAgICAgICAgICAgICDwn5OLXHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2xhc3MtY2FyZC1pbmZvXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJjbGFzcy1jYXJkLXRpdGxlXCI+e2NsYXNzSW5mby5jbGFzc05hbWV9PC9oMz5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiY2xhc3MtY2FyZC1zdWJ0aXRsZVwiPntzZWxlY3RlZFNjaG9vbC5zY2hvb2xOYW1lfTwvcD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2xhc3MtY2FyZC1iYWRnZVwiPlxyXG4gICAgICAgICAgICAgICAg5b2T5YmN5qih5p2/XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qIOaQnOe0ouahhiAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic2VhcmNoLXNlY3Rpb25cIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzZWFyY2gtYm94XCI+XHJcbiAgICAgICAgICAgICAgPFNlYXJjaCBzaXplPXsxNn0gY2xhc3NOYW1lPVwic2VhcmNoLWljb25cIiAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiDlrabnlJ/liJfooaggKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0dWRlbnRzLXNlY3Rpb25cIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzdHVkZW50cy1oZWFkZXJcIj5cclxuICAgICAgICAgICAgICA8aDM+5a2m55SfPC9oMz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0dWRlbnRzLWFjdGlvbnNcIj5cclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWRkLXN0dWRlbnQtYnRuXCJcclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNBZGRTdHVkZW50TW9kYWxWaXNpYmxlKHRydWUpfVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8UGx1cyBzaXplPXsxNn0gLz5cclxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BzZWxlY3QtYWxsLWJ0biAke2lzU2VsZWN0QWxsID8gJ2FjdGl2ZScgOiAnJ31gfVxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+eCueWHu+WFqOmAieaMiemSru+8jOW9k+WJjeeKtuaAgTonLCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBpc1NlbGVjdEFsbCxcclxuICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkU3R1ZGVudElkczogc2VsZWN0ZWRTdHVkZW50SWRzLmxlbmd0aCxcclxuICAgICAgICAgICAgICAgICAgICAgIHRvdGFsU3R1ZGVudHM6IHN0dWRlbnRzLmxlbmd0aFxyXG4gICAgICAgICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICAgICAgICAgIGhhbmRsZVNlbGVjdEFsbCgpO1xyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICB0aXRsZT17aXNTZWxlY3RBbGwgPyAn5Y+W5raI5YWo6YCJJyA6ICflhajpgIknfVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8c3Bhbj57aXNTZWxlY3RBbGwgPyAn5Y+W5raI5YWo6YCJJyA6ICflhajpgIknfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG5cclxuICAgICAgICAgICAgICAgIHsvKiDmibnph4/mk43kvZzmjInpkq4gKi99XHJcbiAgICAgICAgICAgICAgICB7c2VsZWN0ZWRTdHVkZW50SWRzLmxlbmd0aCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJhdGNoLWFjdGlvbnMtY29udGFpbmVyXCIgcmVmPXtiYXRjaEFjdGlvbnNEcm9wZG93blJlZn0+XHJcbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmF0Y2gtYWN0aW9ucy1idG5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNCYXRjaEFjdGlvbnNEcm9wZG93bk9wZW4oIWlzQmF0Y2hBY3Rpb25zRHJvcGRvd25PcGVuKX1cclxuICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwi5om56YeP5pON5L2cXCJcclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7mibnph4/mk43kvZwoe3NlbGVjdGVkU3R1ZGVudElkcy5sZW5ndGh9KTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uRG93biBzaXplPXsxNn0gLz5cclxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIOaJuemHj+aTjeS9nOS4i+aLieiPnOWNlSAqL31cclxuICAgICAgICAgICAgICAgICAgICB7aXNCYXRjaEFjdGlvbnNEcm9wZG93bk9wZW4gJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiYXRjaC1hY3Rpb25zLWRyb3Bkb3duXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiYXRjaC1hY3Rpb24taXRlbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQmF0Y2hBY3Rpb24oJ2JhdGNoQXNzaWduQmxvY2tzJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QmxvY2tzIHNpemU9ezE2fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuaJuemHj+WIhumFjeenr+acqDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiYXRjaC1hY3Rpb24taXRlbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQmF0Y2hBY3Rpb24oJ2JhdGNoQXNzaWduUG9pbnRzJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8WmFwIHNpemU9ezE2fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuaJuemHj+WIhumFjeiDvemHjzwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiYXRjaC1hY3Rpb24taXRlbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQmF0Y2hBY3Rpb24oJ2JhdGNoVXNlS2V5UGFja2FnZScpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEdpZnQgc2l6ZT17MTZ9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5om56YeP5YWR5o2i5a+G5LukPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJhdGNoLWFjdGlvbi1pdGVtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVCYXRjaEFjdGlvbignYmF0Y2hEZWxldGUnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaDIgc2l6ZT17MTZ9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5om56YeP56e75Ye654+t57qnPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJhdGNoLWFjdGlvbi1pdGVtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVCYXRjaEFjdGlvbignYmF0Y2hFeHBvcnQnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxEb3dubG9hZCBzaXplPXsxNn0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7mibnph4/lr7zlh7o8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3R1ZGVudHMtbGlzdFwiPlxyXG4gICAgICAgICAgICAgIDxTdHVkZW50TGlzdFxyXG4gICAgICAgICAgICAgICAgc3R1ZGVudHM9e3N0dWRlbnRzfVxyXG4gICAgICAgICAgICAgICAgbG9hZGluZz17bG9hZGluZ31cclxuICAgICAgICAgICAgICAgIGVycm9yPXtlcnJvcn1cclxuICAgICAgICAgICAgICAgIHNlbGVjdGVkU3R1ZGVudD17c2VsZWN0ZWRTdHVkZW50fVxyXG4gICAgICAgICAgICAgICAgc2VsZWN0ZWRTdHVkZW50SWRzPXtzZWxlY3RlZFN0dWRlbnRJZHN9XHJcbiAgICAgICAgICAgICAgICBjdXJyZW50VGVtcGxhdGU9e2dsb2JhbEN1cnJlbnRUZW1wbGF0ZSB8fCBjdXJyZW50VGVtcGxhdGV9IC8vIOS8oOmAkuaVmeW4iOaooeadv+S9nOS4uuWbnumAgFxyXG4gICAgICAgICAgICAgICAgcmVuZGVyVmVyc2lvbj17cmVuZGVyVmVyc2lvbn1cclxuICAgICAgICAgICAgICAgIG9uU3R1ZGVudENsaWNrPXtoYW5kbGVTdHVkZW50Q2xpY2t9XHJcbiAgICAgICAgICAgICAgICBvblN0dWRlbnRTZWxlY3Q9e2hhbmRsZVN0dWRlbnRTZWxlY3R9XHJcbiAgICAgICAgICAgICAgICBvblJldHJ5PXtmZXRjaFN0dWRlbnRzfVxyXG4gICAgICAgICAgICAgICAgb25JbmRpdmlkdWFsQXNzaWduQmxvY2tzPXtoYW5kbGVJbmRpdmlkdWFsQXNzaWduQmxvY2tzfVxyXG4gICAgICAgICAgICAgICAgb25Bc3NpZ25Qb2ludHM9eyhzdHVkZW50SWQ6IG51bWJlcikgPT4ge1xyXG4gICAgICAgICAgICAgICAgICBjb25zdCBzdHVkZW50ID0gc3R1ZGVudHMuZmluZChzID0+IHMudXNlcklkID09PSBzdHVkZW50SWQpO1xyXG4gICAgICAgICAgICAgICAgICBpZiAoc3R1ZGVudCkge1xyXG4gICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkU3R1ZGVudChzdHVkZW50KTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRJc0Fzc2lnblBvaW50c01vZGFsVmlzaWJsZSh0cnVlKTtcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7Lyog5Y+z5L6n77ya5Yqf6IO95Yy65Z+fICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmlnaHQtc2VjdGlvblwiPlxyXG4gICAgICAgICAgey8qIOWtpueUn+S/oeaBr+WktOmDqCAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3R1ZGVudC1pbmZvLWhlYWRlclwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0dWRlbnQtYXZhdGFyLWxhcmdlXCI+XHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic3R1ZGVudC1hdmF0YXItY2lyY2xlXCJcclxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IHNlbGVjdGVkU3R1ZGVudCA/XHJcbiAgICAgICAgICAgICAgICAgICAgZ2V0QXZhdGFyQ29sb3IoTWF0aC5tYXgoMCwgc3R1ZGVudHMuZmluZEluZGV4KHMgPT4gcy51c2VySWQgPT09IHNlbGVjdGVkU3R1ZGVudC51c2VySWQpKSkgOlxyXG4gICAgICAgICAgICAgICAgICAgIGdldEF2YXRhckNvbG9yKDApXHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtzZWxlY3RlZFN0dWRlbnQgPyBzZWxlY3RlZFN0dWRlbnQubmlja05hbWU/LmNoYXJBdCgwKSB8fCAnUycgOiAnUyd9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0dWRlbnQtZGV0YWlsc1wiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3R1ZGVudC1uYW1lLWxhcmdlXCI+XHJcbiAgICAgICAgICAgICAgICB7c2VsZWN0ZWRTdHVkZW50ID8gc2VsZWN0ZWRTdHVkZW50Lm5pY2tOYW1lIHx8IGDlrabnlJ8ke3NlbGVjdGVkU3R1ZGVudC5zdHVkZW50TnVtYmVyIHx8IHNlbGVjdGVkU3R1ZGVudC51c2VySWR9YCA6ICfor7fpgInmi6nlrabnlJ8nfVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3R1ZGVudC1pZC1sYXJnZVwiPlxyXG4gICAgICAgICAgICAgICAge3NlbGVjdGVkU3R1ZGVudCA/IHNlbGVjdGVkU3R1ZGVudC5zdHVkZW50TnVtYmVyIHx8ICfml6Dlrablj7cnIDogJyd9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZGVsZXRlLXN0dWRlbnQtYnRuXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVEZWxldGVTdHVkZW50fVxyXG4gICAgICAgICAgICAgIGRpc2FibGVkPXshc2VsZWN0ZWRTdHVkZW50fVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPFRyYXNoMiBzaXplPXsyMH0gLz5cclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICB7Lyog5Yqf6IO95Yy65Z+f5a655ZmoICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmdW5jdGlvbnMtY29udGFpbmVyXCI+XHJcbiAgICAgICAgICAgIHsvKiDmm7TlpJrlip/og70gKi99XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZnVuY3Rpb25zLXNlY3Rpb25cIj5cclxuICAgICAgICAgICAgICA8aDM+5pu05aSa5Yqf6IO9PC9oMz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZ1bmN0aW9uLWJ1dHRvbnNcIj5cclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZnVuY3Rpb24tYnRuIHB1Ymxpc2gtdGFza1wiXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzUHVibGlzaFRhc2tNb2RhbFZpc2libGUodHJ1ZSl9XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZnVuY3Rpb24taWNvblwiPvCfk508L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4+5Y+R5biD5Lu75YqhPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZ1bmN0aW9uLWJ0biBkaXN0cmlidXRlLWJsb2Nrc1wiXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUFzc2lnbkJsb2Nrc31cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmdW5jdGlvbi1pY29uXCI+8J+nqTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8c3Bhbj7liIbphY3np6/mnKg8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZnVuY3Rpb24tYnRuIGRpc3RyaWJ1dGUtZW5lcmd5XCJcclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNBc3NpZ25Qb2ludHNNb2RhbFZpc2libGUodHJ1ZSl9XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZnVuY3Rpb24taWNvblwiPuKaoTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8c3Bhbj7liIbphY3og73ph488L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZnVuY3Rpb24tYnRuIGV4Y2hhbmdlLXRva2Vuc1wiXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzQmF0Y2hVc2VLZXlQYWNrYWdlTW9kYWxWaXNpYmxlKHRydWUpfVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZ1bmN0aW9uLWljb25cIj7wn46BPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuPuWFkeaNouWvhuS7pDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmdW5jdGlvbi1idG4gcmVzZXQtcGFzc3dvcmRcIlxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc1Jlc2V0UGFzc3dvcmRNb2RhbFZpc2libGUodHJ1ZSl9XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZnVuY3Rpb24taWNvblwiPvCflJE8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4+6YeN572u5a+G56CBPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgey8qIOivvueoi+WtpuS5oOaDheWGtSAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZWFybmluZy1zdGF0dXNcIj5cclxuICAgICAgICAgICAgICA8aDM+6K++56iL5a2m5Lmg5oOF5Ya1PC9oMz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0YXR1cy1wbGFjZWhvbGRlclwiPlxyXG4gICAgICAgICAgICAgICAg6K+l5Yy65Z+f5Yqf6IO95q2j5Zyo562J5b6F5byA5pS+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG5cclxuXHJcbiAgICAgICAgICB7Lyog5bqV6YOo5Yy65Z+fICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3R0b20tc2VjdGlvblwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImVuZXJneS1wcm9ncmVzcy1zZWN0aW9uXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJlbmVyZ3ktcHJvZ3Jlc3MtaGVhZGVyXCI+XHJcbiAgICAgICAgICAgICAgICA8c3Bhbj7lj6/nlKjog73ph48v5oC76IO96YePPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPHNwYW4+XHJcbiAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZFN0dWRlbnQ/LmF2YWlsYWJsZVBvaW50cyB8fCAwfS97c2VsZWN0ZWRTdHVkZW50Py50b3RhbFBvaW50cyB8fCAwfVxyXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZW5lcmd5LXByb2dyZXNzLWJhclwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJlbmVyZ3ktcHJvZ3Jlc3MtZmlsbFwiXHJcbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6IHNlbGVjdGVkU3R1ZGVudD8udG90YWxQb2ludHNcclxuICAgICAgICAgICAgICAgICAgICAgID8gYCR7KE51bWJlcihzZWxlY3RlZFN0dWRlbnQuYXZhaWxhYmxlUG9pbnRzIHx8IDApIC8gTnVtYmVyKHNlbGVjdGVkU3R1ZGVudC50b3RhbFBvaW50cyB8fCAwKSkgKiAxMDB9JWBcclxuICAgICAgICAgICAgICAgICAgICAgIDogJzAlJ1xyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgPjwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGVtcGxhdGUtY2FyZC1zZWN0aW9uXCI+XHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGVtcGxhdGUtY2FyZFwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZWxlY3RlZFN0dWRlbnQgJiYgaGFuZGxlVGVtcGxhdGVBc3NpZ25tZW50KCl9XHJcbiAgICAgICAgICAgICAgICBzdHlsZT17eyBjdXJzb3I6IHNlbGVjdGVkU3R1ZGVudCA/ICdwb2ludGVyJyA6ICdkZWZhdWx0JyB9fVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGVtcGxhdGUtY2FyZC1oZWFkZXJcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZW1wbGF0ZS1jYXJkLWxlZnRcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRlbXBsYXRlLWNhcmQtaWNvblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAg8J+Ti1xyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGVtcGxhdGUtY2FyZC1pbmZvXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGVtcGxhdGUtY2FyZC10aXRsZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7KCgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoc2VsZWN0ZWRTdHVkZW50Py5jdXJyZW50VGVtcGxhdGUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBzZWxlY3RlZFN0dWRlbnQuY3VycmVudFRlbXBsYXRlLnRlbXBsYXRlTmFtZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGdsb2JhbEN1cnJlbnRUZW1wbGF0ZSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGdsb2JhbEN1cnJlbnRUZW1wbGF0ZS50ZW1wbGF0ZU5hbWU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChjdXJyZW50VGVtcGxhdGUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBjdXJyZW50VGVtcGxhdGUudGVtcGxhdGVOYW1lO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoc2VsZWN0ZWRTdHVkZW50KSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gJ+WKoOi9veS4rS4uLic7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAn6K+36YCJ5oup5a2m55SfJztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pKCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2g0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGVtcGxhdGUtY2FyZC1zdWJ0aXRsZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRTdHVkZW50ID9cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAoc2VsZWN0ZWRTdHVkZW50LmN1cnJlbnRUZW1wbGF0ZT8uaXNPZmZpY2lhbCA/ICflrpjmlrnmqKHmnb8nIDogJ+iHquWumuS5ieaooeadvycpIDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAn54K55Ye76YCJ5oup5a2m55Sf5p+l55yL5qih5p2/J1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRlbXBsYXRlLWNhcmQtcmlnaHRcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRlbXBsYXRlLWNhcmQtYmFkZ2VcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIOW9k+WJjeaooeadv1xyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRlbXBsYXRlLWNhcmQtc3dhcC1pY29uXCJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkU3R1ZGVudCAmJiBoYW5kbGVUZW1wbGF0ZUFzc2lnbm1lbnQoKTtcclxuICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIuabtOaNouaooeadv1wiXHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAg4oeEXHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIOa3u+WKoOWtpueUn+W8ueeqlyAqL31cclxuICAgICAgPEFkZFN0dWRlbnRNb2RhbFxyXG4gICAgICAgIHZpc2libGU9e2lzQWRkU3R1ZGVudE1vZGFsVmlzaWJsZX1cclxuICAgICAgICBvbkNhbmNlbD17KCkgPT4gc2V0SXNBZGRTdHVkZW50TW9kYWxWaXNpYmxlKGZhbHNlKX1cclxuICAgICAgICBvbk9rPXtoYW5kbGVBZGRTdHVkZW50fVxyXG4gICAgICAvPlxyXG5cclxuICAgICAgey8qIOe8lui+keePree6p+W8ueeqlyAqL31cclxuICAgICAgPEVkaXRDbGFzc01vZGFsXHJcbiAgICAgICAgdmlzaWJsZT17aXNFZGl0Q2xhc3NNb2RhbFZpc2libGV9XHJcbiAgICAgICAgb25DYW5jZWw9eygpID0+IHNldElzRWRpdENsYXNzTW9kYWxWaXNpYmxlKGZhbHNlKX1cclxuICAgICAgICBvbk9rPXtoYW5kbGVFZGl0Q2xhc3N9XHJcbiAgICAgICAgaW5pdGlhbFZhbHVlcz17e1xyXG4gICAgICAgICAgY2xhc3NOYW1lOiBjbGFzc0luZm8uY2xhc3NOYW1lXHJcbiAgICAgICAgfX1cclxuICAgICAgLz5cclxuXHJcbiAgICAgIHsvKiDlr7zlhaXlrabnlJ/lvLnnqpcgKi99XHJcbiAgICAgIDxJbXBvcnRTdHVkZW50TW9kYWxcclxuICAgICAgICB2aXNpYmxlPXtpc0ltcG9ydFN0dWRlbnRNb2RhbFZpc2libGV9XHJcbiAgICAgICAgb25DYW5jZWw9eygpID0+IHNldElzSW1wb3J0U3R1ZGVudE1vZGFsVmlzaWJsZShmYWxzZSl9XHJcbiAgICAgICAgb25JbXBvcnQ9e2hhbmRsZUltcG9ydFN0dWRlbnRzfVxyXG4gICAgICAgIGNsYXNzSWQ9e2NsYXNzSW5mby5pZH1cclxuICAgICAgLz5cclxuXHJcbiAgICAgIHsvKiDovazorqnnj63nuqflvLnnqpcgKi99XHJcbiAgICAgIDxUcmFuc2ZlckNsYXNzTW9kYWxcclxuICAgICAgICB2aXNpYmxlPXtpc1RyYW5zZmVyQ2xhc3NNb2RhbFZpc2libGV9XHJcbiAgICAgICAgb25DYW5jZWw9eygpID0+IHtcclxuICAgICAgICAgIHNldElzVHJhbnNmZXJDbGFzc01vZGFsVmlzaWJsZShmYWxzZSk7XHJcbiAgICAgICAgICBzZXRTZWFyY2hlZFRlYWNoZXIobnVsbCk7XHJcbiAgICAgICAgfX1cclxuICAgICAgICBvbk9rPXtoYW5kbGVUcmFuc2ZlckNsYXNzfVxyXG4gICAgICAgIG9uU2VhcmNoVGVhY2hlcj17aGFuZGxlU2VhcmNoVGVhY2hlcn1cclxuICAgICAgICBzZWFyY2hlZFRlYWNoZXI9e3NlYXJjaGVkVGVhY2hlcn1cclxuICAgICAgICBoYXNBc3Npc3RhbnRUZWFjaGVyPXshIWNsYXNzSW5mby5hc3Npc3RhbnRUZWFjaGVySWR9XHJcbiAgICAgICAgb25SZW1vdmVBc3Npc3RhbnQ9e2hhbmRsZVJlbW92ZUFzc2lzdGFudH1cclxuICAgICAgICBsb2FkaW5nPXt0cmFuc2ZlckxvYWRpbmd9XHJcbiAgICAgIC8+XHJcblxyXG4gICAgICB7Lyog6YKA6K+356CB5by556qXICovfVxyXG4gICAgICB7aXNJbnZpdGVDb2RlTW9kYWxWaXNpYmxlICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgcC02IG1heC13LW1kIHctZnVsbFwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi00XCI+XHJcbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZFwiPumCgOivt+eggeeUn+aIkOaIkOWKnzwvaDI+XHJcbiAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWdyYXktNzAwXCJcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzSW52aXRlQ29kZU1vZGFsVmlzaWJsZShmYWxzZSl9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgY2xhc3NOYW1lPVwiaC02IHctNlwiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiPlxyXG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNiAxOEwxOCA2TTYgNmwxMiAxMlwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGJnLWdyYXktNTAgcC0zIHJvdW5kZWQtbGcgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tb25vIHRleHQtbGcgdGV4dC1ibHVlLTYwMCBtci00XCI+e2ludml0ZUNvZGV9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTEgYmctYmx1ZS01MDAgdGV4dC13aGl0ZSByb3VuZGVkIGhvdmVyOmJnLWJsdWUtNjAwXCJcclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIG5hdmlnYXRvci5jbGlwYm9hcmQud3JpdGVUZXh0KGludml0ZUNvZGUpXHJcbiAgICAgICAgICAgICAgICAgICAgICAudGhlbigoKSA9PiBhbGVydCgn6YKA6K+356CB5bey5aSN5Yi2JykpXHJcbiAgICAgICAgICAgICAgICAgICAgICAuY2F0Y2goKCkgPT4gYWxlcnQoJ+WkjeWItuWksei0pe+8jOivt+aJi+WKqOWkjeWIticpKTtcclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAg5aSN5Yi2XHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1zbSBzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgIDxwPuaCqOWPr+S7pTo8L3A+XHJcbiAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwibGlzdC1kaXNjIGxpc3QtaW5zaWRlIHNwYWNlLXktMVwiPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+5bCG6YKA6K+356CB5YiG5Lqr57uZ5a2m55Sf77yM6K6p5LuW5Lus5Yqg5YWl54+t57qnPC9saT5cclxuICAgICAgICAgICAgICAgICAgPGxpPumCgOivt+WFtuS7luiAgeW4iOS9nOS4uuWNj+WKqeaVmeW4iOWKoOWFpeePree6pzwvbGk+XHJcbiAgICAgICAgICAgICAgICA8L3VsPlxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgIOKPsCDpgoDor7fnoIHmnInmlYjmnJ/kuLoyNOWwj+aXtlxyXG4gICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kXCI+XHJcbiAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWJsdWUtNTAwIHRleHQtd2hpdGUgcm91bmRlZCBob3ZlcjpiZy1ibHVlLTYwMFwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc0ludml0ZUNvZGVNb2RhbFZpc2libGUoZmFsc2UpfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIOWFs+mXrVxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApfVxyXG5cclxuXHJcblxyXG4gICAgICB7Lyog5Y+R5biD5Lu75Yqh5by556qXICovfVxyXG4gICAgICA8UHVibGlzaFRhc2tNb2RhbFxyXG4gICAgICAgIHZpc2libGU9e2lzUHVibGlzaFRhc2tNb2RhbFZpc2libGV9XHJcbiAgICAgICAgb25DYW5jZWw9eygpID0+IHNldElzUHVibGlzaFRhc2tNb2RhbFZpc2libGUoZmFsc2UpfVxyXG4gICAgICAgIG9uT2s9e2hhbmRsZVB1Ymxpc2hUYXNrfVxyXG4gICAgICAgIHN0dWRlbnRzPXtzdHVkZW50c31cclxuICAgICAgICBzZWxlY3RlZFN0dWRlbnRzPXtzZWxlY3RlZFN0dWRlbnRJZHN9XHJcbiAgICAgICAgc2V0U2VsZWN0ZWRTdHVkZW50cz17c2V0U2VsZWN0ZWRTdHVkZW50SWRzfVxyXG4gICAgICAgIGZpbGVMaXN0PXtmaWxlTGlzdH1cclxuICAgICAgICB1cGxvYWRpbmc9e3VwbG9hZGluZ31cclxuICAgICAgICBoYW5kbGVVcGxvYWQ9e2hhbmRsZVVwbG9hZH1cclxuICAgICAgICBoYW5kbGVSZW1vdmVGaWxlPXtoYW5kbGVSZW1vdmVGaWxlfVxyXG4gICAgICAgIGRpc3BsYXlUZW1wbGF0ZXM9e3RlbXBsYXRlc31cclxuICAgICAgICBvZmZpY2lhbFRlbXBsYXRlcz17b2ZmaWNpYWxUZW1wbGF0ZXN9XHJcbiAgICAgICAgc2VsZWN0ZWRUZW1wbGF0ZUlkPXtzZWxlY3RlZFRlbXBsYXRlSWR9XHJcbiAgICAgICAgc2V0U2VsZWN0ZWRUZW1wbGF0ZUlkPXtzZXRTZWxlY3RlZFRlbXBsYXRlSWR9XHJcbiAgICAgIC8+XHJcblxyXG4gICAgICB7Lyog6YeN572u5a+G56CB5by556qXICovfVxyXG4gICAgICA8UmVzZXRQYXNzd29yZE1vZGFsXHJcbiAgICAgICAgdmlzaWJsZT17aXNSZXNldFBhc3N3b3JkTW9kYWxWaXNpYmxlfVxyXG4gICAgICAgIG9uQ2FuY2VsPXsoKSA9PiBzZXRJc1Jlc2V0UGFzc3dvcmRNb2RhbFZpc2libGUoZmFsc2UpfVxyXG4gICAgICAgIG9uT2s9e2hhbmRsZVJlc2V0UGFzc3dvcmR9XHJcbiAgICAgICAgaXNCYXRjaD17ZmFsc2V9XHJcbiAgICAgICAgY291bnQ9ezF9XHJcbiAgICAgIC8+XHJcblxyXG4gICAgICB7Lyog5YiG6YWN56ev5pyo5by556qXICovfVxyXG4gICAgICA8QXNzaWduQmxvY2tzTW9kYWxcclxuICAgICAgICB2aXNpYmxlPXtpc0Fzc2lnbkJsb2Nrc01vZGFsVmlzaWJsZX1cclxuICAgICAgICBvbkNhbmNlbD17KCkgPT4ge1xyXG4gICAgICAgICAgc2V0SXNBc3NpZ25CbG9ja3NNb2RhbFZpc2libGUoZmFsc2UpO1xyXG4gICAgICAgICAgc2V0U2VsZWN0ZWRTdHVkZW50SWQobnVsbCk7XHJcbiAgICAgICAgICBzZXRTZWxlY3RlZFN0dWRlbnRJZHMoW10pO1xyXG4gICAgICAgIH19XHJcbiAgICAgICAgaXNDbGFzc0NhcmRBc3NpZ249e3NlbGVjdGVkU3R1ZGVudElkICE9PSBudWxsfVxyXG4gICAgICAgIGxvYWRpbmdUZW1wbGF0ZXM9e2xvYWRpbmdUZW1wbGF0ZXN9XHJcbiAgICAgICAgdGVtcGxhdGVzPXt0ZW1wbGF0ZXN9XHJcbiAgICAgICAgc3R1ZGVudFRlbXBsYXRlVXNhZ2U9e3N0dWRlbnRUZW1wbGF0ZVVzYWdlfVxyXG4gICAgICAgIHRlYWNoZXJUZW1wbGF0ZT17dGVhY2hlclRlbXBsYXRlfVxyXG4gICAgICAgIG9uU2VsZWN0VGVtcGxhdGU9e2hhbmRsZVNlbGVjdFRlbXBsYXRlfVxyXG4gICAgICAgIG9uVGVtcGxhdGVVc2FnZUNsaWNrPXtoYW5kbGVUZW1wbGF0ZVVzYWdlQ2xpY2t9XHJcbiAgICAgICAgdXNlcklkPXt1c2VySWR9XHJcbiAgICAgICAgb25SZWZyZXNoVGVtcGxhdGVzPXtmZXRjaFRlbXBsYXRlc31cclxuICAgICAgICBzdHVkZW50cz17c3R1ZGVudHN9XHJcbiAgICAgICAgc2VsZWN0ZWRTdHVkZW50SWRzPXtzZWxlY3RlZFN0dWRlbnRJZHN9XHJcbiAgICAgICAgdXNlclJvbGVzPXt1c2VyUm9sZXN9XHJcbiAgICAgICAgb25TdWNjZXNzPXsoKSA9PiB7XHJcbiAgICAgICAgICByZWZyZXNoU3R1ZGVudExpc3QoKTtcclxuICAgICAgICAgIGZldGNoVGVtcGxhdGVVc2FnZSgpO1xyXG4gICAgICAgIH19XHJcbiAgICAgICAgb25VcGRhdGVTdHVkZW50VGVtcGxhdGU9e2hhbmRsZVVwZGF0ZVN0dWRlbnRUZW1wbGF0ZX1cclxuICAgICAgLz5cclxuXHJcbiAgICAgIHsvKiDliIbphY3og73ph4/lvLnnqpcgKi99XHJcbiAgICAgIDxBc3NpZ25Qb2ludHNNb2RhbFxyXG4gICAgICAgIHZpc2libGU9e2lzQXNzaWduUG9pbnRzTW9kYWxWaXNpYmxlfVxyXG4gICAgICAgIG9uQ2FuY2VsPXsoKSA9PiB7XHJcbiAgICAgICAgICBzZXRJc0Fzc2lnblBvaW50c01vZGFsVmlzaWJsZShmYWxzZSk7XHJcbiAgICAgICAgICBzZXRTZWxlY3RlZFN0dWRlbnQobnVsbCk7XHJcbiAgICAgICAgfX1cclxuICAgICAgICAvLyDmoLnmja4gc2VsZWN0ZWRTdHVkZW50IOaYr+WQpuWtmOWcqOadpeWGs+Wumuiwg+eUqOWTquS4quWkhOeQhuWHveaVsFxyXG4gICAgICAgIG9uT2s9e3NlbGVjdGVkU3R1ZGVudCA/IGhhbmRsZUFzc2lnblBvaW50cyA6IGhhbmRsZUJhdGNoQXNzaWduUG9pbnRzfVxyXG4gICAgICAgIHN0dWRlbnROYW1lPXtzZWxlY3RlZFN0dWRlbnQgPyBgJHtzZWxlY3RlZFN0dWRlbnQubmlja05hbWV9YCA6IGDlt7LpgInmi6kgJHtzZWxlY3RlZFN0dWRlbnRJZHMubGVuZ3RofSDlkI3lrabnlJ9gfVxyXG4gICAgICAgIHN0dWRlbnRJZD17c2VsZWN0ZWRTdHVkZW50Py5pZCB8fCAwfVxyXG4gICAgICAgIHVzZXJJZD17c2VsZWN0ZWRTdHVkZW50Py51c2VySWQgfHwgMH1cclxuICAgICAgICBzdHVkZW50PXtzZWxlY3RlZFN0dWRlbnR9XHJcbiAgICAgICAgaXNCYXRjaD17IXNlbGVjdGVkU3R1ZGVudH1cclxuICAgICAgICBzZWxlY3RlZFN0dWRlbnRzPXtzZWxlY3RlZFN0dWRlbnRJZHN9XHJcbiAgICAgICAgc3R1ZGVudHM9e3N0dWRlbnRzfVxyXG4gICAgICAgIG9uU3VjY2Vzcz17KCkgPT4ge1xyXG4gICAgICAgICAgLy8g56e76Zmk6L+Z6YeM55qE5Yi35paw77yM5Zug5Li6IG9uT2sg5YaF6YOo5bey57uP5aSE55CG5LqGXHJcbiAgICAgICAgfX1cclxuICAgICAgICByZWZyZXNoU3R1ZGVudExpc3Q9e3JlZnJlc2hTdHVkZW50TGlzdH1cclxuICAgICAgICBvbkdvVG9SZWRlZW1LZXk9eyhzdHVkZW50SWRzKSA9PiB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygn5YmN5b6A5YWR5o2i5a+G6ZKlOicsIHN0dWRlbnRJZHMpO1xyXG4gICAgICAgICAgc2V0SXNCYXRjaFVzZUtleVBhY2thZ2VNb2RhbFZpc2libGUodHJ1ZSk7XHJcbiAgICAgICAgfX1cclxuICAgICAgLz5cclxuXHJcbiAgICAgIHsvKiDlhZHmjaLlr4bku6TlvLnnqpcgKi99XHJcbiAgICAgIDxCYXRjaFVzZUtleVBhY2thZ2VNb2RhbFxyXG4gICAgICAgIG9wZW49e2lzQmF0Y2hVc2VLZXlQYWNrYWdlTW9kYWxWaXNpYmxlfVxyXG4gICAgICAgIHNlbGVjdGVkU3R1ZGVudElkcz17c2VsZWN0ZWRTdHVkZW50SWRzfVxyXG4gICAgICAgIHN0dWRlbnRzPXtzdHVkZW50c31cclxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRJc0JhdGNoVXNlS2V5UGFja2FnZU1vZGFsVmlzaWJsZShmYWxzZSl9XHJcbiAgICAgICAgb25TdWNjZXNzPXtoYW5kbGVCYXRjaFVzZUtleVBhY2thZ2VTdWNjZXNzfVxyXG4gICAgICAgIG9uR29Ub0Fzc2lnblBvaW50cz17aGFuZGxlR29Ub0Fzc2lnblBvaW50c0Zyb21SZWRlZW19XHJcbiAgICAgIC8+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgQ2xhc3NEZXRhaWw7XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUmVmIiwiQXJyb3dMZWZ0IiwiU2VhcmNoIiwiUGx1cyIsIlNldHRpbmdzIiwiVHJhc2gyIiwiRWRpdCIsIlVzZXJQbHVzIiwiVXBsb2FkIiwiRG93bmxvYWQiLCJVc2VycyIsIkxpbmsiLCJHaWZ0IiwiQmxvY2tzIiwiQ2hldnJvbkRvd24iLCJaYXAiLCJjbGFzc0FwaSIsInN0dWRlbnRBcGkiLCJ1c2VTZWxlY3RvciIsInVzZVRlbXBsYXRlIiwiQWRkU3R1ZGVudE1vZGFsIiwiRWRpdENsYXNzTW9kYWwiLCJJbXBvcnRTdHVkZW50TW9kYWwiLCJQdWJsaXNoVGFza01vZGFsIiwiUmVzZXRQYXNzd29yZE1vZGFsIiwiQXNzaWduQmxvY2tzTW9kYWwiLCJUcmFuc2ZlckNsYXNzTW9kYWwiLCJBc3NpZ25Qb2ludHNNb2RhbCIsIkJhdGNoVXNlS2V5UGFja2FnZU1vZGFsIiwiU3R1ZGVudExpc3QiLCJDbGFzc0RldGFpbCIsImNsYXNzSW5mbyIsImluaXRpYWxDbGFzc0luZm8iLCJzZWxlY3RlZFNjaG9vbCIsIm9uQmFjayIsIm9uQ2xhc3NJbmZvVXBkYXRlIiwib25DbGFzc0RlbGV0ZWQiLCJzZWxlY3RlZFN0dWRlbnQiLCJzZXRDbGFzc0luZm8iLCJzdHVkZW50cyIsInNldFN0dWRlbnRzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwic2V0U2VsZWN0ZWRTdHVkZW50IiwiaXNBZGRTdHVkZW50TW9kYWxWaXNpYmxlIiwic2V0SXNBZGRTdHVkZW50TW9kYWxWaXNpYmxlIiwiaXNTZXR0aW5nc0Ryb3Bkb3duT3BlbiIsInNldElzU2V0dGluZ3NEcm9wZG93bk9wZW4iLCJpc0JhdGNoQWN0aW9uc0Ryb3Bkb3duT3BlbiIsInNldElzQmF0Y2hBY3Rpb25zRHJvcGRvd25PcGVuIiwiZHJvcGRvd25SZWYiLCJzZXR0aW5nc0Ryb3Bkb3duUmVmIiwiYmF0Y2hBY3Rpb25zRHJvcGRvd25SZWYiLCJpc0VkaXRDbGFzc01vZGFsVmlzaWJsZSIsInNldElzRWRpdENsYXNzTW9kYWxWaXNpYmxlIiwiaXNJbXBvcnRTdHVkZW50TW9kYWxWaXNpYmxlIiwic2V0SXNJbXBvcnRTdHVkZW50TW9kYWxWaXNpYmxlIiwiaXNUcmFuc2ZlckNsYXNzTW9kYWxWaXNpYmxlIiwic2V0SXNUcmFuc2ZlckNsYXNzTW9kYWxWaXNpYmxlIiwiaXNJbnZpdGVDb2RlTW9kYWxWaXNpYmxlIiwic2V0SXNJbnZpdGVDb2RlTW9kYWxWaXNpYmxlIiwiaXNBc3NpZ25CbG9ja3NNb2RhbFZpc2libGUiLCJzZXRJc0Fzc2lnbkJsb2Nrc01vZGFsVmlzaWJsZSIsImludml0ZUNvZGUiLCJzZXRJbnZpdGVDb2RlIiwiaXNQdWJsaXNoVGFza01vZGFsVmlzaWJsZSIsInNldElzUHVibGlzaFRhc2tNb2RhbFZpc2libGUiLCJpc1Jlc2V0UGFzc3dvcmRNb2RhbFZpc2libGUiLCJzZXRJc1Jlc2V0UGFzc3dvcmRNb2RhbFZpc2libGUiLCJpc0Fzc2lnblBvaW50c01vZGFsVmlzaWJsZSIsInNldElzQXNzaWduUG9pbnRzTW9kYWxWaXNpYmxlIiwiaXNCYXRjaFVzZUtleVBhY2thZ2VNb2RhbFZpc2libGUiLCJzZXRJc0JhdGNoVXNlS2V5UGFja2FnZU1vZGFsVmlzaWJsZSIsInNlYXJjaGVkVGVhY2hlciIsInNldFNlYXJjaGVkVGVhY2hlciIsInRyYW5zZmVyTG9hZGluZyIsInNldFRyYW5zZmVyTG9hZGluZyIsImZpbGVMaXN0Iiwic2V0RmlsZUxpc3QiLCJ1cGxvYWRpbmciLCJzZXRVcGxvYWRpbmciLCJ0ZW1wbGF0ZXMiLCJzZXRUZW1wbGF0ZXMiLCJvZmZpY2lhbFRlbXBsYXRlcyIsInNlbGVjdGVkVGVtcGxhdGVJZCIsInNldFNlbGVjdGVkVGVtcGxhdGVJZCIsImxvYWRpbmdUZW1wbGF0ZXMiLCJzZXRMb2FkaW5nVGVtcGxhdGVzIiwic3R1ZGVudFRlbXBsYXRlVXNhZ2UiLCJzZXRTdHVkZW50VGVtcGxhdGVVc2FnZSIsInRlYWNoZXJUZW1wbGF0ZSIsInNldFRlYWNoZXJUZW1wbGF0ZSIsInNlbGVjdGVkU3R1ZGVudElkIiwic2V0U2VsZWN0ZWRTdHVkZW50SWQiLCJ1c2VyUm9sZXMiLCJzZXRVc2VyUm9sZXMiLCJzZWxlY3RlZFN0dWRlbnRJZHMiLCJzZXRTZWxlY3RlZFN0dWRlbnRJZHMiLCJpc1NlbGVjdEFsbCIsInNldElzU2VsZWN0QWxsIiwiY3VycmVudFRlbXBsYXRlIiwiZ2xvYmFsVGVtcGxhdGVDaGFuZ2VWZXJzaW9uIiwicmVmcmVzaEN1cnJlbnRUZW1wbGF0ZSIsImdsb2JhbEN1cnJlbnRUZW1wbGF0ZSIsInNldEdsb2JhbEN1cnJlbnRUZW1wbGF0ZSIsImZldGNoVGVhY2hlckN1cnJlbnRUZW1wbGF0ZSIsImlkIiwic2Nob29sSWQiLCJyZXNwb25zZSIsImdldFRlYWNoZXJDbGFzc2VzIiwidXNlcklkIiwiZGF0YSIsImNvZGUiLCJjdXJyZW50Q2xhc3MiLCJmaW5kIiwiY2xzIiwidGVtcGxhdGVOYW1lIiwidGVtcGxhdGVJbmZvIiwidGVtcGxhdGVJZCIsImlzT2ZmaWNpYWwiLCJjb25zb2xlIiwibG9nIiwic3luY1RlYWNoZXJUZW1wbGF0ZVRvU3R1ZGVudHMiLCJuZXdUZW1wbGF0ZSIsImxlbmd0aCIsImN1cnJlbnRQZXJzb25hbEFzc2lnbm1lbnRzIiwicGVyc29uYWxUZW1wbGF0ZUFzc2lnbm1lbnRzUmVmIiwiY3VycmVudCIsInByZXZTdHVkZW50cyIsIm1hcCIsInN0dWRlbnQiLCJoYXMiLCJuaWNrTmFtZSIsInBlcnNvbmFsVGVtcGxhdGVBc3NpZ25tZW50cyIsInNldFBlcnNvbmFsVGVtcGxhdGVBc3NpZ25tZW50cyIsIk1hcCIsInJlbmRlclZlcnNpb24iLCJzdGF0ZSIsInVzZXIiLCJ1c2VyU3RhdGUiLCJyb2xlSWQiLCJmZXRjaFN0dWRlbnRzIiwiUHJvbWlzZSIsImFsbCIsImdldENsYXNzU3R1ZGVudHMiLCJzdHVkZW50c0RhdGEiLCJzdHVkZW50Q291bnQiLCJ1cGRhdGVkQ2xhc3NJbmZvIiwidXNlcklkcyIsInMiLCJzdHVkZW50SW5mb01hcCIsIkFycmF5IiwiZnJvbSIsIk1hdGgiLCJjZWlsIiwiXyIsImkiLCJzbGljZSIsImJhdGNoSWQiLCJnZXRTdHVkZW50c0JhdGNoSW5mbyIsInRoZW4iLCJjYXRjaCIsInJlc3VsdHMiLCJPYmplY3QiLCJhc3NpZ24iLCJjb21wbGV0ZVN0dWRlbnRzIiwic3R1ZGVudEluZm8iLCJwZXJzb25hbFRlbXBsYXRlIiwiZ2V0Iiwic3R1ZGVudE51bWJlciIsInRvdGFsUG9pbnRzIiwiYXZhaWxhYmxlUG9pbnRzIiwiYXZhdGFyVXJsIiwicmVzdWx0Iiwic3R1ZGVudElkIiwic3R1ZGVudE5hbWUiLCJzdHVkZW50QXBpVGVtcGxhdGUiLCJmaW5hbFRlbXBsYXRlIiwibmV3VXNlclJvbGVzIiwicHVzaCIsIm1lc3NhZ2UiLCJlcnIiLCJFcnJvciIsInN0YWNrIiwidW5kZWZpbmVkIiwiY2xhc3NJZCIsImZldGNoVGVtcGxhdGVzIiwiZ2V0Um9sZVRlbXBsYXRlTGlzdCIsImdldE9mZmljaWFsVGVtcGxhdGVzIiwiY3VzdG9tUmVzcG9uc2UiLCJvZmZpY2lhbFJlc3BvbnNlIiwiY3VzdG9tVGVtcGxhdGVzIiwibWFya2VkT2ZmaWNpYWxUZW1wbGF0ZXMiLCJ0ZW1wbGF0ZSIsImFsbFRlbXBsYXRlcyIsImZldGNoVGVtcGxhdGVVc2FnZSIsImdldFVzZXJDdXJyZW50VGVtcGxhdGUiLCJnZXRTdHVkZW50VGVtcGxhdGVzIiwidGVhY2hlclJlc3BvbnNlIiwidGVhY2hlclRlbXBsYXRlRGF0YSIsInN0dWRlbnRSZXNwb25zZSIsInRlYWNoZXJJZCIsInBhZ2UiLCJzaXplIiwidXNhZ2UiLCJsaXN0IiwiZm9yRWFjaCIsIml0ZW0iLCJlbnRyaWVzIiwic3R1ZGVudHNDb3VudCIsImhhc1BlcnNvbmFsVGVtcGxhdGUiLCJ3aWxsVXBkYXRlIiwiaW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsImNsZWFySW50ZXJ2YWwiLCJoYW5kbGVDbGlja091dHNpZGUiLCJldmVudCIsImNvbnRhaW5zIiwidGFyZ2V0IiwiZG9jdW1lbnQiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImhhbmRsZVN0dWRlbnRDbGljayIsImhhbmRsZURlbGV0ZVN0dWRlbnQiLCJhbGVydCIsImNvbmZpcm1lZCIsIndpbmRvdyIsImNvbmZpcm0iLCJyZW1vdmVTdHVkZW50RnJvbUNsYXNzIiwiaGFuZGxlRWRpdENsYXNzIiwidmFsdWVzIiwiR2V0Tm90aWZpY2F0aW9uIiwibm90aWZpY2F0aW9uIiwib3JpZ2luYWxDbGFzc05hbWUiLCJjbGFzc05hbWUiLCJ1cGRhdGVDbGFzcyIsImdyYWRlIiwic3VjY2VzcyIsInN0YXR1cyIsImhhbmRsZUltcG9ydFN0dWRlbnRzIiwiZmlsZSIsImhhbmRsZUV4cG9ydFN0dWRlbnRzIiwiZXhwb3J0U3R1ZGVudHMiLCJoYW5kbGVTZWFyY2hUZWFjaGVyIiwicGhvbmUiLCJzZWFyY2hUZWFjaGVyQnlQaG9uZSIsImhhbmRsZVRyYW5zZmVyQ2xhc3MiLCJuZXdUZWFjaGVySWQiLCJ0cmFuc2ZlclR5cGUiLCJhc3Npc3RhbnRUZWFjaGVySWQiLCJ0cmFuc2ZlckNsYXNzIiwiaGFuZGxlUmVtb3ZlQXNzaXN0YW50IiwiaGFuZGxlR2VuZXJhdGVJbnZpdGVDb2RlIiwiZ2VuZXJhdGVJbnZpdGVDb2RlIiwiaGFuZGxlQXNzaWduQmxvY2tzIiwiaGFuZGxlSW5kaXZpZHVhbEFzc2lnbkJsb2NrcyIsImhhbmRsZVRlbXBsYXRlQXNzaWdubWVudCIsImhhbmRsZURlbGV0ZUNsYXNzIiwiTW9kYWwiLCJ3YXJuaW5nIiwidGl0bGUiLCJjZW50ZXJlZCIsImNvbnRlbnQiLCJkaXYiLCJwIiwib2wiLCJsaSIsIm9rVGV4dCIsInN0cm9uZyIsInN2ZyIsImZpbGwiLCJ2aWV3Qm94IiwicGF0aCIsImZpbGxSdWxlIiwiZCIsImNsaXBSdWxlIiwic3BhbiIsImNhbmNlbFRleHQiLCJva0J1dHRvblByb3BzIiwiZGFuZ2VyIiwib25PayIsImRlbGV0ZUNsYXNzIiwiaGFuZGxlTWVudUl0ZW1DbGljayIsImFjdGlvbiIsImhhbmRsZVNldHRpbmdzTWVudUl0ZW1DbGljayIsInNob3VsZEJlU2VsZWN0QWxsIiwidG90YWxTdHVkZW50cyIsImhhbmRsZVNlbGVjdEFsbCIsImFsbFN0dWRlbnRJZHMiLCJoYW5kbGVTdHVkZW50U2VsZWN0IiwiaW5jbHVkZXMiLCJuZXdTZWxlY3RlZElkcyIsImZpbHRlciIsImhhbmRsZUJhdGNoQWN0aW9uIiwiaGFuZGxlQmF0Y2hSZW1vdmVTdHVkZW50cyIsImhhbmRsZUJhdGNoVXNlS2V5UGFja2FnZSIsImluZm8iLCJzdHVkZW50SWRzIiwic2VsZWN0ZWRTdHVkZW50c0luZm8iLCJ0b3RhbEF2YWlsYWJsZVBvaW50cyIsInJlZHVjZSIsInN1bSIsIkluZm9DaXJjbGVPdXRsaW5lZCIsImhpZGVMb2FkaW5nIiwiY2xvc2UiLCJoYW5kbGVBZGRTdHVkZW50Iiwic3R1ZGVudERhdGEiLCJwYXNzd29yZCIsImFkZFN0dWRlbnRUb0NsYXNzIiwiaGFuZGxlVXBsb2FkIiwiaGFuZGxlUmVtb3ZlRmlsZSIsInByZXYiLCJmIiwidWlkIiwiaGFuZGxlUHVibGlzaFRhc2siLCJkZWZhdWx0IiwidGFza0FwaSIsInBhcmFtcyIsInRhc2tOYW1lIiwidGFza0Rlc2NyaXB0aW9uIiwidGFza1R5cGUiLCJzdGFydERhdGUiLCJEYXRlIiwiZW5kRGF0ZSIsIm5vdyIsInRhc2tDb250ZW50IiwiYXR0YWNobWVudHMiLCJ1cmwiLCJCb29sZWFuIiwic2VsZkFzc2Vzc21lbnRJdGVtcyIsInByaW9yaXR5IiwiaXNQdWJsaWMiLCJhbGxvd0xhdGVTdWJtaXNzaW9uIiwicHVibGlzaFRhc2siLCJoYW5kbGVSZXNldFBhc3N3b3JkIiwiaGFuZGxlU2VsZWN0VGVtcGxhdGUiLCJhZGRVc2VySm9pblJvbGUiLCJiYXRjaEFkZFVzZXJKb2luUm9sZSIsInRhcmdldFN0dWRlbnRzIiwidXNlclJvbGVzTWFwIiwidXNlcnNEYXRhIiwidXNlckluZm8iLCJ1Iiwib3JpZ2luYWxUZW1wbGF0ZUlkIiwiYmF0Y2hVc2VycyIsInVzZXJzIiwic3VjY2Vzc0NvdW50IiwiZmFpbENvdW50Iiwic29tZSIsInIiLCJzZWxlY3RlZFRlbXBsYXRlIiwidCIsInRlbXBsYXRlRGF0YSIsIm5hbWUiLCJuZXdNYXAiLCJzZXQiLCJ1cGRhdGVkU3R1ZGVudHMiLCJwZXJzb25hbFRlbXBsYXRlQXNzaWdubWVudHNCZWZvcmUiLCJwZXJzb25hbFRlbXBsYXRlQXNzaWdubWVudHNBZnRlciIsInNldFRpbWVvdXQiLCJoYW5kbGVUZW1wbGF0ZVVzYWdlQ2xpY2siLCJlIiwic3RvcFByb3BhZ2F0aW9uIiwiaGFuZGxlQXNzaWduUG9pbnRzIiwiZXhwaXJlVGltZSIsInN0dWRlbnRFeHBpcmllcyIsInBvaW50c0FwaSIsImFzc2lnblBlcm1pc3Npb24iLCJzdHVkZW50VXNlcklkIiwicmVtYXJrIiwicmVmcmVzaFN0dWRlbnRMaXN0IiwidXBkYXRlQ2xhc3NDdXJyZW50VGVtcGxhdGUiLCJoYW5kbGVVcGRhdGVTdHVkZW50VGVtcGxhdGUiLCJoYW5kbGVCYXRjaFVzZUtleVBhY2thZ2VTdWNjZXNzIiwiaGFuZGxlR29Ub0Fzc2lnblBvaW50c0Zyb21SZWRlZW0iLCJoYW5kbGVCYXRjaEFzc2lnblBvaW50cyIsImJhdGNoQXNzaWduUGVybWlzc2lvbiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJnZXRBdmF0YXJDb2xvciIsImluZGV4IiwiY29sb3JzIiwic2FmZUluZGV4IiwibWF4IiwicmVmIiwic3R5bGUiLCJjb2xvciIsImgzIiwic2Nob29sTmFtZSIsIm9uU3R1ZGVudENsaWNrIiwib25TdHVkZW50U2VsZWN0Iiwib25SZXRyeSIsIm9uSW5kaXZpZHVhbEFzc2lnbkJsb2NrcyIsIm9uQXNzaWduUG9pbnRzIiwiYmFja2dyb3VuZCIsImZpbmRJbmRleCIsImNoYXJBdCIsImRpc2FibGVkIiwid2lkdGgiLCJOdW1iZXIiLCJjdXJzb3IiLCJoNCIsInZpc2libGUiLCJvbkNhbmNlbCIsImluaXRpYWxWYWx1ZXMiLCJvbkltcG9ydCIsIm9uU2VhcmNoVGVhY2hlciIsImhhc0Fzc2lzdGFudFRlYWNoZXIiLCJvblJlbW92ZUFzc2lzdGFudCIsImgyIiwieG1sbnMiLCJzdHJva2UiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsIm5hdmlnYXRvciIsImNsaXBib2FyZCIsIndyaXRlVGV4dCIsInVsIiwic2VsZWN0ZWRTdHVkZW50cyIsInNldFNlbGVjdGVkU3R1ZGVudHMiLCJkaXNwbGF5VGVtcGxhdGVzIiwiaXNCYXRjaCIsImNvdW50IiwiaXNDbGFzc0NhcmRBc3NpZ24iLCJvblNlbGVjdFRlbXBsYXRlIiwib25UZW1wbGF0ZVVzYWdlQ2xpY2siLCJvblJlZnJlc2hUZW1wbGF0ZXMiLCJvblN1Y2Nlc3MiLCJvblVwZGF0ZVN0dWRlbnRUZW1wbGF0ZSIsIm9uR29Ub1JlZGVlbUtleSIsIm9wZW4iLCJvbkNsb3NlIiwib25Hb1RvQXNzaWduUG9pbnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/ClassDetail.tsx\n"));

/***/ })

});