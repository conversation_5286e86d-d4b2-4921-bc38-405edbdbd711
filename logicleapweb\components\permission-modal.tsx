import React, { useState, useEffect } from 'react';
import { Modal, Table, Switch, List, Typography, Button, Tag, Dropdown } from 'antd';
import { 
    getExtensions, 
    getUserPermissions,
    updateExtensionPermission,
    updateBlockPermission,
} from '../lib/api/permission';
import { getTemplateInfo } from '../lib/api/role';
import { EllipsisOutlined, MoreOutlined } from '@ant-design/icons';
import { GetNotification } from 'logic-common/dist/components/Notification';

const { Text } = Typography;
const notification = GetNotification();

interface PermissionModalProps {
    userId: number;
    visible: boolean;
    onClose: () => void;
    templateId?: number;
    readOnly?: boolean;
    onUseTemplate?: (templateId: number) => void;
    onEditTemplate?: (templateId: number) => void;
    onCreateBaseTemplate?: (templateId: number) => void;
    onDeleteTemplate?: (templateId: number) => void;
    hideMoreOptions?: boolean;
}

interface ExtensionPermission {
    id?: number;
    extensionId: string;
    isEnabled: boolean;
}

interface BlockPermission {
    id?: number;
    extensionId: string;
    blockId: string;
    isEnabled: boolean;
}

const PermissionModal: React.FC<PermissionModalProps> = ({ 
    userId, 
    visible, 
    onClose, 
    templateId,
    readOnly = false,
    onUseTemplate,
    onEditTemplate,
    onCreateBaseTemplate,
    onDeleteTemplate,
    hideMoreOptions = false
}) => {
    const [extensions, setExtensions] = useState<any[]>([]);
    const [selectedPermissions, setSelectedPermissions] = useState<{
        extensions: ExtensionPermission[];
        blocks: BlockPermission[];
    }>({ extensions: [], blocks: [] });
    const [loading, setLoading] = useState(false);
    const [permissions, setPermissions] = useState<any | null>(null);

    // 获取所有数据
    const fetchData = async () => {
        setLoading(true);
        try {
            const [extensionsRes, templateRes] = await Promise.all([
                getExtensions(),
                templateId 
                    ? getTemplateInfo(templateId)
                    : getUserPermissions(userId)
            ]);
            console.log('获取权限数据响应:', templateRes);
            // 过滤掉 URL 相关的扩展
            const extensionsList = (extensionsRes.data?.data || []).filter((ext: any) => 
                !ext.extensionId.includes('logicleapUrl') && 
                !ext.extensionId.includes('url') &&
                !ext.extensionId.includes('URL')
            );

            let permissions;
            if (templateId) {
                // 处理模板权限数据
                const templateData = templateRes.data?.data;
                
                // 过滤掉模板中的 URL 相关权限
                if (templateData?.permissions) {
                    templateData.permissions.extensions = templateData.permissions.extensions.filter(
                        (ext: any) => !ext.extensionId.includes('logicleapUrl') && 
                                     !ext.extensionId.includes('url') &&
                                     !ext.extensionId.includes('URL')
                    );
                    templateData.permissions.blocks = templateData.permissions.blocks.filter(
                        (block: any) => !block.extensionId.includes('logicleapUrl') && 
                                       !block.extensionId.includes('url') &&
                                       !block.extensionId.includes('URL')
                    );
                }
                
                permissions = {
                    extensions: templateData?.permissions?.extensions || [],
                    blocks: templateData?.permissions?.blocks || [],
                    templateInfo: {
                        name: templateData?.templateName,
                        description: templateData?.templateDescription,
                        isDefault: templateData?.isDefault,
                        isOfficial: templateData?.isOfficial
                    }
                };
            } else {
                permissions = templateRes.data?.data || { extensions: [], blocks: [] };
            }

            // 初始化已选权限
            const initialPermissions = {
                extensions: permissions.extensions?.map((ext: any) => ({
                    extensionId: ext.extensionId,
                    isEnabled: ext.isEnabled,
                    id: ext.id
                })) || [],
                blocks: permissions.blocks?.map((block: any) => ({
                    extensionId: block.extensionId,
                    blockId: block.blockId,
                    isEnabled: block.isEnabled,
                    id: block.id
                })) || [],
            };

            // 合并扩展数据和权限数据
            const mergedExtensions = extensionsList.map((ext: any) => {
                // 查找扩展权限
                const extensionPermission = permissions.extensions?.find(
                    (p: any) => p.extensionId === ext.extensionId
                );

                return {
                    ...ext,
                    isEnabled: extensionPermission ? extensionPermission.isEnabled : false,
                    blocks: Array.isArray(ext.blocks) ? ext.blocks.map((block: any) => {
                        // 查找积木块权限
                        const blockPermission = permissions.blocks?.find(
                            (p: any) => p.extensionId === ext.extensionId && p.blockId === block.blockId
                        );

                        return {
                            ...block,
                            isEnabled: blockPermission ? blockPermission.isEnabled : false
                        };
                    }) : []
                };
            });

            

            setExtensions(mergedExtensions);
            setPermissions(permissions);
            setSelectedPermissions(initialPermissions);

        } catch (error) {
            console.error('获取权限数据失败:', error);
            notification.error('获取权限数据失败');
        }
        setLoading(false);
    };

    // 处理扩展权限变更
    const handleExtensionChange = (extensionId: string, enabled: boolean) => {
        const updatedExtensions = extensions.map(ext => 
            ext.extensionId === extensionId ? { 
                ...ext, 
                isEnabled: enabled,
                blocks: ext.blocks.map((block: any) => ({
                    ...block,
                    isEnabled: enabled ? block.isEnabled : false
                }))
            } : ext
        );
        setExtensions(updatedExtensions);
        
        // 更新选中的权限
        setSelectedPermissions(prev => {
            // 查找是否已存在该扩展的权限记录
            const existingExt = prev.extensions.find(e => e.extensionId === extensionId);
            
            const newExtensions = [
                ...prev.extensions.filter(e => e.extensionId !== extensionId),
                existingExt ? 
                    { ...existingExt, isEnabled: enabled } : 
                    { extensionId, isEnabled: enabled }
            ];

            // 如果禁用扩展，将其下所有积木块的权限设置为禁用
            const newBlocks = prev.blocks.map(b => 
                b.extensionId === extensionId ? 
                    { ...b, isEnabled: false } : 
                    b
            );
            
            return {
                extensions: newExtensions,
                blocks: newBlocks
            };
        });
    };

    // 处理积木块权限变更
    const handleBlockChange = (extensionId: string, blockId: string, enabled: boolean) => {
        const updatedExtensions = extensions.map(ext => 
            ext.extensionId === extensionId ? {
                ...ext,
                blocks: ext.blocks.map((block: any) =>
                    block.blockId === blockId ? { ...block, isEnabled: enabled } : block
                )
            } : ext
        );
        setExtensions(updatedExtensions);
        
        // 更新选中的权限
        setSelectedPermissions(prev => {
            // 查找是否已存在该积木块的权限记录
            const existingBlock = prev.blocks.find(b => b.blockId === blockId);
            
            const newBlocks = [
                ...prev.blocks.filter(b => b.blockId !== blockId),
                existingBlock ? 
                    { ...existingBlock, isEnabled: enabled } : 
                    { extensionId, blockId, isEnabled: enabled }
            ];

            return {
                ...prev,
                blocks: newBlocks
            };
        });
    };

    // 保存所有权限
    const handleSave = async () => {
        setLoading(true);
        try {
            // 批量更新扩展权限
            for (const ext of selectedPermissions.extensions) {
                if (ext.id) {
                    // 更新已有权限
                    await updateExtensionPermission(userId, ext.extensionId, ext.isEnabled);
                } else {
                    // 新增权限
                    await updateExtensionPermission(userId, ext.extensionId, ext.isEnabled);
                }
            }
            
            // 批量更新积木块权限
            for (const block of selectedPermissions.blocks) {
                if (block.id) {
                    // 更新已有权限
                    await updateBlockPermission(userId, block.extensionId, block.blockId, block.isEnabled);
                } else {
                    // 新增权限
                    await updateBlockPermission(userId, block.extensionId, block.blockId, block.isEnabled);
                }
            }
            
            notification.success('保存权限成功');
            onClose();
        } catch (error) {
            console.error('保存权限失败:', error);
            notification.error('保存权限失败');
        }
        setLoading(false);
    };

    useEffect(() => {
        if (visible) {
            fetchData();
        }
    }, [visible, userId]);

    const columns = [
        {
            title: '扩展名称',
            dataIndex: 'extensionName',
            key: 'extensionName'
        },
        {
            title: '描述',
            dataIndex: 'description',
            key: 'description'
        },
        {
            title: '启用状态',
            key: 'isEnabled',
            render: (_: any, record: any) => (
                <Switch
                    checked={record.isEnabled}
                    onChange={(checked) => handleExtensionChange(record.extensionId, checked)}
                    disabled={readOnly}
                />
            )
        }
    ];

    return (
        <Modal
            title={templateId ? (readOnly ? "查看权限模板（仅查看，不可编辑）" : "编辑权限") : "积木权限管理"}
            open={visible}
            onCancel={onClose}
            onOk={handleSave}
            width={800}
            confirmLoading={loading}
            footer={readOnly ? (
                <div className="flex justify-between items-center">
                    <div></div>
                    <div className="flex gap-2">
                        <Button 
                            key="use"
                            type="primary"
                            onClick={() => onUseTemplate?.(templateId!)}
                            className="!bg-[#4766C2] hover:!bg-[#3d57a7]"
                        >
                            使用此模板
                        </Button>
                        {/* <Button key="close" onClick={onClose}>
                            关闭
                        </Button> */}
                    </div>
                </div>
            ) : undefined}
            centered
            style={{
                maxWidth: '90vw',
                margin: '10vh auto',
                padding: 0,
                top: 0
            }}
            bodyStyle={{
                height: 'calc(80vh - 110px)',
                padding: '20px',
                overflow: 'auto'
            }}
        >
            <div className="h-full flex flex-col">
                {templateId && (
                    <div className="flex-none mb-4 p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                            <h3 className="text-base font-medium text-gray-800">
                                {permissions?.templateInfo?.name || '模板信息'}
                            </h3>
                            <Tag color="blue">
                                已启用 {extensions.filter(e => e.isEnabled).length} 个扩展
                            </Tag>
                        </div>
                        {permissions?.templateInfo?.description && (
                            <p className="text-sm text-gray-500 mb-2">
                                {permissions.templateInfo.description}
                            </p>
                        )}
                        <div className="text-sm text-gray-500">
                            <p>已启用积木块：{
                                extensions.reduce((total, ext) => 
                                    total + (ext.blocks || []).filter((block: any) => block.isEnabled).length, 
                                0)
                            } 个</p>
                        </div>
                    </div>
                )}

                <div className="flex-1 min-h-0 overflow-auto">
                    <Table
                        columns={columns}
                        dataSource={extensions}
                        rowKey="extensionId"
                        loading={loading}
                        pagination={false}
                        scroll={{ x: 'max-content' }}
                        expandable={{
                            expandedRowRender: (record) => (
                                <List
                                    size="small"
                                    dataSource={record.blocks || []}
                                    renderItem={(block: any) => (
                                        <List.Item
                                            actions={[
                                                <Switch
                                                    key="switch"
                                                    checked={block.isEnabled}
                                                    disabled={!record.isEnabled || readOnly}
                                                    onChange={(checked) => 
                                                        handleBlockChange(record.extensionId, block.blockId, checked)
                                                    }
                                                />
                                            ]}
                                        >
                                            <List.Item.Meta
                                                title={block.name}
                                                description={block.description}
                                            />
                                        </List.Item>
                                    )}
                                />
                            )
                        }}
                    />
                </div>
            </div>
        </Modal>
    );
};

export default PermissionModal; 