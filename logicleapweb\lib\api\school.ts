/*
 * @Author: Zwww <EMAIL>
 * @Date: 2025-04-29 11:54:36
 * @LastEditors: Zwww <EMAIL>
 * @LastEditTime: 2025-05-06 17:27:15
 * @FilePath: \sourceCode\logicleapweb\lib\api\school.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import request from '../request'

interface SchoolParams {
  schoolName: string;
  province: string;
  city: string;
  district: string;
  address?: string;
  contactPhone?: string;
  contactPerson?: string;
}

interface SchoolQueryParams {
  userId?: number;
  keyword?: string;
  page?: number;
  size?: number;
  province?: string;
  district?: string;
  city?: string;
}

const schoolBaseUrl = '/api/user-school';
const schoolRelationBaseUrl = '/api/user-school-relation';

export const schoolApi = {
  // 获取学校列表  okok
  getList: (params: SchoolQueryParams) => {
    return request.get(schoolBaseUrl, { params });
  },

  // 添加学校   okok
  add: (school: SchoolParams) => {
    return request.post(schoolBaseUrl, school);
  },

  // 更新学校信息   okok
  update: (school: SchoolParams, id: number) => {
    console.log(school);
    return request.patch(schoolBaseUrl + id, school);
  },

  // 删除学校    okok
  delete: (id: number) => {
    return request.delete(schoolBaseUrl + "/" + id);
  },

  // 获取省市区数据    没有用到 直接okok
  getRegions: () => {
    return request.get('/app/user/school/regions');
  },

  // 绑定学校   okok
  bindSchool: (params: { userId: number; schoolId: number }) => {
    return request.post(schoolRelationBaseUrl, params);
  },

  // 解绑学校  okok
  unbindSchool: (params: { userId: number; schoolId: number }) => {
    return request.delete(`${schoolRelationBaseUrl}/user/${params.userId}/school/${params.schoolId}`);
  },

  // 获取用户关联的学校列表  
  getUserSchools: () => {
    return request.get('/api/user-school/listByUserId');
  },

  // 获取学校的教师列表    okok
  getSchoolTeachers: (schoolId: number) => {
    return request.get(`${schoolRelationBaseUrl}/school/${schoolId}/teachers`);
  },

  // 获取学校的班级列表 
  getSchoolClasses: (params: { schoolId: number; page?: number; size?: number }) => {
    return request.get(`/api/user/class/school/${params.schoolId}/all`, {
      params
    });
  },

  // 获取学校的省市区信息  没人调用他。okok
  getSchoolRegion: (schoolId: number) => {
    return request.get(`/app/user/school/${schoolId}/region`);
  }
} 