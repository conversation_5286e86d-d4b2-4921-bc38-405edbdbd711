"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./components/permission-modal.tsx":
/*!*****************************************!*\
  !*** ./components/permission-modal.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,List,Modal,Switch,Table,Tag,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,List,Modal,Switch,Table,Tag,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/switch/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,List,Modal,Switch,Table,Tag,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,List,Modal,Switch,Table,Tag,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,List,Modal,Switch,Table,Tag,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,List,Modal,Switch,Table,Tag,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,List,Modal,Switch,Table,Tag,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/list/index.js\");\n/* harmony import */ var _lib_api_permission__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/api/permission */ \"(app-pages-browser)/./lib/api/permission.ts\");\n/* harmony import */ var _lib_api_role__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst { Text } = _barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_4__.GetNotification)();\nconst PermissionModal = (param)=>{\n    let { userId, visible, onClose, templateId, readOnly = false, onUseTemplate, onEditTemplate, onCreateBaseTemplate, onDeleteTemplate, hideMoreOptions = false } = param;\n    var _permissions_templateInfo, _permissions_templateInfo1;\n    _s();\n    const [extensions, setExtensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPermissions, setSelectedPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        extensions: [],\n        blocks: []\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 获取所有数据\n    const fetchData = async ()=>{\n        setLoading(true);\n        try {\n            var _extensionsRes_data, _permissions_extensions, _permissions_blocks;\n            const [extensionsRes, templateRes] = await Promise.all([\n                (0,_lib_api_permission__WEBPACK_IMPORTED_MODULE_2__.getExtensions)(),\n                templateId ? (0,_lib_api_role__WEBPACK_IMPORTED_MODULE_3__.getTemplateInfo)(templateId) : (0,_lib_api_permission__WEBPACK_IMPORTED_MODULE_2__.getUserPermissions)(userId)\n            ]);\n            console.log(\"获取权限数据响应:\", templateRes);\n            // 过滤掉 URL 相关的扩展\n            const extensionsList = (((_extensionsRes_data = extensionsRes.data) === null || _extensionsRes_data === void 0 ? void 0 : _extensionsRes_data.data) || []).filter((ext)=>!ext.extensionId.includes(\"logicleapUrl\") && !ext.extensionId.includes(\"url\") && !ext.extensionId.includes(\"URL\"));\n            let permissions;\n            if (templateId) {\n                var _templateRes_data, _templateData_permissions, _templateData_permissions1;\n                // 处理模板权限数据\n                const templateData = (_templateRes_data = templateRes.data) === null || _templateRes_data === void 0 ? void 0 : _templateRes_data.data;\n                // 过滤掉模板中的 URL 相关权限\n                if (templateData === null || templateData === void 0 ? void 0 : templateData.permissions) {\n                    templateData.permissions.extensions = templateData.permissions.extensions.filter((ext)=>!ext.extensionId.includes(\"logicleapUrl\") && !ext.extensionId.includes(\"url\") && !ext.extensionId.includes(\"URL\"));\n                    templateData.permissions.blocks = templateData.permissions.blocks.filter((block)=>!block.extensionId.includes(\"logicleapUrl\") && !block.extensionId.includes(\"url\") && !block.extensionId.includes(\"URL\"));\n                }\n                permissions = {\n                    extensions: (templateData === null || templateData === void 0 ? void 0 : (_templateData_permissions = templateData.permissions) === null || _templateData_permissions === void 0 ? void 0 : _templateData_permissions.extensions) || [],\n                    blocks: (templateData === null || templateData === void 0 ? void 0 : (_templateData_permissions1 = templateData.permissions) === null || _templateData_permissions1 === void 0 ? void 0 : _templateData_permissions1.blocks) || [],\n                    templateInfo: {\n                        name: templateData === null || templateData === void 0 ? void 0 : templateData.templateName,\n                        description: templateData === null || templateData === void 0 ? void 0 : templateData.templateDescription,\n                        isDefault: templateData === null || templateData === void 0 ? void 0 : templateData.isDefault,\n                        isOfficial: templateData === null || templateData === void 0 ? void 0 : templateData.isOfficial\n                    }\n                };\n            } else {\n                var _templateRes_data1;\n                permissions = ((_templateRes_data1 = templateRes.data) === null || _templateRes_data1 === void 0 ? void 0 : _templateRes_data1.data) || {\n                    extensions: [],\n                    blocks: []\n                };\n            }\n            // 初始化已选权限\n            const initialPermissions = {\n                extensions: ((_permissions_extensions = permissions.extensions) === null || _permissions_extensions === void 0 ? void 0 : _permissions_extensions.map((ext)=>({\n                        extensionId: ext.extensionId,\n                        isEnabled: ext.isEnabled,\n                        id: ext.id\n                    }))) || [],\n                blocks: ((_permissions_blocks = permissions.blocks) === null || _permissions_blocks === void 0 ? void 0 : _permissions_blocks.map((block)=>({\n                        extensionId: block.extensionId,\n                        blockId: block.blockId,\n                        isEnabled: block.isEnabled,\n                        id: block.id\n                    }))) || []\n            };\n            // 合并扩展数据和权限数据\n            const mergedExtensions = extensionsList.map((ext)=>{\n                var _permissions_extensions;\n                // 查找扩展权限\n                const extensionPermission = (_permissions_extensions = permissions.extensions) === null || _permissions_extensions === void 0 ? void 0 : _permissions_extensions.find((p)=>p.extensionId === ext.extensionId);\n                return {\n                    ...ext,\n                    isEnabled: extensionPermission ? extensionPermission.isEnabled : false,\n                    blocks: Array.isArray(ext.blocks) ? ext.blocks.map((block)=>{\n                        var _permissions_blocks;\n                        // 查找积木块权限\n                        const blockPermission = (_permissions_blocks = permissions.blocks) === null || _permissions_blocks === void 0 ? void 0 : _permissions_blocks.find((p)=>p.extensionId === ext.extensionId && p.blockId === block.blockId);\n                        return {\n                            ...block,\n                            isEnabled: blockPermission ? blockPermission.isEnabled : false\n                        };\n                    }) : []\n                };\n            });\n            setExtensions(mergedExtensions);\n            setPermissions(permissions);\n            setSelectedPermissions(initialPermissions);\n        } catch (error) {\n            console.error(\"获取权限数据失败:\", error);\n            notification.error(\"获取权限数据失败\");\n        }\n        setLoading(false);\n    };\n    // 处理扩展权限变更\n    const handleExtensionChange = (extensionId, enabled)=>{\n        const updatedExtensions = extensions.map((ext)=>ext.extensionId === extensionId ? {\n                ...ext,\n                isEnabled: enabled,\n                blocks: ext.blocks.map((block)=>({\n                        ...block,\n                        isEnabled: enabled ? block.isEnabled : false\n                    }))\n            } : ext);\n        setExtensions(updatedExtensions);\n        // 更新选中的权限\n        setSelectedPermissions((prev)=>{\n            // 查找是否已存在该扩展的权限记录\n            const existingExt = prev.extensions.find((e)=>e.extensionId === extensionId);\n            const newExtensions = [\n                ...prev.extensions.filter((e)=>e.extensionId !== extensionId),\n                existingExt ? {\n                    ...existingExt,\n                    isEnabled: enabled\n                } : {\n                    extensionId,\n                    isEnabled: enabled\n                }\n            ];\n            // 如果禁用扩展，将其下所有积木块的权限设置为禁用\n            const newBlocks = prev.blocks.map((b)=>b.extensionId === extensionId ? {\n                    ...b,\n                    isEnabled: false\n                } : b);\n            return {\n                extensions: newExtensions,\n                blocks: newBlocks\n            };\n        });\n    };\n    // 处理积木块权限变更\n    const handleBlockChange = (extensionId, blockId, enabled)=>{\n        const updatedExtensions = extensions.map((ext)=>ext.extensionId === extensionId ? {\n                ...ext,\n                blocks: ext.blocks.map((block)=>block.blockId === blockId ? {\n                        ...block,\n                        isEnabled: enabled\n                    } : block)\n            } : ext);\n        setExtensions(updatedExtensions);\n        // 更新选中的权限\n        setSelectedPermissions((prev)=>{\n            // 查找是否已存在该积木块的权限记录\n            const existingBlock = prev.blocks.find((b)=>b.blockId === blockId);\n            const newBlocks = [\n                ...prev.blocks.filter((b)=>b.blockId !== blockId),\n                existingBlock ? {\n                    ...existingBlock,\n                    isEnabled: enabled\n                } : {\n                    extensionId,\n                    blockId,\n                    isEnabled: enabled\n                }\n            ];\n            return {\n                ...prev,\n                blocks: newBlocks\n            };\n        });\n    };\n    // 保存所有权限\n    const handleSave = async ()=>{\n        setLoading(true);\n        try {\n            // 批量更新扩展权限\n            for (const ext of selectedPermissions.extensions){\n                if (ext.id) {\n                    // 更新已有权限\n                    await (0,_lib_api_permission__WEBPACK_IMPORTED_MODULE_2__.updateExtensionPermission)(userId, ext.extensionId, ext.isEnabled);\n                } else {\n                    // 新增权限\n                    await (0,_lib_api_permission__WEBPACK_IMPORTED_MODULE_2__.updateExtensionPermission)(userId, ext.extensionId, ext.isEnabled);\n                }\n            }\n            // 批量更新积木块权限\n            for (const block of selectedPermissions.blocks){\n                if (block.id) {\n                    // 更新已有权限\n                    await (0,_lib_api_permission__WEBPACK_IMPORTED_MODULE_2__.updateBlockPermission)(userId, block.extensionId, block.blockId, block.isEnabled);\n                } else {\n                    // 新增权限\n                    await (0,_lib_api_permission__WEBPACK_IMPORTED_MODULE_2__.updateBlockPermission)(userId, block.extensionId, block.blockId, block.isEnabled);\n                }\n            }\n            notification.success(\"保存权限成功\");\n            onClose();\n        } catch (error) {\n            console.error(\"保存权限失败:\", error);\n            notification.error(\"保存权限失败\");\n        }\n        setLoading(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (visible) {\n            fetchData();\n        }\n    }, [\n        visible,\n        userId\n    ]);\n    const columns = [\n        {\n            title: \"扩展名称\",\n            dataIndex: \"extensionName\",\n            key: \"extensionName\"\n        },\n        {\n            title: \"描述\",\n            dataIndex: \"description\",\n            key: \"description\"\n        },\n        {\n            title: \"启用状态\",\n            key: \"isEnabled\",\n            render: (_, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    checked: record.isEnabled,\n                    onChange: (checked)=>handleExtensionChange(record.extensionId, checked),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 17\n                }, undefined)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        title: templateId ? readOnly ? \"查看权限模板（仅查看，不可编辑）\" : \"编辑权限\" : \"积木权限管理\",\n        open: visible,\n        onCancel: onClose,\n        onOk: handleSave,\n        width: 800,\n        confirmLoading: loading,\n        footer: readOnly ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 21\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        type: \"primary\",\n                        onClick: ()=>onUseTemplate === null || onUseTemplate === void 0 ? void 0 : onUseTemplate(templateId),\n                        className: \"!bg-[#4766C2] hover:!bg-[#3d57a7]\",\n                        children: \"使用此模板\"\n                    }, \"use\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 25\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 21\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n            lineNumber: 310,\n            columnNumber: 17\n        }, void 0) : undefined,\n        centered: true,\n        style: {\n            maxWidth: \"90vw\",\n            margin: \"10vh auto\",\n            padding: 0,\n            top: 0\n        },\n        bodyStyle: {\n            height: \"calc(80vh - 110px)\",\n            padding: \"20px\",\n            overflow: \"auto\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col\",\n            children: [\n                templateId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-none mb-4 p-4 bg-gray-50 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-base font-medium text-gray-800\",\n                                    children: (permissions === null || permissions === void 0 ? void 0 : (_permissions_templateInfo = permissions.templateInfo) === null || _permissions_templateInfo === void 0 ? void 0 : _permissions_templateInfo.name) || \"模板信息\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    color: \"blue\",\n                                    children: [\n                                        \"已启用 \",\n                                        extensions.filter((e)=>e.isEnabled).length,\n                                        \" 个扩展\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 25\n                        }, undefined),\n                        (permissions === null || permissions === void 0 ? void 0 : (_permissions_templateInfo1 = permissions.templateInfo) === null || _permissions_templateInfo1 === void 0 ? void 0 : _permissions_templateInfo1.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 mb-2\",\n                            children: permissions.templateInfo.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 29\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"已启用积木块：\",\n                                    extensions.reduce((total, ext)=>total + (ext.blocks || []).filter((block)=>block.isEnabled).length, 0),\n                                    \" 个\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-h-0 overflow-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        columns: columns,\n                        dataSource: extensions,\n                        rowKey: \"extensionId\",\n                        loading: loading,\n                        pagination: false,\n                        scroll: {\n                            x: \"max-content\"\n                        },\n                        expandable: {\n                            expandedRowRender: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    size: \"small\",\n                                    dataSource: record.blocks || [],\n                                    renderItem: (block)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Item, {\n                                            actions: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    checked: block.isEnabled,\n                                                    disabled: !record.isEnabled || readOnly,\n                                                    onChange: (checked)=>handleBlockChange(record.extensionId, block.blockId, checked)\n                                                }, \"switch\", false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 49\n                                                }, void 0)\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_List_Modal_Switch_Table_Tag_Typography_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Item.Meta, {\n                                                title: block.name,\n                                                description: block.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 45\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 41\n                                        }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 33\n                                }, void 0)\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n            lineNumber: 340,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\components\\\\permission-modal.tsx\",\n        lineNumber: 302,\n        columnNumber: 9\n    }, undefined);\n};\n_s(PermissionModal, \"dhVrNzzF+Ha4MFLzlFV6S2XBaDo=\");\n_c = PermissionModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PermissionModal);\nvar _c;\n$RefreshReg$(_c, \"PermissionModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/permission-modal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api/role.ts":
/*!*************************!*\
  !*** ./lib/api/role.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addUserJoinRole: function() { return /* binding */ addUserJoinRole; },\n/* harmony export */   batchAddUserJoinRole: function() { return /* binding */ batchAddUserJoinRole; },\n/* harmony export */   createTemplate: function() { return /* binding */ createTemplate; },\n/* harmony export */   deleteOfficialTemplate: function() { return /* binding */ deleteOfficialTemplate; },\n/* harmony export */   deleteTemplate: function() { return /* binding */ deleteTemplate; },\n/* harmony export */   getAdminTemplateList: function() { return /* binding */ getAdminTemplateList; },\n/* harmony export */   getOfficialTemplateList: function() { return /* binding */ getOfficialTemplateList; },\n/* harmony export */   getOfficialTemplates: function() { return /* binding */ getOfficialTemplates; },\n/* harmony export */   getRoleList: function() { return /* binding */ getRoleList; },\n/* harmony export */   getRoleTemplateList: function() { return /* binding */ getRoleTemplateList; },\n/* harmony export */   getStudentTemplates: function() { return /* binding */ getStudentTemplates; },\n/* harmony export */   getTeacherDefaultTemplate: function() { return /* binding */ getTeacherDefaultTemplate; },\n/* harmony export */   getTemplateInfo: function() { return /* binding */ getTemplateInfo; },\n/* harmony export */   getUserCurrentTemplate: function() { return /* binding */ getUserCurrentTemplate; },\n/* harmony export */   getUserRoleInfo: function() { return /* binding */ getUserRoleInfo; },\n/* harmony export */   setDefaultTemplate: function() { return /* binding */ setDefaultTemplate; },\n/* harmony export */   updateTemplate: function() { return /* binding */ updateTemplate; }\n/* harmony export */ });\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../request */ \"(app-pages-browser)/./lib/request.ts\");\n// /lib/api/role.ts\n\nconst baseUrl = \"/api/user/srch/templates\";\n// /**\n//  * 获取角色列表\n//  */\n// export const getRoleList = (params?: {\n//   keyword?: string;\n//   status?: number;\n// }) => request.post('/app/user/role/list', params);\n/**\r\n * 获取角色列表\r\n */ const getRoleList = (params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/user-role/condition\", params);\n// /**\n//  * 获取角色详情\n//  */\n// export const getRoleInfo = (id: number) =>\n//   request.post('/app/user/role/info', { id });\n// /**\n//  * 创建角色\n//  */\n// export const createRole = (params: {\n//   roleName: string;\n//   roleCode: string;\n//   roleDescription?: string;\n// }) => request.post('/app/user/role/add', params);\n// /**\n//  * 更新角色\n//  */\n// export const updateRole = (params: {\n//   id: number;\n//   roleName?: string;\n//   roleCode?: string;\n//   roleDescription?: string;\n// }) => request.post('/app/user/role/update', params);\n// /**\n//  * 删除角色\n//  */\n// export const deleteRole = (id: number) =>\n//   request.post('/app/user/role/delete', { id });\n/**\r\n * 获取角色权限模板列表\r\n */ const getRoleTemplateList = (id)=>{\n    return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(baseUrl + \"/list/\" + id);\n};\n/**\r\n * 获取模板详情\r\n */ const getTemplateInfo = (id)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/user/srch/templates/\" + id);\n/**\r\n * 创建角色权限模板\r\n */ const createTemplate = (params)=>{\n    return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/user/srch/templates/create\", params).then((response)=>{\n        return response;\n    }).catch((error)=>{\n        throw error;\n    });\n};\n/**\r\n * 更新权限模板\r\n */ const updateTemplate = (params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/user/srch/templates/update\", params);\n/**\r\n * 删除权限模板\r\n */ const deleteTemplate = (id)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/api/user/srch/templates/\" + id);\n/**\r\n * 设置默认模板\r\n */ const setDefaultTemplate = (templateId)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/user/srch/templates/\" + templateId + \"/default\");\n// /**\n//  * 复制权限模板\n//  */\n// export const copyTemplate = (params: {\n//   sourceTemplateId: number;\n//   targetRoleId: number;\n//   newTemplateName: string;\n// }) => request.post('/app/user/role/template/copy', params);\n/**\r\n * 获取用户的角色和权限信息\r\n */ const getUserRoleInfo = (userId)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/user-join-role/getUserRoleAndTemplateId\", {\n        userId\n    });\n// /**\n//  * 分配角色\n//  */\n// export const assignRole = (params: {\n//   userId: number;\n//   roleId: number;\n//   templateId?: number;\n// }) => request.post('/app/user/role/assign', params);\n// /**\n//  * 批量分配用户角色\n//  */\n// export const batchAssignUserRole = (params: {\n//   userIds: number[];\n//   roleId: number;\n//   templateId?: number;\n// }) => request.post('/app/user/role/user/batchAssign', params);\n// /**\n//  * 移除用户角色\n//  */\n// export const removeUserRole = (params: {\n//   userId: number;\n//   roleId: number;\n// }) => request.post('/app/user/role/user/remove', params);\n// /**\n//  * 获取角色下的所有用户\n//  */\n// export const getRoleUsers = (params: {\n//   roleId: number;\n//   page?: number;\n//   size?: number;\n// }) => request.post('/app/user/role/users', params);\n/**\r\n * 新增用户角色关联    okok\r\n */ const addUserJoinRole = (params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/user-join-role/createUserJoinRole\", params);\n/**\r\n * 批量新增用户角色关联\r\n */ const batchAddUserJoinRole = (params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/user-join-role/batchCreateUserJoinRole\", params);\n/**\r\n * 获取官方模板列表\r\n */ const getOfficialTemplates = ()=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(baseUrl + \"/official/list\");\n// /**\n//  * 从官方模板创建自定义模板\n//  */\n// export const createFromOfficialTemplate = (params: {\n//   sourceTemplateId: number;\n//   roleId: number;\n//   userId: number;\n//   templateName: string;\n// }) => request.post('/app/user/role/template/createFromOfficial', params);\n/**\r\n * 通过page获取官方模板,从下面修改而来，上面那个没有页面选择\r\n */ const getOfficialTemplateList = (page, size)=>{\n    return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/user/srch/template/official/list/page\", {\n        page,\n        size\n    });\n};\n// 获取所有模板（管理员）\nconst getAdminTemplateList = (page, size)=>{\n    return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/user/srch/templates/list\", {\n        page,\n        size\n    });\n};\n/**\r\n * 删除官方模板（管理员专用）\r\n */ const deleteOfficialTemplate = (params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/user/srch/template/official/delete\", params);\n/**\r\n * 获取用户当前使用的模板\r\n */ const getUserCurrentTemplate = (userId)=>{\n    return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(baseUrl + \"/current/\" + userId);\n};\n/**\r\n * 获取老师的学生使用的模板列表\r\n */ const getStudentTemplates = (params)=>{\n    return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(baseUrl + \"/teacher_students\", params);\n};\n/**\r\n * 获取系统默认模板列表\r\n */ const getTeacherDefaultTemplate = ()=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/user/srch/template/teacher/default\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/role.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api/school.ts":
/*!***************************!*\
  !*** ./lib/api/school.ts ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   schoolApi: function() { return /* binding */ schoolApi; }\n/* harmony export */ });\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../request */ \"(app-pages-browser)/./lib/request.ts\");\n/*\r\n * @Author: Zwww <EMAIL>\r\n * @Date: 2025-04-29 11:54:36\r\n * @LastEditors: Zwww <EMAIL>\r\n * @LastEditTime: 2025-05-06 17:27:15\r\n * @FilePath: \\sourceCode\\logicleapweb\\lib\\api\\school.ts\r\n * @Description: \r\n * \r\n * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. \r\n */ \nconst schoolBaseUrl = \"/api/user-school\";\nconst schoolRelationBaseUrl = \"/api/user-school-relation\";\nconst schoolApi = {\n    // 获取学校列表  okok\n    getList: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(schoolBaseUrl, {\n            params\n        });\n    },\n    // 添加学校   okok\n    add: (school)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(schoolBaseUrl, school);\n    },\n    // 更新学校信息   okok\n    update: (school, id)=>{\n        console.log(school);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(schoolBaseUrl + id, school);\n    },\n    // 删除学校    okok\n    delete: (id)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(schoolBaseUrl + \"/\" + id);\n    },\n    // 获取省市区数据    没有用到 直接okok\n    getRegions: ()=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/app/user/school/regions\");\n    },\n    // 绑定学校   okok\n    bindSchool: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(schoolRelationBaseUrl, params);\n    },\n    // 解绑学校  okok\n    unbindSchool: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"\".concat(schoolRelationBaseUrl, \"/user/\").concat(params.userId, \"/school/\").concat(params.schoolId));\n    },\n    // 获取用户关联的学校列表  \n    getUserSchools: ()=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/user-school/listByUserId\");\n    },\n    // 获取学校的教师列表    okok\n    getSchoolTeachers: (schoolId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(schoolRelationBaseUrl, \"/school/\").concat(schoolId, \"/teachers\"));\n    },\n    // 获取学校的班级列表 \n    getSchoolClasses: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/user/class/school/\".concat(params.schoolId, \"/all\"), {\n            params\n        });\n    },\n    // 获取学校的省市区信息  没人调用他。okok\n    getSchoolRegion: (schoolId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/app/user/school/\".concat(schoolId, \"/region\"));\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9hcGkvc2Nob29sLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7Ozs7Ozs7OztDQVNDLEdBQytCO0FBc0JoQyxNQUFNQyxnQkFBZ0I7QUFDdEIsTUFBTUMsd0JBQXdCO0FBRXZCLE1BQU1DLFlBQVk7SUFDdkIsZUFBZTtJQUNmQyxTQUFTLENBQUNDO1FBQ1IsT0FBT0wsZ0RBQU9BLENBQUNNLEdBQUcsQ0FBQ0wsZUFBZTtZQUFFSTtRQUFPO0lBQzdDO0lBRUEsY0FBYztJQUNkRSxLQUFLLENBQUNDO1FBQ0osT0FBT1IsZ0RBQU9BLENBQUNTLElBQUksQ0FBQ1IsZUFBZU87SUFDckM7SUFFQSxnQkFBZ0I7SUFDaEJFLFFBQVEsQ0FBQ0YsUUFBc0JHO1FBQzdCQyxRQUFRQyxHQUFHLENBQUNMO1FBQ1osT0FBT1IsZ0RBQU9BLENBQUNjLEtBQUssQ0FBQ2IsZ0JBQWdCVSxJQUFJSDtJQUMzQztJQUVBLGVBQWU7SUFDZk8sUUFBUSxDQUFDSjtRQUNQLE9BQU9YLGdEQUFPQSxDQUFDZSxNQUFNLENBQUNkLGdCQUFnQixNQUFNVTtJQUM5QztJQUVBLHlCQUF5QjtJQUN6QkssWUFBWTtRQUNWLE9BQU9oQixnREFBT0EsQ0FBQ00sR0FBRyxDQUFDO0lBQ3JCO0lBRUEsY0FBYztJQUNkVyxZQUFZLENBQUNaO1FBQ1gsT0FBT0wsZ0RBQU9BLENBQUNTLElBQUksQ0FBQ1AsdUJBQXVCRztJQUM3QztJQUVBLGFBQWE7SUFDYmEsY0FBYyxDQUFDYjtRQUNiLE9BQU9MLGdEQUFPQSxDQUFDZSxNQUFNLENBQUMsR0FBaUNWLE9BQTlCSCx1QkFBc0IsVUFBZ0NHLE9BQXhCQSxPQUFPYyxNQUFNLEVBQUMsWUFBMEIsT0FBaEJkLE9BQU9lLFFBQVE7SUFDaEc7SUFFQSxnQkFBZ0I7SUFDaEJDLGdCQUFnQjtRQUNkLE9BQU9yQixnREFBT0EsQ0FBQ00sR0FBRyxDQUFDO0lBQ3JCO0lBRUEsb0JBQW9CO0lBQ3BCZ0IsbUJBQW1CLENBQUNGO1FBQ2xCLE9BQU9wQixnREFBT0EsQ0FBQ00sR0FBRyxDQUFDLEdBQW1DYyxPQUFoQ2xCLHVCQUFzQixZQUFtQixPQUFUa0IsVUFBUztJQUNqRTtJQUVBLGFBQWE7SUFDYkcsa0JBQWtCLENBQUNsQjtRQUNqQixPQUFPTCxnREFBT0EsQ0FBQ00sR0FBRyxDQUFDLDBCQUEwQyxPQUFoQkQsT0FBT2UsUUFBUSxFQUFDLFNBQU87WUFDbEVmO1FBQ0Y7SUFDRjtJQUVBLHlCQUF5QjtJQUN6Qm1CLGlCQUFpQixDQUFDSjtRQUNoQixPQUFPcEIsZ0RBQU9BLENBQUNNLEdBQUcsQ0FBQyxvQkFBNkIsT0FBVGMsVUFBUztJQUNsRDtBQUNGLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbGliL2FwaS9zY2hvb2wudHM/NTFmZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxyXG4gKiBAQXV0aG9yOiBad3d3IDE2MjYwMTYxNTNAcXEuY29tXHJcbiAqIEBEYXRlOiAyMDI1LTA0LTI5IDExOjU0OjM2XHJcbiAqIEBMYXN0RWRpdG9yczogWnd3dyAxNjI2MDE2MTUzQHFxLmNvbVxyXG4gKiBATGFzdEVkaXRUaW1lOiAyMDI1LTA1LTA2IDE3OjI3OjE1XHJcbiAqIEBGaWxlUGF0aDogXFxzb3VyY2VDb2RlXFxsb2dpY2xlYXB3ZWJcXGxpYlxcYXBpXFxzY2hvb2wudHNcclxuICogQERlc2NyaXB0aW9uOiBcclxuICogXHJcbiAqIENvcHlyaWdodCAoYykgMjAyNSBieSAke2dpdF9uYW1lX2VtYWlsfSwgQWxsIFJpZ2h0cyBSZXNlcnZlZC4gXHJcbiAqL1xyXG5pbXBvcnQgcmVxdWVzdCBmcm9tICcuLi9yZXF1ZXN0J1xyXG5cclxuaW50ZXJmYWNlIFNjaG9vbFBhcmFtcyB7XHJcbiAgc2Nob29sTmFtZTogc3RyaW5nO1xyXG4gIHByb3ZpbmNlOiBzdHJpbmc7XHJcbiAgY2l0eTogc3RyaW5nO1xyXG4gIGRpc3RyaWN0OiBzdHJpbmc7XHJcbiAgYWRkcmVzcz86IHN0cmluZztcclxuICBjb250YWN0UGhvbmU/OiBzdHJpbmc7XHJcbiAgY29udGFjdFBlcnNvbj86IHN0cmluZztcclxufVxyXG5cclxuaW50ZXJmYWNlIFNjaG9vbFF1ZXJ5UGFyYW1zIHtcclxuICB1c2VySWQ/OiBudW1iZXI7XHJcbiAga2V5d29yZD86IHN0cmluZztcclxuICBwYWdlPzogbnVtYmVyO1xyXG4gIHNpemU/OiBudW1iZXI7XHJcbiAgcHJvdmluY2U/OiBzdHJpbmc7XHJcbiAgZGlzdHJpY3Q/OiBzdHJpbmc7XHJcbiAgY2l0eT86IHN0cmluZztcclxufVxyXG5cclxuY29uc3Qgc2Nob29sQmFzZVVybCA9ICcvYXBpL3VzZXItc2Nob29sJztcclxuY29uc3Qgc2Nob29sUmVsYXRpb25CYXNlVXJsID0gJy9hcGkvdXNlci1zY2hvb2wtcmVsYXRpb24nO1xyXG5cclxuZXhwb3J0IGNvbnN0IHNjaG9vbEFwaSA9IHtcclxuICAvLyDojrflj5blrabmoKHliJfooaggIG9rb2tcclxuICBnZXRMaXN0OiAocGFyYW1zOiBTY2hvb2xRdWVyeVBhcmFtcykgPT4ge1xyXG4gICAgcmV0dXJuIHJlcXVlc3QuZ2V0KHNjaG9vbEJhc2VVcmwsIHsgcGFyYW1zIH0pO1xyXG4gIH0sXHJcblxyXG4gIC8vIOa3u+WKoOWtpuagoSAgIG9rb2tcclxuICBhZGQ6IChzY2hvb2w6IFNjaG9vbFBhcmFtcykgPT4ge1xyXG4gICAgcmV0dXJuIHJlcXVlc3QucG9zdChzY2hvb2xCYXNlVXJsLCBzY2hvb2wpO1xyXG4gIH0sXHJcblxyXG4gIC8vIOabtOaWsOWtpuagoeS/oeaBryAgIG9rb2tcclxuICB1cGRhdGU6IChzY2hvb2w6IFNjaG9vbFBhcmFtcywgaWQ6IG51bWJlcikgPT4ge1xyXG4gICAgY29uc29sZS5sb2coc2Nob29sKTtcclxuICAgIHJldHVybiByZXF1ZXN0LnBhdGNoKHNjaG9vbEJhc2VVcmwgKyBpZCwgc2Nob29sKTtcclxuICB9LFxyXG5cclxuICAvLyDliKDpmaTlrabmoKEgICAgb2tva1xyXG4gIGRlbGV0ZTogKGlkOiBudW1iZXIpID0+IHtcclxuICAgIHJldHVybiByZXF1ZXN0LmRlbGV0ZShzY2hvb2xCYXNlVXJsICsgXCIvXCIgKyBpZCk7XHJcbiAgfSxcclxuXHJcbiAgLy8g6I635Y+W55yB5biC5Yy65pWw5o2uICAgIOayoeacieeUqOWIsCDnm7TmjqVva29rXHJcbiAgZ2V0UmVnaW9uczogKCkgPT4ge1xyXG4gICAgcmV0dXJuIHJlcXVlc3QuZ2V0KCcvYXBwL3VzZXIvc2Nob29sL3JlZ2lvbnMnKTtcclxuICB9LFxyXG5cclxuICAvLyDnu5HlrprlrabmoKEgICBva29rXHJcbiAgYmluZFNjaG9vbDogKHBhcmFtczogeyB1c2VySWQ6IG51bWJlcjsgc2Nob29sSWQ6IG51bWJlciB9KSA9PiB7XHJcbiAgICByZXR1cm4gcmVxdWVzdC5wb3N0KHNjaG9vbFJlbGF0aW9uQmFzZVVybCwgcGFyYW1zKTtcclxuICB9LFxyXG5cclxuICAvLyDop6Pnu5HlrabmoKEgIG9rb2tcclxuICB1bmJpbmRTY2hvb2w6IChwYXJhbXM6IHsgdXNlcklkOiBudW1iZXI7IHNjaG9vbElkOiBudW1iZXIgfSkgPT4ge1xyXG4gICAgcmV0dXJuIHJlcXVlc3QuZGVsZXRlKGAke3NjaG9vbFJlbGF0aW9uQmFzZVVybH0vdXNlci8ke3BhcmFtcy51c2VySWR9L3NjaG9vbC8ke3BhcmFtcy5zY2hvb2xJZH1gKTtcclxuICB9LFxyXG5cclxuICAvLyDojrflj5bnlKjmiLflhbPogZTnmoTlrabmoKHliJfooaggIFxyXG4gIGdldFVzZXJTY2hvb2xzOiAoKSA9PiB7XHJcbiAgICByZXR1cm4gcmVxdWVzdC5nZXQoJy9hcGkvdXNlci1zY2hvb2wvbGlzdEJ5VXNlcklkJyk7XHJcbiAgfSxcclxuXHJcbiAgLy8g6I635Y+W5a2m5qCh55qE5pWZ5biI5YiX6KGoICAgIG9rb2tcclxuICBnZXRTY2hvb2xUZWFjaGVyczogKHNjaG9vbElkOiBudW1iZXIpID0+IHtcclxuICAgIHJldHVybiByZXF1ZXN0LmdldChgJHtzY2hvb2xSZWxhdGlvbkJhc2VVcmx9L3NjaG9vbC8ke3NjaG9vbElkfS90ZWFjaGVyc2ApO1xyXG4gIH0sXHJcblxyXG4gIC8vIOiOt+WPluWtpuagoeeahOePree6p+WIl+ihqCBcclxuICBnZXRTY2hvb2xDbGFzc2VzOiAocGFyYW1zOiB7IHNjaG9vbElkOiBudW1iZXI7IHBhZ2U/OiBudW1iZXI7IHNpemU/OiBudW1iZXIgfSkgPT4ge1xyXG4gICAgcmV0dXJuIHJlcXVlc3QuZ2V0KGAvYXBpL3VzZXIvY2xhc3Mvc2Nob29sLyR7cGFyYW1zLnNjaG9vbElkfS9hbGxgLCB7XHJcbiAgICAgIHBhcmFtc1xyXG4gICAgfSk7XHJcbiAgfSxcclxuXHJcbiAgLy8g6I635Y+W5a2m5qCh55qE55yB5biC5Yy65L+h5oGvICDmsqHkurrosIPnlKjku5bjgIJva29rXHJcbiAgZ2V0U2Nob29sUmVnaW9uOiAoc2Nob29sSWQ6IG51bWJlcikgPT4ge1xyXG4gICAgcmV0dXJuIHJlcXVlc3QuZ2V0KGAvYXBwL3VzZXIvc2Nob29sLyR7c2Nob29sSWR9L3JlZ2lvbmApO1xyXG4gIH1cclxufSAiXSwibmFtZXMiOlsicmVxdWVzdCIsInNjaG9vbEJhc2VVcmwiLCJzY2hvb2xSZWxhdGlvbkJhc2VVcmwiLCJzY2hvb2xBcGkiLCJnZXRMaXN0IiwicGFyYW1zIiwiZ2V0IiwiYWRkIiwic2Nob29sIiwicG9zdCIsInVwZGF0ZSIsImlkIiwiY29uc29sZSIsImxvZyIsInBhdGNoIiwiZGVsZXRlIiwiZ2V0UmVnaW9ucyIsImJpbmRTY2hvb2wiLCJ1bmJpbmRTY2hvb2wiLCJ1c2VySWQiLCJzY2hvb2xJZCIsImdldFVzZXJTY2hvb2xzIiwiZ2V0U2Nob29sVGVhY2hlcnMiLCJnZXRTY2hvb2xDbGFzc2VzIiwiZ2V0U2Nob29sUmVnaW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/school.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api/student.ts":
/*!****************************!*\
  !*** ./lib/api/student.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   studentApi: function() { return /* binding */ studentApi; }\n/* harmony export */ });\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../request */ \"(app-pages-browser)/./lib/request.ts\");\n/*\r\n * @Author: Zwww <EMAIL>\r\n * @Date: 2025-04-30 16:32:51\r\n * @LastEditors: Zwww <EMAIL>\r\n * @LastEditTime: 2025-05-09 15:32:22\r\n * @FilePath: \\sourceCode\\logicleapweb\\lib\\api\\student.ts\r\n * @Description: \r\n * \r\n * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. \r\n */ \nconst studentApi = {\n    // 重置学生密码 okok\n    resetPassword: (studentIds, password)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/user-auth/student/reset-password\", {\n            studentIds,\n            password\n        });\n    },\n    //  导出学生\n    exportStudents: (classId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/user-student/export\", {\n            classId\n        });\n    },\n    // ... other APIs ...     okok\n    getStudentInfo: (userId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/user-student/user/\".concat(userId));\n    },\n    // 搜索学生  okok\n    searchStudents: (classId, keyword)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/user-student/search\", {\n            params: {\n                classId,\n                keyword\n            }\n        });\n    },\n    // 批量匹配学生信息  涉及class  待定\n    matchStudents: (data)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/app/user/student/match\", data);\n    },\n    // okok\n    getCurrentTemplate: (userIds)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/user/srch/templates/batch-current\", {\n            userIds\n        });\n    },\n    // 批量获取学生信息（包括模板、积分等所有数据）\n    getStudentsBatchInfo: (userIds)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/user-student/batch-info\", {\n            userIds\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/student.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api/task.ts":
/*!*************************!*\
  !*** ./lib/api/task.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Priority: function() { return /* binding */ Priority; },\n/* harmony export */   TaskStatus: function() { return /* binding */ TaskStatus; },\n/* harmony export */   TaskType: function() { return /* binding */ TaskType; }\n/* harmony export */ });\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../request */ \"(app-pages-browser)/./lib/request.ts\");\n\nvar TaskType;\n(function(TaskType) {\n    TaskType[TaskType[\"GRAPHIC\"] = 1] = \"GRAPHIC\";\n    TaskType[TaskType[\"NODE\"] = 2] = \"NODE\";\n    TaskType[TaskType[\"AI\"] = 3] = \"AI\"; // AI应用任务\n})(TaskType || (TaskType = {}));\nvar Priority;\n(function(Priority) {\n    Priority[Priority[\"NORMAL\"] = 0] = \"NORMAL\";\n    Priority[Priority[\"IMPORTANT\"] = 1] = \"IMPORTANT\";\n    Priority[Priority[\"URGENT\"] = 2] = \"URGENT\"; // 紧急\n})(Priority || (Priority = {}));\nvar TaskStatus;\n(function(TaskStatus) {\n    TaskStatus[TaskStatus[\"NOT_STARTED\"] = 0] = \"NOT_STARTED\";\n    TaskStatus[TaskStatus[\"IN_PROGRESS\"] = 1] = \"IN_PROGRESS\";\n    TaskStatus[TaskStatus[\"COMPLETED\"] = 2] = \"COMPLETED\";\n    TaskStatus[TaskStatus[\"EXPIRED\"] = 3] = \"EXPIRED\";\n    TaskStatus[TaskStatus[\"RE_DO\"] = 4] = \"RE_DO\"; // 重新修改\n})(TaskStatus || (TaskStatus = {}));\n// 任务相关 API\nconst taskApi = {\n    /**\r\n   * 获取任务统计\r\n   */ // 没人用它   okok\n    getTaskStats () {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/app/user/teacher_task/stats\");\n    },\n    /**\r\n   * 获取任务列表   okok\r\n   */ getTaskList (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/teacher-task/list\", {\n            params\n        });\n    },\n    /**\r\n   * 发布任务    okok\r\n   */ publishTask (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task/publish\", params);\n    },\n    /**\r\n   * 获取任务完成统计   没人用 ，okok\r\n   */ getAssignmentStats () {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/app/user/teacher_task/assignment/stats\");\n    },\n    /**\r\n   * 批量分配任务  没人用okok\r\n   */ batchAssignTask (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/app/user/teacher_task/assignment/batchAssign\", params);\n    },\n    /**\r\n   * 提交任务   okok\r\n   */ submitTask (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task-assignment/submitWork\", params);\n    },\n    /**\r\n   * 评分   okok\r\n   */ gradeTask (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task-assignment/grade\", params);\n    },\n    /**\r\n   * 获取任务详情  okok\r\n   */ getTaskDetail (taskId) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/teacher-task/\".concat(taskId));\n    },\n    /**\r\n   * 获取学生提交的作品    okok\r\n   */ getStudentWork (assignmentId) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/teacher-task-assignment/studentCommitWork\", {\n            params: {\n                assignmentId\n            }\n        });\n    },\n    /**\r\n   * 打回修改   okok\r\n   */ returnForRevision (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task-assignment/return-revision\", params);\n    },\n    /**\r\n   * 更新任务状态   okok\r\n   */ updateTaskStatus (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task-assignment/update-status\", params);\n    },\n    /**\r\n   * 更新任务     okok\r\n   */ updateTask (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task/update-task\", params);\n    },\n    /**\r\n   * 发布学生作品到班级项目  okok\r\n   */ publishToClass (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task-assignment/publish-to-class\", params);\n    },\n    /**\r\n   * 删除任务  okok\r\n   */ deleteTask (taskId) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task/delete-task\", {\n            taskId\n        });\n    },\n    /**\r\n   * 搜索任务   okok\r\n   */ searchTasks (keyword, classId) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/teacher-task/search\", {\n            params: {\n                keyword,\n                classId\n            }\n        });\n    },\n    /**\r\n   * 获取作品的提交记录   okok\r\n   */ getWorkSubmissions (workId) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/teacher-task-assignment/workSubmissionsRecord\", {\n            params: {\n                workId\n            }\n        });\n    },\n    // 获取学生任务列表      没有人调用他  okok\n    getStudentTasks: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/app/user/teacher_task/list\", {\n            params: {\n                studentId: params.studentId,\n                status: params.status,\n                roleId: 1,\n                page: 1,\n                size: 100\n            }\n        });\n    },\n    // 添加标记已读的方法   okok\n    markAsRead: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task-assignment/mark-read\", params);\n    },\n    // 根据任务ID获取自评项\n    getSelfAssessmentItemsByTaskId: (taskId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/self-assessment-item/task/\".concat(taskId));\n    },\n    // 搜索自评项\n    searchSelfAssessmentItems (keyword) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/task-self-assessment-item/search\", {\n            params: {\n                keyword\n            }\n        });\n    },\n    /**\r\n   * 检查自评项是否有学生自评记录\r\n   */ checkSelfAssessmentRecords (itemIds) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/task-self-assessment-item/check-records\", {\n            itemIds\n        });\n    },\n    /**\r\n   * 更新任务的自评项\r\n   */ updateSelfAssessmentItems (taskId, selfAssessmentItems) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/task-self-assessment-item/update-by-task\", {\n            taskId,\n            selfAssessmentItems\n        });\n    }\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (taskApi);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/task.ts\n"));

/***/ })

});