"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/activity/festival/page",{

/***/ "(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx":
/*!*************************************************************!*\
  !*** ./app/activity/festival/components/ActivityHeader.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./lib/store.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ActivityMediaGallery */ \"(app-pages-browser)/./app/activity/festival/components/ActivityMediaGallery.tsx\");\n/* harmony import */ var _barrel_optimize_names_Modal_Upload_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Modal,Upload!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Modal_Upload_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Modal,Upload!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _components_login_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/login-dialog */ \"(app-pages-browser)/./components/login-dialog.tsx\");\n/* harmony import */ var _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/activity */ \"(app-pages-browser)/./lib/api/activity/index.ts\");\n/* harmony import */ var _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/activity/events-task */ \"(app-pages-browser)/./lib/api/activity/events-task.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../../lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var _lib_api_user__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/user */ \"(app-pages-browser)/./lib/api/user.ts\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 临时实现formatDate函数，以解决模块导入问题\nconst formatDate = (dateString)=>{\n    // 在服务端渲染时，直接返回原始字符串，避免水合错误\n    if (false) {}\n    const date = new Date(dateString);\n    // 检查日期是否有效\n    if (isNaN(date.getTime())) {\n        return dateString; // 如果无效，返回原始字符串\n    }\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, \"0\");\n    const day = String(date.getDate()).padStart(2, \"0\");\n    return \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n};\nconst ActivityHeader = (param)=>{\n    let { title: initialTitle, startTime: initialStartTime, endTime: initialEndTime, bannerImage: initialBannerImage, expanded, setExpanded, activityId, tags = [], organizer: initialOrganizer = \"\", activityType = _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.ActivityType.WORK, onRefreshWorks, // 媒体字段\n    promotionImage, backgroundImage, galleryImages, attachmentFiles } = param;\n    _s();\n    console.log(\"\\uD83D\\uDD0D [DEBUG] ActivityHeader 组件渲染\", {\n        activityId,\n        initialTitle\n    });\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_10__.GetNotification)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.usePathname)();\n    // 显示用的标题和时间\n    const title = initialTitle;\n    const startTime = initialStartTime;\n    const endTime = initialEndTime;\n    const bannerImage = initialBannerImage;\n    const organizer = initialOrganizer;\n    // 获取当前用户信息和dispatch\n    const userState = (0,react_redux__WEBPACK_IMPORTED_MODULE_12__.useSelector)(_lib_store__WEBPACK_IMPORTED_MODULE_3__.selectUserState);\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_12__.useDispatch)();\n    const isAdmin = (userState === null || userState === void 0 ? void 0 : userState.roleId) === 4;\n    // 同步用户状态的函数 - 增强版，从API获取最新用户信息\n    const syncUserState = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 开始同步用户状态\");\n            const localUser = localStorage.getItem(\"user\");\n            if (localUser) {\n                const userData = JSON.parse(localUser);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中的用户数据\", userData);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] Redux 中的用户数据\", userState);\n                // 如果用户已登录但phone为空，尝试从API获取最新用户信息\n                if (userData.isLoggedIn && userData.userId && (!userData.phone || userData.phone.trim() === \"\")) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户phone为空，从API获取最新用户信息\");\n                    try {\n                        const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_9__.userApi.getUserInfo(userData.userId);\n                        if (response.code === 200 && response.data) {\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取到最新用户信息\", response.data);\n                            // 构建更新后的用户数据\n                            const updatedUserData = {\n                                ...userData,\n                                phone: response.data.phone || \"\",\n                                nickName: response.data.nickName || userData.nickName,\n                                avatarUrl: response.data.avatarUrl || userData.avatarUrl,\n                                gender: response.data.gender || userData.gender,\n                                roleId: response.data.roleId || userData.roleId\n                            };\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 更新用户数据\", {\n                                oldPhone: userData.phone,\n                                newPhone: updatedUserData.phone,\n                                updated: updatedUserData\n                            });\n                            // 更新localStorage和Redux\n                            localStorage.setItem(\"user\", JSON.stringify(updatedUserData));\n                            dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(updatedUserData));\n                            return;\n                        }\n                    } catch (error) {\n                        console.error(\"\\uD83D\\uDD0D [DEBUG] 从API获取用户信息失败:\", error);\n                    }\n                }\n                // 检查关键字段是否有变化\n                const hasPhoneChanged = userData.phone !== (userState === null || userState === void 0 ? void 0 : userState.phone);\n                const hasRoleChanged = userData.roleId !== (userState === null || userState === void 0 ? void 0 : userState.roleId);\n                const hasLoginStatusChanged = userData.isLoggedIn !== (userState === null || userState === void 0 ? void 0 : userState.isLoggedIn);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态变化检查\", {\n                    phoneChanged: hasPhoneChanged,\n                    roleChanged: hasRoleChanged,\n                    loginStatusChanged: hasLoginStatusChanged,\n                    oldPhone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                    newPhone: userData.phone,\n                    oldRole: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                    newRole: userData.roleId\n                });\n                // 如果localStorage中的用户状态与Redux中的不一致，说明需要更新\n                if (hasPhoneChanged || hasRoleChanged || hasLoginStatusChanged) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户状态变化，同步Redux状态\");\n                    dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(userData));\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态无变化，无需同步\");\n                }\n            } else {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中无用户数据\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD0D [DEBUG] 检查用户状态失败:\", error);\n        }\n    };\n    // 监听页面焦点变化和路由变化，重新检查用户状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 页面加载时立即检查一次\n        syncUserState().catch(console.error);\n        const handleVisibilityChange = ()=>{\n            if (document.visibilityState === \"visible\") {\n                syncUserState().catch(console.error);\n            }\n        };\n        const handleFocus = ()=>{\n            syncUserState().catch(console.error);\n        };\n        // 添加多种事件监听确保状态同步\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n        window.addEventListener(\"focus\", handleFocus);\n        return ()=>{\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n            window.removeEventListener(\"focus\", handleFocus);\n        };\n    }, [\n        userState === null || userState === void 0 ? void 0 : userState.phone,\n        dispatch\n    ]);\n    // 工具函数：检查用户是否为学生身份\n    const checkIsStudent = ()=>{\n        try {\n            const userStr = localStorage.getItem(\"user\");\n            const localUser = userStr ? JSON.parse(userStr) : null;\n            const userRoleId = (localUser === null || localUser === void 0 ? void 0 : localUser.roleId) || (userState === null || userState === void 0 ? void 0 : userState.roleId);\n            return userRoleId === 1; // 学生身份的roleId为1\n        } catch (error) {\n            console.error(\"解析localStorage中的用户信息失败:\", error);\n            return false;\n        }\n    };\n    // 工具函数：显示身份验证失败提示\n    const showStudentOnlyModal = ()=>{\n        _barrel_optimize_names_Modal_Upload_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"].confirm({\n            title: \"身份验证\",\n            content: \"抱歉，只有学生身份的用户才能参加此活动报名。如果您是学生，请联系管理员更新您的身份信息。\",\n            okText: \"我知道了\",\n            cancelText: \"联系管理员\",\n            onCancel: ()=>{\n                notification.info(\"如需帮助，请联系客服：400-123-4567\");\n            }\n        });\n    };\n    // 获取报名状态文本\n    const getRegistrationStatusText = ()=>{\n        switch(registrationStatus){\n            case 0:\n                return \"已取消\";\n            case 1:\n                return \"已报名\";\n            case 2:\n                return \"已审核通过\";\n            case 3:\n                return \"已拒绝\";\n            case 4:\n                return \"评审中\";\n            case 5:\n                return \"已获奖\";\n            case 6:\n                return \"审核中\";\n            default:\n                return \"已报名\";\n        }\n    };\n    // 可编辑的导航标签文本\n    const [navTags, setNavTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"tag1\",\n            text: \"官方活动\"\n        },\n        {\n            id: \"tag2\",\n            text: \"AIGC作品征集\"\n        }\n    ]);\n    // 活动标签ID列表\n    const [tagIds, setTagIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 标签编辑状态\n    const [editingTagId, setEditingTagId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingTagText, setEditingTagText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 登录和报名相关状态\n    const [showLoginDialog, setShowLoginDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSubmitted, setHasSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [registrationStatus, setRegistrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 报名状态：0-已取消 1-已报名 2-已审核通过 3-已拒绝 4-评审中 5-已获奖 6-审核中\n    const [uploadLoading, setUploadLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fileList, setFileList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUI, setShowUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 添加客户端检测来解决水合错误\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    // 页面加载时检查报名状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkRegistrationStatus = async ()=>{\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态 - useEffect 触发\", {\n                isLoggedIn: userState.isLoggedIn,\n                roleId: userState.roleId,\n                phone: userState.phone,\n                isClient,\n                activityId\n            });\n            // 只有在用户已登录且是学生身份时才检查报名状态\n            const isStudent = checkIsStudent();\n            if (!userState.isLoggedIn || !isStudent) {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录或不是学生身份，重置报名状态\", {\n                    isLoggedIn: userState.isLoggedIn,\n                    isStudent\n                });\n                // 如果用户未登录或不是学生，重置状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n                return;\n            }\n            try {\n                var _response_data, _response_data1;\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 开始检查报名状态 API 调用\");\n                setIsChecking(true);\n                const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.checkRegistration(Number(activityId));\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 页面加载检查报名状态响应:\", response);\n                if ((response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200 && ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data)) {\n                    const { submitted, submit } = response.data.data;\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态检查结果\", {\n                        submitted,\n                        submit\n                    });\n                    setHasSubmitted(submitted);\n                    if (submit && submit.status !== undefined) {\n                        setRegistrationStatus(submit.status);\n                    } else {\n                        setRegistrationStatus(null);\n                    }\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态响应不符合预期，设置为未报名状态\");\n                    // 如果响应不符合预期，设置为未报名状态\n                    setHasSubmitted(false);\n                    setRegistrationStatus(null);\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态失败:\", error);\n                // 出错时设置为未报名状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n            } finally{\n                setIsChecking(false);\n            }\n        };\n        // 只在客户端执行，避免服务端渲染问题\n        if (isClient) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 客户端环境，执行报名状态检查\");\n            checkRegistrationStatus();\n        } else {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 非客户端环境，跳过报名状态检查\");\n        }\n    }, [\n        activityId,\n        userState.isLoggedIn,\n        userState.roleId,\n        userState.phone,\n        isClient\n    ]);\n    // 检查用户是否已提交作品\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkUserSubmitted = async ()=>{\n            try {\n                // 仅针对已登录用户进行检查\n                if (userState && userState.isLoggedIn) {\n                    var _response_data;\n                    setIsChecking(true);\n                    const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityApi.checkUserSubmitted(Number(activityId));\n                    console.log(\"页面加载时检查用户提交状态响应:\", response);\n                    if ((response === null || response === void 0 ? void 0 : response.status) === 200 && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data)) {\n                        setHasSubmitted(response.data.data.submitted);\n                    } else {\n                        // 如果响应不符合预期，默认为未提交\n                        setHasSubmitted(false);\n                    }\n                    setIsChecking(false);\n                } else {\n                    // 未登录用户，直接设置为未提交\n                    setHasSubmitted(false);\n                    setIsChecking(false);\n                }\n            } catch (error) {\n                console.error(\"检查用户提交状态失败:\", error);\n                // 出错时默认设置为未提交\n                setHasSubmitted(false);\n                setIsChecking(false);\n            }\n        };\n        // 页面加载时执行检查\n        if (isClient) {\n            checkUserSubmitted();\n        }\n    }, [\n        activityId,\n        userState,\n        isClient\n    ]);\n    // 加载活动标签\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchActivityTags = async ()=>{\n            if (activityId) {\n                try {\n                    const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityTagApi.getActivityTags(Number(activityId));\n                    if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n                        setTagIds(response.data.data || []);\n                    }\n                } catch (error) {\n                    console.error(\"获取活动标签失败:\", error);\n                }\n            }\n        };\n        fetchActivityTags();\n    }, [\n        activityId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 初始化文件列表\n        if (bannerImage) {\n            setFileList([\n                {\n                    uid: \"-1\",\n                    name: \"当前封面\",\n                    status: \"done\",\n                    url: bannerImage\n                }\n            ]);\n        } else {\n            setFileList([]);\n        }\n    }, [\n        bannerImage\n    ]);\n    // 处理取消编辑\n    const handleCancelEdit = ()=>{\n        setTitle(initialTitle);\n        setStartTime(initialStartTime);\n        setEndTime(initialEndTime);\n        setBannerImage(initialBannerImage);\n        setOrganizer(initialOrganizer);\n        setIsEditing(false);\n    };\n    // 处理保存编辑\n    const handleSaveEdit = async ()=>{\n        if (!activityId) {\n            notification.error(\"活动ID不存在，无法保存\");\n            return;\n        }\n        try {\n            setIsSaving(true);\n            // 准备更新数据\n            const updateData = {\n                id: Number(activityId),\n                name: title,\n                startTime: new Date(startTime),\n                endTime: new Date(endTime),\n                coverImage: bannerImage,\n                organizer: organizer,\n                // 这些字段为必填，但我们不修改它们\n                detailContent: \"\",\n                rulesContent: \"\",\n                awardsContent: \"\"\n            };\n            console.log(\"保存活动数据:\", updateData);\n            // 更新活动基本信息\n            const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityApi.update(updateData);\n            console.log(\"保存活动响应:\", response);\n            if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n                // 如果有标签，则更新标签\n                const tagResponse = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityTagApi.update(Number(activityId), tagIds);\n                console.log(\"标签更新响应:\", tagResponse);\n                notification.success(\"活动信息已成功保存\");\n                setIsEditing(false);\n            } else {\n                var _response_data;\n                notification.error((response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.message) || \"保存失败\");\n            }\n        } catch (error) {\n            console.error(\"保存活动信息失败:\", error);\n            notification.error(\"保存活动信息失败，请稍后重试\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    // 处理标签点击编辑\n    const handleTagClick = (tag)=>{\n        if (isEditing) {\n            setTagIds(tagIds.includes(Number(tag.id)) ? tagIds.filter((id)=>id !== Number(tag.id)) : [\n                ...tagIds,\n                Number(tag.id)\n            ]);\n        }\n    };\n    // 处理标签编辑取消\n    const handleTagEditCancel = ()=>{\n        setEditingTagId(null);\n    };\n    // 处理添加新标签\n    const handleAddTag = ()=>{\n        if (navTags.length < 5) {\n            const newTag = {\n                id: \"tag-\".concat(Date.now()),\n                text: \"新标签\"\n            };\n            setNavTags([\n                ...navTags,\n                newTag\n            ]);\n            setTagIds([\n                ...tagIds,\n                Number(newTag.id)\n            ]);\n        }\n    };\n    // 处理删除标签\n    const handleDeleteTag = (id)=>{\n        if (navTags.length > 1) {\n            setNavTags(navTags.filter((tag)=>tag.id !== id));\n            setTagIds(tagIds.filter((id)=>id !== Number(id)));\n        }\n    };\n    // 前往任务函数\n    const handleGoToTask = async ()=>{\n        try {\n            const loadingMessage = notification.loading(\"正在跳转到任务...\");\n            // 获取当前用户的赛事任务列表\n            const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n            if (eventsTaskResponse.data.code === 200) {\n                const eventsTasks = eventsTaskResponse.data.data;\n                // 查找与当前活动相关的赛事任务\n                const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                if (relatedTask) {\n                    // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                    window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                    notification.success(\"正在跳转到任务页面...\");\n                } else {\n                    // 如果没有找到对应任务，在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                    notification.info(\"未找到对应任务，已打开班级空间\");\n                }\n            } else {\n                // 如果获取任务失败，在新标签页中打开班级空间\n                window.open(\"/class-space\", \"_blank\");\n                notification.info(\"获取任务失败，已打开班级空间\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"跳转任务失败:\", error);\n            notification.error(\"跳转任务失败，请稍后重试\");\n        }\n    };\n    // 处理报名按钮点击\n    const handleSubmitClick = async ()=>{\n        var _userState_phone, _localUser_phone;\n        // 获取localStorage中的用户信息进行对比\n        const localUserStr = localStorage.getItem(\"user\");\n        const localUser = localUserStr ? JSON.parse(localUserStr) : null;\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 报名按钮点击 - 详细用户状态检查\", {\n            hasSubmitted,\n            \"Redux userState\": {\n                isLoggedIn: userState === null || userState === void 0 ? void 0 : userState.isLoggedIn,\n                phone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                phoneLength: userState === null || userState === void 0 ? void 0 : (_userState_phone = userState.phone) === null || _userState_phone === void 0 ? void 0 : _userState_phone.length,\n                phoneType: typeof (userState === null || userState === void 0 ? void 0 : userState.phone),\n                roleId: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                userId: userState === null || userState === void 0 ? void 0 : userState.userId,\n                nickName: userState === null || userState === void 0 ? void 0 : userState.nickName\n            },\n            \"localStorage user\": {\n                phone: localUser === null || localUser === void 0 ? void 0 : localUser.phone,\n                phoneLength: localUser === null || localUser === void 0 ? void 0 : (_localUser_phone = localUser.phone) === null || _localUser_phone === void 0 ? void 0 : _localUser_phone.length,\n                phoneType: typeof (localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                userId: localUser === null || localUser === void 0 ? void 0 : localUser.userId,\n                nickName: localUser === null || localUser === void 0 ? void 0 : localUser.nickName\n            },\n            \"phone validation\": {\n                \"userState.phone exists\": !!(userState === null || userState === void 0 ? void 0 : userState.phone),\n                \"userState.phone not empty\": (userState === null || userState === void 0 ? void 0 : userState.phone) && userState.phone.trim() !== \"\",\n                \"localUser.phone exists\": !!(localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                \"localUser.phone not empty\": (localUser === null || localUser === void 0 ? void 0 : localUser.phone) && (localUser === null || localUser === void 0 ? void 0 : localUser.phone.trim()) !== \"\"\n            }\n        });\n        // 临时调用API获取最新用户信息进行对比\n        if (userState === null || userState === void 0 ? void 0 : userState.userId) {\n            try {\n                var _response_data, _response_data_phone, _response_data1, _response_data2, _response_data3, _response_data4;\n                const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_9__.userApi.getUserInfo(userState.userId);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取的最新用户信息:\", {\n                    code: response.code,\n                    phone: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.phone,\n                    phoneLength: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_phone = _response_data1.phone) === null || _response_data_phone === void 0 ? void 0 : _response_data_phone.length,\n                    phoneType: typeof ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.phone),\n                    nickName: (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.nickName,\n                    userId: (_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : _response_data4.id\n                });\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 获取用户信息失败:\", error);\n            }\n        }\n        // 如果用户已报名，跳转到任务页面\n        if (hasSubmitted) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已报名，跳转到任务页面\");\n            handleGoToTask();\n            return;\n        }\n        // 检查用户是否登录\n        if (!userState || !userState.isLoggedIn) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录，跳转到登录页面\");\n            // 未登录，显示登录对话框\n            const redirectUrl = pathname || \"/home\";\n            router.push(\"/login?redirect=\".concat(encodeURIComponent(redirectUrl)));\n            return;\n        }\n        // 检查用户身份：只有学生（roleId为1）才能报名\n        const isStudent = checkIsStudent();\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 检查用户身份\", {\n            isStudent,\n            roleId: userState === null || userState === void 0 ? void 0 : userState.roleId\n        });\n        if (!isStudent) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户不是学生身份，显示身份验证弹窗\");\n            showStudentOnlyModal();\n            return;\n        }\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 身份验证通过，开始报名流程\");\n        // 身份验证通过，直接进行报名\n        handleDirectRegistration();\n    };\n    // 处理登录成功\n    const handleLoginSuccess = ()=>{\n        notification.success(\"登录成功\");\n    // 登录成功后，页面的useEffect会自动检查报名状态\n    // 不需要在这里重复检查，让useEffect处理状态更新\n    };\n    // 处理报名成功\n    const handleSubmitSuccess = ()=>{\n        // 更新提交状态\n        setHasSubmitted(true);\n        setRegistrationStatus(1); // 设置为已报名状态\n        notification.success(\"报名成功\");\n        // 通知页面刷新参赛作品列表\n        if (onRefreshWorks) {\n            onRefreshWorks();\n        }\n    };\n    // 直接报名处理函数\n    const handleDirectRegistration = async ()=>{\n        try {\n            var _response_data;\n            // 检查用户是否绑定了手机号\n            if (!userState.phone || userState.phone.trim() === \"\") {\n                // 显示确认对话框\n                _barrel_optimize_names_Modal_Upload_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"].confirm({\n                    title: \"需要绑定手机号\",\n                    content: \"报名活动需要绑定手机号，是否前往绑定？\",\n                    okText: \"前往绑定\",\n                    cancelText: \"取消\",\n                    onOk: ()=>{\n                        // 跳转到登录页面，并在URL中添加参数表示需要绑定手机号\n                        const currentUrl = window.location.href;\n                        router.push(\"/login?needBindPhone=true&redirect=\".concat(encodeURIComponent(currentUrl)));\n                    },\n                    onCancel: ()=>{\n                        notification.info(\"未绑定手机号，无法报名活动\");\n                    }\n                });\n                return;\n            }\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已绑定手机号，继续报名流程\");\n            // 显示加载提示\n            const loadingMessage = notification.loading(\"正在提交报名...\");\n            // 提交报名数据（使用默认值，跳过协议确认步骤）\n            const submitData = {\n                activityId: Number(activityId),\n                agreementAccepted: true,\n                parentConsentAccepted: true,\n                parentSignaturePath: \"\",\n                signatureTime: new Date().toISOString(),\n                remark: \"快速报名时间：\".concat(new Date().toLocaleString(\"zh-CN\"))\n            };\n            const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.submitRegistration(submitData);\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                // 更新UI状态\n                setHasSubmitted(true);\n                setRegistrationStatus(1);\n                notification.success(\"报名成功！正在跳转到班级空间...\");\n                // 通知页面刷新参赛作品列表\n                if (onRefreshWorks) {\n                    onRefreshWorks();\n                }\n                // 报名成功后，获取对应的赛事任务并跳转到班级空间\n                try {\n                    // 等待一小段时间确保后端创建赛事任务完成\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    // 获取当前用户的赛事任务列表\n                    const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n                    if (eventsTaskResponse.data.code === 200) {\n                        const eventsTasks = eventsTaskResponse.data.data;\n                        // 查找与当前活动相关的赛事任务\n                        const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                        if (relatedTask) {\n                            // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                            window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                        } else {\n                            // 如果没有找到对应任务，在新标签页中打开班级空间\n                            window.open(\"/class-space\", \"_blank\");\n                        }\n                    } else {\n                        // 如果获取任务失败，在新标签页中打开班级空间\n                        window.open(\"/class-space\", \"_blank\");\n                    }\n                } catch (taskError) {\n                    console.error(\"获取赛事任务失败:\", taskError);\n                    // 即使获取任务失败，也要在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                }\n            } else {\n                var _response_data1;\n                throw new Error(((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message) || \"报名失败\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"报名失败:\", error);\n            notification.error(error instanceof Error ? error.message : \"报名失败，请稍后重试\");\n        }\n    };\n    // 处理自定义上传请求\n    const handleCustomRequest = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        setUploadLoading(true);\n        try {\n            // 上传到OSS\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_8__.uploadApi.uploadToOss(file);\n            console.log(\"图片上传成功，URL:\", url);\n            // 更新文件列表\n            setFileList([\n                {\n                    ...file,\n                    uid: file.uid,\n                    name: file.name,\n                    status: \"done\",\n                    url: url\n                }\n            ]);\n            // 更新封面图片URL\n            setBannerImage(url);\n            if (onSuccess) {\n                onSuccess(null, file);\n            }\n            notification.success(\"封面图片上传成功\");\n        } catch (err) {\n            console.error(\"上传失败:\", err);\n            notification.error(\"上传失败: \".concat(err.message || \"请稍后重试\"));\n            setFileList([\n                {\n                    ...file,\n                    uid: file.uid,\n                    name: file.name,\n                    status: \"error\",\n                    error: err\n                }\n            ]);\n            if (onError) {\n                onError(err);\n            }\n        } finally{\n            setUploadLoading(false);\n        }\n    };\n    // 处理删除文件\n    const handleRemove = async (file)=>{\n        if (file.url && file.url !== initialBannerImage) {\n            try {\n                await _lib_api_upload__WEBPACK_IMPORTED_MODULE_8__.uploadApi.deleteFromOss(file.url);\n                notification.success(\"已从服务器删除图片\");\n            } catch (error) {\n                console.error(\"从OSS删除文件失败:\", error);\n                notification.error(\"服务器删除图片失败\");\n            }\n        }\n        setFileList([]);\n        setBannerImage(\"\");\n        return true;\n    };\n    // 处理分享按钮点击事件\n    const handleShare = ()=>{\n        if (true) {\n            // 获取当前页面URL\n            const currentUrl = window.location.href;\n            // 复制到剪贴板\n            navigator.clipboard.writeText(currentUrl).then(()=>{\n                notification.success(\"活动链接已复制，快去分享吧！\");\n            }).catch((err)=>{\n                console.error(\"复制失败:\", err);\n                notification.error(\"复制链接失败，请手动复制\");\n            });\n        }\n    };\n    // 如果还没有在客户端挂载，返回一个简单的占位符\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-64 bg-gray-200 animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 848,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-300 rounded mb-2 w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 851,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded mb-1 w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 852,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 853,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 850,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 849,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 847,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n            lineNumber: 846,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n                children: [\n                    isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 left-2 z-50 flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowUI(!showUI),\n                            className: \"p-2 bg-black/20 hover:bg-black/40 text-white rounded-full transition-colors\",\n                            title: showUI ? \"隐藏界面元素\" : \"显示界面元素\",\n                            children: showUI ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"w-4 h-4\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 874,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                        x1: \"1\",\n                                        y1: \"1\",\n                                        x2: \"23\",\n                                        y2: \"23\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 875,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 873,\n                                columnNumber: 19\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"w-4 h-4\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                        cx: \"12\",\n                                        cy: \"12\",\n                                        r: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 880,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 878,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 867,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 866,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 z-50 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSaveEdit,\n                                    className: \"p-2 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors\",\n                                    title: \"保存编辑\",\n                                    disabled: isSaving,\n                                    children: isSaving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 899,\n                                        columnNumber: 31\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 899,\n                                        columnNumber: 77\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 893,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCancelEdit,\n                                    className: \"p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors\",\n                                    title: \"取消编辑\",\n                                    disabled: isSaving,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 907,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 892,\n                            columnNumber: 15\n                        }, undefined) : isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsEditing(true),\n                            className: \"p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors\",\n                            title: \"进入编辑模式\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 917,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 912,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 890,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-[300px] overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-transparent to-black/30 z-10 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 926,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-blue-500/10 animate-pulse z-0 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 929,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 z-10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-full h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: bannerImage,\n                                                        alt: title,\n                                                        fill: true,\n                                                        priority: true,\n                                                        style: {\n                                                            objectFit: \"cover\"\n                                                        },\n                                                        className: \"z-10\",\n                                                        onError: (e)=>{\n                                                            const target = e.target;\n                                                            target.style.background = \"linear-gradient(120deg, #f3f4f6, #e5e7eb)\";\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                    lineNumber: 933,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 flex items-center justify-center bg-black/30 z-20 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-4 w-full max-w-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                className: \"w-full p-2 mb-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm bg-white/90\",\n                                                                value: bannerImage,\n                                                                onChange: (e)=>setBannerImage(e.target.value),\n                                                                placeholder: \"输入横幅图片URL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                                lineNumber: 951,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Modal_Upload_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                listType: \"picture-card\",\n                                                                fileList: fileList,\n                                                                customRequest: handleCustomRequest,\n                                                                onRemove: handleRemove,\n                                                                maxCount: 1,\n                                                                showUploadList: {\n                                                                    showPreviewIcon: true,\n                                                                    showRemoveIcon: true\n                                                                },\n                                                                onPreview: (file)=>{\n                                                                    if (file.url) {\n                                                                        window.open(file.url, \"_blank\");\n                                                                    }\n                                                                },\n                                                                children: fileList.length >= 1 ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                                            lineNumber: 973,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                marginTop: 8\n                                                                            },\n                                                                            children: \"上传\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                                            lineNumber: 974,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                                    lineNumber: 972,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                                lineNumber: 958,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                    lineNumber: 949,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 932,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 931,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: bannerImage,\n                                            alt: title,\n                                            fill: true,\n                                            priority: true,\n                                            style: {\n                                                objectFit: \"cover\"\n                                            },\n                                            className: \"z-10 hover:scale-105 transition-transform duration-700\",\n                                            onError: (e)=>{\n                                                const target = e.target;\n                                                target.style.background = \"linear-gradient(120deg, #f3f4f6, #e5e7eb)\";\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 986,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 983,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 928,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-4 left-4 right-4 z-20 flex justify-between items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 1005,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 924,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                className: \"w-full md:w-3/4 p-2 text-2xl font-bold border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                value: title,\n                                                onChange: (e)=>setTitle(e.target.value),\n                                                placeholder: \"输入活动标题\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1077,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                className: \"w-full md:w-3/4 p-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                value: organizer,\n                                                onChange: (e)=>setOrganizer(e.target.value),\n                                                placeholder: \"输入主办方\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1085,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 1076,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-800 flex items-center\",\n                                                children: [\n                                                    title,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"ml-2 w-5 h-5 text-yellow-400 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                        lineNumber: 1097,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1095,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            organizer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm\",\n                                                children: [\n                                                    \"主办方: \",\n                                                    organizer\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1100,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                className: \"p-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                value: startTime,\n                                                onChange: (e)=>setStartTime(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1107,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"self-center\",\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1113,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                className: \"p-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                value: endTime,\n                                                onChange: (e)=>setEndTime(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1114,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 1106,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm mt-2 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-500 to-indigo-500 h-1.5 w-1.5 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1123,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"活动时间：\",\n                                            formatDate(startTime),\n                                            \" - \",\n                                            formatDate(endTime)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 1122,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 1074,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3 mt-4 md:mt-0\",\n                                children: [\n                                    !isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSubmitClick,\n                                        disabled: isChecking,\n                                        className: \"flex items-center px-5 py-2.5 text-sm font-medium rounded-full \".concat(hasSubmitted ? \"bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\" : \"bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\", \" transition-all duration-200\"),\n                                        children: isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"loading-spinner mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                    lineNumber: 1158,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                \"检查状态中\"\n                                            ]\n                                        }, void 0, true) : hasSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"已报名 前往任务\"\n                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"立即报名\"\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 1148,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex items-center p-2.5 text-gray-500 hover:text-blue-500 hover:bg-gray-50 rounded-full hover:scale-110 active:scale-95 transition-all duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 1173,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 1169,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 1129,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 1073,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 863,\n                columnNumber: 7\n            }, undefined),\n            (promotionImage || backgroundImage || galleryImages || attachmentFiles) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    promotionImage: promotionImage,\n                    backgroundImage: backgroundImage,\n                    galleryImages: galleryImages,\n                    attachmentFiles: attachmentFiles,\n                    activityTitle: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                    lineNumber: 1182,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 1181,\n                columnNumber: 9\n            }, undefined),\n            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_login_dialog__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showLoginDialog,\n                onClose: ()=>setShowLoginDialog(false),\n                onSuccess: handleLoginSuccess\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 1194,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ActivityHeader, \"9puqDtaYsDo7+fZ2UWLRshIk+yw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.usePathname,\n        react_redux__WEBPACK_IMPORTED_MODULE_12__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_12__.useDispatch\n    ];\n});\n_c = ActivityHeader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ActivityHeader);\nvar _c;\n$RefreshReg$(_c, \"ActivityHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9hY3Rpdml0eS9mZXN0aXZhbC9jb21wb25lbnRzL0FjdGl2aXR5SGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVrRDtBQUNwQjtBQUN3QztBQUNoQjtBQUNBO0FBRUc7QUFDUjtBQUNEO0FBQ2E7QUFDMEM7QUFDekM7QUFDUjtBQUNkO0FBRW1DO0FBQ25CO0FBRXhELDZCQUE2QjtBQUM3QixNQUFNOEIsYUFBYSxDQUFDQztJQUNsQiwyQkFBMkI7SUFDM0IsSUFBSSxLQUFrQixFQUFhLEVBRWxDO0lBRUQsTUFBTUMsT0FBTyxJQUFJQyxLQUFLRjtJQUV0QixXQUFXO0lBQ1gsSUFBSUcsTUFBTUYsS0FBS0csT0FBTyxLQUFLO1FBQ3pCLE9BQU9KLFlBQVksZUFBZTtJQUNwQztJQUVBLE1BQU1LLE9BQU9KLEtBQUtLLFdBQVc7SUFDN0IsTUFBTUMsUUFBUUMsT0FBT1AsS0FBS1EsUUFBUSxLQUFLLEdBQUdDLFFBQVEsQ0FBQyxHQUFHO0lBQ3RELE1BQU1DLE1BQU1ILE9BQU9QLEtBQUtXLE9BQU8sSUFBSUYsUUFBUSxDQUFDLEdBQUc7SUFFL0MsT0FBTyxHQUFXSCxPQUFSRixNQUFLLEtBQVlNLE9BQVRKLE9BQU0sS0FBTyxPQUFKSTtBQUM3QjtBQWdDQSxNQUFNRSxpQkFBZ0Q7UUFBQyxFQUNyREMsT0FBT0MsWUFBWSxFQUNuQkMsV0FBV0MsZ0JBQWdCLEVBQzNCQyxTQUFTQyxjQUFjLEVBQ3ZCQyxhQUFhQyxrQkFBa0IsRUFDL0JDLFFBQVEsRUFDUkMsV0FBVyxFQUNYQyxVQUFVLEVBQ1ZDLE9BQU8sRUFBRSxFQUNUQyxXQUFXQyxtQkFBbUIsRUFBRSxFQUVoQ0MsZUFBZXBDLDJEQUFZQSxDQUFDcUMsSUFBSSxFQUNoQ0MsY0FBYyxFQUNkLE9BQU87SUFDUEMsY0FBYyxFQUNkQyxlQUFlLEVBQ2ZDLGFBQWEsRUFDYkMsZUFBZSxFQUNoQjs7SUFDQ0MsUUFBUUMsR0FBRyxDQUFDLDRDQUFrQztRQUFFWjtRQUFZVDtJQUFhO0lBR3pFLE1BQU1zQixlQUFlekMsMkZBQWVBO0lBQ3BDLE1BQU0wQyxTQUFTekMsMkRBQVNBO0lBQ3hCLE1BQU0wQyxXQUFXekMsNkRBQVdBO0lBRTVCLFlBQVk7SUFDWixNQUFNZ0IsUUFBUUM7SUFDZCxNQUFNQyxZQUFZQztJQUNsQixNQUFNQyxVQUFVQztJQUNoQixNQUFNQyxjQUFjQztJQUNwQixNQUFNSyxZQUFZQztJQUVsQixvQkFBb0I7SUFDcEIsTUFBTWEsWUFBWTNELHlEQUFXQSxDQUFDRix1REFBZUE7SUFDN0MsTUFBTThELFdBQVczRCx5REFBV0E7SUFDNUIsTUFBTTRELFVBQVVGLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBV0csTUFBTSxNQUFLO0lBRXRDLCtCQUErQjtJQUMvQixNQUFNQyxnQkFBZ0I7UUFDcEIsSUFBSTtZQUNGVCxRQUFRQyxHQUFHLENBQUM7WUFDWixNQUFNUyxZQUFZQyxhQUFhQyxPQUFPLENBQUM7WUFDdkMsSUFBSUYsV0FBVztnQkFDYixNQUFNRyxXQUFXQyxLQUFLQyxLQUFLLENBQUNMO2dCQUM1QlYsUUFBUUMsR0FBRyxDQUFDLDRDQUFrQ1k7Z0JBQzlDYixRQUFRQyxHQUFHLENBQUMscUNBQTJCSTtnQkFFdkMsaUNBQWlDO2dCQUNqQyxJQUFJUSxTQUFTRyxVQUFVLElBQUlILFNBQVNJLE1BQU0sSUFBSyxFQUFDSixTQUFTSyxLQUFLLElBQUlMLFNBQVNLLEtBQUssQ0FBQ0MsSUFBSSxPQUFPLEVBQUMsR0FBSTtvQkFDL0ZuQixRQUFRQyxHQUFHLENBQUM7b0JBQ1osSUFBSTt3QkFDRixNQUFNbUIsV0FBVyxNQUFNNUQsa0RBQU9BLENBQUM2RCxXQUFXLENBQUNSLFNBQVNJLE1BQU07d0JBQzFELElBQUlHLFNBQVNFLElBQUksS0FBSyxPQUFPRixTQUFTRyxJQUFJLEVBQUU7NEJBQzFDdkIsUUFBUUMsR0FBRyxDQUFDLHNDQUE0Qm1CLFNBQVNHLElBQUk7NEJBRXJELGFBQWE7NEJBQ2IsTUFBTUMsa0JBQWtCO2dDQUN0QixHQUFHWCxRQUFRO2dDQUNYSyxPQUFPRSxTQUFTRyxJQUFJLENBQUNMLEtBQUssSUFBSTtnQ0FDOUJPLFVBQVVMLFNBQVNHLElBQUksQ0FBQ0UsUUFBUSxJQUFJWixTQUFTWSxRQUFRO2dDQUNyREMsV0FBV04sU0FBU0csSUFBSSxDQUFDRyxTQUFTLElBQUliLFNBQVNhLFNBQVM7Z0NBQ3hEQyxRQUFRUCxTQUFTRyxJQUFJLENBQUNJLE1BQU0sSUFBSWQsU0FBU2MsTUFBTTtnQ0FDL0NuQixRQUFRWSxTQUFTRyxJQUFJLENBQUNmLE1BQU0sSUFBSUssU0FBU0wsTUFBTTs0QkFDakQ7NEJBRUFSLFFBQVFDLEdBQUcsQ0FBQywrQkFBcUI7Z0NBQy9CMkIsVUFBVWYsU0FBU0ssS0FBSztnQ0FDeEJXLFVBQVVMLGdCQUFnQk4sS0FBSztnQ0FDL0JZLFNBQVNOOzRCQUNYOzRCQUVBLHVCQUF1Qjs0QkFDdkJiLGFBQWFvQixPQUFPLENBQUMsUUFBUWpCLEtBQUtrQixTQUFTLENBQUNSOzRCQUM1Q2xCLFNBQVM3RCxtREFBT0EsQ0FBQytFOzRCQUNqQjt3QkFDRjtvQkFDRixFQUFFLE9BQU9TLE9BQU87d0JBQ2RqQyxRQUFRaUMsS0FBSyxDQUFDLHNDQUE0QkE7b0JBQzVDO2dCQUNGO2dCQUVBLGNBQWM7Z0JBQ2QsTUFBTUMsa0JBQWtCckIsU0FBU0ssS0FBSyxNQUFLYixzQkFBQUEsZ0NBQUFBLFVBQVdhLEtBQUs7Z0JBQzNELE1BQU1pQixpQkFBaUJ0QixTQUFTTCxNQUFNLE1BQUtILHNCQUFBQSxnQ0FBQUEsVUFBV0csTUFBTTtnQkFDNUQsTUFBTTRCLHdCQUF3QnZCLFNBQVNHLFVBQVUsTUFBS1gsc0JBQUFBLGdDQUFBQSxVQUFXVyxVQUFVO2dCQUUzRWhCLFFBQVFDLEdBQUcsQ0FBQyxpQ0FBdUI7b0JBQ2pDb0MsY0FBY0g7b0JBQ2RJLGFBQWFIO29CQUNiSSxvQkFBb0JIO29CQUNwQlIsUUFBUSxFQUFFdkIsc0JBQUFBLGdDQUFBQSxVQUFXYSxLQUFLO29CQUMxQlcsVUFBVWhCLFNBQVNLLEtBQUs7b0JBQ3hCc0IsT0FBTyxFQUFFbkMsc0JBQUFBLGdDQUFBQSxVQUFXRyxNQUFNO29CQUMxQmlDLFNBQVM1QixTQUFTTCxNQUFNO2dCQUMxQjtnQkFFQSx5Q0FBeUM7Z0JBQ3pDLElBQUkwQixtQkFBbUJDLGtCQUFrQkMsdUJBQXVCO29CQUM5RHBDLFFBQVFDLEdBQUcsQ0FBQztvQkFDWkssU0FBUzdELG1EQUFPQSxDQUFDb0U7Z0JBQ25CLE9BQU87b0JBQ0xiLFFBQVFDLEdBQUcsQ0FBQztnQkFDZDtZQUNGLE9BQU87Z0JBQ0xELFFBQVFDLEdBQUcsQ0FBQztZQUNkO1FBQ0YsRUFBRSxPQUFPZ0MsT0FBTztZQUNkakMsUUFBUWlDLEtBQUssQ0FBQyxrQ0FBd0JBO1FBQ3hDO0lBQ0Y7SUFFQSx5QkFBeUI7SUFDekJqRyxnREFBU0EsQ0FBQztRQUNSLGNBQWM7UUFDZHlFLGdCQUFnQmlDLEtBQUssQ0FBQzFDLFFBQVFpQyxLQUFLO1FBRW5DLE1BQU1VLHlCQUF5QjtZQUM3QixJQUFJQyxTQUFTQyxlQUFlLEtBQUssV0FBVztnQkFDMUNwQyxnQkFBZ0JpQyxLQUFLLENBQUMxQyxRQUFRaUMsS0FBSztZQUNyQztRQUNGO1FBRUEsTUFBTWEsY0FBYztZQUNsQnJDLGdCQUFnQmlDLEtBQUssQ0FBQzFDLFFBQVFpQyxLQUFLO1FBQ3JDO1FBRUEsaUJBQWlCO1FBQ2pCVyxTQUFTRyxnQkFBZ0IsQ0FBQyxvQkFBb0JKO1FBQzlDSyxPQUFPRCxnQkFBZ0IsQ0FBQyxTQUFTRDtRQUVqQyxPQUFPO1lBQ0xGLFNBQVNLLG1CQUFtQixDQUFDLG9CQUFvQk47WUFDakRLLE9BQU9DLG1CQUFtQixDQUFDLFNBQVNIO1FBQ3RDO0lBQ0YsR0FBRztRQUFDekMsc0JBQUFBLGdDQUFBQSxVQUFXYSxLQUFLO1FBQUVaO0tBQVM7SUFFL0IsbUJBQW1CO0lBQ25CLE1BQU00QyxpQkFBaUI7UUFDckIsSUFBSTtZQUNGLE1BQU1DLFVBQVV4QyxhQUFhQyxPQUFPLENBQUM7WUFDckMsTUFBTUYsWUFBWXlDLFVBQVVyQyxLQUFLQyxLQUFLLENBQUNvQyxXQUFXO1lBQ2xELE1BQU1DLGFBQWExQyxDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVdGLE1BQU0sTUFBSUgsc0JBQUFBLGdDQUFBQSxVQUFXRyxNQUFNO1lBQ3pELE9BQU80QyxlQUFlLEdBQUcsZ0JBQWdCO1FBQzNDLEVBQUUsT0FBT25CLE9BQU87WUFDZGpDLFFBQVFpQyxLQUFLLENBQUMsMkJBQTJCQTtZQUN6QyxPQUFPO1FBQ1Q7SUFDRjtJQUlBLGtCQUFrQjtJQUNsQixNQUFNb0IsdUJBQXVCO1FBQzNCdEcsaUZBQUtBLENBQUN1RyxPQUFPLENBQUM7WUFDWjNFLE9BQU87WUFDUDRFLFNBQVM7WUFDVEMsUUFBUTtZQUNSQyxZQUFZO1lBQ1pDLFVBQVU7Z0JBQ1J4RCxhQUFheUQsSUFBSSxDQUFDO1lBQ3BCO1FBQ0Y7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNQyw0QkFBNEI7UUFDaEMsT0FBUUM7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLGFBQWE7SUFDYixNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR2hJLCtDQUFRQSxDQUFXO1FBQy9DO1lBQUVpSSxJQUFJO1lBQVFDLE1BQU07UUFBTztRQUMzQjtZQUFFRCxJQUFJO1lBQVFDLE1BQU07UUFBVztLQUNoQztJQUVELFdBQVc7SUFDWCxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR3BJLCtDQUFRQSxDQUFXLEVBQUU7SUFFakQsU0FBUztJQUNULE1BQU0sQ0FBQ3FJLGNBQWNDLGdCQUFnQixHQUFHdEksK0NBQVFBLENBQWdCO0lBQ2hFLE1BQU0sQ0FBQ3VJLGdCQUFnQkMsa0JBQWtCLEdBQUd4SSwrQ0FBUUEsQ0FBQztJQUVyRCxZQUFZO0lBQ1osTUFBTSxDQUFDeUksaUJBQWlCQyxtQkFBbUIsR0FBRzFJLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQzJJLGNBQWNDLGdCQUFnQixHQUFHNUksK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDNkksWUFBWUMsY0FBYyxHQUFHOUksK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDOEgsb0JBQW9CaUIsc0JBQXNCLEdBQUcvSSwrQ0FBUUEsQ0FBZ0IsT0FBTyxtREFBbUQ7SUFFdEksTUFBTSxDQUFDZ0osZUFBZUMsaUJBQWlCLEdBQUdqSiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUNrSixVQUFVQyxZQUFZLEdBQUduSiwrQ0FBUUEsQ0FBcUIsRUFBRTtJQUkvRCxNQUFNLENBQUNvSixRQUFRQyxVQUFVLEdBQUdySiwrQ0FBUUEsQ0FBQztJQUVyQyxpQkFBaUI7SUFDakIsTUFBTSxDQUFDc0osVUFBVUMsWUFBWSxHQUFHdkosK0NBQVFBLENBQUM7SUFFekNDLGdEQUFTQSxDQUFDO1FBQ1JzSixZQUFZO0lBQ2QsR0FBRyxFQUFFO0lBRUwsY0FBYztJQUNkdEosZ0RBQVNBLENBQUM7UUFDUixNQUFNdUosMEJBQTBCO1lBQzlCdkYsUUFBUUMsR0FBRyxDQUFDLDhDQUFvQztnQkFDOUNlLFlBQVlYLFVBQVVXLFVBQVU7Z0JBQ2hDUixRQUFRSCxVQUFVRyxNQUFNO2dCQUN4QlUsT0FBT2IsVUFBVWEsS0FBSztnQkFDdEJtRTtnQkFDQWhHO1lBQ0Y7WUFFQSx5QkFBeUI7WUFDekIsTUFBTW1HLFlBQVl0QztZQUNsQixJQUFJLENBQUM3QyxVQUFVVyxVQUFVLElBQUksQ0FBQ3dFLFdBQVc7Z0JBQ3ZDeEYsUUFBUUMsR0FBRyxDQUFDLDRDQUFrQztvQkFDNUNlLFlBQVlYLFVBQVVXLFVBQVU7b0JBQ2hDd0U7Z0JBQ0Y7Z0JBQ0Esb0JBQW9CO2dCQUNwQmIsZ0JBQWdCO2dCQUNoQkcsc0JBQXNCO2dCQUN0QjtZQUNGO1lBRUEsSUFBSTtvQkFNRTFELGdCQUFnQ0E7Z0JBTHBDcEIsUUFBUUMsR0FBRyxDQUFDO2dCQUNaNEUsY0FBYztnQkFDZCxNQUFNekQsV0FBVyxNQUFNaEUsc0VBQXVCQSxDQUFDcUksaUJBQWlCLENBQUNDLE9BQU9yRztnQkFDeEVXLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBNEJtQjtnQkFFeEMsSUFBSUEsQ0FBQUEscUJBQUFBLGdDQUFBQSxpQkFBQUEsU0FBVUcsSUFBSSxjQUFkSCxxQ0FBQUEsZUFBZ0JFLElBQUksTUFBSyxTQUFPRixrQkFBQUEsU0FBU0csSUFBSSxjQUFiSCxzQ0FBQUEsZ0JBQWVHLElBQUksR0FBRTtvQkFDdkQsTUFBTSxFQUFFb0UsU0FBUyxFQUFFQyxNQUFNLEVBQUUsR0FBR3hFLFNBQVNHLElBQUksQ0FBQ0EsSUFBSTtvQkFDaER2QixRQUFRQyxHQUFHLENBQUMsaUNBQXVCO3dCQUFFMEY7d0JBQVdDO29CQUFPO29CQUN2RGpCLGdCQUFnQmdCO29CQUVoQixJQUFJQyxVQUFVQSxPQUFPQyxNQUFNLEtBQUtDLFdBQVc7d0JBQ3pDaEIsc0JBQXNCYyxPQUFPQyxNQUFNO29CQUNyQyxPQUFPO3dCQUNMZixzQkFBc0I7b0JBQ3hCO2dCQUNGLE9BQU87b0JBQ0w5RSxRQUFRQyxHQUFHLENBQUM7b0JBQ1oscUJBQXFCO29CQUNyQjBFLGdCQUFnQjtvQkFDaEJHLHNCQUFzQjtnQkFDeEI7WUFDRixFQUFFLE9BQU83QyxPQUFPO2dCQUNkakMsUUFBUWlDLEtBQUssQ0FBQyxrQ0FBd0JBO2dCQUN0QyxjQUFjO2dCQUNkMEMsZ0JBQWdCO2dCQUNoQkcsc0JBQXNCO1lBQ3hCLFNBQVU7Z0JBQ1JELGNBQWM7WUFDaEI7UUFDRjtRQUVBLG9CQUFvQjtRQUNwQixJQUFJUSxVQUFVO1lBQ1pyRixRQUFRQyxHQUFHLENBQUM7WUFDWnNGO1FBQ0YsT0FBTztZQUNMdkYsUUFBUUMsR0FBRyxDQUFDO1FBQ2Q7SUFDRixHQUFHO1FBQUNaO1FBQVlnQixVQUFVVyxVQUFVO1FBQUVYLFVBQVVHLE1BQU07UUFBRUgsVUFBVWEsS0FBSztRQUFFbUU7S0FBUztJQU1sRixjQUFjO0lBQ2RySixnREFBU0EsQ0FBQztRQUNSLE1BQU0rSixxQkFBcUI7WUFDekIsSUFBSTtnQkFDRixlQUFlO2dCQUNmLElBQUkxRixhQUFhQSxVQUFVVyxVQUFVLEVBQUU7d0JBSUxJO29CQUhoQ3lELGNBQWM7b0JBQ2QsTUFBTXpELFdBQVcsTUFBTWxFLDBEQUFXQSxDQUFDNkksa0JBQWtCLENBQUNMLE9BQU9yRztvQkFDN0RXLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0JtQjtvQkFDaEMsSUFBSUEsQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVeUUsTUFBTSxNQUFLLFNBQU96RSxpQkFBQUEsU0FBU0csSUFBSSxjQUFiSCxxQ0FBQUEsZUFBZUcsSUFBSSxHQUFFO3dCQUNuRG9ELGdCQUFnQnZELFNBQVNHLElBQUksQ0FBQ0EsSUFBSSxDQUFDb0UsU0FBUztvQkFDOUMsT0FBTzt3QkFDTCxtQkFBbUI7d0JBQ25CaEIsZ0JBQWdCO29CQUNsQjtvQkFDQUUsY0FBYztnQkFDaEIsT0FBTztvQkFDTCxpQkFBaUI7b0JBQ2pCRixnQkFBZ0I7b0JBQ2hCRSxjQUFjO2dCQUNoQjtZQUNGLEVBQUUsT0FBTzVDLE9BQU87Z0JBQ2RqQyxRQUFRaUMsS0FBSyxDQUFDLGVBQWVBO2dCQUM3QixjQUFjO2dCQUNkMEMsZ0JBQWdCO2dCQUNoQkUsY0FBYztZQUNoQjtRQUNGO1FBRUEsWUFBWTtRQUNaLElBQUlRLFVBQVU7WUFDWlU7UUFDRjtJQUNGLEdBQUc7UUFBQzFHO1FBQVlnQjtRQUFXZ0Y7S0FBUztJQUVwQyxTQUFTO0lBQ1RySixnREFBU0EsQ0FBQztRQUNSLE1BQU1nSyxvQkFBb0I7WUFDeEIsSUFBSTNHLFlBQVk7Z0JBQ2QsSUFBSTtvQkFDRixNQUFNK0IsV0FBVyxNQUFNakUsNkRBQWNBLENBQUM4SSxlQUFlLENBQUNQLE9BQU9yRztvQkFDN0QsSUFBSStCLENBQUFBLHFCQUFBQSwrQkFBQUEsU0FBVXlFLE1BQU0sTUFBSyxLQUFLO3dCQUM1QjFCLFVBQVUvQyxTQUFTRyxJQUFJLENBQUNBLElBQUksSUFBSSxFQUFFO29CQUNwQztnQkFDRixFQUFFLE9BQU9VLE9BQU87b0JBQ2RqQyxRQUFRaUMsS0FBSyxDQUFDLGFBQWFBO2dCQUM3QjtZQUNGO1FBQ0Y7UUFFQStEO0lBQ0YsR0FBRztRQUFDM0c7S0FBVztJQUVmckQsZ0RBQVNBLENBQUM7UUFDUixVQUFVO1FBQ1YsSUFBSWlELGFBQWE7WUFDZmlHLFlBQVk7Z0JBQ1Y7b0JBQ0VnQixLQUFLO29CQUNMQyxNQUFNO29CQUNOTixRQUFRO29CQUNSTyxLQUFLbkg7Z0JBQ1A7YUFDRDtRQUNILE9BQU87WUFDTGlHLFlBQVksRUFBRTtRQUNoQjtJQUNGLEdBQUc7UUFBQ2pHO0tBQVk7SUFFaEIsU0FBUztJQUNULE1BQU1vSCxtQkFBbUI7UUFDdkJDLFNBQVMxSDtRQUNUMkgsYUFBYXpIO1FBQ2IwSCxXQUFXeEg7UUFDWHlILGVBQWV2SDtRQUNmd0gsYUFBYWxIO1FBSWJtSCxhQUFhO0lBQ2Y7SUFFQSxTQUFTO0lBQ1QsTUFBTUMsaUJBQWlCO1FBQ3JCLElBQUksQ0FBQ3ZILFlBQVk7WUFDZmEsYUFBYStCLEtBQUssQ0FBQztZQUNuQjtRQUNGO1FBRUEsSUFBSTtZQUNGNEUsWUFBWTtZQUlaLFNBQVM7WUFDVCxNQUFNQyxhQUFhO2dCQUNqQjlDLElBQUkwQixPQUFPckc7Z0JBQ1g4RyxNQUFNeEg7Z0JBQ05FLFdBQVcsSUFBSWQsS0FBS2M7Z0JBQ3BCRSxTQUFTLElBQUloQixLQUFLZ0I7Z0JBQ2xCZ0ksWUFBWTlIO2dCQUNaTSxXQUFXQTtnQkFFWCxtQkFBbUI7Z0JBQ25CeUgsZUFBZTtnQkFDZkMsY0FBYztnQkFDZEMsZUFBZTtZQUNqQjtZQUVBbEgsUUFBUUMsR0FBRyxDQUFDLFdBQVc2RztZQUV2QixXQUFXO1lBQ1gsTUFBTTFGLFdBQVcsTUFBTWxFLDBEQUFXQSxDQUFDaUssTUFBTSxDQUFDTDtZQUMxQzlHLFFBQVFDLEdBQUcsQ0FBQyxXQUFXbUI7WUFFdkIsSUFBSUEsQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVeUUsTUFBTSxNQUFLLEtBQUs7Z0JBQzVCLGNBQWM7Z0JBQ2QsTUFBTXVCLGNBQWMsTUFBTWpLLDZEQUFjQSxDQUFDZ0ssTUFBTSxDQUFDekIsT0FBT3JHLGFBQWE2RTtnQkFDcEVsRSxRQUFRQyxHQUFHLENBQUMsV0FBV21IO2dCQUV2QmxILGFBQWFtSCxPQUFPLENBQUM7Z0JBQ3JCVixhQUFhO1lBQ2YsT0FBTztvQkFDY3ZGO2dCQUFuQmxCLGFBQWErQixLQUFLLENBQUNiLENBQUFBLHFCQUFBQSxnQ0FBQUEsaUJBQUFBLFNBQVVHLElBQUksY0FBZEgscUNBQUFBLGVBQWdCa0csT0FBTyxLQUFJO1lBQ2hEO1FBQ0YsRUFBRSxPQUFPckYsT0FBTztZQUNkakMsUUFBUWlDLEtBQUssQ0FBQyxhQUFhQTtZQUMzQi9CLGFBQWErQixLQUFLLENBQUM7UUFDckIsU0FBVTtZQUNSNEUsWUFBWTtRQUNkO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTVUsaUJBQWlCLENBQUNDO1FBQ3RCLElBQUlDLFdBQVc7WUFDYnRELFVBQVVELE9BQU93RCxRQUFRLENBQUNoQyxPQUFPOEIsSUFBSXhELEVBQUUsS0FBS0UsT0FBT3lELE1BQU0sQ0FBQzNELENBQUFBLEtBQU1BLE9BQU8wQixPQUFPOEIsSUFBSXhELEVBQUUsS0FBSzttQkFBSUU7Z0JBQVF3QixPQUFPOEIsSUFBSXhELEVBQUU7YUFBRTtRQUN0SDtJQUNGO0lBRUEsV0FBVztJQUNYLE1BQU00RCxzQkFBc0I7UUFDMUJ2RCxnQkFBZ0I7SUFDbEI7SUFFQSxVQUFVO0lBQ1YsTUFBTXdELGVBQWU7UUFDbkIsSUFBSS9ELFFBQVFnRSxNQUFNLEdBQUcsR0FBRztZQUN0QixNQUFNQyxTQUFpQjtnQkFDckIvRCxJQUFJLE9BQWtCLE9BQVhqRyxLQUFLaUssR0FBRztnQkFDbkIvRCxNQUFNO1lBQ1I7WUFDQUYsV0FBVzttQkFBSUQ7Z0JBQVNpRTthQUFPO1lBQy9CNUQsVUFBVTttQkFBSUQ7Z0JBQVF3QixPQUFPcUMsT0FBTy9ELEVBQUU7YUFBRTtRQUMxQztJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1pRSxrQkFBa0IsQ0FBQ2pFO1FBQ3ZCLElBQUlGLFFBQVFnRSxNQUFNLEdBQUcsR0FBRztZQUN0Qi9ELFdBQVdELFFBQVE2RCxNQUFNLENBQUNILENBQUFBLE1BQU9BLElBQUl4RCxFQUFFLEtBQUtBO1lBQzVDRyxVQUFVRCxPQUFPeUQsTUFBTSxDQUFDM0QsQ0FBQUEsS0FBTUEsT0FBTzBCLE9BQU8xQjtRQUM5QztJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1rRSxpQkFBaUI7UUFDckIsSUFBSTtZQUNGLE1BQU1DLGlCQUFpQmpJLGFBQWFrSSxPQUFPLENBQUM7WUFFNUMsZ0JBQWdCO1lBQ2hCLE1BQU1DLHFCQUFxQixNQUFNL0ssd0VBQWFBLENBQUNnTCxVQUFVO1lBRXpELElBQUlELG1CQUFtQjlHLElBQUksQ0FBQ0QsSUFBSSxLQUFLLEtBQUs7Z0JBQ3hDLE1BQU1pSCxjQUFjRixtQkFBbUI5RyxJQUFJLENBQUNBLElBQUk7Z0JBQ2hELGlCQUFpQjtnQkFDakIsTUFBTWlILGNBQWNELFlBQVlFLElBQUksQ0FBQyxDQUFDQyxPQUFjQSxLQUFLckosVUFBVSxLQUFLcUcsT0FBT3JHO2dCQUUvRSxJQUFJbUosYUFBYTtvQkFDZiwyQkFBMkI7b0JBQzNCeEYsT0FBTzJGLElBQUksQ0FBQyx1QkFBc0MsT0FBZkgsWUFBWXhFLEVBQUUsR0FBSTtvQkFDckQ5RCxhQUFhbUgsT0FBTyxDQUFDO2dCQUN2QixPQUFPO29CQUNMLDBCQUEwQjtvQkFDMUJyRSxPQUFPMkYsSUFBSSxDQUFDLGdCQUFnQjtvQkFDNUJ6SSxhQUFheUQsSUFBSSxDQUFDO2dCQUNwQjtZQUNGLE9BQU87Z0JBQ0wsd0JBQXdCO2dCQUN4QlgsT0FBTzJGLElBQUksQ0FBQyxnQkFBZ0I7Z0JBQzVCekksYUFBYXlELElBQUksQ0FBQztZQUNwQjtZQUVBLElBQUl3RSxnQkFBZ0I7Z0JBQ2xCQSxlQUFlUyxLQUFLO1lBQ3RCO1FBQ0YsRUFBRSxPQUFPM0csT0FBTztZQUNkakMsUUFBUWlDLEtBQUssQ0FBQyxXQUFXQTtZQUN6Qi9CLGFBQWErQixLQUFLLENBQUM7UUFDckI7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNNEcsb0JBQW9CO1lBVVB4SSxrQkFRQUs7UUFqQmpCLDJCQUEyQjtRQUMzQixNQUFNb0ksZUFBZW5JLGFBQWFDLE9BQU8sQ0FBQztRQUMxQyxNQUFNRixZQUFZb0ksZUFBZWhJLEtBQUtDLEtBQUssQ0FBQytILGdCQUFnQjtRQUU1RDlJLFFBQVFDLEdBQUcsQ0FBQywwQ0FBZ0M7WUFDMUN5RTtZQUNBLG1CQUFtQjtnQkFDakIxRCxVQUFVLEVBQUVYLHNCQUFBQSxnQ0FBQUEsVUFBV1csVUFBVTtnQkFDakNFLEtBQUssRUFBRWIsc0JBQUFBLGdDQUFBQSxVQUFXYSxLQUFLO2dCQUN2QjZILFdBQVcsRUFBRTFJLHNCQUFBQSxpQ0FBQUEsbUJBQUFBLFVBQVdhLEtBQUssY0FBaEJiLHVDQUFBQSxpQkFBa0J5SCxNQUFNO2dCQUNyQ2tCLFdBQVcsUUFBTzNJLHNCQUFBQSxnQ0FBQUEsVUFBV2EsS0FBSztnQkFDbENWLE1BQU0sRUFBRUgsc0JBQUFBLGdDQUFBQSxVQUFXRyxNQUFNO2dCQUN6QlMsTUFBTSxFQUFFWixzQkFBQUEsZ0NBQUFBLFVBQVdZLE1BQU07Z0JBQ3pCUSxRQUFRLEVBQUVwQixzQkFBQUEsZ0NBQUFBLFVBQVdvQixRQUFRO1lBQy9CO1lBQ0EscUJBQXFCO2dCQUNuQlAsS0FBSyxFQUFFUixzQkFBQUEsZ0NBQUFBLFVBQVdRLEtBQUs7Z0JBQ3ZCNkgsV0FBVyxFQUFFckksc0JBQUFBLGlDQUFBQSxtQkFBQUEsVUFBV1EsS0FBSyxjQUFoQlIsdUNBQUFBLGlCQUFrQm9ILE1BQU07Z0JBQ3JDa0IsV0FBVyxRQUFPdEksc0JBQUFBLGdDQUFBQSxVQUFXUSxLQUFLO2dCQUNsQ0QsTUFBTSxFQUFFUCxzQkFBQUEsZ0NBQUFBLFVBQVdPLE1BQU07Z0JBQ3pCUSxRQUFRLEVBQUVmLHNCQUFBQSxnQ0FBQUEsVUFBV2UsUUFBUTtZQUMvQjtZQUNBLG9CQUFvQjtnQkFDbEIsMEJBQTBCLENBQUMsRUFBQ3BCLHNCQUFBQSxnQ0FBQUEsVUFBV2EsS0FBSztnQkFDNUMsNkJBQTZCYixDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVdhLEtBQUssS0FBSWIsVUFBVWEsS0FBSyxDQUFDQyxJQUFJLE9BQU87Z0JBQzVFLDBCQUEwQixDQUFDLEVBQUNULHNCQUFBQSxnQ0FBQUEsVUFBV1EsS0FBSztnQkFDNUMsNkJBQTZCUixDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVdRLEtBQUssS0FBSVIsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXUSxLQUFLLENBQUNDLElBQUksUUFBTztZQUMvRTtRQUNGO1FBRUEsc0JBQXNCO1FBQ3RCLElBQUlkLHNCQUFBQSxnQ0FBQUEsVUFBV1ksTUFBTSxFQUFFO1lBQ3JCLElBQUk7b0JBSU9HLGdCQUNNQSxzQkFBQUEsaUJBQ0tBLGlCQUNSQSxpQkFDRkE7Z0JBUFYsTUFBTUEsV0FBVyxNQUFNNUQsa0RBQU9BLENBQUM2RCxXQUFXLENBQUNoQixVQUFVWSxNQUFNO2dCQUMzRGpCLFFBQVFDLEdBQUcsQ0FBQyx1Q0FBNkI7b0JBQ3ZDcUIsTUFBTUYsU0FBU0UsSUFBSTtvQkFDbkJKLEtBQUssR0FBRUUsaUJBQUFBLFNBQVNHLElBQUksY0FBYkgscUNBQUFBLGVBQWVGLEtBQUs7b0JBQzNCNkgsV0FBVyxHQUFFM0gsa0JBQUFBLFNBQVNHLElBQUksY0FBYkgsdUNBQUFBLHVCQUFBQSxnQkFBZUYsS0FBSyxjQUFwQkUsMkNBQUFBLHFCQUFzQjBHLE1BQU07b0JBQ3pDa0IsV0FBVyxTQUFPNUgsa0JBQUFBLFNBQVNHLElBQUksY0FBYkgsc0NBQUFBLGdCQUFlRixLQUFLO29CQUN0Q08sUUFBUSxHQUFFTCxrQkFBQUEsU0FBU0csSUFBSSxjQUFiSCxzQ0FBQUEsZ0JBQWVLLFFBQVE7b0JBQ2pDUixNQUFNLEdBQUVHLGtCQUFBQSxTQUFTRyxJQUFJLGNBQWJILHNDQUFBQSxnQkFBZTRDLEVBQUU7Z0JBQzNCO1lBQ0YsRUFBRSxPQUFPL0IsT0FBTztnQkFDZGpDLFFBQVFpQyxLQUFLLENBQUMsa0NBQXdCQTtZQUN4QztRQUNGO1FBRUEsa0JBQWtCO1FBQ2xCLElBQUl5QyxjQUFjO1lBQ2hCMUUsUUFBUUMsR0FBRyxDQUFDO1lBQ1ppSTtZQUNBO1FBQ0Y7UUFFQSxXQUFXO1FBQ1gsSUFBSSxDQUFDN0gsYUFBYSxDQUFDQSxVQUFVVyxVQUFVLEVBQUU7WUFDdkNoQixRQUFRQyxHQUFHLENBQUM7WUFDWixjQUFjO1lBQ2QsTUFBTWdKLGNBQWM3SSxZQUFZO1lBQ2hDRCxPQUFPK0ksSUFBSSxDQUFDLG1CQUFtRCxPQUFoQ0MsbUJBQW1CRjtZQUNsRDtRQUNGO1FBRUEsNEJBQTRCO1FBQzVCLE1BQU16RCxZQUFZdEM7UUFDbEJsRCxRQUFRQyxHQUFHLENBQUMsK0JBQXFCO1lBQUV1RjtZQUFXaEYsTUFBTSxFQUFFSCxzQkFBQUEsZ0NBQUFBLFVBQVdHLE1BQU07UUFBQztRQUN4RSxJQUFJLENBQUNnRixXQUFXO1lBQ2R4RixRQUFRQyxHQUFHLENBQUM7WUFDWm9EO1lBQ0E7UUFDRjtRQUVBckQsUUFBUUMsR0FBRyxDQUFDO1FBQ1osZ0JBQWdCO1FBQ2hCbUo7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNQyxxQkFBcUI7UUFDekJuSixhQUFhbUgsT0FBTyxDQUFDO0lBRXJCLDhCQUE4QjtJQUM5Qiw4QkFBOEI7SUFDaEM7SUFFQSxTQUFTO0lBQ1QsTUFBTWlDLHNCQUFzQjtRQUMxQixTQUFTO1FBQ1QzRSxnQkFBZ0I7UUFDaEJHLHNCQUFzQixJQUFJLFdBQVc7UUFDckM1RSxhQUFhbUgsT0FBTyxDQUFDO1FBRXJCLGVBQWU7UUFDZixJQUFJMUgsZ0JBQWdCO1lBQ2xCQTtRQUNGO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTXlKLDJCQUEyQjtRQUMvQixJQUFJO2dCQXNDRWhJO1lBckNKLGVBQWU7WUFDZixJQUFJLENBQUNmLFVBQVVhLEtBQUssSUFBSWIsVUFBVWEsS0FBSyxDQUFDQyxJQUFJLE9BQU8sSUFBSTtnQkFDckQsVUFBVTtnQkFDVnBFLGlGQUFLQSxDQUFDdUcsT0FBTyxDQUFDO29CQUNaM0UsT0FBTztvQkFDUDRFLFNBQVM7b0JBQ1RDLFFBQVE7b0JBQ1JDLFlBQVk7b0JBQ1o4RixNQUFNO3dCQUNKLDhCQUE4Qjt3QkFDOUIsTUFBTUMsYUFBYXhHLE9BQU95RyxRQUFRLENBQUNDLElBQUk7d0JBQ3ZDdkosT0FBTytJLElBQUksQ0FBQyxzQ0FBcUUsT0FBL0JDLG1CQUFtQks7b0JBQ3ZFO29CQUNBOUYsVUFBVTt3QkFDUnhELGFBQWF5RCxJQUFJLENBQUM7b0JBQ3BCO2dCQUNGO2dCQUNBO1lBQ0Y7WUFFQTNELFFBQVFDLEdBQUcsQ0FBQztZQUVaLFNBQVM7WUFDVCxNQUFNa0ksaUJBQWlCakksYUFBYWtJLE9BQU8sQ0FBQztZQUU1Qyx5QkFBeUI7WUFDekIsTUFBTXVCLGFBQWE7Z0JBQ2pCdEssWUFBWXFHLE9BQU9yRztnQkFDbkJ1SyxtQkFBbUI7Z0JBQ25CQyx1QkFBdUI7Z0JBQ3ZCQyxxQkFBcUI7Z0JBQ3JCQyxlQUFlLElBQUloTSxPQUFPaU0sV0FBVztnQkFDckNDLFFBQVEsVUFBNkMsT0FBbkMsSUFBSWxNLE9BQU9tTSxjQUFjLENBQUM7WUFDOUM7WUFFQSxNQUFNOUksV0FBVyxNQUFNaEUsc0VBQXVCQSxDQUFDK00sa0JBQWtCLENBQUNSO1lBRWxFLElBQUl2SSxFQUFBQSxpQkFBQUEsU0FBU0csSUFBSSxjQUFiSCxxQ0FBQUEsZUFBZUUsSUFBSSxNQUFLLEtBQUs7Z0JBQy9CLFNBQVM7Z0JBQ1RxRCxnQkFBZ0I7Z0JBQ2hCRyxzQkFBc0I7Z0JBQ3RCNUUsYUFBYW1ILE9BQU8sQ0FBQztnQkFFckIsZUFBZTtnQkFDZixJQUFJMUgsZ0JBQWdCO29CQUNsQkE7Z0JBQ0Y7Z0JBRUEsMEJBQTBCO2dCQUMxQixJQUFJO29CQUNGLHNCQUFzQjtvQkFDdEIsTUFBTSxJQUFJeUssUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztvQkFFakQsZ0JBQWdCO29CQUNoQixNQUFNaEMscUJBQXFCLE1BQU0vSyx3RUFBYUEsQ0FBQ2dMLFVBQVU7b0JBRXpELElBQUlELG1CQUFtQjlHLElBQUksQ0FBQ0QsSUFBSSxLQUFLLEtBQUs7d0JBQ3hDLE1BQU1pSCxjQUFjRixtQkFBbUI5RyxJQUFJLENBQUNBLElBQUk7d0JBQ2hELGlCQUFpQjt3QkFDakIsTUFBTWlILGNBQWNELFlBQVlFLElBQUksQ0FBQyxDQUFDQyxPQUFjQSxLQUFLckosVUFBVSxLQUFLcUcsT0FBT3JHO3dCQUUvRSxJQUFJbUosYUFBYTs0QkFDZiwyQkFBMkI7NEJBQzNCeEYsT0FBTzJGLElBQUksQ0FBQyx1QkFBc0MsT0FBZkgsWUFBWXhFLEVBQUUsR0FBSTt3QkFDdkQsT0FBTzs0QkFDTCwwQkFBMEI7NEJBQzFCaEIsT0FBTzJGLElBQUksQ0FBQyxnQkFBZ0I7d0JBQzlCO29CQUNGLE9BQU87d0JBQ0wsd0JBQXdCO3dCQUN4QjNGLE9BQU8yRixJQUFJLENBQUMsZ0JBQWdCO29CQUM5QjtnQkFDRixFQUFFLE9BQU80QixXQUFXO29CQUNsQnZLLFFBQVFpQyxLQUFLLENBQUMsYUFBYXNJO29CQUMzQiwwQkFBMEI7b0JBQzFCdkgsT0FBTzJGLElBQUksQ0FBQyxnQkFBZ0I7Z0JBQzlCO1lBQ0YsT0FBTztvQkFDV3ZIO2dCQUFoQixNQUFNLElBQUlvSixNQUFNcEosRUFBQUEsa0JBQUFBLFNBQVNHLElBQUksY0FBYkgsc0NBQUFBLGdCQUFla0csT0FBTyxLQUFJO1lBQzVDO1lBRUEsSUFBSWEsZ0JBQWdCO2dCQUNsQkEsZUFBZVMsS0FBSztZQUN0QjtRQUNGLEVBQUUsT0FBTzNHLE9BQU87WUFDZGpDLFFBQVFpQyxLQUFLLENBQUMsU0FBU0E7WUFDdkIvQixhQUFhK0IsS0FBSyxDQUFDQSxpQkFBaUJ1SSxRQUFRdkksTUFBTXFGLE9BQU8sR0FBRztRQUM5RDtJQUNGO0lBRUEsWUFBWTtJQUNaLE1BQU1tRCxzQkFBc0IsT0FBT0M7UUFDakMsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsT0FBTyxFQUFFLEdBQUdIO1FBQ3JDMUYsaUJBQWlCO1FBRWpCLElBQUk7WUFDRixTQUFTO1lBQ1QsTUFBTW9CLE1BQU0sTUFBTTdJLHNEQUFTQSxDQUFDdU4sV0FBVyxDQUFDSDtZQUN4QzNLLFFBQVFDLEdBQUcsQ0FBQyxlQUFlbUc7WUFFM0IsU0FBUztZQUNUbEIsWUFBWTtnQkFBQztvQkFDWCxHQUFJeUYsSUFBSTtvQkFDUnpFLEtBQUt5RSxLQUFLekUsR0FBRztvQkFDYkMsTUFBTXdFLEtBQUt4RSxJQUFJO29CQUNmTixRQUFRO29CQUNSTyxLQUFLQTtnQkFDUDthQUFFO1lBRUYsWUFBWTtZQUNaSyxlQUFlTDtZQUVmLElBQUl3RSxXQUFXO2dCQUNiQSxVQUFVLE1BQU1EO1lBQ2xCO1lBRUF6SyxhQUFhbUgsT0FBTyxDQUFDO1FBQ3ZCLEVBQUUsT0FBTzBELEtBQVU7WUFDakIvSyxRQUFRaUMsS0FBSyxDQUFDLFNBQVM4STtZQUN2QjdLLGFBQWErQixLQUFLLENBQUMsU0FBZ0MsT0FBdkI4SSxJQUFJekQsT0FBTyxJQUFJO1lBQzNDcEMsWUFBWTtnQkFBQztvQkFDWCxHQUFJeUYsSUFBSTtvQkFDUnpFLEtBQUt5RSxLQUFLekUsR0FBRztvQkFDYkMsTUFBTXdFLEtBQUt4RSxJQUFJO29CQUNmTixRQUFRO29CQUNSNUQsT0FBTzhJO2dCQUNUO2FBQUU7WUFFRixJQUFJRixTQUFTO2dCQUNYQSxRQUFRRTtZQUNWO1FBQ0YsU0FBVTtZQUNSL0YsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTWdHLGVBQWUsT0FBT0w7UUFDMUIsSUFBSUEsS0FBS3ZFLEdBQUcsSUFBSXVFLEtBQUt2RSxHQUFHLEtBQUtsSCxvQkFBb0I7WUFDL0MsSUFBSTtnQkFDRixNQUFNM0Isc0RBQVNBLENBQUMwTixhQUFhLENBQUNOLEtBQUt2RSxHQUFHO2dCQUN0Q2xHLGFBQWFtSCxPQUFPLENBQUM7WUFDdkIsRUFBRSxPQUFPcEYsT0FBTztnQkFDZGpDLFFBQVFpQyxLQUFLLENBQUMsZUFBZUE7Z0JBQzdCL0IsYUFBYStCLEtBQUssQ0FBQztZQUNyQjtRQUNGO1FBRUFpRCxZQUFZLEVBQUU7UUFDZHVCLGVBQWU7UUFDZixPQUFPO0lBQ1Q7SUFRQSxhQUFhO0lBQ2IsTUFBTXlFLGNBQWM7UUFDbEIsSUFBSSxJQUFrQixFQUFhO1lBQ2pDLFlBQVk7WUFDWixNQUFNMUIsYUFBYXhHLE9BQU95RyxRQUFRLENBQUNDLElBQUk7WUFFdkMsU0FBUztZQUNUeUIsVUFBVUMsU0FBUyxDQUFDQyxTQUFTLENBQUM3QixZQUMzQjhCLElBQUksQ0FBQztnQkFDSnBMLGFBQWFtSCxPQUFPLENBQUM7WUFDdkIsR0FDQzNFLEtBQUssQ0FBQ3FJLENBQUFBO2dCQUNML0ssUUFBUWlDLEtBQUssQ0FBQyxTQUFTOEk7Z0JBQ3ZCN0ssYUFBYStCLEtBQUssQ0FBQztZQUNyQjtRQUNKO0lBQ0Y7SUFFQSx5QkFBeUI7SUFDekIsSUFBSSxDQUFDb0QsVUFBVTtRQUNiLHFCQUNFLDhEQUFDa0c7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7Ozs7OzhDQUNmLDhEQUFDRDtvQ0FBSUMsV0FBVTs7Ozs7OzhDQUNmLDhEQUFDRDtvQ0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU0zQjtJQUVBLHFCQUNFOzswQkFDRSw4REFBQ0Q7Z0JBQUlDLFdBQVU7O29CQUVWL0QsMkJBQ0MsOERBQUM4RDt3QkFBSUMsV0FBWTtrQ0FDZiw0RUFBQ0M7NEJBQ0NDLFNBQVMsSUFBTXRHLFVBQVUsQ0FBQ0Q7NEJBQzFCcUcsV0FBVTs0QkFDVjdNLE9BQU93RyxTQUFTLFdBQVc7c0NBRTFCQSx1QkFDQyw4REFBQ3dHO2dDQUFJQyxPQUFNO2dDQUE2QkosV0FBVTtnQ0FBVUssU0FBUTtnQ0FBWUMsTUFBSztnQ0FBT0MsUUFBTztnQ0FBZUMsYUFBWTs7a0RBQzVILDhEQUFDQzt3Q0FBS0MsR0FBRTs7Ozs7O2tEQUNSLDhEQUFDQzt3Q0FBS0MsSUFBRzt3Q0FBSUMsSUFBRzt3Q0FBSUMsSUFBRzt3Q0FBS0MsSUFBRzs7Ozs7Ozs7Ozs7MERBR2pDLDhEQUFDWjtnQ0FBSUMsT0FBTTtnQ0FBNkJKLFdBQVU7Z0NBQVVLLFNBQVE7Z0NBQVlDLE1BQUs7Z0NBQU9DLFFBQU87Z0NBQWVDLGFBQVk7O2tEQUM1SCw4REFBQ0M7d0NBQUtDLEdBQUU7Ozs7OztrREFDUiw4REFBQ007d0NBQU9DLElBQUc7d0NBQUtDLElBQUc7d0NBQUtDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBVXBDLDhEQUFDcEI7d0JBQUlDLFdBQVcsK0RBQXdILE9BQXpEckcsU0FBUyxnQkFBZ0I7a0NBQ3JHc0MsMEJBQ0MsOERBQUM4RDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNDO29DQUNDQyxTQUFTOUU7b0NBQ1Q0RSxXQUFVO29DQUNWN00sT0FBTTtvQ0FDTmlPLFVBQVVDOzhDQUVUQSx5QkFBVyw4REFBQ3RRLCtHQUFNQTt3Q0FBQ2lQLFdBQVU7Ozs7O2tFQUE0Qiw4REFBQ25QLCtHQUFJQTt3Q0FBQ21QLFdBQVU7Ozs7Ozs7Ozs7OzhDQUU1RSw4REFBQ0M7b0NBQ0NDLFNBQVNyRjtvQ0FDVG1GLFdBQVU7b0NBQ1Y3TSxPQUFNO29DQUNOaU8sVUFBVUM7OENBRVYsNEVBQUN2USwrR0FBQ0E7d0NBQUNrUCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7O3dDQUlqQmpMLHlCQUNFLDhEQUFDa0w7NEJBQ0NDLFNBQVMsSUFBTS9FLGFBQWE7NEJBQzVCNkUsV0FBVTs0QkFDVjdNLE9BQU07c0NBRU4sNEVBQUN2QywrR0FBSUE7Z0NBQUNvUCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7O2tDQU94Qiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVyx1R0FBNEksT0FBckNyRyxTQUFTLGdCQUFnQjs7Ozs7OzBDQUVoSiw4REFBQ29HO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVcscUZBQTBILE9BQXJDckcsU0FBUyxnQkFBZ0I7Ozs7OztvQ0FDN0hzQywwQkFDQyw4REFBQzhEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDdlAsa0RBQUtBO3dEQUNKNlEsS0FBSzdOO3dEQUNMOE4sS0FBS3BPO3dEQUNMbU4sSUFBSTt3REFDSmtCLFFBQVE7d0RBQ1JDLE9BQU87NERBQ0xDLFdBQVc7d0RBQ2I7d0RBQ0ExQixXQUFVO3dEQUNWWCxTQUFTLENBQUNzQzs0REFDUixNQUFNQyxTQUFTRCxFQUFFQyxNQUFNOzREQUN2QkEsT0FBT0gsS0FBSyxDQUFDSSxVQUFVLEdBQUc7d0RBQzVCOzs7Ozs7Ozs7Ozs4REFHSiw4REFBQzlCO29EQUFJQyxXQUFXLHNHQUErSixPQUF6RHJHLFNBQVMsZ0JBQWdCOzhEQUM3SSw0RUFBQ29HO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQzhCO2dFQUNDQyxNQUFLO2dFQUNML0IsV0FBVTtnRUFDVmdDLE9BQU92TztnRUFDUHdPLFVBQVUsQ0FBQ04sSUFBTTFHLGVBQWUwRyxFQUFFQyxNQUFNLENBQUNJLEtBQUs7Z0VBQzlDRSxhQUFZOzs7Ozs7MEVBRWQsOERBQUM1USxpRkFBU0E7Z0VBQ1I2USxVQUFTO2dFQUNUMUksVUFBVUE7Z0VBQ1YySSxlQUFlbkQ7Z0VBQ2ZvRCxVQUFVN0M7Z0VBQ1Y4QyxVQUFVO2dFQUNWQyxnQkFBZ0I7b0VBQUVDLGlCQUFpQjtvRUFBTUMsZ0JBQWdCO2dFQUFLO2dFQUM5REMsV0FBVyxDQUFDdkQ7b0VBQ1YsSUFBSUEsS0FBS3ZFLEdBQUcsRUFBRTt3RUFDWnBELE9BQU8yRixJQUFJLENBQUNnQyxLQUFLdkUsR0FBRyxFQUFFO29FQUN4QjtnRUFDRjswRUFFQ25CLFNBQVM2QyxNQUFNLElBQUksSUFBSSxxQkFDdEIsOERBQUN5RDs7c0ZBQ0MsOERBQUN2Tyw2RkFBWUE7Ozs7O3NGQUNiLDhEQUFDdU87NEVBQUkwQixPQUFPO2dGQUFFa0IsV0FBVzs0RUFBRTtzRkFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBUzVDLDhEQUFDNUM7d0NBQ0NDLFdBQVU7a0RBRVYsNEVBQUN2UCxrREFBS0E7NENBQ0o2USxLQUFLN047NENBQ0w4TixLQUFLcE87NENBQ0xtTixJQUFJOzRDQUNKa0IsUUFBUTs0Q0FDUkMsT0FBTztnREFDTEMsV0FBVzs0Q0FDYjs0Q0FDQTFCLFdBQVU7NENBQ1ZYLFNBQVMsQ0FBQ3NDO2dEQUNSLE1BQU1DLFNBQVNELEVBQUVDLE1BQU07Z0RBQ3ZCQSxPQUFPSCxLQUFLLENBQUNJLFVBQVUsR0FBRzs0Q0FDNUI7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU9SLDhEQUFDOUI7Z0NBQUlDLFdBQVcsMkdBQW9LLE9BQXpEckcsU0FBUyxnQkFBZ0I7Ozs7Ozs7Ozs7OztrQ0FvRXRKLDhEQUFDb0c7d0JBQUlDLFdBQVcsc0hBQStLLE9BQXpEckcsU0FBUyxnQkFBZ0I7OzBDQUM3Siw4REFBQ29HO2dDQUFJQyxXQUFVOztvQ0FDWi9ELDBCQUNDLDhEQUFDOEQ7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDOEI7Z0RBQ0NDLE1BQUs7Z0RBQ0wvQixXQUFVO2dEQUNWZ0MsT0FBTzdPO2dEQUNQOE8sVUFBVSxDQUFDTixJQUFNN0csU0FBUzZHLEVBQUVDLE1BQU0sQ0FBQ0ksS0FBSztnREFDeENFLGFBQVk7Ozs7OzswREFHZCw4REFBQ0o7Z0RBQ0NDLE1BQUs7Z0RBQ0wvQixXQUFVO2dEQUNWZ0MsT0FBT2pPO2dEQUNQa08sVUFBVSxDQUFDTixJQUFNekcsYUFBYXlHLEVBQUVDLE1BQU0sQ0FBQ0ksS0FBSztnREFDNUNFLGFBQVk7Ozs7Ozs7Ozs7O2tFQUloQjs7MERBQ0UsOERBQUNVO2dEQUFHNUMsV0FBVTs7b0RBQ1g3TTtrRUFDRCw4REFBQ3hDLCtHQUFRQTt3REFBQ3FQLFdBQVU7Ozs7Ozs7Ozs7Ozs0Q0FFckJqTSwyQkFDQyw4REFBQzhPO2dEQUFFN0MsV0FBVTs7b0RBQXdCO29EQUFNak07Ozs7Ozs7OztvQ0FLaERrSSwwQkFDQyw4REFBQzhEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzhCO2dEQUNDQyxNQUFLO2dEQUNML0IsV0FBVTtnREFDVmdDLE9BQU8zTztnREFDUDRPLFVBQVUsQ0FBQ04sSUFBTTVHLGFBQWE0RyxFQUFFQyxNQUFNLENBQUNJLEtBQUs7Ozs7OzswREFFOUMsOERBQUNjO2dEQUFLOUMsV0FBVTswREFBYzs7Ozs7OzBEQUM5Qiw4REFBQzhCO2dEQUNDQyxNQUFLO2dEQUNML0IsV0FBVTtnREFDVmdDLE9BQU96TztnREFDUDBPLFVBQVUsQ0FBQ04sSUFBTTNHLFdBQVcyRyxFQUFFQyxNQUFNLENBQUNJLEtBQUs7Ozs7Ozs7Ozs7O2tFQUk5Qyw4REFBQ2E7d0NBQUU3QyxXQUFVOzswREFDWCw4REFBQzhDO2dEQUFLOUMsV0FBVTs7Ozs7OzRDQUFrRzs0Q0FDNUc1TixXQUFXaUI7NENBQVc7NENBQUlqQixXQUFXbUI7Ozs7Ozs7Ozs7Ozs7MENBS2pELDhEQUFDd007Z0NBQUlDLFdBQVU7O29DQWtCWixDQUFDL0QsMkJBQ0EsOERBQUNnRTt3Q0FDQ0MsU0FBUzdDO3dDQUNUK0QsVUFBVWhJO3dDQUNWNEcsV0FBVyxrRUFHUixPQUgwRTlHLGVBQ3pFLHlJQUNBLHNJQUNEO2tEQUVGRSwyQkFDQzs7OERBQ0UsOERBQUMwSjtvREFBSzlDLFdBQVU7Ozs7OztnREFBOEI7OzJEQUc5QzlHLDZCQUNGO3NEQUFFOzBFQUVGO3NEQUFFOzs7Ozs7O2tEQUtSLDhEQUFDK0c7d0NBQ0NDLFNBQVNSO3dDQUNUTSxXQUFVO2tEQUVWLDRFQUFDdFAsK0dBQU1BOzRDQUFDc1AsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFPMUI1TCxDQUFBQSxrQkFBa0JDLG1CQUFtQkMsaUJBQWlCQyxlQUFjLG1CQUNwRSw4REFBQ3dMO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDNU8sNkRBQW9CQTtvQkFDbkJnRCxnQkFBZ0JBO29CQUNoQkMsaUJBQWlCQTtvQkFDakJDLGVBQWVBO29CQUNmQyxpQkFBaUJBO29CQUNqQndPLGVBQWU1UDs7Ozs7Ozs7Ozs7WUFNcEIwRywwQkFDQyw4REFBQ3BJLGdFQUFXQTtnQkFDVnVSLFFBQVFoSztnQkFDUmlLLFNBQVMsSUFBTWhLLG1CQUFtQjtnQkFDbENtRyxXQUFXdkI7Ozs7Ozs7O0FBS3JCO0dBMW1DTTNLOztRQXVCV2hCLHVEQUFTQTtRQUNQQyx5REFBV0E7UUFVVmpCLHFEQUFXQTtRQUNaQyxxREFBV0E7OztLQW5DeEIrQjtBQTRtQ04sK0RBQWVBLGNBQWNBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2FjdGl2aXR5L2Zlc3RpdmFsL2NvbXBvbmVudHMvQWN0aXZpdHlIZWFkZXIudHN4PzgwYzEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xyXG5pbXBvcnQgSW1hZ2UgZnJvbSAnbmV4dC9pbWFnZSdcclxuaW1wb3J0IHsgU2hhcmUyLCBTcGFya2xlcywgRWRpdCwgU2F2ZSwgWCwgTG9hZGVyIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xyXG5pbXBvcnQgeyBzZWxlY3RVc2VyU3RhdGUsIHNldFVzZXIgfSBmcm9tICdAL2xpYi9zdG9yZSdcclxuaW1wb3J0IHsgdXNlU2VsZWN0b3IsIHVzZURpc3BhdGNoIH0gZnJvbSAncmVhY3QtcmVkdXgnXHJcblxyXG5pbXBvcnQgQWN0aXZpdHlNZWRpYUdhbGxlcnkgZnJvbSAnLi9BY3Rpdml0eU1lZGlhR2FsbGVyeSdcclxuaW1wb3J0IHsgVXBsb2FkIGFzIEFudFVwbG9hZCwgTW9kYWwgfSBmcm9tICdhbnRkJ1xyXG5pbXBvcnQgeyBQbHVzT3V0bGluZWQgfSBmcm9tICdAYW50LWRlc2lnbi9pY29ucydcclxuaW1wb3J0IExvZ2luRGlhbG9nIGZyb20gJy4uLy4uLy4uLy4uL2NvbXBvbmVudHMvbG9naW4tZGlhbG9nJ1xyXG5pbXBvcnQgeyBhY3Rpdml0eUFwaSwgYWN0aXZpdHlUYWdBcGksIGFjdGl2aXR5UmVnaXN0cmF0aW9uQXBpLCBBY3Rpdml0eVR5cGUgfSBmcm9tICdAL2xpYi9hcGkvYWN0aXZpdHknXHJcbmltcG9ydCB7IGV2ZW50c1Rhc2tBcGkgfSBmcm9tICdAL2xpYi9hcGkvYWN0aXZpdHkvZXZlbnRzLXRhc2snXHJcbmltcG9ydCB7IHVwbG9hZEFwaSB9IGZyb20gJy4uLy4uLy4uLy4uL2xpYi9hcGkvdXBsb2FkJ1xyXG5pbXBvcnQgeyB1c2VyQXBpIH0gZnJvbSAnQC9saWIvYXBpL3VzZXInXHJcbmltcG9ydCB7IFJjRmlsZSwgVXBsb2FkRmlsZSB9IGZyb20gJ2FudGQvZXMvdXBsb2FkL2ludGVyZmFjZSdcclxuaW1wb3J0IHsgR2V0Tm90aWZpY2F0aW9uIH0gZnJvbSAnbG9naWMtY29tbW9uL2Rpc3QvY29tcG9uZW50cy9Ob3RpZmljYXRpb24nXHJcbmltcG9ydCB7IHVzZVJvdXRlciwgdXNlUGF0aG5hbWUgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXHJcblxyXG4vLyDkuLTml7blrp7njrBmb3JtYXREYXRl5Ye95pWw77yM5Lul6Kej5Yaz5qih5Z2X5a+85YWl6Zeu6aKYXHJcbmNvbnN0IGZvcm1hdERhdGUgPSAoZGF0ZVN0cmluZzogc3RyaW5nKTogc3RyaW5nID0+IHtcclxuICAvLyDlnKjmnI3liqHnq6/muLLmn5Pml7bvvIznm7TmjqXov5Tlm57ljp/lp4vlrZfnrKbkuLLvvIzpgb/lhY3msLTlkIjplJnor69cclxuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgIHJldHVybiBkYXRlU3RyaW5nO1xyXG4gIH1cclxuXHJcbiAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGRhdGVTdHJpbmcpO1xyXG5cclxuICAvLyDmo4Dmn6Xml6XmnJ/mmK/lkKbmnInmlYhcclxuICBpZiAoaXNOYU4oZGF0ZS5nZXRUaW1lKCkpKSB7XHJcbiAgICByZXR1cm4gZGF0ZVN0cmluZzsgLy8g5aaC5p6c5peg5pWI77yM6L+U5Zue5Y6f5aeL5a2X56ym5LiyXHJcbiAgfVxyXG5cclxuICBjb25zdCB5ZWFyID0gZGF0ZS5nZXRGdWxsWWVhcigpO1xyXG4gIGNvbnN0IG1vbnRoID0gU3RyaW5nKGRhdGUuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJyk7XHJcbiAgY29uc3QgZGF5ID0gU3RyaW5nKGRhdGUuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpO1xyXG5cclxuICByZXR1cm4gYCR7eWVhcn0tJHttb250aH0tJHtkYXl9YDtcclxufTtcclxuXHJcbmludGVyZmFjZSBBY3Rpdml0eUhlYWRlclByb3BzIHtcclxuICB0aXRsZTogc3RyaW5nXHJcbiAgc3RhcnRUaW1lOiBzdHJpbmdcclxuICBlbmRUaW1lOiBzdHJpbmdcclxuICBiYW5uZXJJbWFnZTogc3RyaW5nXHJcbiAgZXhwYW5kZWQ6IGJvb2xlYW5cclxuICBzZXRFeHBhbmRlZDogUmVhY3QuRGlzcGF0Y2g8UmVhY3QuU2V0U3RhdGVBY3Rpb248Ym9vbGVhbj4+XHJcbiAgYWN0aXZpdHlJZDogbnVtYmVyIHwgc3RyaW5nXHJcbiAgdGFncz86IEFycmF5PHsgaWQ6IG51bWJlciwgbmFtZTogc3RyaW5nLCBjb2xvcj86IHN0cmluZyB9PlxyXG4gIG9yZ2FuaXplcj86IHN0cmluZ1xyXG5cclxuICBhY3Rpdml0eVR5cGU/OiBudW1iZXIgLy8g5re75Yqg5rS75Yqo57G75Z6L5bGe5oCnXHJcbiAgb25SZWZyZXNoV29ya3M/OiAoKSA9PiB2b2lkXHJcbiAgLy8g5paw5aKe5aqS5L2T5a2X5q61XHJcbiAgcHJvbW90aW9uSW1hZ2U/OiBzdHJpbmdcclxuICBiYWNrZ3JvdW5kSW1hZ2U/OiBzdHJpbmdcclxuICBnYWxsZXJ5SW1hZ2VzPzogc3RyaW5nXHJcbiAgYXR0YWNobWVudEZpbGVzPzogc3RyaW5nXHJcbn1cclxuXHJcbi8vIOagh+etvuaOpeWPo+WumuS5iVxyXG5pbnRlcmZhY2UgTmF2VGFnIHtcclxuICBpZDogc3RyaW5nO1xyXG4gIHRleHQ6IHN0cmluZztcclxufVxyXG5cclxuaW50ZXJmYWNlIEN1c3RvbVVwbG9hZEZpbGUgZXh0ZW5kcyBVcGxvYWRGaWxlIHtcclxuICBlcnJvcj86IGFueTtcclxufVxyXG5cclxuY29uc3QgQWN0aXZpdHlIZWFkZXI6IFJlYWN0LkZDPEFjdGl2aXR5SGVhZGVyUHJvcHM+ID0gKHtcclxuICB0aXRsZTogaW5pdGlhbFRpdGxlLFxyXG4gIHN0YXJ0VGltZTogaW5pdGlhbFN0YXJ0VGltZSxcclxuICBlbmRUaW1lOiBpbml0aWFsRW5kVGltZSxcclxuICBiYW5uZXJJbWFnZTogaW5pdGlhbEJhbm5lckltYWdlLFxyXG4gIGV4cGFuZGVkLFxyXG4gIHNldEV4cGFuZGVkLFxyXG4gIGFjdGl2aXR5SWQsXHJcbiAgdGFncyA9IFtdLFxyXG4gIG9yZ2FuaXplcjogaW5pdGlhbE9yZ2FuaXplciA9ICcnLFxyXG5cclxuICBhY3Rpdml0eVR5cGUgPSBBY3Rpdml0eVR5cGUuV09SSywgLy8g6buY6K6k5Li65L2c5ZOB5rS75YqoXHJcbiAgb25SZWZyZXNoV29ya3MsXHJcbiAgLy8g5aqS5L2T5a2X5q61XHJcbiAgcHJvbW90aW9uSW1hZ2UsXHJcbiAgYmFja2dyb3VuZEltYWdlLFxyXG4gIGdhbGxlcnlJbWFnZXMsXHJcbiAgYXR0YWNobWVudEZpbGVzXHJcbn0pID0+IHtcclxuICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIEFjdGl2aXR5SGVhZGVyIOe7hOS7tua4suafkycsIHsgYWN0aXZpdHlJZCwgaW5pdGlhbFRpdGxlIH0pO1xyXG5cclxuXHJcbiAgY29uc3Qgbm90aWZpY2F0aW9uID0gR2V0Tm90aWZpY2F0aW9uKCk7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpO1xyXG5cclxuICAvLyDmmL7npLrnlKjnmoTmoIfpopjlkozml7bpl7RcclxuICBjb25zdCB0aXRsZSA9IGluaXRpYWxUaXRsZTtcclxuICBjb25zdCBzdGFydFRpbWUgPSBpbml0aWFsU3RhcnRUaW1lO1xyXG4gIGNvbnN0IGVuZFRpbWUgPSBpbml0aWFsRW5kVGltZTtcclxuICBjb25zdCBiYW5uZXJJbWFnZSA9IGluaXRpYWxCYW5uZXJJbWFnZTtcclxuICBjb25zdCBvcmdhbml6ZXIgPSBpbml0aWFsT3JnYW5pemVyO1xyXG5cclxuICAvLyDojrflj5blvZPliY3nlKjmiLfkv6Hmga/lkoxkaXNwYXRjaFxyXG4gIGNvbnN0IHVzZXJTdGF0ZSA9IHVzZVNlbGVjdG9yKHNlbGVjdFVzZXJTdGF0ZSlcclxuICBjb25zdCBkaXNwYXRjaCA9IHVzZURpc3BhdGNoKClcclxuICBjb25zdCBpc0FkbWluID0gdXNlclN0YXRlPy5yb2xlSWQgPT09IDRcclxuXHJcbiAgLy8g5ZCM5q2l55So5oi354q25oCB55qE5Ye95pWwIC0g5aKe5by654mI77yM5LuOQVBJ6I635Y+W5pyA5paw55So5oi35L+h5oGvXHJcbiAgY29uc3Qgc3luY1VzZXJTdGF0ZSA9IGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10g5byA5aeL5ZCM5q2l55So5oi354q25oCBJyk7XHJcbiAgICAgIGNvbnN0IGxvY2FsVXNlciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VyJyk7XHJcbiAgICAgIGlmIChsb2NhbFVzZXIpIHtcclxuICAgICAgICBjb25zdCB1c2VyRGF0YSA9IEpTT04ucGFyc2UobG9jYWxVc2VyKTtcclxuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIGxvY2FsU3RvcmFnZSDkuK3nmoTnlKjmiLfmlbDmja4nLCB1c2VyRGF0YSk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSBSZWR1eCDkuK3nmoTnlKjmiLfmlbDmja4nLCB1c2VyU3RhdGUpO1xyXG5cclxuICAgICAgICAvLyDlpoLmnpznlKjmiLflt7LnmbvlvZXkvYZwaG9uZeS4uuepuu+8jOWwneivleS7jkFQSeiOt+WPluacgOaWsOeUqOaIt+S/oeaBr1xyXG4gICAgICAgIGlmICh1c2VyRGF0YS5pc0xvZ2dlZEluICYmIHVzZXJEYXRhLnVzZXJJZCAmJiAoIXVzZXJEYXRhLnBob25lIHx8IHVzZXJEYXRhLnBob25lLnRyaW0oKSA9PT0gJycpKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIOajgOa1i+WIsOeUqOaIt3Bob25l5Li656m677yM5LuOQVBJ6I635Y+W5pyA5paw55So5oi35L+h5oGvJyk7XHJcbiAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHVzZXJBcGkuZ2V0VXNlckluZm8odXNlckRhdGEudXNlcklkKTtcclxuICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCAmJiByZXNwb25zZS5kYXRhKSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSDku45BUEnojrflj5bliLDmnIDmlrDnlKjmiLfkv6Hmga8nLCByZXNwb25zZS5kYXRhKTtcclxuXHJcbiAgICAgICAgICAgICAgLy8g5p6E5bu65pu05paw5ZCO55qE55So5oi35pWw5o2uXHJcbiAgICAgICAgICAgICAgY29uc3QgdXBkYXRlZFVzZXJEYXRhID0ge1xyXG4gICAgICAgICAgICAgICAgLi4udXNlckRhdGEsXHJcbiAgICAgICAgICAgICAgICBwaG9uZTogcmVzcG9uc2UuZGF0YS5waG9uZSB8fCAnJyxcclxuICAgICAgICAgICAgICAgIG5pY2tOYW1lOiByZXNwb25zZS5kYXRhLm5pY2tOYW1lIHx8IHVzZXJEYXRhLm5pY2tOYW1lLFxyXG4gICAgICAgICAgICAgICAgYXZhdGFyVXJsOiByZXNwb25zZS5kYXRhLmF2YXRhclVybCB8fCB1c2VyRGF0YS5hdmF0YXJVcmwsXHJcbiAgICAgICAgICAgICAgICBnZW5kZXI6IHJlc3BvbnNlLmRhdGEuZ2VuZGVyIHx8IHVzZXJEYXRhLmdlbmRlcixcclxuICAgICAgICAgICAgICAgIHJvbGVJZDogcmVzcG9uc2UuZGF0YS5yb2xlSWQgfHwgdXNlckRhdGEucm9sZUlkXHJcbiAgICAgICAgICAgICAgfTtcclxuXHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSDmm7TmlrDnlKjmiLfmlbDmja4nLCB7XHJcbiAgICAgICAgICAgICAgICBvbGRQaG9uZTogdXNlckRhdGEucGhvbmUsXHJcbiAgICAgICAgICAgICAgICBuZXdQaG9uZTogdXBkYXRlZFVzZXJEYXRhLnBob25lLFxyXG4gICAgICAgICAgICAgICAgdXBkYXRlZDogdXBkYXRlZFVzZXJEYXRhXHJcbiAgICAgICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgICAgIC8vIOabtOaWsGxvY2FsU3RvcmFnZeWSjFJlZHV4XHJcbiAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3VzZXInLCBKU09OLnN0cmluZ2lmeSh1cGRhdGVkVXNlckRhdGEpKTtcclxuICAgICAgICAgICAgICBkaXNwYXRjaChzZXRVc2VyKHVwZGF0ZWRVc2VyRGF0YSkpO1xyXG4gICAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcign8J+UjSBbREVCVUddIOS7jkFQSeiOt+WPlueUqOaIt+S/oeaBr+Wksei0pTonLCBlcnJvcik7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyDmo4Dmn6XlhbPplK7lrZfmrrXmmK/lkKbmnInlj5jljJZcclxuICAgICAgICBjb25zdCBoYXNQaG9uZUNoYW5nZWQgPSB1c2VyRGF0YS5waG9uZSAhPT0gdXNlclN0YXRlPy5waG9uZTtcclxuICAgICAgICBjb25zdCBoYXNSb2xlQ2hhbmdlZCA9IHVzZXJEYXRhLnJvbGVJZCAhPT0gdXNlclN0YXRlPy5yb2xlSWQ7XHJcbiAgICAgICAgY29uc3QgaGFzTG9naW5TdGF0dXNDaGFuZ2VkID0gdXNlckRhdGEuaXNMb2dnZWRJbiAhPT0gdXNlclN0YXRlPy5pc0xvZ2dlZEluO1xyXG5cclxuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIOeUqOaIt+eKtuaAgeWPmOWMluajgOafpScsIHtcclxuICAgICAgICAgIHBob25lQ2hhbmdlZDogaGFzUGhvbmVDaGFuZ2VkLFxyXG4gICAgICAgICAgcm9sZUNoYW5nZWQ6IGhhc1JvbGVDaGFuZ2VkLFxyXG4gICAgICAgICAgbG9naW5TdGF0dXNDaGFuZ2VkOiBoYXNMb2dpblN0YXR1c0NoYW5nZWQsXHJcbiAgICAgICAgICBvbGRQaG9uZTogdXNlclN0YXRlPy5waG9uZSxcclxuICAgICAgICAgIG5ld1Bob25lOiB1c2VyRGF0YS5waG9uZSxcclxuICAgICAgICAgIG9sZFJvbGU6IHVzZXJTdGF0ZT8ucm9sZUlkLFxyXG4gICAgICAgICAgbmV3Um9sZTogdXNlckRhdGEucm9sZUlkXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIC8vIOWmguaenGxvY2FsU3RvcmFnZeS4reeahOeUqOaIt+eKtuaAgeS4jlJlZHV45Lit55qE5LiN5LiA6Ie077yM6K+05piO6ZyA6KaB5pu05pawXHJcbiAgICAgICAgaWYgKGhhc1Bob25lQ2hhbmdlZCB8fCBoYXNSb2xlQ2hhbmdlZCB8fCBoYXNMb2dpblN0YXR1c0NoYW5nZWQpIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10g5qOA5rWL5Yiw55So5oi354q25oCB5Y+Y5YyW77yM5ZCM5q2lUmVkdXjnirbmgIEnKTtcclxuICAgICAgICAgIGRpc3BhdGNoKHNldFVzZXIodXNlckRhdGEpKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSDnlKjmiLfnirbmgIHml6Dlj5jljJbvvIzml6DpnIDlkIzmraUnKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSBsb2NhbFN0b3JhZ2Ug5Lit5peg55So5oi35pWw5o2uJyk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ/CflI0gW0RFQlVHXSDmo4Dmn6XnlKjmiLfnirbmgIHlpLHotKU6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOebkeWQrOmhtemdoueEpueCueWPmOWMluWSjOi3r+eUseWPmOWMlu+8jOmHjeaWsOajgOafpeeUqOaIt+eKtuaAgVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAvLyDpobXpnaLliqDovb3ml7bnq4vljbPmo4Dmn6XkuIDmrKFcclxuICAgIHN5bmNVc2VyU3RhdGUoKS5jYXRjaChjb25zb2xlLmVycm9yKTtcclxuXHJcbiAgICBjb25zdCBoYW5kbGVWaXNpYmlsaXR5Q2hhbmdlID0gKCkgPT4ge1xyXG4gICAgICBpZiAoZG9jdW1lbnQudmlzaWJpbGl0eVN0YXRlID09PSAndmlzaWJsZScpIHtcclxuICAgICAgICBzeW5jVXNlclN0YXRlKCkuY2F0Y2goY29uc29sZS5lcnJvcik7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgY29uc3QgaGFuZGxlRm9jdXMgPSAoKSA9PiB7XHJcbiAgICAgIHN5bmNVc2VyU3RhdGUoKS5jYXRjaChjb25zb2xlLmVycm9yKTtcclxuICAgIH07XHJcblxyXG4gICAgLy8g5re75Yqg5aSa56eN5LqL5Lu255uR5ZCs56Gu5L+d54q25oCB5ZCM5q2lXHJcbiAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCd2aXNpYmlsaXR5Y2hhbmdlJywgaGFuZGxlVmlzaWJpbGl0eUNoYW5nZSk7XHJcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignZm9jdXMnLCBoYW5kbGVGb2N1cyk7XHJcblxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcigndmlzaWJpbGl0eWNoYW5nZScsIGhhbmRsZVZpc2liaWxpdHlDaGFuZ2UpO1xyXG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignZm9jdXMnLCBoYW5kbGVGb2N1cyk7XHJcbiAgICB9O1xyXG4gIH0sIFt1c2VyU3RhdGU/LnBob25lLCBkaXNwYXRjaF0pO1xyXG5cclxuICAvLyDlt6Xlhbflh73mlbDvvJrmo4Dmn6XnlKjmiLfmmK/lkKbkuLrlrabnlJ/ouqvku71cclxuICBjb25zdCBjaGVja0lzU3R1ZGVudCA9ICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHVzZXJTdHIgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndXNlcicpO1xyXG4gICAgICBjb25zdCBsb2NhbFVzZXIgPSB1c2VyU3RyID8gSlNPTi5wYXJzZSh1c2VyU3RyKSA6IG51bGw7XHJcbiAgICAgIGNvbnN0IHVzZXJSb2xlSWQgPSBsb2NhbFVzZXI/LnJvbGVJZCB8fCB1c2VyU3RhdGU/LnJvbGVJZDtcclxuICAgICAgcmV0dXJuIHVzZXJSb2xlSWQgPT09IDE7IC8vIOWtpueUn+i6q+S7veeahHJvbGVJZOS4ujFcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekGxvY2FsU3RvcmFnZeS4reeahOeUqOaIt+S/oeaBr+Wksei0pTonLCBlcnJvcik7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuXHJcblxyXG4gIC8vIOW3peWFt+WHveaVsO+8muaYvuekuui6q+S7vemqjOivgeWksei0peaPkOekulxyXG4gIGNvbnN0IHNob3dTdHVkZW50T25seU1vZGFsID0gKCkgPT4ge1xyXG4gICAgTW9kYWwuY29uZmlybSh7XHJcbiAgICAgIHRpdGxlOiAn6Lqr5Lu96aqM6K+BJyxcclxuICAgICAgY29udGVudDogJ+aKseatie+8jOWPquacieWtpueUn+i6q+S7veeahOeUqOaIt+aJjeiDveWPguWKoOatpOa0u+WKqOaKpeWQjeOAguWmguaenOaCqOaYr+WtpueUn++8jOivt+iBlOezu+euoeeQhuWRmOabtOaWsOaCqOeahOi6q+S7veS/oeaBr+OAgicsXHJcbiAgICAgIG9rVGV4dDogJ+aIkeefpemBk+S6hicsXHJcbiAgICAgIGNhbmNlbFRleHQ6ICfogZTns7vnrqHnkIblkZgnLFxyXG4gICAgICBvbkNhbmNlbDogKCkgPT4ge1xyXG4gICAgICAgIG5vdGlmaWNhdGlvbi5pbmZvKCflpoLpnIDluK7liqnvvIzor7fogZTns7vlrqLmnI3vvJo0MDAtMTIzLTQ1NjcnKTtcclxuICAgICAgfVxyXG4gICAgfSk7XHJcbiAgfTtcclxuXHJcbiAgLy8g6I635Y+W5oql5ZCN54q25oCB5paH5pysXHJcbiAgY29uc3QgZ2V0UmVnaXN0cmF0aW9uU3RhdHVzVGV4dCA9ICgpID0+IHtcclxuICAgIHN3aXRjaCAocmVnaXN0cmF0aW9uU3RhdHVzKSB7XHJcbiAgICAgIGNhc2UgMDpcclxuICAgICAgICByZXR1cm4gJ+W3suWPlua2iCc7XHJcbiAgICAgIGNhc2UgMTpcclxuICAgICAgICByZXR1cm4gJ+W3suaKpeWQjSc7XHJcbiAgICAgIGNhc2UgMjpcclxuICAgICAgICByZXR1cm4gJ+W3suWuoeaguOmAmui/hyc7XHJcbiAgICAgIGNhc2UgMzpcclxuICAgICAgICByZXR1cm4gJ+W3suaLkue7nSc7XHJcbiAgICAgIGNhc2UgNDpcclxuICAgICAgICByZXR1cm4gJ+ivhOWuoeS4rSc7XHJcbiAgICAgIGNhc2UgNTpcclxuICAgICAgICByZXR1cm4gJ+W3suiOt+Wllic7XHJcbiAgICAgIGNhc2UgNjpcclxuICAgICAgICByZXR1cm4gJ+WuoeaguOS4rSc7XHJcbiAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgcmV0dXJuICflt7LmiqXlkI0nO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOWPr+e8lui+keeahOWvvOiIquagh+etvuaWh+acrFxyXG4gIGNvbnN0IFtuYXZUYWdzLCBzZXROYXZUYWdzXSA9IHVzZVN0YXRlPE5hdlRhZ1tdPihbXHJcbiAgICB7IGlkOiAndGFnMScsIHRleHQ6IFwi5a6Y5pa55rS75YqoXCIgfSxcclxuICAgIHsgaWQ6ICd0YWcyJywgdGV4dDogXCJBSUdD5L2c5ZOB5b6B6ZuGXCIgfVxyXG4gIF0pO1xyXG5cclxuICAvLyDmtLvliqjmoIfnrb5JROWIl+ihqFxyXG4gIGNvbnN0IFt0YWdJZHMsIHNldFRhZ0lkc10gPSB1c2VTdGF0ZTxudW1iZXJbXT4oW10pO1xyXG5cclxuICAvLyDmoIfnrb7nvJbovpHnirbmgIFcclxuICBjb25zdCBbZWRpdGluZ1RhZ0lkLCBzZXRFZGl0aW5nVGFnSWRdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2VkaXRpbmdUYWdUZXh0LCBzZXRFZGl0aW5nVGFnVGV4dF0gPSB1c2VTdGF0ZSgnJyk7XHJcblxyXG4gIC8vIOeZu+W9leWSjOaKpeWQjeebuOWFs+eKtuaAgVxyXG4gIGNvbnN0IFtzaG93TG9naW5EaWFsb2csIHNldFNob3dMb2dpbkRpYWxvZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2hhc1N1Ym1pdHRlZCwgc2V0SGFzU3VibWl0dGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNDaGVja2luZywgc2V0SXNDaGVja2luZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3JlZ2lzdHJhdGlvblN0YXR1cywgc2V0UmVnaXN0cmF0aW9uU3RhdHVzXSA9IHVzZVN0YXRlPG51bWJlciB8IG51bGw+KG51bGwpOyAvLyDmiqXlkI3nirbmgIHvvJowLeW3suWPlua2iCAxLeW3suaKpeWQjSAyLeW3suWuoeaguOmAmui/hyAzLeW3suaLkue7nSA0LeivhOWuoeS4rSA1LeW3suiOt+WlliA2LeWuoeaguOS4rVxyXG5cclxuICBjb25zdCBbdXBsb2FkTG9hZGluZywgc2V0VXBsb2FkTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2ZpbGVMaXN0LCBzZXRGaWxlTGlzdF0gPSB1c2VTdGF0ZTxDdXN0b21VcGxvYWRGaWxlW10+KFtdKTtcclxuXHJcblxyXG5cclxuICBjb25zdCBbc2hvd1VJLCBzZXRTaG93VUldID0gdXNlU3RhdGUodHJ1ZSk7XHJcblxyXG4gIC8vIOa3u+WKoOWuouaIt+err+ajgOa1i+adpeino+WGs+awtOWQiOmUmeivr1xyXG4gIGNvbnN0IFtpc0NsaWVudCwgc2V0SXNDbGllbnRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgc2V0SXNDbGllbnQodHJ1ZSk7XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyDpobXpnaLliqDovb3ml7bmo4Dmn6XmiqXlkI3nirbmgIFcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgY2hlY2tSZWdpc3RyYXRpb25TdGF0dXMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10g5qOA5p+l5oql5ZCN54q25oCBIC0gdXNlRWZmZWN0IOinpuWPkScsIHtcclxuICAgICAgICBpc0xvZ2dlZEluOiB1c2VyU3RhdGUuaXNMb2dnZWRJbixcclxuICAgICAgICByb2xlSWQ6IHVzZXJTdGF0ZS5yb2xlSWQsXHJcbiAgICAgICAgcGhvbmU6IHVzZXJTdGF0ZS5waG9uZSxcclxuICAgICAgICBpc0NsaWVudCxcclxuICAgICAgICBhY3Rpdml0eUlkXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgLy8g5Y+q5pyJ5Zyo55So5oi35bey55m75b2V5LiU5piv5a2m55Sf6Lqr5Lu95pe25omN5qOA5p+l5oql5ZCN54q25oCBXHJcbiAgICAgIGNvbnN0IGlzU3R1ZGVudCA9IGNoZWNrSXNTdHVkZW50KCk7XHJcbiAgICAgIGlmICghdXNlclN0YXRlLmlzTG9nZ2VkSW4gfHwgIWlzU3R1ZGVudCkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10g55So5oi35pyq55m75b2V5oiW5LiN5piv5a2m55Sf6Lqr5Lu977yM6YeN572u5oql5ZCN54q25oCBJywge1xyXG4gICAgICAgICAgaXNMb2dnZWRJbjogdXNlclN0YXRlLmlzTG9nZ2VkSW4sXHJcbiAgICAgICAgICBpc1N0dWRlbnRcclxuICAgICAgICB9KTtcclxuICAgICAgICAvLyDlpoLmnpznlKjmiLfmnKrnmbvlvZXmiJbkuI3mmK/lrabnlJ/vvIzph43nva7nirbmgIFcclxuICAgICAgICBzZXRIYXNTdWJtaXR0ZWQoZmFsc2UpO1xyXG4gICAgICAgIHNldFJlZ2lzdHJhdGlvblN0YXR1cyhudWxsKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSDlvIDlp4vmo4Dmn6XmiqXlkI3nirbmgIEgQVBJIOiwg+eUqCcpO1xyXG4gICAgICAgIHNldElzQ2hlY2tpbmcodHJ1ZSk7XHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhY3Rpdml0eVJlZ2lzdHJhdGlvbkFwaS5jaGVja1JlZ2lzdHJhdGlvbihOdW1iZXIoYWN0aXZpdHlJZCkpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10g6aG16Z2i5Yqg6L295qOA5p+l5oql5ZCN54q25oCB5ZON5bqUOicsIHJlc3BvbnNlKTtcclxuXHJcbiAgICAgICAgaWYgKHJlc3BvbnNlPy5kYXRhPy5jb2RlID09PSAyMDAgJiYgcmVzcG9uc2UuZGF0YT8uZGF0YSkge1xyXG4gICAgICAgICAgY29uc3QgeyBzdWJtaXR0ZWQsIHN1Ym1pdCB9ID0gcmVzcG9uc2UuZGF0YS5kYXRhO1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSDmiqXlkI3nirbmgIHmo4Dmn6Xnu5PmnpwnLCB7IHN1Ym1pdHRlZCwgc3VibWl0IH0pO1xyXG4gICAgICAgICAgc2V0SGFzU3VibWl0dGVkKHN1Ym1pdHRlZCk7XHJcblxyXG4gICAgICAgICAgaWYgKHN1Ym1pdCAmJiBzdWJtaXQuc3RhdHVzICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgc2V0UmVnaXN0cmF0aW9uU3RhdHVzKHN1Ym1pdC5zdGF0dXMpO1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgc2V0UmVnaXN0cmF0aW9uU3RhdHVzKG51bGwpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIOaKpeWQjeeKtuaAgeWTjeW6lOS4jeespuWQiOmihOacn++8jOiuvue9ruS4uuacquaKpeWQjeeKtuaAgScpO1xyXG4gICAgICAgICAgLy8g5aaC5p6c5ZON5bqU5LiN56ym5ZCI6aKE5pyf77yM6K6+572u5Li65pyq5oql5ZCN54q25oCBXHJcbiAgICAgICAgICBzZXRIYXNTdWJtaXR0ZWQoZmFsc2UpO1xyXG4gICAgICAgICAgc2V0UmVnaXN0cmF0aW9uU3RhdHVzKG51bGwpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCfwn5SNIFtERUJVR10g5qOA5p+l5oql5ZCN54q25oCB5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgICAvLyDlh7rplJnml7borr7nva7kuLrmnKrmiqXlkI3nirbmgIFcclxuICAgICAgICBzZXRIYXNTdWJtaXR0ZWQoZmFsc2UpO1xyXG4gICAgICAgIHNldFJlZ2lzdHJhdGlvblN0YXR1cyhudWxsKTtcclxuICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgICBzZXRJc0NoZWNraW5nKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICAvLyDlj6rlnKjlrqLmiLfnq6/miafooYzvvIzpgb/lhY3mnI3liqHnq6/muLLmn5Ppl67pophcclxuICAgIGlmIChpc0NsaWVudCkge1xyXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIOWuouaIt+err+eOr+Wig++8jOaJp+ihjOaKpeWQjeeKtuaAgeajgOafpScpO1xyXG4gICAgICBjaGVja1JlZ2lzdHJhdGlvblN0YXR1cygpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSDpnZ7lrqLmiLfnq6/njq/looPvvIzot7Pov4fmiqXlkI3nirbmgIHmo4Dmn6UnKTtcclxuICAgIH1cclxuICB9LCBbYWN0aXZpdHlJZCwgdXNlclN0YXRlLmlzTG9nZ2VkSW4sIHVzZXJTdGF0ZS5yb2xlSWQsIHVzZXJTdGF0ZS5waG9uZSwgaXNDbGllbnRdKTtcclxuXHJcblxyXG5cclxuXHJcblxyXG4gIC8vIOajgOafpeeUqOaIt+aYr+WQpuW3suaPkOS6pOS9nOWTgVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBjaGVja1VzZXJTdWJtaXR0ZWQgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgLy8g5LuF6ZKI5a+55bey55m75b2V55So5oi36L+b6KGM5qOA5p+lXHJcbiAgICAgICAgaWYgKHVzZXJTdGF0ZSAmJiB1c2VyU3RhdGUuaXNMb2dnZWRJbikge1xyXG4gICAgICAgICAgc2V0SXNDaGVja2luZyh0cnVlKTtcclxuICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYWN0aXZpdHlBcGkuY2hlY2tVc2VyU3VibWl0dGVkKE51bWJlcihhY3Rpdml0eUlkKSk7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygn6aG16Z2i5Yqg6L295pe25qOA5p+l55So5oi35o+Q5Lqk54q25oCB5ZON5bqUOicsIHJlc3BvbnNlKTtcclxuICAgICAgICAgIGlmIChyZXNwb25zZT8uc3RhdHVzID09PSAyMDAgJiYgcmVzcG9uc2UuZGF0YT8uZGF0YSkge1xyXG4gICAgICAgICAgICBzZXRIYXNTdWJtaXR0ZWQocmVzcG9uc2UuZGF0YS5kYXRhLnN1Ym1pdHRlZCk7XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAvLyDlpoLmnpzlk43lupTkuI3nrKblkIjpooTmnJ/vvIzpu5jorqTkuLrmnKrmj5DkuqRcclxuICAgICAgICAgICAgc2V0SGFzU3VibWl0dGVkKGZhbHNlKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIHNldElzQ2hlY2tpbmcoZmFsc2UpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAvLyDmnKrnmbvlvZXnlKjmiLfvvIznm7TmjqXorr7nva7kuLrmnKrmj5DkuqRcclxuICAgICAgICAgIHNldEhhc1N1Ym1pdHRlZChmYWxzZSk7XHJcbiAgICAgICAgICBzZXRJc0NoZWNraW5nKGZhbHNlKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcign5qOA5p+l55So5oi35o+Q5Lqk54q25oCB5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgICAvLyDlh7rplJnml7bpu5jorqTorr7nva7kuLrmnKrmj5DkuqRcclxuICAgICAgICBzZXRIYXNTdWJtaXR0ZWQoZmFsc2UpO1xyXG4gICAgICAgIHNldElzQ2hlY2tpbmcoZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIC8vIOmhtemdouWKoOi9veaXtuaJp+ihjOajgOafpVxyXG4gICAgaWYgKGlzQ2xpZW50KSB7XHJcbiAgICAgIGNoZWNrVXNlclN1Ym1pdHRlZCgpO1xyXG4gICAgfVxyXG4gIH0sIFthY3Rpdml0eUlkLCB1c2VyU3RhdGUsIGlzQ2xpZW50XSk7XHJcblxyXG4gIC8vIOWKoOi9vea0u+WKqOagh+etvlxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBmZXRjaEFjdGl2aXR5VGFncyA9IGFzeW5jICgpID0+IHtcclxuICAgICAgaWYgKGFjdGl2aXR5SWQpIHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhY3Rpdml0eVRhZ0FwaS5nZXRBY3Rpdml0eVRhZ3MoTnVtYmVyKGFjdGl2aXR5SWQpKTtcclxuICAgICAgICAgIGlmIChyZXNwb25zZT8uc3RhdHVzID09PSAyMDApIHtcclxuICAgICAgICAgICAgc2V0VGFnSWRzKHJlc3BvbnNlLmRhdGEuZGF0YSB8fCBbXSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlua0u+WKqOagh+etvuWksei0pTonLCBlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIGZldGNoQWN0aXZpdHlUYWdzKCk7XHJcbiAgfSwgW2FjdGl2aXR5SWRdKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIOWIneWni+WMluaWh+S7tuWIl+ihqFxyXG4gICAgaWYgKGJhbm5lckltYWdlKSB7XHJcbiAgICAgIHNldEZpbGVMaXN0KFtcclxuICAgICAgICB7XHJcbiAgICAgICAgICB1aWQ6ICctMScsXHJcbiAgICAgICAgICBuYW1lOiAn5b2T5YmN5bCB6Z2iJyxcclxuICAgICAgICAgIHN0YXR1czogJ2RvbmUnLFxyXG4gICAgICAgICAgdXJsOiBiYW5uZXJJbWFnZVxyXG4gICAgICAgIH1cclxuICAgICAgXSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBzZXRGaWxlTGlzdChbXSk7XHJcbiAgICB9XHJcbiAgfSwgW2Jhbm5lckltYWdlXSk7XHJcblxyXG4gIC8vIOWkhOeQhuWPlua2iOe8lui+kVxyXG4gIGNvbnN0IGhhbmRsZUNhbmNlbEVkaXQgPSAoKSA9PiB7XHJcbiAgICBzZXRUaXRsZShpbml0aWFsVGl0bGUpO1xyXG4gICAgc2V0U3RhcnRUaW1lKGluaXRpYWxTdGFydFRpbWUpO1xyXG4gICAgc2V0RW5kVGltZShpbml0aWFsRW5kVGltZSk7XHJcbiAgICBzZXRCYW5uZXJJbWFnZShpbml0aWFsQmFubmVySW1hZ2UpO1xyXG4gICAgc2V0T3JnYW5pemVyKGluaXRpYWxPcmdhbml6ZXIpO1xyXG5cclxuXHJcblxyXG4gICAgc2V0SXNFZGl0aW5nKGZhbHNlKTtcclxuICB9O1xyXG5cclxuICAvLyDlpITnkIbkv53lrZjnvJbovpFcclxuICBjb25zdCBoYW5kbGVTYXZlRWRpdCA9IGFzeW5jICgpID0+IHtcclxuICAgIGlmICghYWN0aXZpdHlJZCkge1xyXG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+a0u+WKqElE5LiN5a2Y5Zyo77yM5peg5rOV5L+d5a2YJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRJc1NhdmluZyh0cnVlKTtcclxuXHJcblxyXG5cclxuICAgICAgLy8g5YeG5aSH5pu05paw5pWw5o2uXHJcbiAgICAgIGNvbnN0IHVwZGF0ZURhdGEgPSB7XHJcbiAgICAgICAgaWQ6IE51bWJlcihhY3Rpdml0eUlkKSxcclxuICAgICAgICBuYW1lOiB0aXRsZSxcclxuICAgICAgICBzdGFydFRpbWU6IG5ldyBEYXRlKHN0YXJ0VGltZSksXHJcbiAgICAgICAgZW5kVGltZTogbmV3IERhdGUoZW5kVGltZSksXHJcbiAgICAgICAgY292ZXJJbWFnZTogYmFubmVySW1hZ2UsXHJcbiAgICAgICAgb3JnYW5pemVyOiBvcmdhbml6ZXIsXHJcblxyXG4gICAgICAgIC8vIOi/meS6m+Wtl+auteS4uuW/heWhq++8jOS9huaIkeS7rOS4jeS/ruaUueWug+S7rFxyXG4gICAgICAgIGRldGFpbENvbnRlbnQ6IFwiXCIsXHJcbiAgICAgICAgcnVsZXNDb250ZW50OiBcIlwiLFxyXG4gICAgICAgIGF3YXJkc0NvbnRlbnQ6IFwiXCJcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKCfkv53lrZjmtLvliqjmlbDmja46JywgdXBkYXRlRGF0YSk7XHJcblxyXG4gICAgICAvLyDmm7TmlrDmtLvliqjln7rmnKzkv6Hmga9cclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhY3Rpdml0eUFwaS51cGRhdGUodXBkYXRlRGF0YSk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfkv53lrZjmtLvliqjlk43lupQ6JywgcmVzcG9uc2UpO1xyXG5cclxuICAgICAgaWYgKHJlc3BvbnNlPy5zdGF0dXMgPT09IDIwMCkge1xyXG4gICAgICAgIC8vIOWmguaenOacieagh+etvu+8jOWImeabtOaWsOagh+etvlxyXG4gICAgICAgIGNvbnN0IHRhZ1Jlc3BvbnNlID0gYXdhaXQgYWN0aXZpdHlUYWdBcGkudXBkYXRlKE51bWJlcihhY3Rpdml0eUlkKSwgdGFnSWRzKTtcclxuICAgICAgICBjb25zb2xlLmxvZygn5qCH562+5pu05paw5ZON5bqUOicsIHRhZ1Jlc3BvbnNlKTtcclxuXHJcbiAgICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+a0u+WKqOS/oeaBr+W3suaIkOWKn+S/neWtmCcpO1xyXG4gICAgICAgIHNldElzRWRpdGluZyhmYWxzZSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKHJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICfkv53lrZjlpLHotKUnKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign5L+d5a2Y5rS75Yqo5L+h5oGv5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfkv53lrZjmtLvliqjkv6Hmga/lpLHotKXvvIzor7fnqI3lkI7ph43or5UnKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzU2F2aW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyDlpITnkIbmoIfnrb7ngrnlh7vnvJbovpFcclxuICBjb25zdCBoYW5kbGVUYWdDbGljayA9ICh0YWc6IE5hdlRhZykgPT4ge1xyXG4gICAgaWYgKGlzRWRpdGluZykge1xyXG4gICAgICBzZXRUYWdJZHModGFnSWRzLmluY2x1ZGVzKE51bWJlcih0YWcuaWQpKSA/IHRhZ0lkcy5maWx0ZXIoaWQgPT4gaWQgIT09IE51bWJlcih0YWcuaWQpKSA6IFsuLi50YWdJZHMsIE51bWJlcih0YWcuaWQpXSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8g5aSE55CG5qCH562+57yW6L6R5Y+W5raIXHJcbiAgY29uc3QgaGFuZGxlVGFnRWRpdENhbmNlbCA9ICgpID0+IHtcclxuICAgIHNldEVkaXRpbmdUYWdJZChudWxsKTtcclxuICB9O1xyXG5cclxuICAvLyDlpITnkIbmt7vliqDmlrDmoIfnrb5cclxuICBjb25zdCBoYW5kbGVBZGRUYWcgPSAoKSA9PiB7XHJcbiAgICBpZiAobmF2VGFncy5sZW5ndGggPCA1KSB7IC8vIOacgOWkmuWFgeiuuDXkuKrmoIfnrb5cclxuICAgICAgY29uc3QgbmV3VGFnOiBOYXZUYWcgPSB7XHJcbiAgICAgICAgaWQ6IGB0YWctJHtEYXRlLm5vdygpfWAsXHJcbiAgICAgICAgdGV4dDogXCLmlrDmoIfnrb5cIlxyXG4gICAgICB9O1xyXG4gICAgICBzZXROYXZUYWdzKFsuLi5uYXZUYWdzLCBuZXdUYWddKTtcclxuICAgICAgc2V0VGFnSWRzKFsuLi50YWdJZHMsIE51bWJlcihuZXdUYWcuaWQpXSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8g5aSE55CG5Yig6Zmk5qCH562+XHJcbiAgY29uc3QgaGFuZGxlRGVsZXRlVGFnID0gKGlkOiBzdHJpbmcpID0+IHtcclxuICAgIGlmIChuYXZUYWdzLmxlbmd0aCA+IDEpIHsgLy8g6Iez5bCR5L+d55WZ5LiA5Liq5qCH562+XHJcbiAgICAgIHNldE5hdlRhZ3MobmF2VGFncy5maWx0ZXIodGFnID0+IHRhZy5pZCAhPT0gaWQpKTtcclxuICAgICAgc2V0VGFnSWRzKHRhZ0lkcy5maWx0ZXIoaWQgPT4gaWQgIT09IE51bWJlcihpZCkpKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyDliY3lvoDku7vliqHlh73mlbBcclxuICBjb25zdCBoYW5kbGVHb1RvVGFzayA9IGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IGxvYWRpbmdNZXNzYWdlID0gbm90aWZpY2F0aW9uLmxvYWRpbmcoJ+ato+WcqOi3s+i9rOWIsOS7u+WKoS4uLicpO1xyXG5cclxuICAgICAgLy8g6I635Y+W5b2T5YmN55So5oi355qE6LWb5LqL5Lu75Yqh5YiX6KGoXHJcbiAgICAgIGNvbnN0IGV2ZW50c1Rhc2tSZXNwb25zZSA9IGF3YWl0IGV2ZW50c1Rhc2tBcGkuZ2V0TXlUYXNrcygpO1xyXG5cclxuICAgICAgaWYgKGV2ZW50c1Rhc2tSZXNwb25zZS5kYXRhLmNvZGUgPT09IDIwMCkge1xyXG4gICAgICAgIGNvbnN0IGV2ZW50c1Rhc2tzID0gZXZlbnRzVGFza1Jlc3BvbnNlLmRhdGEuZGF0YTtcclxuICAgICAgICAvLyDmn6Xmib7kuI7lvZPliY3mtLvliqjnm7jlhbPnmoTotZvkuovku7vliqFcclxuICAgICAgICBjb25zdCByZWxhdGVkVGFzayA9IGV2ZW50c1Rhc2tzLmZpbmQoKHRhc2s6IGFueSkgPT4gdGFzay5hY3Rpdml0eUlkID09PSBOdW1iZXIoYWN0aXZpdHlJZCkpO1xyXG5cclxuICAgICAgICBpZiAocmVsYXRlZFRhc2spIHtcclxuICAgICAgICAgIC8vIOWcqOaWsOagh+etvumhteS4reaJk+W8gOePree6p+epuumXtOW5tuiHquWKqOaJk+W8gOWvueW6lOeahOi1m+S6i+S7u+WKoVxyXG4gICAgICAgICAgd2luZG93Lm9wZW4oYC9jbGFzcy1zcGFjZT90YXNrSWQ9JHtyZWxhdGVkVGFzay5pZH1gLCAnX2JsYW5rJyk7XHJcbiAgICAgICAgICBub3RpZmljYXRpb24uc3VjY2Vzcygn5q2j5Zyo6Lez6L2s5Yiw5Lu75Yqh6aG16Z2iLi4uJyk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIC8vIOWmguaenOayoeacieaJvuWIsOWvueW6lOS7u+WKoe+8jOWcqOaWsOagh+etvumhteS4reaJk+W8gOePree6p+epuumXtFxyXG4gICAgICAgICAgd2luZG93Lm9wZW4oJy9jbGFzcy1zcGFjZScsICdfYmxhbmsnKTtcclxuICAgICAgICAgIG5vdGlmaWNhdGlvbi5pbmZvKCfmnKrmib7liLDlr7nlupTku7vliqHvvIzlt7LmiZPlvIDnj63nuqfnqbrpl7QnKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgLy8g5aaC5p6c6I635Y+W5Lu75Yqh5aSx6LSl77yM5Zyo5paw5qCH562+6aG15Lit5omT5byA54+t57qn56m66Ze0XHJcbiAgICAgICAgd2luZG93Lm9wZW4oJy9jbGFzcy1zcGFjZScsICdfYmxhbmsnKTtcclxuICAgICAgICBub3RpZmljYXRpb24uaW5mbygn6I635Y+W5Lu75Yqh5aSx6LSl77yM5bey5omT5byA54+t57qn56m66Ze0Jyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmIChsb2FkaW5nTWVzc2FnZSkge1xyXG4gICAgICAgIGxvYWRpbmdNZXNzYWdlLmNsb3NlKCk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+i3s+i9rOS7u+WKoeWksei0pTonLCBlcnJvcik7XHJcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign6Lez6L2s5Lu75Yqh5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VJyk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8g5aSE55CG5oql5ZCN5oyJ6ZKu54K55Ye7XHJcbiAgY29uc3QgaGFuZGxlU3VibWl0Q2xpY2sgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAvLyDojrflj5Zsb2NhbFN0b3JhZ2XkuK3nmoTnlKjmiLfkv6Hmga/ov5vooYzlr7nmr5RcclxuICAgIGNvbnN0IGxvY2FsVXNlclN0ciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VyJyk7XHJcbiAgICBjb25zdCBsb2NhbFVzZXIgPSBsb2NhbFVzZXJTdHIgPyBKU09OLnBhcnNlKGxvY2FsVXNlclN0cikgOiBudWxsO1xyXG5cclxuICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10g5oql5ZCN5oyJ6ZKu54K55Ye7IC0g6K+m57uG55So5oi354q25oCB5qOA5p+lJywge1xyXG4gICAgICBoYXNTdWJtaXR0ZWQsXHJcbiAgICAgICdSZWR1eCB1c2VyU3RhdGUnOiB7XHJcbiAgICAgICAgaXNMb2dnZWRJbjogdXNlclN0YXRlPy5pc0xvZ2dlZEluLFxyXG4gICAgICAgIHBob25lOiB1c2VyU3RhdGU/LnBob25lLFxyXG4gICAgICAgIHBob25lTGVuZ3RoOiB1c2VyU3RhdGU/LnBob25lPy5sZW5ndGgsXHJcbiAgICAgICAgcGhvbmVUeXBlOiB0eXBlb2YgdXNlclN0YXRlPy5waG9uZSxcclxuICAgICAgICByb2xlSWQ6IHVzZXJTdGF0ZT8ucm9sZUlkLFxyXG4gICAgICAgIHVzZXJJZDogdXNlclN0YXRlPy51c2VySWQsXHJcbiAgICAgICAgbmlja05hbWU6IHVzZXJTdGF0ZT8ubmlja05hbWVcclxuICAgICAgfSxcclxuICAgICAgJ2xvY2FsU3RvcmFnZSB1c2VyJzoge1xyXG4gICAgICAgIHBob25lOiBsb2NhbFVzZXI/LnBob25lLFxyXG4gICAgICAgIHBob25lTGVuZ3RoOiBsb2NhbFVzZXI/LnBob25lPy5sZW5ndGgsXHJcbiAgICAgICAgcGhvbmVUeXBlOiB0eXBlb2YgbG9jYWxVc2VyPy5waG9uZSxcclxuICAgICAgICB1c2VySWQ6IGxvY2FsVXNlcj8udXNlcklkLFxyXG4gICAgICAgIG5pY2tOYW1lOiBsb2NhbFVzZXI/Lm5pY2tOYW1lXHJcbiAgICAgIH0sXHJcbiAgICAgICdwaG9uZSB2YWxpZGF0aW9uJzoge1xyXG4gICAgICAgICd1c2VyU3RhdGUucGhvbmUgZXhpc3RzJzogISF1c2VyU3RhdGU/LnBob25lLFxyXG4gICAgICAgICd1c2VyU3RhdGUucGhvbmUgbm90IGVtcHR5JzogdXNlclN0YXRlPy5waG9uZSAmJiB1c2VyU3RhdGUucGhvbmUudHJpbSgpICE9PSAnJyxcclxuICAgICAgICAnbG9jYWxVc2VyLnBob25lIGV4aXN0cyc6ICEhbG9jYWxVc2VyPy5waG9uZSxcclxuICAgICAgICAnbG9jYWxVc2VyLnBob25lIG5vdCBlbXB0eSc6IGxvY2FsVXNlcj8ucGhvbmUgJiYgbG9jYWxVc2VyPy5waG9uZS50cmltKCkgIT09ICcnXHJcbiAgICAgIH1cclxuICAgIH0pO1xyXG5cclxuICAgIC8vIOS4tOaXtuiwg+eUqEFQSeiOt+WPluacgOaWsOeUqOaIt+S/oeaBr+i/m+ihjOWvueavlFxyXG4gICAgaWYgKHVzZXJTdGF0ZT8udXNlcklkKSB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB1c2VyQXBpLmdldFVzZXJJbmZvKHVzZXJTdGF0ZS51c2VySWQpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10g5LuOQVBJ6I635Y+W55qE5pyA5paw55So5oi35L+h5oGvOicsIHtcclxuICAgICAgICAgIGNvZGU6IHJlc3BvbnNlLmNvZGUsXHJcbiAgICAgICAgICBwaG9uZTogcmVzcG9uc2UuZGF0YT8ucGhvbmUsXHJcbiAgICAgICAgICBwaG9uZUxlbmd0aDogcmVzcG9uc2UuZGF0YT8ucGhvbmU/Lmxlbmd0aCxcclxuICAgICAgICAgIHBob25lVHlwZTogdHlwZW9mIHJlc3BvbnNlLmRhdGE/LnBob25lLFxyXG4gICAgICAgICAgbmlja05hbWU6IHJlc3BvbnNlLmRhdGE/Lm5pY2tOYW1lLFxyXG4gICAgICAgICAgdXNlcklkOiByZXNwb25zZS5kYXRhPy5pZFxyXG4gICAgICAgIH0pO1xyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ/CflI0gW0RFQlVHXSDojrflj5bnlKjmiLfkv6Hmga/lpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8g5aaC5p6c55So5oi35bey5oql5ZCN77yM6Lez6L2s5Yiw5Lu75Yqh6aG16Z2iXHJcbiAgICBpZiAoaGFzU3VibWl0dGVkKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10g55So5oi35bey5oql5ZCN77yM6Lez6L2s5Yiw5Lu75Yqh6aG16Z2iJyk7XHJcbiAgICAgIGhhbmRsZUdvVG9UYXNrKCk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICAvLyDmo4Dmn6XnlKjmiLfmmK/lkKbnmbvlvZVcclxuICAgIGlmICghdXNlclN0YXRlIHx8ICF1c2VyU3RhdGUuaXNMb2dnZWRJbikge1xyXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIOeUqOaIt+acqueZu+W9le+8jOi3s+i9rOWIsOeZu+W9lemhtemdoicpO1xyXG4gICAgICAvLyDmnKrnmbvlvZXvvIzmmL7npLrnmbvlvZXlr7nor53moYZcclxuICAgICAgY29uc3QgcmVkaXJlY3RVcmwgPSBwYXRobmFtZSB8fCAnL2hvbWUnO1xyXG4gICAgICByb3V0ZXIucHVzaChgL2xvZ2luP3JlZGlyZWN0PSR7ZW5jb2RlVVJJQ29tcG9uZW50KHJlZGlyZWN0VXJsKX1gKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIOajgOafpeeUqOaIt+i6q+S7ve+8muWPquacieWtpueUn++8iHJvbGVJZOS4ujHvvInmiY3og73miqXlkI1cclxuICAgIGNvbnN0IGlzU3R1ZGVudCA9IGNoZWNrSXNTdHVkZW50KCk7XHJcbiAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIOajgOafpeeUqOaIt+i6q+S7vScsIHsgaXNTdHVkZW50LCByb2xlSWQ6IHVzZXJTdGF0ZT8ucm9sZUlkIH0pO1xyXG4gICAgaWYgKCFpc1N0dWRlbnQpIHtcclxuICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSDnlKjmiLfkuI3mmK/lrabnlJ/ouqvku73vvIzmmL7npLrouqvku73pqozor4HlvLnnqpcnKTtcclxuICAgICAgc2hvd1N0dWRlbnRPbmx5TW9kYWwoKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10g6Lqr5Lu96aqM6K+B6YCa6L+H77yM5byA5aeL5oql5ZCN5rWB56iLJyk7XHJcbiAgICAvLyDouqvku73pqozor4HpgJrov4fvvIznm7TmjqXov5vooYzmiqXlkI1cclxuICAgIGhhbmRsZURpcmVjdFJlZ2lzdHJhdGlvbigpO1xyXG4gIH07XHJcblxyXG4gIC8vIOWkhOeQhueZu+W9leaIkOWKn1xyXG4gIGNvbnN0IGhhbmRsZUxvZ2luU3VjY2VzcyA9ICgpID0+IHtcclxuICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKCfnmbvlvZXmiJDlip8nKTtcclxuXHJcbiAgICAvLyDnmbvlvZXmiJDlip/lkI7vvIzpobXpnaLnmoR1c2VFZmZlY3TkvJroh6rliqjmo4Dmn6XmiqXlkI3nirbmgIFcclxuICAgIC8vIOS4jemcgOimgeWcqOi/memHjOmHjeWkjeajgOafpe+8jOiuqXVzZUVmZmVjdOWkhOeQhueKtuaAgeabtOaWsFxyXG4gIH07XHJcblxyXG4gIC8vIOWkhOeQhuaKpeWQjeaIkOWKn1xyXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdFN1Y2Nlc3MgPSAoKSA9PiB7XHJcbiAgICAvLyDmm7TmlrDmj5DkuqTnirbmgIFcclxuICAgIHNldEhhc1N1Ym1pdHRlZCh0cnVlKTtcclxuICAgIHNldFJlZ2lzdHJhdGlvblN0YXR1cygxKTsgLy8g6K6+572u5Li65bey5oql5ZCN54q25oCBXHJcbiAgICBub3RpZmljYXRpb24uc3VjY2Vzcygn5oql5ZCN5oiQ5YqfJyk7XHJcblxyXG4gICAgLy8g6YCa55+l6aG16Z2i5Yi35paw5Y+C6LWb5L2c5ZOB5YiX6KGoXHJcbiAgICBpZiAob25SZWZyZXNoV29ya3MpIHtcclxuICAgICAgb25SZWZyZXNoV29ya3MoKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyDnm7TmjqXmiqXlkI3lpITnkIblh73mlbBcclxuICBjb25zdCBoYW5kbGVEaXJlY3RSZWdpc3RyYXRpb24gPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICAvLyDmo4Dmn6XnlKjmiLfmmK/lkKbnu5HlrprkuobmiYvmnLrlj7dcclxuICAgICAgaWYgKCF1c2VyU3RhdGUucGhvbmUgfHwgdXNlclN0YXRlLnBob25lLnRyaW0oKSA9PT0gJycpIHtcclxuICAgICAgICAvLyDmmL7npLrnoa7orqTlr7nor53moYZcclxuICAgICAgICBNb2RhbC5jb25maXJtKHtcclxuICAgICAgICAgIHRpdGxlOiAn6ZyA6KaB57uR5a6a5omL5py65Y+3JyxcclxuICAgICAgICAgIGNvbnRlbnQ6ICfmiqXlkI3mtLvliqjpnIDopoHnu5HlrprmiYvmnLrlj7fvvIzmmK/lkKbliY3lvoDnu5HlrprvvJ8nLFxyXG4gICAgICAgICAgb2tUZXh0OiAn5YmN5b6A57uR5a6aJyxcclxuICAgICAgICAgIGNhbmNlbFRleHQ6ICflj5bmtognLFxyXG4gICAgICAgICAgb25PazogKCkgPT4ge1xyXG4gICAgICAgICAgICAvLyDot7PovazliLDnmbvlvZXpobXpnaLvvIzlubblnKhVUkzkuK3mt7vliqDlj4LmlbDooajnpLrpnIDopoHnu5HlrprmiYvmnLrlj7dcclxuICAgICAgICAgICAgY29uc3QgY3VycmVudFVybCA9IHdpbmRvdy5sb2NhdGlvbi5ocmVmO1xyXG4gICAgICAgICAgICByb3V0ZXIucHVzaChgL2xvZ2luP25lZWRCaW5kUGhvbmU9dHJ1ZSZyZWRpcmVjdD0ke2VuY29kZVVSSUNvbXBvbmVudChjdXJyZW50VXJsKX1gKTtcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICBvbkNhbmNlbDogKCkgPT4ge1xyXG4gICAgICAgICAgICBub3RpZmljYXRpb24uaW5mbygn5pyq57uR5a6a5omL5py65Y+377yM5peg5rOV5oql5ZCN5rS75YqoJyk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIOeUqOaIt+W3sue7keWumuaJi+acuuWPt++8jOe7p+e7reaKpeWQjea1geeoiycpO1xyXG5cclxuICAgICAgLy8g5pi+56S65Yqg6L295o+Q56S6XHJcbiAgICAgIGNvbnN0IGxvYWRpbmdNZXNzYWdlID0gbm90aWZpY2F0aW9uLmxvYWRpbmcoJ+ato+WcqOaPkOS6pOaKpeWQjS4uLicpO1xyXG5cclxuICAgICAgLy8g5o+Q5Lqk5oql5ZCN5pWw5o2u77yI5L2/55So6buY6K6k5YC877yM6Lez6L+H5Y2P6K6u56Gu6K6k5q2l6aqk77yJXHJcbiAgICAgIGNvbnN0IHN1Ym1pdERhdGEgPSB7XHJcbiAgICAgICAgYWN0aXZpdHlJZDogTnVtYmVyKGFjdGl2aXR5SWQpLFxyXG4gICAgICAgIGFncmVlbWVudEFjY2VwdGVkOiB0cnVlLCAvLyDpu5jorqTlkIzmhI/ljY/orq5cclxuICAgICAgICBwYXJlbnRDb25zZW50QWNjZXB0ZWQ6IHRydWUsIC8vIOm7mOiupOWQjOaEj+WutumVv+efpeaDheWQjOaEj+S5plxyXG4gICAgICAgIHBhcmVudFNpZ25hdHVyZVBhdGg6ICcnLCAvLyDnqbrnrb7lkI3ot6/lvoRcclxuICAgICAgICBzaWduYXR1cmVUaW1lOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgcmVtYXJrOiBg5b+r6YCf5oql5ZCN5pe26Ze077yaJHtuZXcgRGF0ZSgpLnRvTG9jYWxlU3RyaW5nKCd6aC1DTicpfWBcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYWN0aXZpdHlSZWdpc3RyYXRpb25BcGkuc3VibWl0UmVnaXN0cmF0aW9uKHN1Ym1pdERhdGEpO1xyXG5cclxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGE/LmNvZGUgPT09IDIwMCkge1xyXG4gICAgICAgIC8vIOabtOaWsFVJ54q25oCBXHJcbiAgICAgICAgc2V0SGFzU3VibWl0dGVkKHRydWUpO1xyXG4gICAgICAgIHNldFJlZ2lzdHJhdGlvblN0YXR1cygxKTtcclxuICAgICAgICBub3RpZmljYXRpb24uc3VjY2Vzcygn5oql5ZCN5oiQ5Yqf77yB5q2j5Zyo6Lez6L2s5Yiw54+t57qn56m66Ze0Li4uJyk7XHJcblxyXG4gICAgICAgIC8vIOmAmuefpemhtemdouWIt+aWsOWPgui1m+S9nOWTgeWIl+ihqFxyXG4gICAgICAgIGlmIChvblJlZnJlc2hXb3Jrcykge1xyXG4gICAgICAgICAgb25SZWZyZXNoV29ya3MoKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIOaKpeWQjeaIkOWKn+WQju+8jOiOt+WPluWvueW6lOeahOi1m+S6i+S7u+WKoeW5tui3s+i9rOWIsOePree6p+epuumXtFxyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAvLyDnrYnlvoXkuIDlsI/mrrXml7bpl7Tnoa7kv53lkI7nq6/liJvlu7rotZvkuovku7vliqHlrozmiJBcclxuICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDAwKSk7XHJcblxyXG4gICAgICAgICAgLy8g6I635Y+W5b2T5YmN55So5oi355qE6LWb5LqL5Lu75Yqh5YiX6KGoXHJcbiAgICAgICAgICBjb25zdCBldmVudHNUYXNrUmVzcG9uc2UgPSBhd2FpdCBldmVudHNUYXNrQXBpLmdldE15VGFza3MoKTtcclxuXHJcbiAgICAgICAgICBpZiAoZXZlbnRzVGFza1Jlc3BvbnNlLmRhdGEuY29kZSA9PT0gMjAwKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGV2ZW50c1Rhc2tzID0gZXZlbnRzVGFza1Jlc3BvbnNlLmRhdGEuZGF0YTtcclxuICAgICAgICAgICAgLy8g5p+l5om+5LiO5b2T5YmN5rS75Yqo55u45YWz55qE6LWb5LqL5Lu75YqhXHJcbiAgICAgICAgICAgIGNvbnN0IHJlbGF0ZWRUYXNrID0gZXZlbnRzVGFza3MuZmluZCgodGFzazogYW55KSA9PiB0YXNrLmFjdGl2aXR5SWQgPT09IE51bWJlcihhY3Rpdml0eUlkKSk7XHJcblxyXG4gICAgICAgICAgICBpZiAocmVsYXRlZFRhc2spIHtcclxuICAgICAgICAgICAgICAvLyDlnKjmlrDmoIfnrb7pobXkuK3miZPlvIDnj63nuqfnqbrpl7Tlubboh6rliqjmiZPlvIDlr7nlupTnmoTotZvkuovku7vliqFcclxuICAgICAgICAgICAgICB3aW5kb3cub3BlbihgL2NsYXNzLXNwYWNlP3Rhc2tJZD0ke3JlbGF0ZWRUYXNrLmlkfWAsICdfYmxhbmsnKTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAvLyDlpoLmnpzmsqHmnInmib7liLDlr7nlupTku7vliqHvvIzlnKjmlrDmoIfnrb7pobXkuK3miZPlvIDnj63nuqfnqbrpl7RcclxuICAgICAgICAgICAgICB3aW5kb3cub3BlbignL2NsYXNzLXNwYWNlJywgJ19ibGFuaycpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAvLyDlpoLmnpzojrflj5bku7vliqHlpLHotKXvvIzlnKjmlrDmoIfnrb7pobXkuK3miZPlvIDnj63nuqfnqbrpl7RcclxuICAgICAgICAgICAgd2luZG93Lm9wZW4oJy9jbGFzcy1zcGFjZScsICdfYmxhbmsnKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9IGNhdGNoICh0YXNrRXJyb3IpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlui1m+S6i+S7u+WKoeWksei0pTonLCB0YXNrRXJyb3IpO1xyXG4gICAgICAgICAgLy8g5Y2z5L2/6I635Y+W5Lu75Yqh5aSx6LSl77yM5Lmf6KaB5Zyo5paw5qCH562+6aG15Lit5omT5byA54+t57qn56m66Ze0XHJcbiAgICAgICAgICB3aW5kb3cub3BlbignL2NsYXNzLXNwYWNlJywgJ19ibGFuaycpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzcG9uc2UuZGF0YT8ubWVzc2FnZSB8fCAn5oql5ZCN5aSx6LSlJyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmIChsb2FkaW5nTWVzc2FnZSkge1xyXG4gICAgICAgIGxvYWRpbmdNZXNzYWdlLmNsb3NlKCk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+aKpeWQjeWksei0pTonLCBlcnJvcik7XHJcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICfmiqXlkI3lpLHotKXvvIzor7fnqI3lkI7ph43or5UnKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyDlpITnkIboh6rlrprkuYnkuIrkvKDor7fmsYJcclxuICBjb25zdCBoYW5kbGVDdXN0b21SZXF1ZXN0ID0gYXN5bmMgKG9wdGlvbnM6IGFueSkgPT4ge1xyXG4gICAgY29uc3QgeyBmaWxlLCBvblN1Y2Nlc3MsIG9uRXJyb3IgfSA9IG9wdGlvbnM7XHJcbiAgICBzZXRVcGxvYWRMb2FkaW5nKHRydWUpO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIOS4iuS8oOWIsE9TU1xyXG4gICAgICBjb25zdCB1cmwgPSBhd2FpdCB1cGxvYWRBcGkudXBsb2FkVG9Pc3MoZmlsZSBhcyBSY0ZpbGUpO1xyXG4gICAgICBjb25zb2xlLmxvZygn5Zu+54mH5LiK5Lyg5oiQ5Yqf77yMVVJMOicsIHVybCk7XHJcblxyXG4gICAgICAvLyDmm7TmlrDmlofku7bliJfooahcclxuICAgICAgc2V0RmlsZUxpc3QoW3tcclxuICAgICAgICAuLi4oZmlsZSBhcyBSY0ZpbGUpLFxyXG4gICAgICAgIHVpZDogZmlsZS51aWQsXHJcbiAgICAgICAgbmFtZTogZmlsZS5uYW1lLFxyXG4gICAgICAgIHN0YXR1czogJ2RvbmUnLFxyXG4gICAgICAgIHVybDogdXJsXHJcbiAgICAgIH1dKTtcclxuXHJcbiAgICAgIC8vIOabtOaWsOWwgemdouWbvueJh1VSTFxyXG4gICAgICBzZXRCYW5uZXJJbWFnZSh1cmwpO1xyXG5cclxuICAgICAgaWYgKG9uU3VjY2Vzcykge1xyXG4gICAgICAgIG9uU3VjY2VzcyhudWxsLCBmaWxlKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+WwgemdouWbvueJh+S4iuS8oOaIkOWKnycpO1xyXG4gICAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign5LiK5Lyg5aSx6LSlOicsIGVycik7XHJcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihg5LiK5Lyg5aSx6LSlOiAke2Vyci5tZXNzYWdlIHx8ICfor7fnqI3lkI7ph43or5UnfWApO1xyXG4gICAgICBzZXRGaWxlTGlzdChbe1xyXG4gICAgICAgIC4uLihmaWxlIGFzIFJjRmlsZSksXHJcbiAgICAgICAgdWlkOiBmaWxlLnVpZCxcclxuICAgICAgICBuYW1lOiBmaWxlLm5hbWUsXHJcbiAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxyXG4gICAgICAgIGVycm9yOiBlcnJcclxuICAgICAgfV0pO1xyXG5cclxuICAgICAgaWYgKG9uRXJyb3IpIHtcclxuICAgICAgICBvbkVycm9yKGVycik7XHJcbiAgICAgIH1cclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldFVwbG9hZExvYWRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOWkhOeQhuWIoOmZpOaWh+S7tlxyXG4gIGNvbnN0IGhhbmRsZVJlbW92ZSA9IGFzeW5jIChmaWxlOiBDdXN0b21VcGxvYWRGaWxlKSA9PiB7XHJcbiAgICBpZiAoZmlsZS51cmwgJiYgZmlsZS51cmwgIT09IGluaXRpYWxCYW5uZXJJbWFnZSkge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGF3YWl0IHVwbG9hZEFwaS5kZWxldGVGcm9tT3NzKGZpbGUudXJsKTtcclxuICAgICAgICBub3RpZmljYXRpb24uc3VjY2Vzcygn5bey5LuO5pyN5Yqh5Zmo5Yig6Zmk5Zu+54mHJyk7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcign5LuOT1NT5Yig6Zmk5paH5Lu25aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+acjeWKoeWZqOWIoOmZpOWbvueJh+Wksei0pScpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgc2V0RmlsZUxpc3QoW10pO1xyXG4gICAgc2V0QmFubmVySW1hZ2UoJycpO1xyXG4gICAgcmV0dXJuIHRydWU7XHJcbiAgfTtcclxuXHJcblxyXG5cclxuXHJcblxyXG5cclxuXHJcbiAgLy8g5aSE55CG5YiG5Lqr5oyJ6ZKu54K55Ye75LqL5Lu2XHJcbiAgY29uc3QgaGFuZGxlU2hhcmUgPSAoKSA9PiB7XHJcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgLy8g6I635Y+W5b2T5YmN6aG16Z2iVVJMXHJcbiAgICAgIGNvbnN0IGN1cnJlbnRVcmwgPSB3aW5kb3cubG9jYXRpb24uaHJlZjtcclxuXHJcbiAgICAgIC8vIOWkjeWItuWIsOWJqui0tOadv1xyXG4gICAgICBuYXZpZ2F0b3IuY2xpcGJvYXJkLndyaXRlVGV4dChjdXJyZW50VXJsKVxyXG4gICAgICAgIC50aGVuKCgpID0+IHtcclxuICAgICAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKCfmtLvliqjpk77mjqXlt7LlpI3liLbvvIzlv6vljrvliIbkuqvlkKfvvIEnKTtcclxuICAgICAgICB9KVxyXG4gICAgICAgIC5jYXRjaChlcnIgPT4ge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcign5aSN5Yi25aSx6LSlOicsIGVycik7XHJcbiAgICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+WkjeWItumTvuaOpeWksei0pe+8jOivt+aJi+WKqOWkjeWIticpO1xyXG4gICAgICAgIH0pO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOWmguaenOi/mOayoeacieWcqOWuouaIt+err+aMgui9ve+8jOi/lOWbnuS4gOS4queugOWNleeahOWNoOS9jeesplxyXG4gIGlmICghaXNDbGllbnQpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWN0aXZpdHktaGVhZGVyIHJlbGF0aXZlIHJvdW5kZWQtMnhsIG92ZXJmbG93LWhpZGRlbiBzaGFkb3ctbWQgYm9yZGVyIGJvcmRlci1ncmF5LTEwMCBiZy13aGl0ZSBob3ZlcjpzaGFkb3ctbGcgdHJhbnNpdGlvbi1zaGFkb3cgZHVyYXRpb24tMzAwXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBoLTY0IGJnLWdyYXktMjAwIGFuaW1hdGUtcHVsc2VcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by10IGZyb20tYmxhY2svNjAgdG8tdHJhbnNwYXJlbnRcIiAvPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBwLTYgZmxleCBmbGV4LWNvbCBtZDpmbGV4LXJvdyBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnQgbWQ6aXRlbXMtY2VudGVyIGgtZnVsbFwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC04IGJnLWdyYXktMzAwIHJvdW5kZWQgbWItMiB3LTMvNFwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLWdyYXktMzAwIHJvdW5kZWQgbWItMSB3LTEvMlwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLWdyYXktMzAwIHJvdW5kZWQgdy0xLzNcIj48L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWN0aXZpdHktaGVhZGVyIHJlbGF0aXZlIHJvdW5kZWQtMnhsIG92ZXJmbG93LWhpZGRlbiBzaGFkb3ctbWQgYm9yZGVyIGJvcmRlci1ncmF5LTEwMCBiZy13aGl0ZSBob3ZlcjpzaGFkb3ctbGcgdHJhbnNpdGlvbi1zaGFkb3cgZHVyYXRpb24tMzAwXCI+XHJcbiAgICAgICAgICB7Lyog5YiH5o2iVUnmmL7npLrnmoTmjInpkq4gLSDlj6rlnKjnvJbovpHmqKHlvI/kuIvmmL7npLogKi99XHJcbiAgICAgICAgICB7aXNFZGl0aW5nICYmIChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BhYnNvbHV0ZSB0b3AtMiBsZWZ0LTIgei01MCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMmB9PlxyXG4gICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dVSSghc2hvd1VJKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiBiZy1ibGFjay8yMCBob3ZlcjpiZy1ibGFjay80MCB0ZXh0LXdoaXRlIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWNvbG9yc1wiXHJcbiAgICAgICAgICAgICAgICB0aXRsZT17c2hvd1VJID8gXCLpmpDol4/nlYzpnaLlhYPntKBcIiA6IFwi5pi+56S655WM6Z2i5YWD57SgXCJ9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAge3Nob3dVSSA/IChcclxuICAgICAgICAgICAgICAgICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgY2xhc3NOYW1lPVwidy00IGgtNFwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMTcuOTQgMTcuOTRBMTAuMDcgMTAuMDcgMCAwIDEgMTIgMjBjLTcgMC0xMS04LTExLThhMTguNDUgMTguNDUgMCAwIDEgNS4wNi01Ljk0TTkuOSA0LjI0QTkuMTIgOS4xMiAwIDAgMSAxMiA0YzcgMCAxMSA4IDExIDhhMTguNSAxOC41IDAgMCAxLTIuMTYgMy4xOW0tNi43Mi0xLjA3YTMgMyAwIDEgMS00LjI0LTQuMjRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxsaW5lIHgxPVwiMVwiIHkxPVwiMVwiIHgyPVwiMjNcIiB5Mj1cIjIzXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICA8c3ZnIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiBjbGFzc05hbWU9XCJ3LTQgaC00XCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCIyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk0xIDEyczQtOCAxMS04IDExIDggMTEgOC00IDgtMTEgOC0xMS04LTExLTh6XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8Y2lyY2xlIGN4PVwiMTJcIiBjeT1cIjEyXCIgcj1cIjNcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKX1cclxuXHJcblxyXG5cclxuICAgICAgICAgIHsvKiDnvJbovpHmqKHlvI/lvIDlhbMgKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGFic29sdXRlIHRvcC0yIHJpZ2h0LTIgei01MCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwICR7c2hvd1VJID8gJ29wYWNpdHktMTAwJyA6ICdvcGFjaXR5LTAgcG9pbnRlci1ldmVudHMtbm9uZSd9YH0+XHJcbiAgICAgICAgICAgIHtpc0VkaXRpbmcgPyAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMlwiPlxyXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTYXZlRWRpdH1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIGJnLWdyZWVuLTUwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtZnVsbCBob3ZlcjpiZy1ncmVlbi02MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIuS/neWtmOe8lui+kVwiXHJcbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1NhdmluZ31cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAge2lzU2F2aW5nID8gPExvYWRlciBjbGFzc05hbWU9XCJ3LTQgaC00IGFuaW1hdGUtc3BpblwiIC8+IDogPFNhdmUgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+fVxyXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNhbmNlbEVkaXR9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiBiZy1yZWQtNTAwIHRleHQtd2hpdGUgcm91bmRlZC1mdWxsIGhvdmVyOmJnLXJlZC02MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIuWPlua2iOe8lui+kVwiXHJcbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1NhdmluZ31cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICBpc0FkbWluICYmIChcclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNFZGl0aW5nKHRydWUpfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgYmctYmx1ZS01MDAgdGV4dC13aGl0ZSByb3VuZGVkLWZ1bGwgaG92ZXI6YmctYmx1ZS02MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIui/m+WFpee8lui+keaooeW8j1wiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxFZGl0IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qIOaoquW5heWbvueJhyAtIOWni+e7iOaYvuekuiAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy1mdWxsIGgtWzMwMHB4XSBvdmVyZmxvdy1oaWRkZW5cIj5cclxuICAgICAgICAgICAgey8qIEJhbm5lciBJbWFnZSAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLWIgZnJvbS10cmFuc3BhcmVudCB0by1ibGFjay8zMCB6LTEwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi0zMDAgJHtzaG93VUkgPyAnb3BhY2l0eS0xMDAnIDogJ29wYWNpdHktMCd9YH0gLz5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy1mdWxsIGgtZnVsbFwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgYWJzb2x1dGUgaW5zZXQtMCBiZy1ibHVlLTUwMC8xMCBhbmltYXRlLXB1bHNlIHotMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwICR7c2hvd1VJID8gJ29wYWNpdHktMTAwJyA6ICdvcGFjaXR5LTAnfWB9PjwvZGl2PlxyXG4gICAgICAgICAgICAgIHtpc0VkaXRpbmcgPyAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgei0xMFwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHctZnVsbCBoLWZ1bGxcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzcmM9e2Jhbm5lckltYWdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBhbHQ9e3RpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBmaWxsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHByaW9yaXR5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb2JqZWN0Rml0OiAnY292ZXInXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInotMTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkVycm9yPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHRhcmdldCA9IGUudGFyZ2V0IGFzIEhUTUxJbWFnZUVsZW1lbnQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSAnbGluZWFyLWdyYWRpZW50KDEyMGRlZywgI2YzZjRmNiwgI2U1ZTdlYiknO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGFic29sdXRlIGluc2V0LTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctYmxhY2svMzAgei0yMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwICR7c2hvd1VJID8gJ29wYWNpdHktMTAwJyA6ICdvcGFjaXR5LTAgcG9pbnRlci1ldmVudHMtbm9uZSd9YH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHAtNCB3LWZ1bGwgbWF4LXcteHNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBwLTIgbWItNCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IHRleHQtc20gYmctd2hpdGUvOTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtiYW5uZXJJbWFnZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEJhbm5lckltYWdlKGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIui+k+WFpeaoquW5heWbvueJh1VSTFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxBbnRVcGxvYWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBsaXN0VHlwZT1cInBpY3R1cmUtY2FyZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsZUxpc3Q9e2ZpbGVMaXN0fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGN1c3RvbVJlcXVlc3Q9e2hhbmRsZUN1c3RvbVJlcXVlc3R9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25SZW1vdmU9e2hhbmRsZVJlbW92ZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBtYXhDb3VudD17MX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzaG93VXBsb2FkTGlzdD17eyBzaG93UHJldmlld0ljb246IHRydWUsIHNob3dSZW1vdmVJY29uOiB0cnVlIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25QcmV2aWV3PXsoZmlsZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGZpbGUudXJsKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpbmRvdy5vcGVuKGZpbGUudXJsLCAnX2JsYW5rJyk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtmaWxlTGlzdC5sZW5ndGggPj0gMSA/IG51bGwgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UGx1c091dGxpbmVkIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luVG9wOiA4IH19PuS4iuS8oDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9BbnRVcGxvYWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPEltYWdlXHJcbiAgICAgICAgICAgICAgICAgICAgc3JjPXtiYW5uZXJJbWFnZX1cclxuICAgICAgICAgICAgICAgICAgICBhbHQ9e3RpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgIGZpbGxcclxuICAgICAgICAgICAgICAgICAgICBwcmlvcml0eVxyXG4gICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICBvYmplY3RGaXQ6ICdjb3ZlcidcclxuICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInotMTAgaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTcwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgb25FcnJvcj17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHRhcmdldCA9IGUudGFyZ2V0IGFzIEhUTUxJbWFnZUVsZW1lbnQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICB0YXJnZXQuc3R5bGUuYmFja2dyb3VuZCA9ICdsaW5lYXItZ3JhZGllbnQoMTIwZGVnLCAjZjNmNGY2LCAjZTVlN2ViKSc7XHJcbiAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgey8qIOa0u+WKqOS/oeaBr+WPoOWKoOWxgiAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BhYnNvbHV0ZSBib3R0b20tNCBsZWZ0LTQgcmlnaHQtNCB6LTIwIGZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwICR7c2hvd1VJID8gJ29wYWNpdHktMTAwJyA6ICdvcGFjaXR5LTAgcG9pbnRlci1ldmVudHMtbm9uZSd9YH0+XHJcbiAgICAgICAgICAgICAgey8qIOagh+etvuWKn+iDveaaguaXtuazqOmHiu+8jOS/neeVmeS7peS+v+WwhuadpeS9v+eUqCBcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTEuNSBiZy13aGl0ZS85MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtZnVsbCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS02MDAgc2hhZG93LXNtIGhvdmVyOnNoYWRvdy1tZCB0cmFuc2l0aW9uLXNoYWRvdyBkdXJhdGlvbi0zMDAgaG92ZXI6Ymctd2hpdGUgdHJhbnNmb3JtIGhvdmVyOi10cmFuc2xhdGUteS0xIGhvdmVyOnNjYWxlLTEwNSByZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICB7aXNFZGl0aW5nICYmIG5hdlRhZ3MubGVuZ3RoIDwgNSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b24gXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQWRkVGFnfVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtOCBsZWZ0LTAgZmxleCBpdGVtcy1jZW50ZXIgcHgtMiBweS0xIGJnLWJsdWUtNTAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyB0ZXh0LXhzIGhvdmVyOmJnLWJsdWUtNjAwIHRyYW5zaXRpb24tY29sb3JzXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cInctMyBoLTMgbXItMVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAg5re75Yqg5qCH562+XHJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICB7dGFncyAmJiB0YWdzLmxlbmd0aCA+IDAgPyAoXHJcbiAgICAgICAgICAgICAgICAgIHRhZ3MubWFwKCh0YWcsIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPFJlYWN0LkZyYWdtZW50IGtleT17dGFnLmlkfT5cclxuICAgICAgICAgICAgICAgICAgICAgIHtpbmRleCA+IDAgJiYgPHNwYW4gY2xhc3NOYW1lPVwibXgtMiB0ZXh0LWdyYXktMzAwXCI+fDwvc3Bhbj59XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMiBweS0wLjUgcm91bmRlZC1mdWxsIHRleHQteHNcIiBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogdGFnLmNvbG9yID8gYCR7dGFnLmNvbG9yfTMwYCA6ICcjZTVlN2ViJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiB0YWcuY29sb3IgfHwgJyM0YjU1NjMnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHt0YWcubmFtZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7aXNFZGl0aW5nICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXQgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWwtMSB3LTMgaC0zXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3RhZ0lkcy5pbmNsdWRlcyh0YWcuaWQpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChlLnRhcmdldC5jaGVja2VkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0VGFnSWRzKFsuLi50YWdJZHMsIHRhZy5pZF0pO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFRhZ0lkcyh0YWdJZHMuZmlsdGVyKGlkID0+IGlkICE9PSB0YWcuaWQpKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvUmVhY3QuRnJhZ21lbnQ+XHJcbiAgICAgICAgICAgICAgICAgICkpXHJcbiAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICBuYXZUYWdzLm1hcCgodGFnLCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxSZWFjdC5GcmFnbWVudCBrZXk9e3RhZy5pZH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7aW5kZXggPiAwICYmIDxzcGFuIGNsYXNzTmFtZT1cIm14LTIgdGV4dC1ncmF5LTMwMFwiPnw8L3NwYW4+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICB7aXNFZGl0aW5nICYmIG5hdlRhZ3MubGVuZ3RoID4gMSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGVUYWcodGFnLmlkKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtbC0xIHRleHQtcmVkLTUwMCBob3Zlcjp0ZXh0LXJlZC03MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwi5Yig6Zmk5qCH562+XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaCBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvUmVhY3QuRnJhZ21lbnQ+XHJcbiAgICAgICAgICAgICAgICAgICkpXHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICovfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiDnroDmtIHniYjlpLTpg6ggLSDlp4vnu4jmmL7npLogKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHJlbGF0aXZlIHAtNiBmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydCBtZDppdGVtcy1jZW50ZXIgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTMwMCAke3Nob3dVSSA/ICdvcGFjaXR5LTEwMCcgOiAnb3BhY2l0eS0wIHBvaW50ZXItZXZlbnRzLW5vbmUnfWB9PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxyXG4gICAgICAgICAgICAgIHtpc0VkaXRpbmcgPyAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIG1kOnctMy80IHAtMiB0ZXh0LTJ4bCBmb250LWJvbGQgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3RpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0VGl0bGUoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6L6T5YWl5rS75Yqo5qCH6aKYXCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgbWQ6dy0zLzQgcC0yIHRleHQtc20gYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e29yZ2FuaXplcn1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldE9yZ2FuaXplcihlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLovpPlhaXkuLvlip7mlrlcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTgwMCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHt0aXRsZX1cclxuICAgICAgICAgICAgICAgICAgICA8U3BhcmtsZXMgY2xhc3NOYW1lPVwibWwtMiB3LTUgaC01IHRleHQteWVsbG93LTQwMCBhbmltYXRlLXB1bHNlXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPC9oMT5cclxuICAgICAgICAgICAgICAgICAge29yZ2FuaXplciAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LXNtXCI+5Li75Yqe5pa5OiB7b3JnYW5pemVyfTwvcD5cclxuICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgIHtpc0VkaXRpbmcgPyAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yIG10LTJcIj5cclxuICAgICAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMS41IHRleHQtc20gYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3N0YXJ0VGltZX1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFN0YXJ0VGltZShlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNlbGYtY2VudGVyXCI+LTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMS41IHRleHQtc20gYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2VuZFRpbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRFbmRUaW1lKGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtc20gbXQtMiBmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8taW5kaWdvLTUwMCBoLTEuNSB3LTEuNSByb3VuZGVkLWZ1bGwgbXItMiBhbmltYXRlLXB1bHNlXCI+PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICDmtLvliqjml7bpl7TvvJp7Zm9ybWF0RGF0ZShzdGFydFRpbWUpfSAtIHtmb3JtYXREYXRlKGVuZFRpbWUpfVxyXG4gICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMyBtdC00IG1kOm10LTBcIj5cclxuICAgICAgICAgICAgICB7LyogPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0RXhwYW5kZWQoIWV4cGFuZGVkKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTUgcHktMi41IHRleHQtc20gZm9udC1tZWRpdW0gcm91bmRlZC1mdWxsIGJvcmRlciBib3JkZXItZ3JheS0yMDAgdGV4dC1ncmF5LTYwMCBob3ZlcjpiZy1ncmF5LTUwIGhvdmVyOnNjYWxlLTEwNSBhY3RpdmU6c2NhbGUtOTUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICB7ZXhwYW5kZWQgPyAoXHJcbiAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAg5pS26LW36K+m5oOFXHJcbiAgICAgICAgICAgICAgICAgICAgPENoZXZyb25VcCBjbGFzc05hbWU9XCJtbC0yIHctNCBoLTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAg5bGV5byA6K+m5oOFXHJcbiAgICAgICAgICAgICAgICAgICAgPENoZXZyb25Eb3duIGNsYXNzTmFtZT1cIm1sLTIgdy00IGgtNFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICA8L2J1dHRvbj4gKi99XHJcblxyXG4gICAgICAgICAgICAgIHshaXNFZGl0aW5nICYmIChcclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU3VibWl0Q2xpY2t9XHJcbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0NoZWNraW5nfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBweC01IHB5LTIuNSB0ZXh0LXNtIGZvbnQtbWVkaXVtIHJvdW5kZWQtZnVsbCAke2hhc1N1Ym1pdHRlZFxyXG4gICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi01MDAgdG8tZW1lcmFsZC01MDAgdGV4dC13aGl0ZSBob3ZlcjpvcGFjaXR5LTkwIGhvdmVyOnNjYWxlLTEwNSBhY3RpdmU6c2NhbGUtOTUgc2hhZG93LW1kIGhvdmVyOnNoYWRvdy1sZydcclxuICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8taW5kaWdvLTUwMCB0ZXh0LXdoaXRlIGhvdmVyOm9wYWNpdHktOTAgaG92ZXI6c2NhbGUtMTA1IGFjdGl2ZTpzY2FsZS05NSBzaGFkb3ctbWQgaG92ZXI6c2hhZG93LWxnJ1xyXG4gICAgICAgICAgICAgICAgICAgIH0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwYH1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAge2lzQ2hlY2tpbmcgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImxvYWRpbmctc3Bpbm5lciBtci0yXCI+PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAg5qOA5p+l54q25oCB5LitXHJcbiAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICAgICkgOiBoYXNTdWJtaXR0ZWQgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgPD7lt7LmiqXlkI0g5YmN5b6A5Lu75YqhPC8+XHJcbiAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPD7nq4vljbPmiqXlkI08Lz5cclxuICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNoYXJlfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcC0yLjUgdGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWJsdWUtNTAwIGhvdmVyOmJnLWdyYXktNTAgcm91bmRlZC1mdWxsIGhvdmVyOnNjYWxlLTExMCBhY3RpdmU6c2NhbGUtOTUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8U2hhcmUyIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIOWqkuS9k+i1hOa6kOWxleekuuWMuuWfnyAqL31cclxuICAgICAgeyhwcm9tb3Rpb25JbWFnZSB8fCBiYWNrZ3JvdW5kSW1hZ2UgfHwgZ2FsbGVyeUltYWdlcyB8fCBhdHRhY2htZW50RmlsZXMpICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTZcIj5cclxuICAgICAgICAgIDxBY3Rpdml0eU1lZGlhR2FsbGVyeVxyXG4gICAgICAgICAgICBwcm9tb3Rpb25JbWFnZT17cHJvbW90aW9uSW1hZ2V9XHJcbiAgICAgICAgICAgIGJhY2tncm91bmRJbWFnZT17YmFja2dyb3VuZEltYWdlfVxyXG4gICAgICAgICAgICBnYWxsZXJ5SW1hZ2VzPXtnYWxsZXJ5SW1hZ2VzfVxyXG4gICAgICAgICAgICBhdHRhY2htZW50RmlsZXM9e2F0dGFjaG1lbnRGaWxlc31cclxuICAgICAgICAgICAgYWN0aXZpdHlUaXRsZT17dGl0bGV9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApfVxyXG5cclxuICAgICAgey8qIOeZu+W9leWvueivneahhiAtIOWPquWcqOWuouaIt+err+a4suafkyAqL31cclxuICAgICAge2lzQ2xpZW50ICYmIChcclxuICAgICAgICA8TG9naW5EaWFsb2dcclxuICAgICAgICAgIGlzT3Blbj17c2hvd0xvZ2luRGlhbG9nfVxyXG4gICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd0xvZ2luRGlhbG9nKGZhbHNlKX1cclxuICAgICAgICAgIG9uU3VjY2Vzcz17aGFuZGxlTG9naW5TdWNjZXNzfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICl9XHJcbiAgICA8Lz5cclxuICApXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IEFjdGl2aXR5SGVhZGVyICJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiSW1hZ2UiLCJTaGFyZTIiLCJTcGFya2xlcyIsIkVkaXQiLCJTYXZlIiwiWCIsIkxvYWRlciIsInNlbGVjdFVzZXJTdGF0ZSIsInNldFVzZXIiLCJ1c2VTZWxlY3RvciIsInVzZURpc3BhdGNoIiwiQWN0aXZpdHlNZWRpYUdhbGxlcnkiLCJVcGxvYWQiLCJBbnRVcGxvYWQiLCJNb2RhbCIsIlBsdXNPdXRsaW5lZCIsIkxvZ2luRGlhbG9nIiwiYWN0aXZpdHlBcGkiLCJhY3Rpdml0eVRhZ0FwaSIsImFjdGl2aXR5UmVnaXN0cmF0aW9uQXBpIiwiQWN0aXZpdHlUeXBlIiwiZXZlbnRzVGFza0FwaSIsInVwbG9hZEFwaSIsInVzZXJBcGkiLCJHZXROb3RpZmljYXRpb24iLCJ1c2VSb3V0ZXIiLCJ1c2VQYXRobmFtZSIsImZvcm1hdERhdGUiLCJkYXRlU3RyaW5nIiwiZGF0ZSIsIkRhdGUiLCJpc05hTiIsImdldFRpbWUiLCJ5ZWFyIiwiZ2V0RnVsbFllYXIiLCJtb250aCIsIlN0cmluZyIsImdldE1vbnRoIiwicGFkU3RhcnQiLCJkYXkiLCJnZXREYXRlIiwiQWN0aXZpdHlIZWFkZXIiLCJ0aXRsZSIsImluaXRpYWxUaXRsZSIsInN0YXJ0VGltZSIsImluaXRpYWxTdGFydFRpbWUiLCJlbmRUaW1lIiwiaW5pdGlhbEVuZFRpbWUiLCJiYW5uZXJJbWFnZSIsImluaXRpYWxCYW5uZXJJbWFnZSIsImV4cGFuZGVkIiwic2V0RXhwYW5kZWQiLCJhY3Rpdml0eUlkIiwidGFncyIsIm9yZ2FuaXplciIsImluaXRpYWxPcmdhbml6ZXIiLCJhY3Rpdml0eVR5cGUiLCJXT1JLIiwib25SZWZyZXNoV29ya3MiLCJwcm9tb3Rpb25JbWFnZSIsImJhY2tncm91bmRJbWFnZSIsImdhbGxlcnlJbWFnZXMiLCJhdHRhY2htZW50RmlsZXMiLCJjb25zb2xlIiwibG9nIiwibm90aWZpY2F0aW9uIiwicm91dGVyIiwicGF0aG5hbWUiLCJ1c2VyU3RhdGUiLCJkaXNwYXRjaCIsImlzQWRtaW4iLCJyb2xlSWQiLCJzeW5jVXNlclN0YXRlIiwibG9jYWxVc2VyIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInVzZXJEYXRhIiwiSlNPTiIsInBhcnNlIiwiaXNMb2dnZWRJbiIsInVzZXJJZCIsInBob25lIiwidHJpbSIsInJlc3BvbnNlIiwiZ2V0VXNlckluZm8iLCJjb2RlIiwiZGF0YSIsInVwZGF0ZWRVc2VyRGF0YSIsIm5pY2tOYW1lIiwiYXZhdGFyVXJsIiwiZ2VuZGVyIiwib2xkUGhvbmUiLCJuZXdQaG9uZSIsInVwZGF0ZWQiLCJzZXRJdGVtIiwic3RyaW5naWZ5IiwiZXJyb3IiLCJoYXNQaG9uZUNoYW5nZWQiLCJoYXNSb2xlQ2hhbmdlZCIsImhhc0xvZ2luU3RhdHVzQ2hhbmdlZCIsInBob25lQ2hhbmdlZCIsInJvbGVDaGFuZ2VkIiwibG9naW5TdGF0dXNDaGFuZ2VkIiwib2xkUm9sZSIsIm5ld1JvbGUiLCJjYXRjaCIsImhhbmRsZVZpc2liaWxpdHlDaGFuZ2UiLCJkb2N1bWVudCIsInZpc2liaWxpdHlTdGF0ZSIsImhhbmRsZUZvY3VzIiwiYWRkRXZlbnRMaXN0ZW5lciIsIndpbmRvdyIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJjaGVja0lzU3R1ZGVudCIsInVzZXJTdHIiLCJ1c2VyUm9sZUlkIiwic2hvd1N0dWRlbnRPbmx5TW9kYWwiLCJjb25maXJtIiwiY29udGVudCIsIm9rVGV4dCIsImNhbmNlbFRleHQiLCJvbkNhbmNlbCIsImluZm8iLCJnZXRSZWdpc3RyYXRpb25TdGF0dXNUZXh0IiwicmVnaXN0cmF0aW9uU3RhdHVzIiwibmF2VGFncyIsInNldE5hdlRhZ3MiLCJpZCIsInRleHQiLCJ0YWdJZHMiLCJzZXRUYWdJZHMiLCJlZGl0aW5nVGFnSWQiLCJzZXRFZGl0aW5nVGFnSWQiLCJlZGl0aW5nVGFnVGV4dCIsInNldEVkaXRpbmdUYWdUZXh0Iiwic2hvd0xvZ2luRGlhbG9nIiwic2V0U2hvd0xvZ2luRGlhbG9nIiwiaGFzU3VibWl0dGVkIiwic2V0SGFzU3VibWl0dGVkIiwiaXNDaGVja2luZyIsInNldElzQ2hlY2tpbmciLCJzZXRSZWdpc3RyYXRpb25TdGF0dXMiLCJ1cGxvYWRMb2FkaW5nIiwic2V0VXBsb2FkTG9hZGluZyIsImZpbGVMaXN0Iiwic2V0RmlsZUxpc3QiLCJzaG93VUkiLCJzZXRTaG93VUkiLCJpc0NsaWVudCIsInNldElzQ2xpZW50IiwiY2hlY2tSZWdpc3RyYXRpb25TdGF0dXMiLCJpc1N0dWRlbnQiLCJjaGVja1JlZ2lzdHJhdGlvbiIsIk51bWJlciIsInN1Ym1pdHRlZCIsInN1Ym1pdCIsInN0YXR1cyIsInVuZGVmaW5lZCIsImNoZWNrVXNlclN1Ym1pdHRlZCIsImZldGNoQWN0aXZpdHlUYWdzIiwiZ2V0QWN0aXZpdHlUYWdzIiwidWlkIiwibmFtZSIsInVybCIsImhhbmRsZUNhbmNlbEVkaXQiLCJzZXRUaXRsZSIsInNldFN0YXJ0VGltZSIsInNldEVuZFRpbWUiLCJzZXRCYW5uZXJJbWFnZSIsInNldE9yZ2FuaXplciIsInNldElzRWRpdGluZyIsImhhbmRsZVNhdmVFZGl0Iiwic2V0SXNTYXZpbmciLCJ1cGRhdGVEYXRhIiwiY292ZXJJbWFnZSIsImRldGFpbENvbnRlbnQiLCJydWxlc0NvbnRlbnQiLCJhd2FyZHNDb250ZW50IiwidXBkYXRlIiwidGFnUmVzcG9uc2UiLCJzdWNjZXNzIiwibWVzc2FnZSIsImhhbmRsZVRhZ0NsaWNrIiwidGFnIiwiaXNFZGl0aW5nIiwiaW5jbHVkZXMiLCJmaWx0ZXIiLCJoYW5kbGVUYWdFZGl0Q2FuY2VsIiwiaGFuZGxlQWRkVGFnIiwibGVuZ3RoIiwibmV3VGFnIiwibm93IiwiaGFuZGxlRGVsZXRlVGFnIiwiaGFuZGxlR29Ub1Rhc2siLCJsb2FkaW5nTWVzc2FnZSIsImxvYWRpbmciLCJldmVudHNUYXNrUmVzcG9uc2UiLCJnZXRNeVRhc2tzIiwiZXZlbnRzVGFza3MiLCJyZWxhdGVkVGFzayIsImZpbmQiLCJ0YXNrIiwib3BlbiIsImNsb3NlIiwiaGFuZGxlU3VibWl0Q2xpY2siLCJsb2NhbFVzZXJTdHIiLCJwaG9uZUxlbmd0aCIsInBob25lVHlwZSIsInJlZGlyZWN0VXJsIiwicHVzaCIsImVuY29kZVVSSUNvbXBvbmVudCIsImhhbmRsZURpcmVjdFJlZ2lzdHJhdGlvbiIsImhhbmRsZUxvZ2luU3VjY2VzcyIsImhhbmRsZVN1Ym1pdFN1Y2Nlc3MiLCJvbk9rIiwiY3VycmVudFVybCIsImxvY2F0aW9uIiwiaHJlZiIsInN1Ym1pdERhdGEiLCJhZ3JlZW1lbnRBY2NlcHRlZCIsInBhcmVudENvbnNlbnRBY2NlcHRlZCIsInBhcmVudFNpZ25hdHVyZVBhdGgiLCJzaWduYXR1cmVUaW1lIiwidG9JU09TdHJpbmciLCJyZW1hcmsiLCJ0b0xvY2FsZVN0cmluZyIsInN1Ym1pdFJlZ2lzdHJhdGlvbiIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsInRhc2tFcnJvciIsIkVycm9yIiwiaGFuZGxlQ3VzdG9tUmVxdWVzdCIsIm9wdGlvbnMiLCJmaWxlIiwib25TdWNjZXNzIiwib25FcnJvciIsInVwbG9hZFRvT3NzIiwiZXJyIiwiaGFuZGxlUmVtb3ZlIiwiZGVsZXRlRnJvbU9zcyIsImhhbmRsZVNoYXJlIiwibmF2aWdhdG9yIiwiY2xpcGJvYXJkIiwid3JpdGVUZXh0IiwidGhlbiIsImRpdiIsImNsYXNzTmFtZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzdmciLCJ4bWxucyIsInZpZXdCb3giLCJmaWxsIiwic3Ryb2tlIiwic3Ryb2tlV2lkdGgiLCJwYXRoIiwiZCIsImxpbmUiLCJ4MSIsInkxIiwieDIiLCJ5MiIsImNpcmNsZSIsImN4IiwiY3kiLCJyIiwiZGlzYWJsZWQiLCJpc1NhdmluZyIsInNyYyIsImFsdCIsInByaW9yaXR5Iiwic3R5bGUiLCJvYmplY3RGaXQiLCJlIiwidGFyZ2V0IiwiYmFja2dyb3VuZCIsImlucHV0IiwidHlwZSIsInZhbHVlIiwib25DaGFuZ2UiLCJwbGFjZWhvbGRlciIsImxpc3RUeXBlIiwiY3VzdG9tUmVxdWVzdCIsIm9uUmVtb3ZlIiwibWF4Q291bnQiLCJzaG93VXBsb2FkTGlzdCIsInNob3dQcmV2aWV3SWNvbiIsInNob3dSZW1vdmVJY29uIiwib25QcmV2aWV3IiwibWFyZ2luVG9wIiwiaDEiLCJwIiwic3BhbiIsImFjdGl2aXR5VGl0bGUiLCJpc09wZW4iLCJvbkNsb3NlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx\n"));

/***/ })

});