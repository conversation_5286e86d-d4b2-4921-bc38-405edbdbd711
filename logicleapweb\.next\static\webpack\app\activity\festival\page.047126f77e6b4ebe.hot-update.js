"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/activity/festival/page",{

/***/ "(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx":
/*!*************************************************************!*\
  !*** ./app/activity/festival/components/ActivityHeader.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Share2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Share2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./lib/store.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ActivityMediaGallery */ \"(app-pages-browser)/./app/activity/festival/components/ActivityMediaGallery.tsx\");\n/* harmony import */ var _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Modal!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _components_login_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/login-dialog */ \"(app-pages-browser)/./components/login-dialog.tsx\");\n/* harmony import */ var _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/activity */ \"(app-pages-browser)/./lib/api/activity/index.ts\");\n/* harmony import */ var _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/activity/events-task */ \"(app-pages-browser)/./lib/api/activity/events-task.ts\");\n/* harmony import */ var _lib_api_user__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/user */ \"(app-pages-browser)/./lib/api/user.ts\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 临时实现formatDate函数，以解决模块导入问题\nconst formatDate = (dateString)=>{\n    // 在服务端渲染时，直接返回原始字符串，避免水合错误\n    if (false) {}\n    const date = new Date(dateString);\n    // 检查日期是否有效\n    if (isNaN(date.getTime())) {\n        return dateString; // 如果无效，返回原始字符串\n    }\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, \"0\");\n    const day = String(date.getDate()).padStart(2, \"0\");\n    return \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n};\nconst ActivityHeader = (param)=>{\n    let { title: initialTitle, startTime: initialStartTime, endTime: initialEndTime, bannerImage: initialBannerImage, activityId, organizer: initialOrganizer = \"\", activityType = _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.ActivityType.WORK, onRefreshWorks, // 媒体字段\n    promotionImage, backgroundImage, galleryImages, attachmentFiles } = param;\n    _s();\n    console.log(\"\\uD83D\\uDD0D [DEBUG] ActivityHeader 组件渲染\", {\n        activityId,\n        initialTitle\n    });\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_9__.GetNotification)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.usePathname)();\n    // 显示用的标题和时间\n    const title = initialTitle;\n    const startTime = initialStartTime;\n    const endTime = initialEndTime;\n    const bannerImage = initialBannerImage;\n    const organizer = initialOrganizer;\n    // 获取当前用户信息和dispatch\n    const userState = (0,react_redux__WEBPACK_IMPORTED_MODULE_11__.useSelector)(_lib_store__WEBPACK_IMPORTED_MODULE_3__.selectUserState);\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_11__.useDispatch)();\n    const isAdmin = (userState === null || userState === void 0 ? void 0 : userState.roleId) === 4;\n    // 同步用户状态的函数 - 增强版，从API获取最新用户信息\n    const syncUserState = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 开始同步用户状态\");\n            const localUser = localStorage.getItem(\"user\");\n            if (localUser) {\n                const userData = JSON.parse(localUser);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中的用户数据\", userData);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] Redux 中的用户数据\", userState);\n                // 如果用户已登录但phone为空，尝试从API获取最新用户信息\n                if (userData.isLoggedIn && userData.userId && (!userData.phone || userData.phone.trim() === \"\")) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户phone为空，从API获取最新用户信息\");\n                    try {\n                        const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_8__.userApi.getUserInfo(userData.userId);\n                        if (response.code === 200 && response.data) {\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取到最新用户信息\", response.data);\n                            // 构建更新后的用户数据\n                            const updatedUserData = {\n                                ...userData,\n                                phone: response.data.phone || \"\",\n                                nickName: response.data.nickName || userData.nickName,\n                                avatarUrl: response.data.avatarUrl || userData.avatarUrl,\n                                gender: response.data.gender || userData.gender,\n                                roleId: response.data.roleId || userData.roleId\n                            };\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 更新用户数据\", {\n                                oldPhone: userData.phone,\n                                newPhone: updatedUserData.phone,\n                                updated: updatedUserData\n                            });\n                            // 更新localStorage和Redux\n                            localStorage.setItem(\"user\", JSON.stringify(updatedUserData));\n                            dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(updatedUserData));\n                            return;\n                        }\n                    } catch (error) {\n                        console.error(\"\\uD83D\\uDD0D [DEBUG] 从API获取用户信息失败:\", error);\n                    }\n                }\n                // 检查关键字段是否有变化\n                const hasPhoneChanged = userData.phone !== (userState === null || userState === void 0 ? void 0 : userState.phone);\n                const hasRoleChanged = userData.roleId !== (userState === null || userState === void 0 ? void 0 : userState.roleId);\n                const hasLoginStatusChanged = userData.isLoggedIn !== (userState === null || userState === void 0 ? void 0 : userState.isLoggedIn);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态变化检查\", {\n                    phoneChanged: hasPhoneChanged,\n                    roleChanged: hasRoleChanged,\n                    loginStatusChanged: hasLoginStatusChanged,\n                    oldPhone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                    newPhone: userData.phone,\n                    oldRole: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                    newRole: userData.roleId\n                });\n                // 如果localStorage中的用户状态与Redux中的不一致，说明需要更新\n                if (hasPhoneChanged || hasRoleChanged || hasLoginStatusChanged) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户状态变化，同步Redux状态\");\n                    dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(userData));\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态无变化，无需同步\");\n                }\n            } else {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中无用户数据\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD0D [DEBUG] 检查用户状态失败:\", error);\n        }\n    };\n    // 监听页面焦点变化和路由变化，重新检查用户状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 页面加载时立即检查一次\n        syncUserState().catch(console.error);\n        const handleVisibilityChange = ()=>{\n            if (document.visibilityState === \"visible\") {\n                syncUserState().catch(console.error);\n            }\n        };\n        const handleFocus = ()=>{\n            syncUserState().catch(console.error);\n        };\n        // 添加多种事件监听确保状态同步\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n        window.addEventListener(\"focus\", handleFocus);\n        return ()=>{\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n            window.removeEventListener(\"focus\", handleFocus);\n        };\n    }, [\n        userState === null || userState === void 0 ? void 0 : userState.phone,\n        dispatch\n    ]);\n    // 工具函数：检查用户是否为学生身份\n    const checkIsStudent = ()=>{\n        try {\n            const userStr = localStorage.getItem(\"user\");\n            const localUser = userStr ? JSON.parse(userStr) : null;\n            const userRoleId = (localUser === null || localUser === void 0 ? void 0 : localUser.roleId) || (userState === null || userState === void 0 ? void 0 : userState.roleId);\n            return userRoleId === 1; // 学生身份的roleId为1\n        } catch (error) {\n            console.error(\"解析localStorage中的用户信息失败:\", error);\n            return false;\n        }\n    };\n    // 工具函数：显示身份验证失败提示\n    const showStudentOnlyModal = ()=>{\n        _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].confirm({\n            title: \"身份验证\",\n            content: \"抱歉，只有学生身份的用户才能参加此活动报名。如果您是学生，请联系管理员更新您的身份信息。\",\n            okText: \"我知道了\",\n            cancelText: \"联系管理员\",\n            onCancel: ()=>{\n                notification.info(\"如需帮助，请联系客服：400-123-4567\");\n            }\n        });\n    };\n    // 获取报名状态文本\n    const getRegistrationStatusText = ()=>{\n        switch(registrationStatus){\n            case 0:\n                return \"已取消\";\n            case 1:\n                return \"已报名\";\n            case 2:\n                return \"已审核通过\";\n            case 3:\n                return \"已拒绝\";\n            case 4:\n                return \"评审中\";\n            case 5:\n                return \"已获奖\";\n            case 6:\n                return \"审核中\";\n            default:\n                return \"已报名\";\n        }\n    };\n    // 登录和报名相关状态\n    const [showLoginDialog, setShowLoginDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSubmitted, setHasSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [registrationStatus, setRegistrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 报名状态：0-已取消 1-已报名 2-已审核通过 3-已拒绝 4-评审中 5-已获奖 6-审核中\n    const [showUI, setShowUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 添加客户端检测来解决水合错误\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    // 页面加载时检查报名状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkRegistrationStatus = async ()=>{\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态 - useEffect 触发\", {\n                isLoggedIn: userState.isLoggedIn,\n                roleId: userState.roleId,\n                phone: userState.phone,\n                isClient,\n                activityId\n            });\n            // 只有在用户已登录且是学生身份时才检查报名状态\n            const isStudent = checkIsStudent();\n            if (!userState.isLoggedIn || !isStudent) {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录或不是学生身份，重置报名状态\", {\n                    isLoggedIn: userState.isLoggedIn,\n                    isStudent\n                });\n                // 如果用户未登录或不是学生，重置状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n                return;\n            }\n            try {\n                var _response_data, _response_data1;\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 开始检查报名状态 API 调用\");\n                setIsChecking(true);\n                const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.checkRegistration(Number(activityId));\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 页面加载检查报名状态响应:\", response);\n                if ((response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200 && ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data)) {\n                    const { submitted, submit } = response.data.data;\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态检查结果\", {\n                        submitted,\n                        submit\n                    });\n                    setHasSubmitted(submitted);\n                    if (submit && submit.status !== undefined) {\n                        setRegistrationStatus(submit.status);\n                    } else {\n                        setRegistrationStatus(null);\n                    }\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态响应不符合预期，设置为未报名状态\");\n                    // 如果响应不符合预期，设置为未报名状态\n                    setHasSubmitted(false);\n                    setRegistrationStatus(null);\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态失败:\", error);\n                // 出错时设置为未报名状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n            } finally{\n                setIsChecking(false);\n            }\n        };\n        // 只在客户端执行，避免服务端渲染问题\n        if (isClient) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 客户端环境，执行报名状态检查\");\n            checkRegistrationStatus();\n        } else {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 非客户端环境，跳过报名状态检查\");\n        }\n    }, [\n        activityId,\n        userState.isLoggedIn,\n        userState.roleId,\n        userState.phone,\n        isClient\n    ]);\n    // 前往任务函数\n    const handleGoToTask = async ()=>{\n        try {\n            const loadingMessage = notification.loading(\"正在跳转到任务...\");\n            // 获取当前用户的赛事任务列表\n            const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n            if (eventsTaskResponse.data.code === 200) {\n                const eventsTasks = eventsTaskResponse.data.data;\n                // 查找与当前活动相关的赛事任务\n                const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                if (relatedTask) {\n                    // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                    window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                    notification.success(\"正在跳转到任务页面...\");\n                } else {\n                    // 如果没有找到对应任务，在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                    notification.info(\"未找到对应任务，已打开班级空间\");\n                }\n            } else {\n                // 如果获取任务失败，在新标签页中打开班级空间\n                window.open(\"/class-space\", \"_blank\");\n                notification.info(\"获取任务失败，已打开班级空间\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"跳转任务失败:\", error);\n            notification.error(\"跳转任务失败，请稍后重试\");\n        }\n    };\n    // 处理报名按钮点击\n    const handleSubmitClick = async ()=>{\n        var _userState_phone, _localUser_phone;\n        // 获取localStorage中的用户信息进行对比\n        const localUserStr = localStorage.getItem(\"user\");\n        const localUser = localUserStr ? JSON.parse(localUserStr) : null;\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 报名按钮点击 - 详细用户状态检查\", {\n            hasSubmitted,\n            \"Redux userState\": {\n                isLoggedIn: userState === null || userState === void 0 ? void 0 : userState.isLoggedIn,\n                phone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                phoneLength: userState === null || userState === void 0 ? void 0 : (_userState_phone = userState.phone) === null || _userState_phone === void 0 ? void 0 : _userState_phone.length,\n                phoneType: typeof (userState === null || userState === void 0 ? void 0 : userState.phone),\n                roleId: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                userId: userState === null || userState === void 0 ? void 0 : userState.userId,\n                nickName: userState === null || userState === void 0 ? void 0 : userState.nickName\n            },\n            \"localStorage user\": {\n                phone: localUser === null || localUser === void 0 ? void 0 : localUser.phone,\n                phoneLength: localUser === null || localUser === void 0 ? void 0 : (_localUser_phone = localUser.phone) === null || _localUser_phone === void 0 ? void 0 : _localUser_phone.length,\n                phoneType: typeof (localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                userId: localUser === null || localUser === void 0 ? void 0 : localUser.userId,\n                nickName: localUser === null || localUser === void 0 ? void 0 : localUser.nickName\n            },\n            \"phone validation\": {\n                \"userState.phone exists\": !!(userState === null || userState === void 0 ? void 0 : userState.phone),\n                \"userState.phone not empty\": (userState === null || userState === void 0 ? void 0 : userState.phone) && userState.phone.trim() !== \"\",\n                \"localUser.phone exists\": !!(localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                \"localUser.phone not empty\": (localUser === null || localUser === void 0 ? void 0 : localUser.phone) && (localUser === null || localUser === void 0 ? void 0 : localUser.phone.trim()) !== \"\"\n            }\n        });\n        // 临时调用API获取最新用户信息进行对比\n        if (userState === null || userState === void 0 ? void 0 : userState.userId) {\n            try {\n                var _response_data, _response_data_phone, _response_data1, _response_data2, _response_data3, _response_data4;\n                const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_8__.userApi.getUserInfo(userState.userId);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取的最新用户信息:\", {\n                    code: response.code,\n                    phone: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.phone,\n                    phoneLength: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_phone = _response_data1.phone) === null || _response_data_phone === void 0 ? void 0 : _response_data_phone.length,\n                    phoneType: typeof ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.phone),\n                    nickName: (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.nickName,\n                    userId: (_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : _response_data4.id\n                });\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 获取用户信息失败:\", error);\n            }\n        }\n        // 如果用户已报名，跳转到任务页面\n        if (hasSubmitted) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已报名，跳转到任务页面\");\n            handleGoToTask();\n            return;\n        }\n        // 检查用户是否登录\n        if (!userState || !userState.isLoggedIn) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录，跳转到登录页面\");\n            // 未登录，显示登录对话框\n            const redirectUrl = pathname || \"/home\";\n            router.push(\"/login?redirect=\".concat(encodeURIComponent(redirectUrl)));\n            return;\n        }\n        // 检查用户身份：只有学生（roleId为1）才能报名\n        const isStudent = checkIsStudent();\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 检查用户身份\", {\n            isStudent,\n            roleId: userState === null || userState === void 0 ? void 0 : userState.roleId\n        });\n        if (!isStudent) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户不是学生身份，显示身份验证弹窗\");\n            showStudentOnlyModal();\n            return;\n        }\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 身份验证通过，开始报名流程\");\n        // 身份验证通过，直接进行报名\n        handleDirectRegistration();\n    };\n    // 处理登录成功\n    const handleLoginSuccess = ()=>{\n        notification.success(\"登录成功\");\n    // 登录成功后，页面的useEffect会自动检查报名状态\n    // 不需要在这里重复检查，让useEffect处理状态更新\n    };\n    // 直接报名处理函数\n    const handleDirectRegistration = async ()=>{\n        try {\n            var _response_data;\n            // 检查用户是否绑定了手机号\n            if (!userState.phone || userState.phone.trim() === \"\") {\n                // 显示确认对话框\n                _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].confirm({\n                    title: \"需要绑定手机号\",\n                    content: \"报名活动需要绑定手机号，是否前往绑定？\",\n                    okText: \"前往绑定\",\n                    cancelText: \"取消\",\n                    onOk: ()=>{\n                        // 跳转到登录页面，并在URL中添加参数表示需要绑定手机号\n                        const currentUrl = window.location.href;\n                        router.push(\"/login?needBindPhone=true&redirect=\".concat(encodeURIComponent(currentUrl)));\n                    },\n                    onCancel: ()=>{\n                        notification.info(\"未绑定手机号，无法报名活动\");\n                    }\n                });\n                return;\n            }\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已绑定手机号，继续报名流程\");\n            // 显示加载提示\n            const loadingMessage = notification.loading(\"正在提交报名...\");\n            // 提交报名数据（使用默认值，跳过协议确认步骤）\n            const submitData = {\n                activityId: Number(activityId),\n                agreementAccepted: true,\n                parentConsentAccepted: true,\n                parentSignaturePath: \"\",\n                signatureTime: new Date().toISOString(),\n                remark: \"快速报名时间：\".concat(new Date().toLocaleString(\"zh-CN\"))\n            };\n            const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.submitRegistration(submitData);\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                // 更新UI状态\n                setHasSubmitted(true);\n                setRegistrationStatus(1);\n                notification.success(\"报名成功！正在跳转到班级空间...\");\n                // 通知页面刷新参赛作品列表\n                if (onRefreshWorks) {\n                    onRefreshWorks();\n                }\n                // 报名成功后，获取对应的赛事任务并跳转到班级空间\n                try {\n                    // 等待一小段时间确保后端创建赛事任务完成\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    // 获取当前用户的赛事任务列表\n                    const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n                    if (eventsTaskResponse.data.code === 200) {\n                        const eventsTasks = eventsTaskResponse.data.data;\n                        // 查找与当前活动相关的赛事任务\n                        const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                        if (relatedTask) {\n                            // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                            window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                        } else {\n                            // 如果没有找到对应任务，在新标签页中打开班级空间\n                            window.open(\"/class-space\", \"_blank\");\n                        }\n                    } else {\n                        // 如果获取任务失败，在新标签页中打开班级空间\n                        window.open(\"/class-space\", \"_blank\");\n                    }\n                } catch (taskError) {\n                    console.error(\"获取赛事任务失败:\", taskError);\n                    // 即使获取任务失败，也要在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                }\n            } else {\n                var _response_data1;\n                throw new Error(((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message) || \"报名失败\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"报名失败:\", error);\n            notification.error(error instanceof Error ? error.message : \"报名失败，请稍后重试\");\n        }\n    };\n    // 处理分享按钮点击事件\n    const handleShare = ()=>{\n        if (true) {\n            // 获取当前页面URL\n            const currentUrl = window.location.href;\n            // 复制到剪贴板\n            navigator.clipboard.writeText(currentUrl).then(()=>{\n                notification.success(\"活动链接已复制，快去分享吧！\");\n            }).catch((err)=>{\n                console.error(\"复制失败:\", err);\n                notification.error(\"复制链接失败，请手动复制\");\n            });\n        }\n    };\n    // 如果还没有在客户端挂载，返回一个简单的占位符\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-64 bg-gray-200 animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-300 rounded mb-2 w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded mb-1 w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 590,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 589,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 587,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n            lineNumber: 586,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-[300px] overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-transparent to-black/30 z-10 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 609,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-blue-500/10 animate-pulse z-0 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: bannerImage,\n                                            alt: title,\n                                            fill: true,\n                                            priority: true,\n                                            style: {\n                                                objectFit: \"cover\"\n                                            },\n                                            className: \"z-10 hover:scale-105 transition-transform duration-700\",\n                                            onError: (e)=>{\n                                                const target = e.target;\n                                                target.style.background = \"linear-gradient(120deg, #f3f4f6, #e5e7eb)\";\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-4 left-4 right-4 z-20 flex justify-between items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 607,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-800 flex items-center\",\n                                        children: [\n                                            title,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"ml-2 w-5 h-5 text-yellow-400 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    organizer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: [\n                                            \"主办方: \",\n                                            organizer\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm mt-2 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-500 to-indigo-500 h-1.5 w-1.5 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 711,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"活动时间：\",\n                                            formatDate(startTime),\n                                            \" - \",\n                                            formatDate(endTime)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 710,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3 mt-4 md:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSubmitClick,\n                                        disabled: isChecking,\n                                        className: \"flex items-center px-5 py-2.5 text-sm font-medium rounded-full \".concat(hasSubmitted ? \"bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\" : \"bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\", \" transition-all duration-200\"),\n                                        children: isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"loading-spinner mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                    lineNumber: 744,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"检查状态中\"\n                                            ]\n                                        }, void 0, true) : hasSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"已报名 前往任务\"\n                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"立即报名\"\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex items-center p-2.5 text-gray-500 hover:text-blue-500 hover:bg-gray-50 rounded-full hover:scale-110 active:scale-95 transition-all duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 758,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 716,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 700,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 603,\n                columnNumber: 7\n            }, undefined),\n            (promotionImage || backgroundImage || galleryImages || attachmentFiles) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    promotionImage: promotionImage,\n                    backgroundImage: backgroundImage,\n                    galleryImages: galleryImages,\n                    attachmentFiles: attachmentFiles,\n                    activityTitle: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                    lineNumber: 767,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 766,\n                columnNumber: 9\n            }, undefined),\n            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_login_dialog__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showLoginDialog,\n                onClose: ()=>setShowLoginDialog(false),\n                onSuccess: handleLoginSuccess\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 779,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ActivityHeader, \"LrQsrruGHwY+vh0xTSNWkYGLl0w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.usePathname,\n        react_redux__WEBPACK_IMPORTED_MODULE_11__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_11__.useDispatch\n    ];\n});\n_c = ActivityHeader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ActivityHeader);\nvar _c;\n$RefreshReg$(_c, \"ActivityHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx\n"));

/***/ })

});