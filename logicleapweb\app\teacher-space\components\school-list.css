.school-card {
  background: white;
  border: 1px solid rgb(229, 231, 235);
  border-radius: 16px;
  transition: all 0.3s;
}

.school-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #e6f4ff;
}

.school-icon-container {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f7ff;
  border-radius: 12px;
  transition: all 0.3s;
}

.group:hover .school-icon-container {
  background: #e6f4ff;
}

.stat-card {
  transition: all 0.3s;
}

.group:hover .stat-card {
  transform: translateY(-2px);
}

.school-card-btn {
  opacity: 0.9;
  transition: all 0.3s;
}

.group:hover .school-card-btn {
  opacity: 1;
  transform: translateX(4px);
}

/* 响应式布局样式 */
@media screen and (max-width: 1024px) {
  .school-card .stats-container {
    flex-direction: column;
    gap: 8px;
  }

  .school-card .stat-card {
    width: 100%;
  }
}

@media screen and (max-width: 768px) {
  .school-card .card-main-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .school-card .school-info {
    width: 100%;
  }

  .school-card .stats-container {
    width: 100%;
    flex-direction: row;
    gap: 12px;
  }

  .school-card .stat-card {
    flex: 1;
  }
}

@media screen and (max-width: 480px) {
  .school-card .card-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .school-card .stats-container {
    flex-direction: column;
  }

  .school-card .stat-card {
    width: 100%;
  }
} 