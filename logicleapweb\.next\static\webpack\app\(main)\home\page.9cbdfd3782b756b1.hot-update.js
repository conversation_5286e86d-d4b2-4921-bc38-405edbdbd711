"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/home/<USER>",{

/***/ "(app-pages-browser)/./app/components/userAuth/teacherAuthModal.tsx":
/*!******************************************************!*\
  !*** ./app/components/userAuth/teacherAuthModal.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/theme/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/auto-complete/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,FileExcelOutlined,FileImageOutlined,FileOutlined,FilePdfOutlined,FileWordOutlined,LoadingOutlined,PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/FileWordOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,FileExcelOutlined,FileImageOutlined,FileOutlined,FilePdfOutlined,FileWordOutlined,LoadingOutlined,PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/FileExcelOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,FileExcelOutlined,FileImageOutlined,FileOutlined,FilePdfOutlined,FileWordOutlined,LoadingOutlined,PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/FilePdfOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,FileExcelOutlined,FileImageOutlined,FileOutlined,FilePdfOutlined,FileWordOutlined,LoadingOutlined,PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/FileImageOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,FileExcelOutlined,FileImageOutlined,FileOutlined,FilePdfOutlined,FileWordOutlined,LoadingOutlined,PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/FileOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,FileExcelOutlined,FileImageOutlined,FileOutlined,FilePdfOutlined,FileWordOutlined,LoadingOutlined,PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,FileExcelOutlined,FileImageOutlined,FileOutlined,FilePdfOutlined,FileWordOutlined,LoadingOutlined,PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,FileExcelOutlined,FileImageOutlined,FileOutlined,FilePdfOutlined,FileWordOutlined,LoadingOutlined,PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _public_pca_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../public/pca.json */ \"(app-pages-browser)/./public/pca.json\");\n/* harmony import */ var _lib_api_school__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/school */ \"(app-pages-browser)/./lib/api/school.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var _lib_api_teacher_auth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/teacher-auth */ \"(app-pages-browser)/./lib/api/teacher-auth.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// 定义滚动条样式\nconst scrollbarStyles = \"\\n  .file-scroll-container::-webkit-scrollbar {\\n    height: 8px;\\n    background-color: #f1f1f1;\\n  }\\n  \\n  .file-scroll-container::-webkit-scrollbar-thumb {\\n    background-color: #888;\\n    border-radius: 4px;\\n  }\\n  \\n  .file-scroll-container::-webkit-scrollbar-thumb:hover {\\n    background-color: #555;\\n  }\\n  \\n  .file-scroll-container::-webkit-scrollbar-track {\\n    background-color: #f1f1f1;\\n    border-radius: 4px;\\n  }\\n  \\n  /* 添加模态框自适应样式 */\\n  @media screen and (max-width: 768px) {\\n    .teacher-auth-modal {\\n      width: 90% !important;\\n      margin: 0 auto;\\n    }\\n  }\\n  \\n  /* 模态框内容滚动样式 */\\n  .teacher-auth-modal-body {\\n    max-height: calc(80vh - 72px); /* 预留底部按钮的空间 */\\n    overflow-y: auto;\\n    padding-right: 8px;\\n  }\\n  \\n  .teacher-auth-modal-body::-webkit-scrollbar {\\n    width: 6px;\\n    background-color: #f1f1f1;\\n  }\\n  \\n  .teacher-auth-modal-body::-webkit-scrollbar-thumb {\\n    background-color: #888;\\n    border-radius: 3px;\\n  }\\n  \\n  .teacher-auth-modal-body::-webkit-scrollbar-thumb:hover {\\n    background-color: #555;\\n  }\\n  \\n  /* 左右布局容器 */\\n  .auth-flex-container {\\n    display: flex;\\n    flex-direction: row;\\n    gap: 16px;\\n    width: 100%;\\n    flex-wrap: wrap;\\n    margin-bottom: 24px; /* 增加底部边距，确保不会与提交按钮重叠 */\\n  }\\n  \\n  /* 左侧和右侧列 */\\n  .auth-column-left, .auth-column-right {\\n    flex: 1 1 calc(50% - 8px);\\n    min-width: 0;\\n    display: flex;\\n    flex-direction: column;\\n  }\\n  \\n  /* 底部横跨列 */\\n  .auth-column-bottom {\\n    flex: 1 1 100%;\\n    margin-top: 16px;\\n  }\\n  \\n  /* 响应式布局 */\\n  @media screen and (max-width: 768px) {\\n    .auth-flex-container {\\n      flex-direction: column;\\n      gap: 16px;\\n    }\\n   \\n    .auth-column-left, .auth-column-right {\\n      flex: 1 1 100%;\\n    }\\n  }\\n  \\n  /* 固定底部按钮样式 */\\n  .teacher-auth-modal-footer {\\n    position: absolute;\\n    bottom: 0;\\n    left: 0;\\n    width: 100%;\\n    padding: 16px 0;\\n    background-color: white;\\n    text-align: center;\\n    border-top: 1px solid #f0f0f0;\\n    border-radius: 0 0 16px 16px;\\n    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\\n  }\\n\";\n// 组件的dom,箭头函数的形参传入成功执行后的回调函数\nconst TeacherAuthModal = (param)=>{\n    let { onSuccess, visible, handleCloseTeacherAuthModal } = param;\n    _s();\n    // 逻辑层\n    // 获取主题变量\n    const { token } = _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useToken();\n    // 添加窗口大小状态\n    const [windowSize, setWindowSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width:  true ? window.innerWidth : 0,\n        height:  true ? window.innerHeight : 0\n    });\n    // 监听窗口大小变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        function handleResize() {\n            setWindowSize({\n                width: window.innerWidth,\n                height: window.innerHeight\n            });\n        }\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    // 根据窗口大小计算模态窗宽度\n    const getModalWidth = ()=>{\n        if (windowSize.width <= 576) return \"95%\";\n        if (windowSize.width <= 768) return 460;\n        return 780; // 增加宽度以适应左右布局\n    };\n    // 获取提示框\n    const nt = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 文件列表\n    const [fileList, setFileList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 本地文件对象列表，用于存储实际文件对象\n    const [localFiles, setLocalFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // 从redux中获取userId\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_8__.useSelector)((state)=>state.user.userState);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [schoolName, setSchoolName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredSchools, setFilteredSchools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [province, setProvince] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [city, setCity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [district, setDistrict] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [cities, setCities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [districts, setDistricts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 学校名词搜索框\n    const [schoolSearchText, setSchoolSearchText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 学校下拉框的展示\n    const [showSchoolDropdown, setShowSchoolDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 表单\n    const [form] = _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm();\n    // 提交状态\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 上传状态 - 新增\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 上传提示文字 - 新增\n    const [uploadingText, setUploadingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 钩子\n    // 省市区发生变动的时候更新下拉框的值\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (province) {\n            const selectedProvince = _public_pca_json__WEBPACK_IMPORTED_MODULE_3__.find((p)=>p.name === province);\n            if (selectedProvince && selectedProvince.children) {\n                if (isMunicipality(province)) {\n                    // 直辖市的情况下，城市列表为空，直接设置区县列表\n                    setCities([]);\n                    setDistricts(selectedProvince.children[0].children || []);\n                    if (!city) {\n                        setCity(province); // 直接将省份名称设为城市名称\n                    }\n                } else {\n                    // 普通省份的处理\n                    setCities(selectedProvince.children);\n                    if (city) {\n                        const selectedCity = selectedProvince.children.find((c)=>c.name === city);\n                        if (selectedCity && selectedCity.children) {\n                            setDistricts(selectedCity.children);\n                        }\n                    }\n                }\n            }\n        } else {\n            setCities([]);\n            setDistricts([]);\n            setCity(\"\");\n            setDistrict(\"\");\n        }\n    }, [\n        province,\n        city,\n        _public_pca_json__WEBPACK_IMPORTED_MODULE_3__\n    ]);\n    // 判断是否是直辖市\n    const isMunicipality = (provinceName)=>{\n        return [\n            \"北京市\",\n            \"上海市\",\n            \"天津市\",\n            \"重庆市\"\n        ].includes(provinceName);\n    };\n    const handleSubmit = async ()=>{\n        try {\n            // 表单验证\n            const values = await form.validateFields();\n            if (!schoolName) {\n                setError(\"请输入或选择学校\");\n                nt.error(\"请输入或选择学校\");\n                return;\n            }\n            if (!values.nickName) {\n                setError(\"请输入您的姓名\");\n                nt.error(\"请输入您的姓名\");\n                return;\n            }\n            // 设置提交状态\n            setSubmitting(true);\n            setUploading(true);\n            setUploadingText(\"提交认证申请中，请稍候...\");\n            // 拼接学校信息\n            const schoolInfo = \"\".concat(schoolName, \"|\").concat(province, \"|\").concat(city, \"|\").concat(district);\n            try {\n                // 上传所有本地文件到OSS\n                const attachments = [];\n                for(const uid in localFiles){\n                    if (fileList.some((file)=>file.uid === uid)) {\n                        try {\n                            const file = localFiles[uid];\n                            // 上传文件到OSS\n                            console.log(\"上传ing\");\n                            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_5__.uploadApi.uploadToOss(file);\n                            // 添加到附件列表\n                            attachments.push({\n                                url: url,\n                                name: file.name,\n                                type: file.type || \"application/octet-stream\",\n                                size: file.size || 0\n                            });\n                        } catch (error) {\n                            console.error(\"上传文件失败:\", error);\n                            nt.error(\"文件 \".concat(localFiles[uid].name, \" 上传失败\"));\n                            setSubmitting(false);\n                            setUploading(false);\n                            return;\n                        }\n                    }\n                }\n                // 准备提交数据\n                const submitData = {\n                    teacherId: user.userId,\n                    teacherName: values.nickName,\n                    schoolInfo: schoolInfo,\n                    attachments: attachments\n                };\n                console.log(\"提交教师认证信息:\", submitData);\n                // 调用提交接口\n                const response = await _lib_api_teacher_auth__WEBPACK_IMPORTED_MODULE_6__[\"default\"].submitAuth(submitData);\n                console.log(\"API响应:\", response);\n                // 检查响应状态\n                if (response.data && response.data.code === 200) {\n                    nt.success(\"认证申请提交成功，请等待审核\");\n                    // 关闭模态框并执行成功回调\n                    handleCloseTeacherAuthModal();\n                    onSuccess();\n                } else {\n                    var _response_data;\n                    // 处理业务错误响应 (HTTP 200但业务错误)\n                    const errorMessage = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.message) || \"认证申请提交失败\";\n                    nt.error(errorMessage);\n                    console.log(\"业务错误:\", response.data);\n                }\n            } catch (apiError) {\n                // 处理HTTP异常，这里可以捕获到后端抛出的HttpException\n                console.error(\"API错误:\", apiError);\n                // 在控制台打印出完整的错误对象，方便调试\n                console.log(\"完整错误对象:\", JSON.stringify(apiError, null, 2));\n                if (apiError.response) {\n                    // 处理后端返回的结构化错误\n                    const errorData = apiError.response.data;\n                    console.log(\"后端错误数据:\", errorData);\n                    // 直接显示完整的错误消息，确保用户能看到\n                    if (typeof errorData === \"object\" && errorData !== null) {\n                        console.log(\"错误消息是对象\");\n                        nt.error(errorData.message || \"提交失败，请稍后重试\");\n                    } else if (typeof errorData === \"string\") {\n                        console.log(\"错误消息是字符串\");\n                        nt.error(errorData || \"提交失败，请稍后重试\");\n                    } else {\n                        console.log(\"错误消息格式未知\");\n                        nt.error(\"请求失败 (\".concat(apiError.response.status, \"): 请稍后重试\"));\n                    }\n                } else if (apiError.request) {\n                    // 请求已发送但没有收到响应\n                    nt.error(\"服务器无响应，请检查网络连接\");\n                } else {\n                    // 请求配置错误\n                    nt.error(apiError.message || \"网络错误，请检查网络连接\");\n                }\n            }\n        } catch (error) {\n            console.error(\"表单验证或其他错误:\", error);\n            nt.error(error.message || \"提交失败，请检查表单信息\");\n        } finally{\n            setSubmitting(false);\n            setUploading(false);\n        }\n    };\n    // 认证信息包括的提示信息For列表\n    const handleCancel = ()=>{\n        // 上传中不允许关闭\n        if (uploading) return;\n        handleCloseTeacherAuthModal();\n    };\n    const handleSchoolSelect = (value, option)=>{\n        // 1. 尝试从option.data获取学校数据\n        // 2. 如果option本身是学校对象，则使用option\n        // 3. 如果以上都失败，则通过id在filteredSchools中查找学校\n        // 4. 如果找不到学校，则使用手动输入的值创建一个新学校对象\n        let selectedSchool = option === null || option === void 0 ? void 0 : option.data;\n        console.log(\"zww:\", selectedSchool);\n        if (!selectedSchool && (option === null || option === void 0 ? void 0 : option.label) && typeof option.value !== \"undefined\") {\n            selectedSchool = {\n                id: option.value,\n                schoolName: option.label\n            };\n        }\n        if (!selectedSchool && typeof value !== \"undefined\") {\n            if (typeof value === \"string\") {\n                // 用户输入的自定义学校，创建一个新的学校对象\n                const currentProvince = province || \"\";\n                const currentCity = city || \"\";\n                const currentDistrict = district || \"\";\n                selectedSchool = {\n                    id: \"custom-\".concat(Date.now()),\n                    schoolName: value,\n                    province: currentProvince,\n                    city: currentCity,\n                    district: currentDistrict\n                };\n            } else {\n                // 通过id在已过滤的学校列表中查找\n                selectedSchool = filteredSchools.find((school)=>school.id === value);\n            }\n        }\n        if (!selectedSchool) {\n            console.error(\"无法找到所选学校的数据:\", {\n                value,\n                option\n            });\n            return;\n        }\n        // 从filteredSchools中找到完整的学校信息（包括省市区）\n        const completeSchoolInfo = filteredSchools.find((school)=>school.id === selectedSchool.id || school.schoolName === selectedSchool.schoolName) || selectedSchool;\n        console.log(\"选中学校数据:\", completeSchoolInfo);\n        setSchoolName(completeSchoolInfo.schoolName);\n        // 处理省市区信息，确保没有null字符串\n        const selectedProvince = completeSchoolInfo.province && completeSchoolInfo.province !== \"null\" ? completeSchoolInfo.province : \"\";\n        if (selectedProvince) {\n            setProvince(selectedProvince);\n            // 设置城市（如果是直辖市，则城市名与省份名相同）\n            const selectedCity = isMunicipality(selectedProvince) ? selectedProvince : completeSchoolInfo.city && completeSchoolInfo.city !== \"null\" ? completeSchoolInfo.city : \"\";\n            setCity(selectedCity);\n            // 设置区县\n            const selectedDistrict = completeSchoolInfo.district && completeSchoolInfo.district !== \"null\" ? completeSchoolInfo.district : \"\";\n            setDistrict(selectedDistrict);\n        }\n        // 隐藏学校下拉列表\n        setShowSchoolDropdown(false);\n    };\n    const fetchSchools = async (searchText)=>{\n        // 如果有搜索文本，则不需要考虑地区筛选条件\n        // 如果没有搜索文本，且没有完整的地区信息，则不搜索\n        if (!searchText && (!province || !isMunicipality(province) && !city || !district)) {\n            setFilteredSchools([]);\n            return;\n        }\n        try {\n            var _response_data;\n            const params = {};\n            if (searchText) {\n                params.keyword = searchText;\n            // 有搜索文本时可以不限制地区\n            } else {\n                // 无搜索文本时，必须有地区筛选\n                if (province) {\n                    params.province = province;\n                    if (!isMunicipality(province) && city) {\n                        params.city = city;\n                    }\n                    if (district) {\n                        params.district = district;\n                    }\n                }\n            }\n            console.log(\"搜索学校参数:\", params);\n            const response = await _lib_api_school__WEBPACK_IMPORTED_MODULE_4__.schoolApi.getList(params);\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                var _response_data1, _response_data2;\n                // 获取学校列表\n                let schools = ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data) || ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.list) || response.data || [];\n                // 处理可能的null字符串\n                schools = schools.map((school)=>({\n                        ...school,\n                        province: school.province && school.province !== \"null\" ? school.province : \"\",\n                        city: school.city && school.city !== \"null\" ? school.city : \"\",\n                        district: school.district && school.district !== \"null\" ? school.district : \"\"\n                    }));\n                console.log(\"获取到学校列表:\", schools);\n                setFilteredSchools(schools);\n            } else {\n                console.warn(\"搜索学校接口返回错误:\", response);\n                setFilteredSchools([]);\n            }\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            setFilteredSchools([]);\n        }\n    };\n    // 获取文件图标\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes(\"word\") || fileType.includes(\"docx\") || fileType.includes(\"doc\")) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                style: {\n                    fontSize: \"28px\",\n                    color: \"#2B5797\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                lineNumber: 509,\n                columnNumber: 14\n            }, undefined);\n        } else if (fileType.includes(\"excel\") || fileType.includes(\"xlsx\") || fileType.includes(\"xls\") || fileType.includes(\"csv\")) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                style: {\n                    fontSize: \"28px\",\n                    color: \"#1D6F42\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                lineNumber: 511,\n                columnNumber: 14\n            }, undefined);\n        } else if (fileType.includes(\"pdf\")) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                style: {\n                    fontSize: \"28px\",\n                    color: \"#FF0000\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                lineNumber: 513,\n                columnNumber: 14\n            }, undefined);\n        } else if (fileType.includes(\"image/\")) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                style: {\n                    fontSize: \"28px\",\n                    color: \"#FFB400\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                lineNumber: 515,\n                columnNumber: 14\n            }, undefined);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                style: {\n                    fontSize: \"28px\",\n                    color: \"#8C8C8C\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                lineNumber: 517,\n                columnNumber: 14\n            }, undefined);\n        }\n    };\n    // 获取文件扩展名\n    const getFileExtension = (fileName)=>{\n        const parts = fileName.split(\".\");\n        if (parts.length > 1) {\n            return parts[parts.length - 1].toUpperCase();\n        }\n        return \"\";\n    };\n    // 自定义文件项渲染\n    const customFileItemRender = (originNode, file, fileList)=>{\n        var _file_type;\n        const isImage = (_file_type = file.type) === null || _file_type === void 0 ? void 0 : _file_type.startsWith(\"image/\");\n        // 如果是图片，使用默认渲染\n        if (isImage && file.thumbUrl) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"custom-upload-item\",\n                style: {\n                    position: \"relative\"\n                },\n                children: [\n                    originNode,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"file-delete-icon\",\n                        style: {\n                            position: \"absolute\",\n                            top: \"0\",\n                            right: \"0\",\n                            background: \"rgba(0,0,0,0.65)\",\n                            width: \"22px\",\n                            height: \"22px\",\n                            borderRadius: \"0 0 0 8px\",\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            cursor: \"pointer\"\n                        },\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            // 直接调用上传组件的onRemove方法\n                            handleRemoveFile(file);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            style: {\n                                color: \"#fff\",\n                                fontSize: \"14px\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                            lineNumber: 559,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                lineNumber: 537,\n                columnNumber: 9\n            }, undefined);\n        }\n        // 非图片类型，自定义渲染\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"custom-file-card\",\n            style: {\n                width: \"104px\",\n                height: \"104px\",\n                border: \"1px solid \".concat(token.colorBorderSecondary),\n                borderRadius: \"12px\",\n                padding: \"8px\",\n                display: \"inline-flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                position: \"relative\",\n                background: token.colorFillQuaternary,\n                boxShadow: \"0 2px 8px \".concat(token.colorBgElevated),\n                transition: \"all 0.3s\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"file-delete-icon\",\n                    style: {\n                        position: \"absolute\",\n                        top: \"0\",\n                        right: \"0\",\n                        background: \"rgba(0,0,0,0.65)\",\n                        width: \"22px\",\n                        height: \"22px\",\n                        borderRadius: \"0 0 0 12px\",\n                        display: \"flex\",\n                        justifyContent: \"center\",\n                        alignItems: \"center\",\n                        cursor: \"pointer\",\n                        transition: \"background 0.2s\"\n                    },\n                    onClick: (e)=>{\n                        e.stopPropagation();\n                        handleRemoveFile(file);\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        style: {\n                            color: \"#fff\",\n                            fontSize: \"14px\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                        lineNumber: 603,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                    lineNumber: 583,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        marginTop: \"10px\"\n                    },\n                    children: getFileIcon(file.type || \"\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                    lineNumber: 606,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: \"center\",\n                        width: \"100%\",\n                        marginTop: \"4px\",\n                        overflow: \"hidden\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Text, {\n                            ellipsis: {\n                                tooltip: file.name\n                            },\n                            style: {\n                                fontSize: \"12px\",\n                                lineHeight: \"1.2\"\n                            },\n                            children: file.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"11px\",\n                                color: token.colorTextSecondary,\n                                marginTop: \"2px\",\n                                background: token.colorFillSecondary,\n                                padding: \"0 4px\",\n                                borderRadius: \"4px\",\n                                display: \"inline-block\"\n                            },\n                            children: getFileExtension(file.name)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                    lineNumber: 610,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n            lineNumber: 567,\n            columnNumber: 7\n        }, undefined);\n    };\n    // 处理文件删除\n    const handleRemoveFile = (file)=>{\n        // 从文件列表中移除\n        setFileList(fileList.filter((item)=>item.uid !== file.uid));\n        // 从本地文件映射中移除\n        setLocalFiles((prev)=>{\n            const newLocalFiles = {\n                ...prev\n            };\n            delete newLocalFiles[file.uid];\n            return newLocalFiles;\n        });\n        // 如果有临时URL，需要释放\n        if (file.thumbUrl && file.thumbUrl.startsWith(\"blob:\")) {\n            URL.revokeObjectURL(file.thumbUrl);\n        }\n    };\n    // 样式对象\n    const modalStyles = {\n        header: {\n            marginBottom: \"24px\",\n            textAlign: \"center\",\n            fontSize: windowSize.width <= 576 ? \"20px\" : \"24px\",\n            fontWeight: \"bold\",\n            color: token.colorTextHeading\n        },\n        sectionTitle: {\n            fontSize: windowSize.width <= 576 ? \"14px\" : \"16px\",\n            fontWeight: \"bold\",\n            marginBottom: \"12px\",\n            color: token.colorTextHeading,\n            position: \"relative\",\n            paddingLeft: \"12px\",\n            display: \"flex\",\n            alignItems: \"center\"\n        },\n        sectionTitleBefore: {\n            content: '\"\"',\n            position: \"absolute\",\n            left: 0,\n            top: \"50%\",\n            transform: \"translateY(-50%)\",\n            width: \"4px\",\n            height: \"16px\",\n            backgroundColor: token.colorPrimary,\n            borderRadius: \"2px\"\n        },\n        formContainer: {\n            padding: \"0 8px\"\n        },\n        listItem: {\n            margin: \"6px 0\",\n            fontSize: \"14px\",\n            color: token.colorTextSecondary\n        },\n        uploadSection: {\n            marginBottom: \"24px\",\n            padding: \"16px\",\n            borderRadius: \"12px\",\n            background: token.colorFillTertiary\n        },\n        submitButton: {\n            borderRadius: \"32px\",\n            height: \"40px\",\n            padding: \"0 32px\",\n            fontSize: \"16px\",\n            boxShadow: \"0 4px 12px \".concat(token.colorPrimaryBg),\n            transition: \"all 0.3s\"\n        }\n    };\n    //  视图层\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                children: scrollbarStyles\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                lineNumber: 713,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                open: visible,\n                zIndex: 1000,\n                onCancel: handleCancel,\n                footer: null,\n                centered: true,\n                maskClosable: !uploading,\n                closable: !uploading,\n                keyboard: !uploading,\n                width: getModalWidth(),\n                style: {\n                    borderRadius: \"16px\",\n                    overflow: \"hidden\",\n                    top: windowSize.width <= 576 ? 20 : undefined // 小屏幕时调整位置\n                },\n                bodyStyle: {\n                    padding: windowSize.width <= 576 ? \"16px 20px 88px\" : \"24px 32px 88px\",\n                    maxHeight: \"80vh\",\n                    overflowX: \"hidden\" // 防止水平滚动条\n                },\n                maskStyle: {\n                    backdropFilter: \"blur(4px)\",\n                    background: \"rgba(0, 0, 0, 0.45)\"\n                },\n                className: \"teacher-auth-modal\",\n                children: [\n                    uploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            left: 0,\n                            top: 0,\n                            width: \"100%\",\n                            height: \"100%\",\n                            background: \"rgba(0, 0, 0, 0.7)\",\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            zIndex: 10,\n                            borderRadius: \"16px\",\n                            color: \"white\",\n                            textAlign: \"center\",\n                            backdropFilter: \"blur(4px)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                indicator: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    style: {\n                                        fontSize: 36,\n                                        color: token.colorPrimary\n                                    },\n                                    spin: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                    lineNumber: 756,\n                                    columnNumber: 30\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                lineNumber: 756,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: 16,\n                                    fontSize: \"16px\",\n                                    fontWeight: 500\n                                },\n                                children: uploadingText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                lineNumber: 757,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                        lineNumber: 739,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"teacher-auth-modal-body\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: modalStyles.header,\n                                children: \"教师身份认证\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                lineNumber: 763,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                form: form,\n                                onFinish: handleSubmit,\n                                layout: \"vertical\",\n                                style: modalStyles.formContainer,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"auth-flex-container\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"auth-column-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: modalStyles.sectionTitle,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: modalStyles.sectionTitleBefore\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"个人信息\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                    label: \"姓名\",\n                                                    name: \"nickName\",\n                                                    rules: [\n                                                        {\n                                                            required: true,\n                                                            message: \"请输入您的姓名\"\n                                                        }\n                                                    ],\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        placeholder: \"请输入您的姓名\",\n                                                        style: {\n                                                            borderRadius: \"8px\",\n                                                            height: \"40px\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: modalStyles.sectionTitle,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: modalStyles.sectionTitleBefore\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 795,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"学校信息\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                    lineNumber: 794,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            style: {\n                                                                margin: \"8px 0 16px\",\n                                                                borderRadius: \"8px\",\n                                                                width: \"100%\"\n                                                            },\n                                                            placeholder: \"请输入学校名称搜索，也可直接输入自定义学校名\",\n                                                            value: schoolName,\n                                                            onChange: (value)=>{\n                                                                if (typeof value === \"string\") {\n                                                                    setSchoolName(value);\n                                                                }\n                                                            },\n                                                            onSelect: (value, option)=>handleSchoolSelect(value, option),\n                                                            onSearch: (value)=>{\n                                                                setSchoolSearchText(value);\n                                                                if (value && value.length >= 1) {\n                                                                    fetchSchools(value);\n                                                                    setShowSchoolDropdown(true);\n                                                                }\n                                                            },\n                                                            onDropdownVisibleChange: (open)=>{\n                                                                setShowSchoolDropdown(open);\n                                                                if (open && province && (isMunicipality(province) ? true : city) && district) {\n                                                                    fetchSchools();\n                                                                }\n                                                            },\n                                                            open: showSchoolDropdown,\n                                                            filterOption: (inputValue, option)=>{\n                                                                if (!(option === null || option === void 0 ? void 0 : option.value)) return false;\n                                                                return String(option.value).toLowerCase().includes(inputValue.toLowerCase());\n                                                            },\n                                                            defaultActiveFirstOption: false,\n                                                            notFoundContent: filteredSchools.length === 0 ? \"未找到相关学校，可直接输入学校名\" : !province && !schoolSearchText ? \"请先选择省份或直接搜索学校名称\" : !isMunicipality(province) && !city && !schoolSearchText ? \"请先选择城市\" : !district && !schoolSearchText ? \"请先选择区县\" : \"请选择学校\",\n                                                            options: filteredSchools.map((school)=>{\n                                                                const hasDuplicateName = filteredSchools.some((s)=>s.schoolName === school.schoolName && (s.province !== school.province || s.city !== school.city || s.district !== school.district));\n                                                                const formatLocation = ()=>{\n                                                                    const schoolProvince = school.province && school.province !== \"null\" ? school.province : \"\";\n                                                                    const schoolCity = schoolProvince && !isMunicipality(schoolProvince) ? school.city && school.city !== \"null\" ? school.city : \"\" : \"\";\n                                                                    const schoolDistrict = school.district && school.district !== \"null\" ? school.district : \"\";\n                                                                    if (!schoolProvince && !schoolCity && !schoolDistrict) {\n                                                                        return \"\";\n                                                                    }\n                                                                    return \"（\".concat(schoolProvince).concat(schoolCity).concat(schoolDistrict, \"）\");\n                                                                };\n                                                                return {\n                                                                    value: hasDuplicateName ? \"\".concat(school.schoolName).concat(formatLocation()) : school.schoolName,\n                                                                    label: hasDuplicateName ? \"\".concat(school.schoolName).concat(formatLocation()) : school.schoolName,\n                                                                    data: school\n                                                                };\n                                                            }),\n                                                            dropdownStyle: {\n                                                                maxHeight: 200,\n                                                                overflow: \"auto\",\n                                                                borderRadius: \"12px\",\n                                                                padding: \"8px\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"12px\",\n                                                                flexWrap: windowSize.width <= 576 ? \"wrap\" : \"nowrap\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    placeholder: \"省\",\n                                                                    value: province || undefined,\n                                                                    onChange: (value)=>{\n                                                                        setProvince(value);\n                                                                        setCity(\"\");\n                                                                        setDistrict(\"\");\n                                                                        setSchoolName(\"\");\n                                                                        setFilteredSchools([]);\n                                                                    },\n                                                                    style: {\n                                                                        flex: windowSize.width <= 576 ? \"1 1 100%\" : 1,\n                                                                        borderRadius: \"8px\"\n                                                                    },\n                                                                    dropdownStyle: {\n                                                                        borderRadius: \"12px\"\n                                                                    },\n                                                                    children: _public_pca_json__WEBPACK_IMPORTED_MODULE_3__.map((p)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Option, {\n                                                                            value: p.name,\n                                                                            children: p.name\n                                                                        }, p.code, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                            lineNumber: 893,\n                                                                            columnNumber: 25\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                    lineNumber: 879,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                !isMunicipality(province) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    placeholder: \"市\",\n                                                                    value: city || undefined,\n                                                                    onChange: (value)=>{\n                                                                        setCity(value);\n                                                                        setDistrict(\"\");\n                                                                        setSchoolName(\"\");\n                                                                        setFilteredSchools([]);\n                                                                    },\n                                                                    disabled: !province,\n                                                                    style: {\n                                                                        flex: windowSize.width <= 576 ? \"1 1 48%\" : 1,\n                                                                        borderRadius: \"8px\"\n                                                                    },\n                                                                    dropdownStyle: {\n                                                                        borderRadius: \"12px\"\n                                                                    },\n                                                                    children: cities.map((c)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Option, {\n                                                                            value: c.name,\n                                                                            children: c.name\n                                                                        }, c.code, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                            lineNumber: 914,\n                                                                            columnNumber: 27\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                    lineNumber: 900,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    placeholder: \"区\",\n                                                                    onChange: (value)=>{\n                                                                        setDistrict(value);\n                                                                        setSchoolName(\"\");\n                                                                        setFilteredSchools([]);\n                                                                    },\n                                                                    disabled: !province || !isMunicipality(province) && !city,\n                                                                    value: district,\n                                                                    style: {\n                                                                        flex: windowSize.width <= 576 ? \"1 1 48%\" : 1,\n                                                                        borderRadius: \"8px\"\n                                                                    },\n                                                                    dropdownStyle: {\n                                                                        borderRadius: \"12px\"\n                                                                    },\n                                                                    children: districts.map((d)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Option, {\n                                                                            value: d.name,\n                                                                            children: d.name\n                                                                        }, d.code, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                            lineNumber: 934,\n                                                                            columnNumber: 25\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                    lineNumber: 921,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 878,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: modalStyles.uploadSection,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: modalStyles.sectionTitle,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: modalStyles.sectionTitleBefore\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                    lineNumber: 945,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \"附加材料\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"12px\",\n                                                                marginTop: \"16px\",\n                                                                width: \"100%\",\n                                                                position: \"relative\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: \"flex\",\n                                                                    gap: \"12px\",\n                                                                    width: \"100%\",\n                                                                    overflowX: \"auto\",\n                                                                    paddingBottom: \"8px\",\n                                                                    WebkitOverflowScrolling: \"touch\",\n                                                                    scrollbarWidth: \"auto\",\n                                                                    whiteSpace: \"nowrap\"\n                                                                },\n                                                                className: \"file-scroll-container\",\n                                                                children: [\n                                                                    fileList.map((file)=>{\n                                                                        var _file_type;\n                                                                        const isImage = (_file_type = file.type) === null || _file_type === void 0 ? void 0 : _file_type.startsWith(\"image/\");\n                                                                        if (isImage && file.thumbUrl) {\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"custom-upload-item\",\n                                                                                style: {\n                                                                                    position: \"relative\",\n                                                                                    width: \"104px\",\n                                                                                    height: \"104px\",\n                                                                                    borderRadius: \"12px\",\n                                                                                    display: \"inline-block\",\n                                                                                    flexShrink: 0,\n                                                                                    /* 防止图片收缩 */ overflow: \"hidden\"\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                        src: file.thumbUrl,\n                                                                                        alt: file.name,\n                                                                                        style: {\n                                                                                            width: \"100%\",\n                                                                                            height: \"100%\",\n                                                                                            objectFit: \"cover\"\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                        lineNumber: 982,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"file-delete-icon\",\n                                                                                        style: {\n                                                                                            position: \"absolute\",\n                                                                                            top: \"0\",\n                                                                                            right: \"0\",\n                                                                                            background: \"rgba(0,0,0,0.65)\",\n                                                                                            width: \"22px\",\n                                                                                            height: \"22px\",\n                                                                                            borderRadius: \"0 0 0 8px\",\n                                                                                            display: \"flex\",\n                                                                                            justifyContent: \"center\",\n                                                                                            alignItems: \"center\",\n                                                                                            cursor: \"pointer\"\n                                                                                        },\n                                                                                        onClick: ()=>handleRemoveFile(file),\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                            style: {\n                                                                                                color: \"#fff\",\n                                                                                                fontSize: \"14px\"\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                            lineNumber: 1007,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                        lineNumber: 991,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, file.uid, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                lineNumber: 973,\n                                                                                columnNumber: 29\n                                                                            }, undefined);\n                                                                        } else {\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"custom-file-card\",\n                                                                                style: {\n                                                                                    width: \"104px\",\n                                                                                    height: \"104px\",\n                                                                                    border: \"1px solid \".concat(token.colorBorderSecondary),\n                                                                                    borderRadius: \"12px\",\n                                                                                    padding: \"8px\",\n                                                                                    display: \"inline-flex\",\n                                                                                    flexDirection: \"column\",\n                                                                                    alignItems: \"center\",\n                                                                                    justifyContent: \"space-between\",\n                                                                                    position: \"relative\",\n                                                                                    flexShrink: 0,\n                                                                                    /* 防止文件卡片收缩 */ background: token.colorFillQuaternary,\n                                                                                    boxShadow: \"0 2px 8px \".concat(token.colorBgElevated),\n                                                                                    transition: \"all 0.3s\"\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"file-delete-icon\",\n                                                                                        style: {\n                                                                                            position: \"absolute\",\n                                                                                            top: \"0\",\n                                                                                            right: \"0\",\n                                                                                            background: \"rgba(0,0,0,0.65)\",\n                                                                                            width: \"22px\",\n                                                                                            height: \"22px\",\n                                                                                            borderRadius: \"0 0 0 12px\",\n                                                                                            display: \"flex\",\n                                                                                            justifyContent: \"center\",\n                                                                                            alignItems: \"center\",\n                                                                                            cursor: \"pointer\",\n                                                                                            transition: \"background 0.2s\"\n                                                                                        },\n                                                                                        onClick: ()=>handleRemoveFile(file),\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                            style: {\n                                                                                                color: \"#fff\",\n                                                                                                fontSize: \"14px\"\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                            lineNumber: 1047,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                        lineNumber: 1030,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        style: {\n                                                                                            marginTop: \"10px\"\n                                                                                        },\n                                                                                        children: getFileIcon(file.type || \"\")\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                        lineNumber: 1050,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        style: {\n                                                                                            textAlign: \"center\",\n                                                                                            width: \"100%\",\n                                                                                            marginTop: \"4px\",\n                                                                                            overflow: \"hidden\"\n                                                                                        },\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Text, {\n                                                                                                ellipsis: {\n                                                                                                    tooltip: file.name\n                                                                                                },\n                                                                                                style: {\n                                                                                                    fontSize: \"12px\",\n                                                                                                    lineHeight: \"1.2\"\n                                                                                                },\n                                                                                                children: file.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                                lineNumber: 1060,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                style: {\n                                                                                                    fontSize: \"11px\",\n                                                                                                    color: token.colorTextSecondary,\n                                                                                                    marginTop: \"2px\",\n                                                                                                    background: token.colorFillSecondary,\n                                                                                                    padding: \"0 4px\",\n                                                                                                    borderRadius: \"4px\",\n                                                                                                    display: \"inline-block\"\n                                                                                                },\n                                                                                                children: getFileExtension(file.name)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                                lineNumber: 1066,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                        lineNumber: 1054,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, file.uid, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                lineNumber: 1013,\n                                                                                columnNumber: 29\n                                                                            }, undefined);\n                                                                        }\n                                                                    }),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        showUploadList: false,\n                                                                        beforeUpload: (file)=>{\n                                                                            try {\n                                                                                const uid = Date.now().toString();\n                                                                                const newFile = {\n                                                                                    uid: uid,\n                                                                                    name: file.name,\n                                                                                    status: \"done\",\n                                                                                    thumbUrl: URL.createObjectURL(file),\n                                                                                    type: file.type,\n                                                                                    size: file.size\n                                                                                };\n                                                                                setFileList([\n                                                                                    ...fileList,\n                                                                                    newFile\n                                                                                ]);\n                                                                                setLocalFiles((prev)=>({\n                                                                                        ...prev,\n                                                                                        [uid]: file\n                                                                                    }));\n                                                                            } catch (error) {\n                                                                                nt.error(\"文件处理失败，请重试\");\n                                                                            }\n                                                                            return false;\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            style: {\n                                                                                border: 0,\n                                                                                background: \"none\",\n                                                                                cursor: \"pointer\",\n                                                                                borderRadius: \"12px\",\n                                                                                padding: \"16px\",\n                                                                                transition: \"all 0.3s\",\n                                                                                width: \"104px\",\n                                                                                height: \"104px\",\n                                                                                display: \"inline-flex\",\n                                                                                flexShrink: 0,\n                                                                                /* 防止按钮收缩 */ flexDirection: \"column\",\n                                                                                justifyContent: \"center\",\n                                                                                alignItems: \"center\",\n                                                                                backgroundColor: token.colorBgContainer,\n                                                                                boxShadow: \"0 2px 8px \".concat(token.colorBorderSecondary)\n                                                                            },\n                                                                            type: \"button\",\n                                                                            disabled: uploading,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    style: {\n                                                                                        fontSize: \"24px\",\n                                                                                        color: token.colorPrimary\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                    lineNumber: 1129,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    style: {\n                                                                                        marginTop: 8,\n                                                                                        color: token.colorTextSecondary\n                                                                                    },\n                                                                                    children: \"点击上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                    lineNumber: 1130,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                            lineNumber: 1108,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                        lineNumber: 1084,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                lineNumber: 956,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 949,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                    lineNumber: 943,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"auth-column-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        padding: \"16px\",\n                                                        borderRadius: \"12px\",\n                                                        background: token.colorInfoBg,\n                                                        border: \"2px solid \".concat(token.colorInfoBorder),\n                                                        height: \"fit-content\",\n                                                        /* 高度适应内容 */ marginBottom: \"6px\",\n                                                        marginTop: \"16px\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: modalStyles.sectionTitle,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        ...modalStyles.sectionTitleBefore,\n                                                                        backgroundColor: token.colorInfo\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                    lineNumber: 1152,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \"注意事项\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 1151,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            style: {\n                                                                listStyle: \"none\",\n                                                                padding: 0,\n                                                                margin: 0\n                                                            },\n                                                            children: [\n                                                                \"同一个身份信息仅支持实名认证3个洛基飞跃账号。\",\n                                                                \"提交审核过后可在沟通群内联系管理员跟进认证。\",\n                                                                \"审核结果将于1个工作日以短信和站内信的方式通知您。审核不通过，请根据页面提示，修改认证证明材料并再次提交审核。\"\n                                                            ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    style: modalStyles.listItem,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: token.colorInfo,\n                                                                                marginRight: \"8px\"\n                                                                            },\n                                                                            children: \"•\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                            lineNumber: 1165,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        item\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                    lineNumber: 1164,\n                                                                    columnNumber: 23\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 1158,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                    lineNumber: 1141,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        marginTop: \"16px\",\n                                                        padding: \"16px\",\n                                                        borderRadius: \"12px\",\n                                                        background: token.colorSuccessBg,\n                                                        border: \"2px solid \".concat(token.colorSuccessBorder),\n                                                        height: \"fit-content\" /* 高度适应内容 */ \n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: modalStyles.sectionTitle,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        ...modalStyles.sectionTitleBefore,\n                                                                        backgroundColor: token.colorSuccess\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                    lineNumber: 1182,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \"认证信息可包括\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 1181,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            style: {\n                                                                listStyle: \"none\",\n                                                                padding: 0,\n                                                                margin: 0\n                                                            },\n                                                            children: [\n                                                                \"教师资格证\",\n                                                                \"教师工作证\",\n                                                                \"中华人民共和国居民身份证信息\",\n                                                                \"学校或教育机构授权\"\n                                                            ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    style: modalStyles.listItem,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: token.colorSuccess,\n                                                                                marginRight: \"8px\"\n                                                                            },\n                                                                            children: \"•\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                            lineNumber: 1191,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        item\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                    lineNumber: 1190,\n                                                                    columnNumber: 23\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 1188,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                    lineNumber: 1173,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                            lineNumber: 1139,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                    lineNumber: 774,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                lineNumber: 768,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                        lineNumber: 761,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"teacher-auth-modal-footer\",\n                        style: {\n                            borderTop: \"1px solid #f0f0f0\" /* 使用浅色边框替代红线 */ \n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            type: \"primary\",\n                            onClick: ()=>form.submit(),\n                            loading: submitting,\n                            disabled: submitting,\n                            style: modalStyles.submitButton,\n                            children: submitting ? \"提交中...\" : \"确认提交\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                            lineNumber: 1204,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                        lineNumber: 1203,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                lineNumber: 714,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(TeacherAuthModal, \"+FxDNWxv1HLtDIlYvaHMrUbsrWg=\", false, function() {\n    return [\n        _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useToken,\n        react_redux__WEBPACK_IMPORTED_MODULE_8__.useSelector,\n        _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm\n    ];\n});\n_c = TeacherAuthModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TeacherAuthModal);\nvar _c;\n$RefreshReg$(_c, \"TeacherAuthModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/userAuth/teacherAuthModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api/school.ts":
/*!***************************!*\
  !*** ./lib/api/school.ts ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   schoolApi: function() { return /* binding */ schoolApi; }\n/* harmony export */ });\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../request */ \"(app-pages-browser)/./lib/request.ts\");\n/*\r\n * @Author: Zwww <EMAIL>\r\n * @Date: 2025-04-29 11:54:36\r\n * @LastEditors: Zwww <EMAIL>\r\n * @LastEditTime: 2025-05-06 17:27:15\r\n * @FilePath: \\sourceCode\\logicleapweb\\lib\\api\\school.ts\r\n * @Description: \r\n * \r\n * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. \r\n */ \nconst schoolBaseUrl = \"/api/user-school\";\nconst schoolRelationBaseUrl = \"/api/user-school-relation\";\nconst schoolApi = {\n    // 获取学校列表  okok\n    getList: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(schoolBaseUrl, {\n            params\n        });\n    },\n    // 添加学校   okok\n    add: (school)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(schoolBaseUrl, school);\n    },\n    // 更新学校信息   okok\n    update: (school, id)=>{\n        console.log(school);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(schoolBaseUrl + id, school);\n    },\n    // 删除学校    okok\n    delete: (id)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(schoolBaseUrl + \"/\" + id);\n    },\n    // 获取省市区数据    没有用到 直接okok\n    getRegions: ()=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/app/user/school/regions\");\n    },\n    // 绑定学校   okok\n    bindSchool: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(schoolRelationBaseUrl, params);\n    },\n    // 解绑学校  okok\n    unbindSchool: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"\".concat(schoolRelationBaseUrl, \"/user/\").concat(params.userId, \"/school/\").concat(params.schoolId));\n    },\n    // 获取用户关联的学校列表  \n    getUserSchools: ()=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/user-school/listByUserId\");\n    },\n    // 获取学校的教师列表    okok\n    getSchoolTeachers: (schoolId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(schoolRelationBaseUrl, \"/school/\").concat(schoolId, \"/teachers\"));\n    },\n    // 获取学校的班级列表 \n    getSchoolClasses: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/user/class/school/\".concat(params.schoolId, \"/all\"), {\n            params\n        });\n    },\n    // 获取学校的省市区信息  没人调用他。okok\n    getSchoolRegion: (schoolId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/app/user/school/\".concat(schoolId, \"/region\"));\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/school.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api/task.ts":
/*!*************************!*\
  !*** ./lib/api/task.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Priority: function() { return /* binding */ Priority; },\n/* harmony export */   TaskStatus: function() { return /* binding */ TaskStatus; },\n/* harmony export */   TaskType: function() { return /* binding */ TaskType; }\n/* harmony export */ });\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../request */ \"(app-pages-browser)/./lib/request.ts\");\n\nvar TaskType;\n(function(TaskType) {\n    TaskType[TaskType[\"GRAPHIC\"] = 1] = \"GRAPHIC\";\n    TaskType[TaskType[\"NODE\"] = 2] = \"NODE\";\n    TaskType[TaskType[\"AI\"] = 3] = \"AI\"; // AI应用任务\n})(TaskType || (TaskType = {}));\nvar Priority;\n(function(Priority) {\n    Priority[Priority[\"NORMAL\"] = 0] = \"NORMAL\";\n    Priority[Priority[\"IMPORTANT\"] = 1] = \"IMPORTANT\";\n    Priority[Priority[\"URGENT\"] = 2] = \"URGENT\"; // 紧急\n})(Priority || (Priority = {}));\nvar TaskStatus;\n(function(TaskStatus) {\n    TaskStatus[TaskStatus[\"NOT_STARTED\"] = 0] = \"NOT_STARTED\";\n    TaskStatus[TaskStatus[\"IN_PROGRESS\"] = 1] = \"IN_PROGRESS\";\n    TaskStatus[TaskStatus[\"COMPLETED\"] = 2] = \"COMPLETED\";\n    TaskStatus[TaskStatus[\"EXPIRED\"] = 3] = \"EXPIRED\";\n    TaskStatus[TaskStatus[\"RE_DO\"] = 4] = \"RE_DO\"; // 重新修改\n})(TaskStatus || (TaskStatus = {}));\n// 任务相关 API\nconst taskApi = {\n    /**\r\n   * 获取任务统计\r\n   */ // 没人用它   okok\n    getTaskStats () {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/app/user/teacher_task/stats\");\n    },\n    /**\r\n   * 获取任务列表   okok\r\n   */ getTaskList (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/teacher-task/list\", {\n            params\n        });\n    },\n    /**\r\n   * 发布任务    okok\r\n   */ publishTask (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task/publish\", params);\n    },\n    /**\r\n   * 获取任务完成统计   没人用 ，okok\r\n   */ getAssignmentStats () {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/app/user/teacher_task/assignment/stats\");\n    },\n    /**\r\n   * 批量分配任务  没人用okok\r\n   */ batchAssignTask (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/app/user/teacher_task/assignment/batchAssign\", params);\n    },\n    /**\r\n   * 提交任务   okok\r\n   */ submitTask (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task-assignment/submitWork\", params);\n    },\n    /**\r\n   * 评分   okok\r\n   */ gradeTask (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task-assignment/grade\", params);\n    },\n    /**\r\n   * 获取任务详情  okok\r\n   */ getTaskDetail (taskId) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/teacher-task/\".concat(taskId));\n    },\n    /**\r\n   * 获取学生提交的作品    okok\r\n   */ getStudentWork (assignmentId) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/teacher-task-assignment/studentCommitWork\", {\n            params: {\n                assignmentId\n            }\n        });\n    },\n    /**\r\n   * 打回修改   okok\r\n   */ returnForRevision (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task-assignment/return-revision\", params);\n    },\n    /**\r\n   * 更新任务状态   okok\r\n   */ updateTaskStatus (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task-assignment/update-status\", params);\n    },\n    /**\r\n   * 更新任务     okok\r\n   */ updateTask (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task/update-task\", params);\n    },\n    /**\r\n   * 发布学生作品到班级项目  okok\r\n   */ publishToClass (params) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task-assignment/publish-to-class\", params);\n    },\n    /**\r\n   * 删除任务  okok\r\n   */ deleteTask (taskId) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task/delete-task\", {\n            taskId\n        });\n    },\n    /**\r\n   * 搜索任务   okok\r\n   */ searchTasks (keyword, classId) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/teacher-task/search\", {\n            params: {\n                keyword,\n                classId\n            }\n        });\n    },\n    /**\r\n   * 获取作品的提交记录   okok\r\n   */ getWorkSubmissions (workId) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/teacher-task-assignment/workSubmissionsRecord\", {\n            params: {\n                workId\n            }\n        });\n    },\n    // 获取学生任务列表      没有人调用他  okok\n    getStudentTasks: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/app/user/teacher_task/list\", {\n            params: {\n                studentId: params.studentId,\n                status: params.status,\n                roleId: 1,\n                page: 1,\n                size: 100\n            }\n        });\n    },\n    // 添加标记已读的方法   okok\n    markAsRead: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/teacher-task-assignment/mark-read\", params);\n    },\n    // 根据任务ID获取自评项\n    getSelfAssessmentItemsByTaskId: (taskId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/self-assessment-item/task/\".concat(taskId));\n    },\n    // 搜索自评项\n    searchSelfAssessmentItems (keyword) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/task-self-assessment-item/search\", {\n            params: {\n                keyword\n            }\n        });\n    },\n    /**\r\n   * 检查自评项是否有学生自评记录\r\n   */ checkSelfAssessmentRecords (itemIds) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/task-self-assessment-item/check-records\", {\n            itemIds\n        });\n    },\n    /**\r\n   * 更新任务的自评项\r\n   */ updateSelfAssessmentItems (taskId, selfAssessmentItems) {\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/task-self-assessment-item/update-by-task\", {\n            taskId,\n            selfAssessmentItems\n        });\n    }\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (taskApi);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/task.ts\n"));

/***/ })

});