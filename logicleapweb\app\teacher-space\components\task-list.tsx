'use client'

import { useState, useEffect } from 'react';
import { Card, Empty, Button, Tag, Progress, Modal, Form, Input, DatePicker, Upload, Checkbox, Tabs, Select, Spin, Carousel, Table, Badge, message, notification, Radio, Space } from 'antd';
import { EyeOutlined, EditOutlined, DeleteOutlined, PlusOutlined, InboxOutlined, ReloadOutlined, MinusCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { Task } from '../types/index';
import { TaskDetailModal } from './task-detail-modal';
import taskApi from '@/lib/api/task';
import dayjs from 'dayjs';
import { uploadApi } from '@/lib/api/upload';
import type { UploadFile } from 'antd/es/upload/interface';
import { getMimeType, isAllowedFileType, isFileSizeAllowed, formatFileSize } from '../utils';
import React from 'react';
import { GetNotification } from 'logic-common/dist/components/Notification';
import { worksApi } from '@/lib/api/works';

interface TaskListProps {
  tasks: Task[];
  onRefresh: () => void;
  students: any[];
  currentClassId: number;
  fetchTasks: (classId: number) => void;
  displayTemplates: any[];
  officialTemplates: any[];
}

// 修改任务状态常量
const TASK_STATUS = {
  NOT_STARTED: 0, // 未开始
  IN_PROGRESS: 1, // 进行中
  COMPLETED: 2,   // 已结束
  EXPIRED: 3,    // 已过期
} as const;

// 添加获取任务状态的函数
const getTaskStatus = (startDate: string, endDate: string) => {
  const now = new Date().getTime();
  const start = new Date(startDate).getTime();
  const end = new Date(endDate).getTime();

  if (now < start) {
    return TASK_STATUS.NOT_STARTED;
  } else if (now >= start && now <= end) {
    return TASK_STATUS.IN_PROGRESS;
  } else {
    return TASK_STATUS.COMPLETED;
  }
};

// 添加获取状态标签的函数
const getStatusTag = (startDate: string, endDate: string, task: Task) => {
  const now = new Date().getTime();
  const start = new Date(startDate).getTime();
  const end = new Date(endDate).getTime();

  // 计算完成率
  const completedCount = task.assignments?.filter((assignment) => assignment.taskStatus === 2).length || 0;
  const totalCount = task.assignments?.length || 0;
  const completionRate = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;

  // 1. 判断是否还未开始
  if (now < start) {
    return <Tag color="default">未开始</Tag>;
  }

  // 2. 判断是否已结束（超过截止时间）
  if (now > end) {
    return <Tag color="success">已结束</Tag>;
  }

  // 3. 判断是否完成（在有效期内且100%完成）
  if (completionRate === 100) {
    return <Tag color="success">已完成</Tag>;
  }

  // 4. 其他情况（在有效期内且未完成）就是进行中
  return <Tag color="processing">进行中</Tag>;
};

// 添加状态选项
const taskStatusOptions = [
  { label: '未开始', value: TASK_STATUS.NOT_STARTED },
  { label: '进行中', value: TASK_STATUS.IN_PROGRESS },
  { label: '已完成', value: TASK_STATUS.COMPLETED },
  { label: '已过期', value: TASK_STATUS.EXPIRED }
];

export const TaskList: React.FC<TaskListProps> = ({
  tasks,
  onRefresh,
  students,
  currentClassId,
  fetchTasks,
  displayTemplates,
  officialTemplates
}) => {
  const notification = GetNotification();
  
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [taskStatistics, setTaskStatistics] = useState<any>({});
  const [sortOrder, setSortOrder] = useState<string>('latest');
  const [editForm] = Form.useForm();
  const [editFileList, setEditFileList] = useState<UploadFile[]>([]);
  const [editUploading, setEditUploading] = useState(false);
  const [works, setWorks] = useState<any[]>([]);
  const [loadingWorks, setLoadingWorks] = useState(false);
  const [selectedWorkIds, setSelectedWorkIds] = useState<number[]>([]);
  const [completionFilter, setCompletionFilter] = useState<number | null>(null);
  const [sortByDeadline, setSortByDeadline] = useState<'deadline' | 'newest'>('deadline');
  const [filterOpen, setFilterOpen] = useState(false);
  const [sortOpen, setSortOpen] = useState(false);
  const contentRef = React.useRef<HTMLDivElement>(null);

  // 添加一个函数来处理更新逻辑
  const updateTaskWithParams = async (values: any) => {
    const params = {
      ...values,
      taskId: selectedTask!.id,
      startDate: values.startDate?.toDate(),
      endDate: values.endDate?.toDate(),
      attachments: editFileList.map((file: UploadFile) => file.url || ''),
      workIdsStr: values.workIds ? 
        (Array.isArray(values.workIds) ? values.workIds.join(',') : String(values.workIds)) : 
        undefined,
    };

    const response = await taskApi.updateTask(params);
    if (response.data.code === 200) {
      // 如果有自评项并且可以更新，则更新自评项
      if (values.selfAssessmentItems && values.selfAssessmentItems.length > 0) {
        try {
          await taskApi.updateSelfAssessmentItems(selectedTask!.id, values.selfAssessmentItems);
        } catch (error) {
          console.error("更新自评项失败:", error);
          notification.warning('任务基本信息更新成功，但自评项更新失败');
          setIsEditModalVisible(false);
          editForm.resetFields();
          setEditFileList([]);
          if (currentClassId) {
            fetchTasks(currentClassId);
          }
          return;
        }
      }
      
      notification.success('更新任务成功');
      setIsEditModalVisible(false);
      editForm.resetFields();
      setEditFileList([]);
      if (currentClassId) {
        fetchTasks(currentClassId);
      }
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      if (filterOpen) setFilterOpen(false);
      if (sortOpen) setSortOpen(false);
    };

    const contentElement = contentRef.current;
    if (contentElement) {
      contentElement.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (contentElement) {
        contentElement.removeEventListener('scroll', handleScroll);
      }
    };
  }, [filterOpen, sortOpen]);

  // 筛选和排序任务
  const filteredAndSortedTasks = [...tasks].filter(task => {
    if (completionFilter === null || completionFilter === 0) return true;

    const now = new Date().getTime();
    const startTime = new Date(task.startDate).getTime();
    const endTime = new Date(task.endDate).getTime();
    const completedCount = task.assignments?.filter((assignment) => assignment.taskStatus === 2).length || 0;
    const totalCount = task.assignments?.length || 0;
    const completionRate = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;

    switch (completionFilter) {
      case 1: // 进行中
        return now >= startTime && now <= endTime && completionRate < 100;
      case 2: // 已结束
        return now > endTime;
      case 3: // 已完成
        return completionRate === 100 && now <= endTime;
      default: return true;
    }
  }).sort((a, b) => {
    const now = new Date().getTime();
    const isAExpired = now > new Date(a.endDate).getTime();
    const isBExpired = now > new Date(b.endDate).getTime();

    // 如果一个已结束一个未结束，将未结束的排在前面
    if (isAExpired && !isBExpired) return 1;
    if (!isAExpired && isBExpired) return -1;

    // 如果都已结束或都未结束，按选择的排序方式排序
    if (sortByDeadline === 'deadline') {
      // 按截止时间排序（距离截止时间最近的在前）
      const timeToEndA = new Date(a.endDate).getTime() - now;
      const timeToEndB = new Date(b.endDate).getTime() - now;
      return timeToEndA - timeToEndB;
    } else {
      // 按发布时间排序（最新发布的在前）
      const timeFromStartA = now - new Date(a.startDate).getTime();
      const timeFromStartB = now - new Date(b.startDate).getTime();
      return timeFromStartA - timeFromStartB;
    }
  });

  const handleDelete = (task: Task) => {
    Modal.confirm({
      title: '确认删除任务',
      content: (
        <div>
          <p>确定要删除任务 "{task.taskName}" 吗？</p>
          <p className="text-red-500 mt-2">此操作将同时删除所有学生的任务分配记录，且不可恢复！</p>
        </div>
      ),
      okText: '确定删除',
      cancelText: '取消',
      okButtonProps: { danger: true },
      centered: true,
      onOk: async () => {
        try {
          await taskApi.deleteTask(task.id);
          notification.success('删除任务成功');
          if (currentClassId) {
            fetchTasks(currentClassId);
          }
        } catch (error: any) {
          notification.error(error.response?.data?.message || '删除任务失败');
        }
      }
    });
  };

  const handleEdit = (task: Task) => {
    setSelectedTask(task);
    setIsEditModalVisible(true);
    
    // 如果有附件，转换为上传组件需要的格式
    if (task.attachments && task.attachments.length > 0) {
      const fileList: UploadFile[] = task.attachments.map((attachment, index) => {
        const url = typeof attachment === 'string' ? attachment : attachment.url;
        const name = url.split('/').pop() || 'file';
        return {
          uid: `${index}`,
          name: name,
          status: 'done',
          url: url,
          type: getFileTypeFromUrl(url),
          size: 0,
        };
      });
      setEditFileList(fileList);
    } else {
      setEditFileList([]);
    }
    
    // 处理参考作品ID
    if (task.workIdsStr) {
      const workIds = task.workIdsStr.split(',').map(id => parseInt(id));
      setSelectedWorkIds(workIds);
      // 加载教师作品
      fetchTeacherWorks();
    } else {
      setSelectedWorkIds([]);
    }

    // 设置表单默认值
    editForm.setFieldsValue({
      ...task,
      startDate: task ? dayjs(task.startDate) : undefined,
      endDate: task ? dayjs(task.endDate) : undefined,
    });

    // 查询任务的自评项
    if (task.id) {
      taskApi.getSelfAssessmentItemsByTaskId(task.id).then((response: any) => {
        if (response.data.code === 200 && response.data.data.length > 0) {
          // 设置自评项
          const selfAssessmentItems = response.data.data.map((item: any) => item.content);
          editForm.setFieldsValue({ selfAssessmentItems });
        }
      }).catch((error) => {
        console.error('获取自评项失败:', error);
      });
    }
  };

  // 获取教师作品
  const fetchTeacherWorks = async () => {
    try {
      setLoadingWorks(true);
      // 从localStorage获取用户信息
      const userData = localStorage.getItem('user');
      const user = userData ? JSON.parse(userData) : null;
      
      if (!user || !user.userId) {
        console.error('未找到用户信息');
        return;
      }

      const response = await worksApi.getTeacherWorks(
        user.userId,
      );

      if (response.data.code === 200) {
        setWorks(response.data.data || []);
      } else {
        console.error('获取作品失败:', response.data.message);
      }
    } catch (error) {
      console.error('获取作品列表失败:', error);
    } finally {
      setLoadingWorks(false);
    }
  };

  // 选择作品（支持多选）
  const handleSelectWork = (workId: number) => {
    if (selectedWorkIds.includes(workId)) {
      // 取消选中
      const newSelectedWorkIds = selectedWorkIds.filter(id => id !== workId);
      setSelectedWorkIds(newSelectedWorkIds);
      editForm.setFieldValue('workIds', newSelectedWorkIds.length > 0 ? newSelectedWorkIds : null);
    } else {
      // 选中
      const newSelectedWorkIds = [...selectedWorkIds, workId];
      setSelectedWorkIds(newSelectedWorkIds);
      editForm.setFieldValue('workIds', newSelectedWorkIds);
    }
  };

  // 获取文件类型
  const getFileTypeFromUrl = (url: string) => {
    const extension = url.split('.').pop()?.toLowerCase();
    switch(extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'image/' + extension;
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'xls':
        return 'application/vnd.ms-excel';
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'ppt':
        return 'application/vnd.ms-powerpoint';
      case 'pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case 'txt':
        return 'text/plain';
      default:
        return 'application/octet-stream';
    }
  };

  if (tasks.length === 0) {
    return <Empty description="暂无任务" />;
  }

  return (
    <div className="bg-white p-6 rounded-t-xl min-h-[calc(100vh-320px)]">
      <div className="space-y-4">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-4">
            <Select
              placeholder="按完成度筛选"
              style={{ width: 150 }}
              value={completionFilter ?? 0}
              onChange={value => setCompletionFilter(value)}
              options={[
                { label: '全部', value: 0 },
                { label: '进行中', value: 1 },
                { label: '已结束', value: 2 },
                { label: '已完成', value: 3 }
              ]}
              open={filterOpen}
              onDropdownVisibleChange={visible => setFilterOpen(visible)}
            />
            <Select
              placeholder="截止时间排序"
              style={{ width: 150 }}
              value={sortByDeadline}
              onChange={value => setSortByDeadline(value)}
              options={[
                { label: '最早截止优先', value: 'deadline' },
                { label: '最新发布优先', value: 'newest' }
              ]}
              open={sortOpen}
              onDropdownVisibleChange={visible => setSortOpen(visible)}
            />
          </div>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => {
              if (currentClassId) {
                fetchTasks(currentClassId);
                notification.success('刷新成功');
              }
            }}
          >
            刷新列表
          </Button>
        </div>
        <div className="grid gap-4" ref={contentRef} style={{ maxHeight: 'calc(100vh - 400px)', overflowY: 'auto' }}>
          {filteredAndSortedTasks.map((task) => (
            <Card key={task.id} className="hover:shadow-md transition-all">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h4 className="text-lg font-medium">{task.taskName}</h4>
                    {getStatusTag(task.startDate.toString(), task.endDate.toString(), task)}
                  </div>
                  <p className="text-gray-500 mt-2">{task.taskDescription}</p>
                  <div className="mt-3 text-sm text-gray-500">
                    <div>发布时间: {new Date(task.startDate).toLocaleString()}</div>
                    <div>截止时间: {new Date(task.endDate).toLocaleString()}</div>
                    <div className="mt-1">
                      完成情况: {task.assignments?.filter((assignment) => assignment.taskStatus === 2).length || 0}/{task.assignments?.length || 0}
                    </div>
                  </div>
                </div>
                <Progress
                  type="circle"
                  percent={Math.round(
                    ((task.assignments?.filter((assignment) => assignment.taskStatus === 2).length || 0) /
                      (task.assignments?.length || 1)) * 100
                  )}
                  width={60}
                />
              </div>
              <div className="flex justify-end mt-4 space-x-2">
                <Button
                  icon={<EyeOutlined />}
                  onClick={() => {
                    setSelectedTask(task);
                    setIsDetailModalVisible(true);
                  }}
                >
                  查看
                </Button>
                <Button
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(task)}
                >
                  编辑
                </Button>
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDelete(task)}
                >
                  删除
                </Button>
              </div>
            </Card>
          ))}
        </div>

        <TaskDetailModal
          task={selectedTask}
          visible={isDetailModalVisible}
          onClose={() => setIsDetailModalVisible(false)}
          students={students}
          onRefresh={onRefresh}
          currentClassId={currentClassId}
        />

        <Modal
          title="编辑任务"
          open={isEditModalVisible}
          onCancel={() => {
            setIsEditModalVisible(false);
            setSelectedTask(null);
            editForm.resetFields();
            setEditFileList([]);
          }}
          footer={null}
          width="95vw"
          style={{
            maxWidth: '800px',
            margin: '10vh auto',
            padding: 0,
            top: 0
          }}
          centered
          bodyStyle={{
            maxHeight: 'calc(80vh - 100px)',
            overflowY: 'auto'
          }}
        >
          <Form
            form={editForm}
            layout="vertical"
            initialValues={{
              ...selectedTask,
              startDate: selectedTask ? dayjs(selectedTask.startDate) : undefined,
              endDate: selectedTask ? dayjs(selectedTask.endDate) : undefined,
            }}
            onFinish={async (values) => {
              try {
                if (!selectedTask) return;

                // 检查自评项是否已有学生提交的自评记录
                let existingSelfAssessmentItems: Array<{id: number, content: string}> = [];
                
                try {
                  // 获取当前任务的自评项
                  const assessmentResponse = await taskApi.getSelfAssessmentItemsByTaskId(selectedTask.id);
                  if (assessmentResponse.data.code === 200) {
                    existingSelfAssessmentItems = assessmentResponse.data.data || [];
                    
                    // 检查是否有学生自评记录
                    if (existingSelfAssessmentItems.length > 0) {
                      const itemIds = existingSelfAssessmentItems.map((item: {id: number}) => item.id);
                      const studentRecordsResponse = await taskApi.checkSelfAssessmentRecords(itemIds);
                      
                      if (studentRecordsResponse.data.code === 200 && studentRecordsResponse.data.data?.hasRecords) {
                        // 已有学生自评记录
                        if (JSON.stringify(existingSelfAssessmentItems.map((item: {content: string}) => item.content)) !== 
                            JSON.stringify(values.selfAssessmentItems || [])) {
                          // 如果自评项有变化，提示用户
                          Modal.confirm({
                            title: '警告',
                            content: '该任务下已有学生提交的自评记录，不能修改自评项内容。如需修改，请先删除学生自评记录。',
                            okText: '继续更新其他信息',
                            cancelText: '取消',
                            onOk: async () => {
                              // 继续更新，但不更新自评项
                              values.selfAssessmentItems = existingSelfAssessmentItems.map((item: {content: string}) => item.content);
                              await updateTaskWithParams(values);
                            }
                          });
                          return;
                        }
                      }
                    }
                  }
                } catch (error) {
                  console.error("检查自评项记录失败:", error);
                  }
                
                // 如果没有学生自评记录或自评项没有变化，直接更新
                await updateTaskWithParams(values);
              } catch (error: any) {
                notification.error(error.response?.data?.message || '更新任务失败');
              }
            }}
          >
            <Tabs
              items={[
                {
                  key: 'basic',
                  label: '基本信息',
                  children: (
                    <>
                      <Form.Item
                        label="任务标题"
                        name="taskName"
                        rules={[{ required: true, message: '请输入任务标题' }]}
                      >
                        <Input placeholder="请输入任务标题" />
                      </Form.Item>

                      <Form.Item
                        label="任务描述"
                        name="taskDescription"
                      >
                        <Input.TextArea rows={4} placeholder="请输入任务描述（选填）" />
                      </Form.Item>

                      <Form.List name="selfAssessmentItems">
                        {(fields, { add, remove }) => (
                          <Form.Item label="课堂自评">
                            <div className='space-y-2'>
                              {fields.map(({ key, name, ...restField }, index) => (
                                <div key={key} className="flex items-center gap-2">
                                  <div className="flex-shrink-0 flex items-center justify-center w-6 h-6 bg-gray-100 text-gray-500 rounded-full text-xs font-medium border">
                                    {index + 1}
                                  </div>
                                  <Form.Item
                                    {...restField}
                                    name={name}
                                    rules={[{ required: true, message: '请输入自评内容' }]}
                                    className="flex-1 mb-0"
                                  >
                                    <Input placeholder={`自评项 ${index + 1}`} />
                                  </Form.Item>
                                  <MinusCircleOutlined
                                    className="text-gray-400 hover:text-red-500 cursor-pointer transition-colors"
                                    onClick={() => remove(name)}
                                  />
                                </div>
                              ))}
                            </div>
                            <Button
                              type="dashed"
                              onClick={() => add('')}
                              block
                              icon={<PlusOutlined />}
                              className="mt-3"
                            >
                              添加自评项
                            </Button>
                          </Form.Item>
                        )}
                      </Form.List>

                      <div className="grid grid-cols-2 gap-4">
                        <Form.Item
                          label="开始时间"
                          name="startDate"
                          rules={[{ required: true, message: '请选择开始时间' }]}
                        >
                          <DatePicker
                            showTime
                            placeholder="选择开始时间"
                            className="w-full"
                          />
                        </Form.Item>

                        <Form.Item
                          label="截止时间"
                          name="endDate"
                          rules={[
                            { required: true, message: '请选择截止时间' },
                            ({ getFieldValue }) => ({
                              validator(_, value) {
                                if (!value || !getFieldValue('startDate') || value.isAfter(getFieldValue('startDate'))) {
                                  return Promise.resolve();
                                }
                                return Promise.reject(new Error('截止时间必须晚于开始时间'));
                              },
                            }),
                          ]}
                        >
                          <DatePicker
                            showTime
                            placeholder="选择截止时间"
                            className="w-full"
                            disabledDate={(current) => {
                              const startDate = editForm.getFieldValue('startDate');
                              return current && startDate && current.isBefore(startDate, 'day');
                            }}
                            disabledTime={(current) => {
                              const startDate = editForm.getFieldValue('startDate');
                              if (current && startDate && current.isSame(startDate, 'day')) {
                                return {
                                  disabledHours: () => Array.from({ length: startDate.hour() + 1 }, (_, i) => i),
                                  disabledMinutes: (hour) => {
                                    if (hour === startDate.hour()) {
                                      return Array.from({ length: startDate.minute() + 1 }, (_, i) => i);
                                    }
                                    return [];
                                  },
                                  disabledSeconds: (hour, minute) => {
                                    if (hour === startDate.hour() && minute === startDate.minute()) {
                                      return Array.from({ length: startDate.second() + 1 }, (_, i) => i);
                                    }
                                    return [];
                                  }
                                };
                              }
                              return {};
                            }}
                          />
                        </Form.Item>
                      </div>
                    </>
                  )
                },
                {
                  key: 'content',
                  label: '任务内容',
                  children: (
                    <>
                      <Form.Item 
                        label="选择作品" 
                        name="workIds"
                        help="选择作品作为任务参考资料（可多选）"
                      >
                        {loadingWorks ? (
                          <div className="flex justify-center items-center p-10">
                            <Spin tip="加载中..." />
                          </div>
                        ) : works.length > 0 ? (
                          <div className="overflow-x-auto pb-4">
                            <div className="flex space-x-4" style={{ minWidth: 'max-content' }}>
                              {works.map(work => (
                                <div
                                  key={work.id}
                                  className={`p-4 rounded-xl border cursor-pointer transition-all flex-shrink-0 w-[280px] ${
                                    selectedWorkIds.includes(work.id)
                                      ? 'border-indigo-400 bg-indigo-50 shadow-md'
                                      : 'border-gray-200 hover:border-indigo-200 hover:shadow-md bg-white'
                                  }`}
                                  onClick={() => handleSelectWork(work.id)}
                                >
                                  <div className="relative mb-3 h-[180px] rounded-lg overflow-hidden border border-indigo-100 shadow-sm">
                                    <Carousel
                                      dots={{ className: 'custom-dots !z-[1]' }}
                                      arrows
                                    >
                                      {work.coverImage && (
                                        <div className="h-[180px] relative">
                                          <img
                                            src={work.coverImage}
                                            alt={`${work.title} 封面`}
                                            className="w-full h-full object-contain rounded-lg"
                                          />
                                          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-2">
                                            <span className="text-white text-xs">作品封面</span>
                                          </div>
                                        </div>
                                      )}
                                      {work.screenShotImage && (
                                        <div className="h-[180px] relative">
                                          <img
                                            src={work.screenShotImage}
                                            alt={`${work.title} 截图`}
                                            className="w-full h-full object-contain rounded-lg"
                                          />
                                          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-2">
                                            <span className="text-white text-xs">作品截图</span>
                                          </div>
                                        </div>
                                      )}
                                    </Carousel>
                                  </div>
                                  <div className="flex justify-between items-start">
                                    <h4 className="font-medium text-sm">{work.title}</h4>
                                    <div className={`px-2 py-0.5 rounded-full text-xs ${
                                      work.status === 0 ? 'bg-gray-100 text-gray-600' : 'bg-green-100 text-green-600'
                                    }`}>
                                      {work.status === 0 ? '未发布' : '已发布'}
                                    </div>
                                  </div>
                                  <p className="text-xs text-gray-500 mt-1">{work.description || '暂无描述'}</p>
                                  <div className="text-xs text-gray-400 mt-1">
                                    创建时间：{new Date(work.createTime).toLocaleString()}
                                  </div>
                                  <div className="mt-2 flex justify-end">
                                    <Checkbox checked={selectedWorkIds.includes(work.id)} />
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        ) : (
                          <Empty description="暂无作品" />
                        )}
                      </Form.Item>
                      {selectedWorkIds.length > 0 && (
                        <div className="mt-2 text-sm text-indigo-600 mb-4">
                          已选择 {selectedWorkIds.length} 个作品
                        </div>
                      )}
                      
                      <Form.Item label="选择附件">
                        <Upload
                          listType="picture-card"
                          fileList={editFileList}
                          multiple
                          beforeUpload={async (file) => {
                            const allowedTypes = [
                              'image/jpeg',
                              'image/png',
                              'image/gif',
                              'application/pdf',
                              'application/msword',
                              'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                              'application/vnd.ms-excel',
                              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                              'application/vnd.ms-powerpoint',
                              'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                              'text/plain'
                            ];

                            if (!allowedTypes.includes(file.type)) {
                              notification.error(`不支持的文件类型: ${file.type}`);
                              return false;
                            }

                            if (file.size > 10 * 1024 * 1024) {
                              notification.error(`文件大小超过限制: ${(file.size / (1024 * 1024)).toFixed(2)}MB，最大允许10MB`);
                              return false;
                            }

                            try {
                              setEditUploading(true);
                              const url = await uploadApi.uploadToOss(file);
                              setEditFileList([...editFileList, {
                                uid: Date.now().toString(),
                                name: file.name,
                                status: 'done',
                                url: url,
                                type: file.type,
                                size: file.size
                              }]);
                              notification.success('上传成功');
                            } catch (error) {
                              notification.error('上传失败');
                            } finally {
                              setEditUploading(false);
                            }
                            return false;
                          }}
                          onRemove={async (file) => {
                            try {
                              if (file.url) {
                                await uploadApi.deleteFromOss(file.url);
                                setEditFileList(prev => prev.filter(f => f.uid !== file.uid));
                                notification.success('删除成功');
                              }
                            } catch (error) {
                              notification.error('删除失败');
                            }
                            return true;
                          }}
                        >
                          {editUploading ? (
                            <div className="flex flex-col items-center">
                              <Spin size="small" />
                              <div className="mt-1">上传中...</div>
                            </div>
                          ) : (
                            <div className="flex flex-col items-center">
                              <PlusOutlined />
                              <div className="mt-2">上传</div>
                            </div>
                          )}
                        </Upload>
                        <div className="text-gray-400 text-xs mt-1">
                          支持 jpg、png、gif、pdf、doc、docx、xls、xlsx、ppt、pptx、txt 格式
                          <br />单个文件不超过 10MB
                        </div>
                      </Form.Item>
                    </>
                  )
                }
              ]}
            />

            <div className="flex justify-end mt-6 space-x-2">
              <Button onClick={() => {
                setIsEditModalVisible(false);
                setSelectedTask(null);
                editForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                更新任务
              </Button>
            </div>
          </Form>
        </Modal>
      </div>
    </div>
  );
}; 