"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/activity/festival/page",{

/***/ "(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx":
/*!*************************************************************!*\
  !*** ./app/activity/festival/components/ActivityHeader.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Share2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Share2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./lib/store.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ActivityMediaGallery */ \"(app-pages-browser)/./app/activity/festival/components/ActivityMediaGallery.tsx\");\n/* harmony import */ var _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Modal!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _components_login_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/login-dialog */ \"(app-pages-browser)/./components/login-dialog.tsx\");\n/* harmony import */ var _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/activity */ \"(app-pages-browser)/./lib/api/activity/index.ts\");\n/* harmony import */ var _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/activity/events-task */ \"(app-pages-browser)/./lib/api/activity/events-task.ts\");\n/* harmony import */ var _lib_api_user__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/user */ \"(app-pages-browser)/./lib/api/user.ts\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 临时实现formatDate函数，以解决模块导入问题\nconst formatDate = (dateString)=>{\n    // 在服务端渲染时，直接返回原始字符串，避免水合错误\n    if (false) {}\n    const date = new Date(dateString);\n    // 检查日期是否有效\n    if (isNaN(date.getTime())) {\n        return dateString; // 如果无效，返回原始字符串\n    }\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, \"0\");\n    const day = String(date.getDate()).padStart(2, \"0\");\n    return \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n};\nconst ActivityHeader = (param)=>{\n    let { title: initialTitle, startTime: initialStartTime, endTime: initialEndTime, bannerImage: initialBannerImage, activityId, organizer: initialOrganizer = \"\", activityType = _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.ActivityType.WORK, onRefreshWorks, // 媒体字段\n    promotionImage, backgroundImage, galleryImages, attachmentFiles } = param;\n    _s();\n    console.log(\"\\uD83D\\uDD0D [DEBUG] ActivityHeader 组件渲染\", {\n        activityId,\n        initialTitle\n    });\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_9__.GetNotification)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.usePathname)();\n    // 显示用的标题和时间\n    const title = initialTitle;\n    const startTime = initialStartTime;\n    const endTime = initialEndTime;\n    const bannerImage = initialBannerImage;\n    const organizer = initialOrganizer;\n    // 获取当前用户信息和dispatch\n    const userState = (0,react_redux__WEBPACK_IMPORTED_MODULE_11__.useSelector)(_lib_store__WEBPACK_IMPORTED_MODULE_3__.selectUserState);\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_11__.useDispatch)();\n    // 同步用户状态的函数 - 增强版，从API获取最新用户信息\n    const syncUserState = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 开始同步用户状态\");\n            const localUser = localStorage.getItem(\"user\");\n            if (localUser) {\n                const userData = JSON.parse(localUser);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中的用户数据\", userData);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] Redux 中的用户数据\", userState);\n                // 如果用户已登录但phone为空，尝试从API获取最新用户信息\n                if (userData.isLoggedIn && userData.userId && (!userData.phone || userData.phone.trim() === \"\")) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户phone为空，从API获取最新用户信息\");\n                    try {\n                        const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_8__.userApi.getUserInfo(userData.userId);\n                        if (response.code === 200 && response.data) {\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取到最新用户信息\", response.data);\n                            // 构建更新后的用户数据\n                            const updatedUserData = {\n                                ...userData,\n                                phone: response.data.phone || \"\",\n                                nickName: response.data.nickName || userData.nickName,\n                                avatarUrl: response.data.avatarUrl || userData.avatarUrl,\n                                gender: response.data.gender || userData.gender,\n                                roleId: response.data.roleId || userData.roleId\n                            };\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 更新用户数据\", {\n                                oldPhone: userData.phone,\n                                newPhone: updatedUserData.phone,\n                                updated: updatedUserData\n                            });\n                            // 更新localStorage和Redux\n                            localStorage.setItem(\"user\", JSON.stringify(updatedUserData));\n                            dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(updatedUserData));\n                            return;\n                        }\n                    } catch (error) {\n                        console.error(\"\\uD83D\\uDD0D [DEBUG] 从API获取用户信息失败:\", error);\n                    }\n                }\n                // 检查关键字段是否有变化\n                const hasPhoneChanged = userData.phone !== (userState === null || userState === void 0 ? void 0 : userState.phone);\n                const hasRoleChanged = userData.roleId !== (userState === null || userState === void 0 ? void 0 : userState.roleId);\n                const hasLoginStatusChanged = userData.isLoggedIn !== (userState === null || userState === void 0 ? void 0 : userState.isLoggedIn);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态变化检查\", {\n                    phoneChanged: hasPhoneChanged,\n                    roleChanged: hasRoleChanged,\n                    loginStatusChanged: hasLoginStatusChanged,\n                    oldPhone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                    newPhone: userData.phone,\n                    oldRole: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                    newRole: userData.roleId\n                });\n                // 如果localStorage中的用户状态与Redux中的不一致，说明需要更新\n                if (hasPhoneChanged || hasRoleChanged || hasLoginStatusChanged) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户状态变化，同步Redux状态\");\n                    dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(userData));\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态无变化，无需同步\");\n                }\n            } else {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中无用户数据\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD0D [DEBUG] 检查用户状态失败:\", error);\n        }\n    };\n    // 监听页面焦点变化和路由变化，重新检查用户状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 页面加载时立即检查一次\n        syncUserState().catch(console.error);\n        const handleVisibilityChange = ()=>{\n            if (document.visibilityState === \"visible\") {\n                syncUserState().catch(console.error);\n            }\n        };\n        const handleFocus = ()=>{\n            syncUserState().catch(console.error);\n        };\n        // 添加多种事件监听确保状态同步\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n        window.addEventListener(\"focus\", handleFocus);\n        return ()=>{\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n            window.removeEventListener(\"focus\", handleFocus);\n        };\n    }, [\n        userState === null || userState === void 0 ? void 0 : userState.phone,\n        dispatch\n    ]);\n    // 工具函数：检查用户是否为学生身份\n    const checkIsStudent = ()=>{\n        try {\n            const userStr = localStorage.getItem(\"user\");\n            const localUser = userStr ? JSON.parse(userStr) : null;\n            const userRoleId = (localUser === null || localUser === void 0 ? void 0 : localUser.roleId) || (userState === null || userState === void 0 ? void 0 : userState.roleId);\n            return userRoleId === 1; // 学生身份的roleId为1\n        } catch (error) {\n            console.error(\"解析localStorage中的用户信息失败:\", error);\n            return false;\n        }\n    };\n    // 工具函数：显示身份验证失败提示\n    const showStudentOnlyModal = ()=>{\n        _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].confirm({\n            title: \"身份验证\",\n            content: \"抱歉，只有学生身份的用户才能参加此活动报名。如果您是学生，请联系管理员更新您的身份信息。\",\n            okText: \"我知道了\",\n            cancelText: \"联系管理员\",\n            onCancel: ()=>{\n                notification.info(\"如需帮助，请联系客服：400-123-4567\");\n            }\n        });\n    };\n    // 登录和报名相关状态\n    const [showLoginDialog, setShowLoginDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSubmitted, setHasSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [registrationStatus, setRegistrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 报名状态：0-已取消 1-已报名 2-已审核通过 3-已拒绝 4-评审中 5-已获奖 6-审核中\n    const [showUI, setShowUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 添加客户端检测来解决水合错误\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    // 页面加载时检查报名状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkRegistrationStatus = async ()=>{\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态 - useEffect 触发\", {\n                isLoggedIn: userState.isLoggedIn,\n                roleId: userState.roleId,\n                phone: userState.phone,\n                isClient,\n                activityId\n            });\n            // 只有在用户已登录且是学生身份时才检查报名状态\n            const isStudent = checkIsStudent();\n            if (!userState.isLoggedIn || !isStudent) {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录或不是学生身份，重置报名状态\", {\n                    isLoggedIn: userState.isLoggedIn,\n                    isStudent\n                });\n                // 如果用户未登录或不是学生，重置状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n                return;\n            }\n            try {\n                var _response_data, _response_data1;\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 开始检查报名状态 API 调用\");\n                setIsChecking(true);\n                const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.checkRegistration(Number(activityId));\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 页面加载检查报名状态响应:\", response);\n                if ((response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200 && ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data)) {\n                    const { submitted, submit } = response.data.data;\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态检查结果\", {\n                        submitted,\n                        submit\n                    });\n                    setHasSubmitted(submitted);\n                    if (submit && submit.status !== undefined) {\n                        setRegistrationStatus(submit.status);\n                    } else {\n                        setRegistrationStatus(null);\n                    }\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态响应不符合预期，设置为未报名状态\");\n                    // 如果响应不符合预期，设置为未报名状态\n                    setHasSubmitted(false);\n                    setRegistrationStatus(null);\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态失败:\", error);\n                // 出错时设置为未报名状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n            } finally{\n                setIsChecking(false);\n            }\n        };\n        // 只在客户端执行，避免服务端渲染问题\n        if (isClient) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 客户端环境，执行报名状态检查\");\n            checkRegistrationStatus();\n        } else {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 非客户端环境，跳过报名状态检查\");\n        }\n    }, [\n        activityId,\n        userState.isLoggedIn,\n        userState.roleId,\n        userState.phone,\n        isClient\n    ]);\n    // 前往任务函数\n    const handleGoToTask = async ()=>{\n        try {\n            const loadingMessage = notification.loading(\"正在跳转到任务...\");\n            // 获取当前用户的赛事任务列表\n            const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n            if (eventsTaskResponse.data.code === 200) {\n                const eventsTasks = eventsTaskResponse.data.data;\n                // 查找与当前活动相关的赛事任务\n                const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                if (relatedTask) {\n                    // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                    window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                    notification.success(\"正在跳转到任务页面...\");\n                } else {\n                    // 如果没有找到对应任务，在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                    notification.info(\"未找到对应任务，已打开班级空间\");\n                }\n            } else {\n                // 如果获取任务失败，在新标签页中打开班级空间\n                window.open(\"/class-space\", \"_blank\");\n                notification.info(\"获取任务失败，已打开班级空间\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"跳转任务失败:\", error);\n            notification.error(\"跳转任务失败，请稍后重试\");\n        }\n    };\n    // 处理报名按钮点击\n    const handleSubmitClick = async ()=>{\n        var _userState_phone, _localUser_phone;\n        // 获取localStorage中的用户信息进行对比\n        const localUserStr = localStorage.getItem(\"user\");\n        const localUser = localUserStr ? JSON.parse(localUserStr) : null;\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 报名按钮点击 - 详细用户状态检查\", {\n            hasSubmitted,\n            \"Redux userState\": {\n                isLoggedIn: userState === null || userState === void 0 ? void 0 : userState.isLoggedIn,\n                phone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                phoneLength: userState === null || userState === void 0 ? void 0 : (_userState_phone = userState.phone) === null || _userState_phone === void 0 ? void 0 : _userState_phone.length,\n                phoneType: typeof (userState === null || userState === void 0 ? void 0 : userState.phone),\n                roleId: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                userId: userState === null || userState === void 0 ? void 0 : userState.userId,\n                nickName: userState === null || userState === void 0 ? void 0 : userState.nickName\n            },\n            \"localStorage user\": {\n                phone: localUser === null || localUser === void 0 ? void 0 : localUser.phone,\n                phoneLength: localUser === null || localUser === void 0 ? void 0 : (_localUser_phone = localUser.phone) === null || _localUser_phone === void 0 ? void 0 : _localUser_phone.length,\n                phoneType: typeof (localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                userId: localUser === null || localUser === void 0 ? void 0 : localUser.userId,\n                nickName: localUser === null || localUser === void 0 ? void 0 : localUser.nickName\n            },\n            \"phone validation\": {\n                \"userState.phone exists\": !!(userState === null || userState === void 0 ? void 0 : userState.phone),\n                \"userState.phone not empty\": (userState === null || userState === void 0 ? void 0 : userState.phone) && userState.phone.trim() !== \"\",\n                \"localUser.phone exists\": !!(localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                \"localUser.phone not empty\": (localUser === null || localUser === void 0 ? void 0 : localUser.phone) && (localUser === null || localUser === void 0 ? void 0 : localUser.phone.trim()) !== \"\"\n            }\n        });\n        // 临时调用API获取最新用户信息进行对比\n        if (userState === null || userState === void 0 ? void 0 : userState.userId) {\n            try {\n                var _response_data, _response_data_phone, _response_data1, _response_data2, _response_data3, _response_data4;\n                const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_8__.userApi.getUserInfo(userState.userId);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取的最新用户信息:\", {\n                    code: response.code,\n                    phone: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.phone,\n                    phoneLength: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_phone = _response_data1.phone) === null || _response_data_phone === void 0 ? void 0 : _response_data_phone.length,\n                    phoneType: typeof ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.phone),\n                    nickName: (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.nickName,\n                    userId: (_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : _response_data4.id\n                });\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 获取用户信息失败:\", error);\n            }\n        }\n        // 如果用户已报名，跳转到任务页面\n        if (hasSubmitted) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已报名，跳转到任务页面\");\n            handleGoToTask();\n            return;\n        }\n        // 检查用户是否登录\n        if (!userState || !userState.isLoggedIn) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录，跳转到登录页面\");\n            // 未登录，显示登录对话框\n            const redirectUrl = pathname || \"/home\";\n            router.push(\"/login?redirect=\".concat(encodeURIComponent(redirectUrl)));\n            return;\n        }\n        // 检查用户身份：只有学生（roleId为1）才能报名\n        const isStudent = checkIsStudent();\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 检查用户身份\", {\n            isStudent,\n            roleId: userState === null || userState === void 0 ? void 0 : userState.roleId\n        });\n        if (!isStudent) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户不是学生身份，显示身份验证弹窗\");\n            showStudentOnlyModal();\n            return;\n        }\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 身份验证通过，开始报名流程\");\n        // 身份验证通过，直接进行报名\n        handleDirectRegistration();\n    };\n    // 处理登录成功\n    const handleLoginSuccess = ()=>{\n        notification.success(\"登录成功\");\n    // 登录成功后，页面的useEffect会自动检查报名状态\n    // 不需要在这里重复检查，让useEffect处理状态更新\n    };\n    // 直接报名处理函数\n    const handleDirectRegistration = async ()=>{\n        try {\n            var _response_data;\n            // 检查用户是否绑定了手机号\n            if (!userState.phone || userState.phone.trim() === \"\") {\n                // 显示确认对话框\n                _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].confirm({\n                    title: \"需要绑定手机号\",\n                    content: \"报名活动需要绑定手机号，是否前往绑定？\",\n                    okText: \"前往绑定\",\n                    cancelText: \"取消\",\n                    onOk: ()=>{\n                        // 跳转到登录页面，并在URL中添加参数表示需要绑定手机号\n                        const currentUrl = window.location.href;\n                        router.push(\"/login?needBindPhone=true&redirect=\".concat(encodeURIComponent(currentUrl)));\n                    },\n                    onCancel: ()=>{\n                        notification.info(\"未绑定手机号，无法报名活动\");\n                    }\n                });\n                return;\n            }\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已绑定手机号，继续报名流程\");\n            // 显示加载提示\n            const loadingMessage = notification.loading(\"正在提交报名...\");\n            // 提交报名数据（使用默认值，跳过协议确认步骤）\n            const submitData = {\n                activityId: Number(activityId),\n                agreementAccepted: true,\n                parentConsentAccepted: true,\n                parentSignaturePath: \"\",\n                signatureTime: new Date().toISOString(),\n                remark: \"快速报名时间：\".concat(new Date().toLocaleString(\"zh-CN\"))\n            };\n            const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.submitRegistration(submitData);\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                // 更新UI状态\n                setHasSubmitted(true);\n                setRegistrationStatus(1);\n                notification.success(\"报名成功！正在跳转到班级空间...\");\n                // 通知页面刷新参赛作品列表\n                if (onRefreshWorks) {\n                    onRefreshWorks();\n                }\n                // 报名成功后，获取对应的赛事任务并跳转到班级空间\n                try {\n                    // 等待一小段时间确保后端创建赛事任务完成\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    // 获取当前用户的赛事任务列表\n                    const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n                    if (eventsTaskResponse.data.code === 200) {\n                        const eventsTasks = eventsTaskResponse.data.data;\n                        // 查找与当前活动相关的赛事任务\n                        const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                        if (relatedTask) {\n                            // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                            window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                        } else {\n                            // 如果没有找到对应任务，在新标签页中打开班级空间\n                            window.open(\"/class-space\", \"_blank\");\n                        }\n                    } else {\n                        // 如果获取任务失败，在新标签页中打开班级空间\n                        window.open(\"/class-space\", \"_blank\");\n                    }\n                } catch (taskError) {\n                    console.error(\"获取赛事任务失败:\", taskError);\n                    // 即使获取任务失败，也要在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                }\n            } else {\n                var _response_data1;\n                throw new Error(((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message) || \"报名失败\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"报名失败:\", error);\n            notification.error(error instanceof Error ? error.message : \"报名失败，请稍后重试\");\n        }\n    };\n    // 处理分享按钮点击事件\n    const handleShare = ()=>{\n        if (true) {\n            // 获取当前页面URL\n            const currentUrl = window.location.href;\n            // 复制到剪贴板\n            navigator.clipboard.writeText(currentUrl).then(()=>{\n                notification.success(\"活动链接已复制，快去分享吧！\");\n            }).catch((err)=>{\n                console.error(\"复制失败:\", err);\n                notification.error(\"复制链接失败，请手动复制\");\n            });\n        }\n    };\n    // 如果还没有在客户端挂载，返回一个简单的占位符\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-64 bg-gray-200 animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 567,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-300 rounded mb-2 w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded mb-1 w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 566,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n            lineNumber: 565,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-[300px] overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-transparent to-black/30 z-10 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-blue-500/10 animate-pulse z-0 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: bannerImage,\n                                            alt: title,\n                                            fill: true,\n                                            priority: true,\n                                            style: {\n                                                objectFit: \"cover\"\n                                            },\n                                            className: \"z-10 hover:scale-105 transition-transform duration-700\",\n                                            onError: (e)=>{\n                                                const target = e.target;\n                                                target.style.background = \"linear-gradient(120deg, #f3f4f6, #e5e7eb)\";\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 590,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-4 left-4 right-4 z-20 flex justify-between items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 586,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-800 flex items-center\",\n                                        children: [\n                                            title,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"ml-2 w-5 h-5 text-yellow-400 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 681,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    organizer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: [\n                                            \"主办方: \",\n                                            organizer\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm mt-2 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-500 to-indigo-500 h-1.5 w-1.5 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"活动时间：\",\n                                            formatDate(startTime),\n                                            \" - \",\n                                            formatDate(endTime)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 680,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3 mt-4 md:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSubmitClick,\n                                        disabled: isChecking,\n                                        className: \"flex items-center px-5 py-2.5 text-sm font-medium rounded-full \".concat(hasSubmitted ? \"bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\" : \"bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\", \" transition-all duration-200\"),\n                                        children: isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"loading-spinner mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"检查状态中\"\n                                            ]\n                                        }, void 0, true) : hasSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"已报名 前往任务\"\n                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"立即报名\"\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex items-center p-2.5 text-gray-500 hover:text-blue-500 hover:bg-gray-50 rounded-full hover:scale-110 active:scale-95 transition-all duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 695,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 679,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 582,\n                columnNumber: 7\n            }, undefined),\n            (promotionImage || backgroundImage || galleryImages || attachmentFiles) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    promotionImage: promotionImage,\n                    backgroundImage: backgroundImage,\n                    galleryImages: galleryImages,\n                    attachmentFiles: attachmentFiles,\n                    activityTitle: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                    lineNumber: 746,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 745,\n                columnNumber: 9\n            }, undefined),\n            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_login_dialog__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showLoginDialog,\n                onClose: ()=>setShowLoginDialog(false),\n                onSuccess: handleLoginSuccess\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 758,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ActivityHeader, \"LrQsrruGHwY+vh0xTSNWkYGLl0w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.usePathname,\n        react_redux__WEBPACK_IMPORTED_MODULE_11__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_11__.useDispatch\n    ];\n});\n_c = ActivityHeader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ActivityHeader);\nvar _c;\n$RefreshReg$(_c, \"ActivityHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx\n"));

/***/ })

});