'use client'

import { useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { Card, Empty, Spin, Button, Select, Modal, Form, Input, Upload, DatePicker, Tooltip, Radio, Tag, Progress, Tabs, InputNumber, Rate, Switch, Checkbox, Dropdown, Avatar, Carousel, Alert } from 'antd';
import request from '../../lib/request';
import {
  PlusOutlined,
  ArrowLeftOutlined,
  UserOutlined,
  CheckSquareOutlined,
  BlockOutlined,
  ThunderboltOutlined,
  FileTextOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  TeamOutlined,
  OrderedListOutlined,
  ProjectOutlined,
  KeyOutlined,
  GiftOutlined,
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import './teacher-space.css';
import { GetNotification } from 'logic-common/dist/components/Notification';
import { getRoleTemplateList, addUserJoinRole, getTemplateInfo, updateTemplate, deleteTemplate, getOfficialTemplates, batchAddUserJoinRole } from '../../lib/api/role';
import { useSelector } from 'react-redux';
import { RootState } from '../../lib/store';
import taskApi, { TaskType, Priority, TeacherTaskParams, TaskGradeParams } from '../../lib/api/task';
import { worksApi } from '@/lib/api/works';
import { uploadApi } from '@/lib/api/upload';
import type { UploadFile } from 'antd/es/upload/interface';
import { pointsApi } from '../../lib/api/points';
import PermissionTemplateModal from '@/components/permission-template-modal';
import PermissionModal from '@/components/permission-modal';

import { studentApi } from '@/lib/api/student';
import { classApi } from '@/lib/api/class';
import { getUserCurrentTemplate, getStudentTemplates } from '../../lib/api/role';
import SchoolList from './components/school-list';
import TemplateList from './components/template-list';
import ClassList from './components/class-list';
import { StudentList } from './components/student-list';
import { TaskList as TaskListComponent } from './components/task-list';
import { ProjectList } from './components/project-list';
import {
  AddStudentModal,
  ImportStudentModal,
  TemplateUsageModal,
  AssignBlocksModal,
  CreateClassModal,
  TransferClassModal,
  PublishTaskModal,
  JoinClassModal,
  EditClassModal,
  ResetPasswordModal,
  InviteCodeModal
} from './components/modals';
import { Student, Task, Assignment, Project, AddStudentForm, School, Class, PermissionTemplate, SearchedTeacher, PermissionTemplateDisplay } from './types/index';
import {
  isMunicipality,
} from './utils';
import { fetchSchools, fetchSchoolStats } from './utils/school';
import {
  createInitialMultiSelectState,
  fetchStudents,
  handleStudentMouseDown,
  handleStudentMouseEnter,
  handleStudentMouseUp,
  handleStudentMultiSelect,
  type MultiSelectState
} from './utils/student';



const { Dragger } = Upload;

// 导入新组件
import { AssignPointsModal } from './components/assign-points-modal';
import { ExpandableSearch } from './components/expandable-search';
import { isAllowedFileType, isFileSizeAllowed, formatFileSize } from './utils';
import * as XLSX from 'xlsx';
import { keyPackageApi } from '@/lib/api/key_package';
import { BatchUseKeyPackageModal } from './components/batch-use-key-package-modal';
import { AxiosResponse } from 'axios';
import userApi from '@/lib/api/user';


interface StudentsCache {
  [key: number]: Student[];
}

interface TasksCache {
  [key: number]: any[];
}

interface ProjectsCache {
  [key: number]: any[];
}

export default function ClassSpace() {
  const router = useRouter();
  const roleId = useSelector((state: RootState) => state.user.userState.roleId);
  const userId = useSelector((state: RootState) => state.user.userState.userId);
  const avatarUrl = useSelector((state: RootState) => state.user.userState.avatarUrl);
  const isTeacherView = roleId === 2;  // 移到组件顶部
  const notification = GetNotification();

  const [selectedSchool, setSelectedSchool] = useState<School | null>(null);
  const [schools, setSchools] = useState<School[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [currentClassId, setCurrentClassId] = useState<number | null>(null);
  const [userRoles, setUserRoles] = useState<Array<{ userId: number, roleId: number }>>([]);
  const [loading, setLoading] = useState(true);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [isImportModalVisible, setIsImportModalVisible] = useState(false);
  const [isCreateClassModalVisible, setIsCreateClassModalVisible] = useState(false);
  const [isEditClassModalVisible, setIsEditClassModalVisible] = useState(false);
  const [editingClassId, setEditingClassId] = useState<number | null>(null);
  const [isMultiSelect, setIsMultiSelect] = useState(false);
  const [selectedStudents, setSelectedStudents] = useState<number[]>([]);
  const [multiSelectState, setMultiSelectState] = useState<MultiSelectState>(createInitialMultiSelectState());
  const [isPublishTaskModalVisible, setIsPublishTaskModalVisible] = useState(false);
  const [isAssignBlocksModalVisible, setIsAssignBlocksModalVisible] = useState(false);
  const [isAssignPointsModalVisible, setIsAssignPointsModalVisible] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [activeTab, setActiveTab] = useState('official'); // 修改默认标签为官方模板
  const [tasks, setTasks] = useState<Task[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [projectLoading, setProjectLoading] = useState(false);
  const [loadingTemplates, setLoadingTemplates] = useState(false);
  const [templates, setTemplates] = useState<PermissionTemplate[]>([]);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [taskFilter, setTaskFilter] = useState({
    type: undefined,
    priority: undefined
  });
  const [isJoinClassModalVisible, setIsJoinClassModalVisible] = useState(false);
  const [isTransferModalVisible, setIsTransferModalVisible] = useState(false);
  const [selectedStudentId, setSelectedStudentId] = useState<number | null>(null);
  const [searchedTeacher, setSearchedTeacher] = useState<SearchedTeacher | null>(null);
  const [isResetPasswordModalVisible, setIsResetPasswordModalVisible] = useState(false);
  const [isBatchResetPassword, setIsBatchResetPassword] = useState(false);

  // 在组件内添加新的状态
  const [displayTemplates, setDisplayTemplates] = useState<PermissionTemplateDisplay[]>([]);
  const [templateLoading, setTemplateLoading] = useState(false);

  // 添加新的状态
  const [isTemplateModalVisible, setIsTemplateModalVisible] = useState(false);

  // Form 实例
  const [form] = Form.useForm<AddStudentForm>();
  const [pointsForm] = Form.useForm();
  const [taskForm] = Form.useForm();
  const [createClassForm] = Form.useForm();
  const [editClassForm] = Form.useForm();
  const [joinClassForm] = Form.useForm();
  const [transferForm] = Form.useForm();
  const [templateForm] = Form.useForm();  // 添加这行

  // 添加新的状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartIndex, setDragStartIndex] = useState<number | null>(null);


  // 添加新的状态来记录鼠标按下的时间和位置
  const [mouseDownTime, setMouseDownTime] = useState<number>(0);
  const [mouseDownPos, setMouseDownPos] = useState<{ x: number, y: number } | null>(null);

  // 添加新的状态来记录拖动开始时每个学生的选中状态
  const [dragStartStates, setDragStartStates] = useState<Map<number, boolean>>(new Map());

  // 添加状态
  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(null);

  // 添加编辑模板的状态
  const [isEditTemplateModalVisible, setIsEditTemplateModalVisible] = useState(false);
  const [editingTemplateId, setEditingTemplateId] = useState<number | null>(null);

  // 添加状态
  const [officialTemplates, setOfficialTemplates] = useState<PermissionTemplateDisplay[]>([]);
  const [loadingOfficialTemplates, setLoadingOfficialTemplates] = useState(false);

  // 1. 首先在组件顶部添加新的状态
  const [teacherTemplate, setTeacherTemplate] = useState<any>(null);
  const [studentTemplateUsage, setStudentTemplateUsage] = useState<{ [key: number]: number }>({}); // 记录每个模板被多少学生使用

  // 添加学生列表缓存状态
  const [studentsCache, setStudentsCache] = useState<StudentsCache>({});
  const [tasksCache, setTasksCache] = useState<TasksCache>({});
  const [projectsCache, setProjectsCache] = useState<ProjectsCache>({});
  const [loadingStudents, setLoadingStudents] = useState(false);

  // 添加状态存储特殊模板ID
  const [specialTemplateIdState, setSpecialTemplateIdState] = useState<number | null>(null);

  // 在组件顶部添加一个 ref
  const studentsCacheRef = useRef<StudentsCache>({});

  // 添加新的useEffect，用于处理学生列表为空时的缓存同步
  // 获取并存储特殊模板ID的函数
  const fetchAndStoreSpecialTemplateId = async () => {
    try {
      const response = await getOfficialTemplates();
      if (response.data.code === 200) {
        const specialTemplateData = response.data.data.find((template: any) => template.userId === -1);
        if (specialTemplateData) {
          setSpecialTemplateIdState(specialTemplateData.id);
          //console.log('获取到特殊模板ID:', specialTemplateData.id);
        } else {
          console.warn('未找到系统默认特殊模板 (userId === -1)');
        }
      }
    } catch (error) {
      console.error('获取特殊模板ID失败:', error);
    }
  };

  // 在组件加载时获取特殊模板ID
  useEffect(() => {
    fetchAndStoreSpecialTemplateId();
  }, []);

  // 修改处理编辑模板的函数
  const handleEditTemplate = (template: PermissionTemplateDisplay) => {
    setEditingTemplateId(template.id);
    setIsEditTemplateModalVisible(true);
  };

  // 添加编辑模板成功的回调函数
  const handleEditTemplateSuccess = () => {
    setIsEditTemplateModalVisible(false);
    setEditingTemplateId(null);
    fetchDisplayTemplates();  // 刷新模板列表
    notification.success('模板更新成功');
  };

  // 修改鼠标按下事件处理函数
  const handleMouseDown = (student: Student, index: number, e: React.MouseEvent) => {
    handleStudentMouseDown(
      e,
      student,
      index,
      students,
      selectedStudents,
      isMultiSelect,
      multiSelectState,
      {
        setMultiSelectState,
        setIsMultiSelect,
        setSelectedStudents,
        onMouseUp: (e: MouseEvent) => handleMouseUp(e)
      }
    );
  };

  // 修改鼠标移入事件处理函数
  const handleMouseEnter = (student: Student, index: number) => {
    handleStudentMouseEnter(
      student,
      index,
      students,
      multiSelectState,
      {
        setSelectedStudents,
        setIsMultiSelect
      }
    );
  };

  // 修改鼠标松开事件处理函数
  const handleMouseUp = (e: MouseEvent) => {
    handleStudentMouseUp(
      e,
      students,
      multiSelectState,
      {
        setSelectedStudents,
        setIsMultiSelect,
        setMultiSelectState
      }
    );
  };

  // 添加 useEffect 清理函数
  useEffect(() => {
    return () => {
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [handleMouseUp]);



  // 修改处理多选的函数
  const handleMultiSelect = (student: Student) => {
    handleStudentMultiSelect(student, {
      setSelectedStudents,
      setIsMultiSelect
    });
  };



  // 修改获取学校列表的函数
  const handleFetchSchools = async () => {
    try {
      setLoading(true);
      const schoolsData = await fetchSchools(userId);
      setSchools(schoolsData);
    } catch (error) {
      console.error('获取学校列表失败:', error);
      notification.error('获取学校列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取学校下的班级列表
  const fetchClasses = async (schoolId: number) => {
    try {
      const response = await classApi.getTeacherClasses(schoolId, userId) as AxiosResponse<any>;
      if (response.data.code === 200) {
        const classList = response.data.data;
        setClasses(classList);
        // 如果有班级，自动选择第一个班级并设置默认标签页为学生列表
        if (classList && classList.length > 0) {
          setCurrentClassId(classList[0].id);
          setActiveTab('students');
          handleFetchStudents(classList[0].id);  // 使用 handleFetchStudents 替代 fetchStudents
          fetchTasks(classList[0].id);
          fetchProjects(classList[0].id);
        } else {
          // 如果没有班级，清空相关状态
          setCurrentClassId(null);
          setStudents([]);
          setTasks([]);
          setProjects([]);
        }
      }
    } catch (error) {
      console.error('获取班级列表失败:', error);
      notification.error('获取班级列表失败');
    }
  };

  // 修改处理获取学生的函数
  const handleFetchStudents = async (classId: number) => {
    console.log("开始读取！");

    let studentsData: Student[] = [];

    // 如果有缓存数据，先使用缓存数据
    if (studentsCacheRef.current[classId]) {
      console.log("使用 ref 缓存", studentsCacheRef.current[classId]);
      studentsData = studentsCacheRef.current[classId];
      setStudents(studentsData);
    } else if (studentsCache[classId]) {
      console.log("使用状态缓存", studentsCache[classId]);
      studentsData = studentsCache[classId];
      setStudents(studentsData);
    } else {
      // 如果没有缓存，显示加载状态
      setLoadingStudents(true);
      try {
        studentsData = await fetchStudents(classId);

        // 更新缓存和当前显示的学生列表
        setStudentsCache(prev => ({
          ...prev,
          [classId]: studentsData
        }));
        setStudents(studentsData);
      } catch (error) {
        console.error('获取学生列表失败:', error);
        notification.error('获取学生列表失败');
        setLoadingStudents(false);
        return;
      } finally {
        setLoadingStudents(false);
      }
    }

    // 无论使用缓存还是新获取的数据，都更新userRoles
    const newUserRoles = studentsData.map(student => ({
      userId: student.userId,
      roleId: 1 // 学生角色ID为1
    }));

    // 添加教师自己的角色
    newUserRoles.push({
      userId: userId,
      roleId: roleId || 2 // 教师角色ID为2
    });

    setUserRoles(newUserRoles);
  };

  // 修改选择班级的处理函数
  const handleClassSelect = (classId: number) => {
    setCurrentClassId(classId);
    // 如果选择了新班级，检查是否有缓存，如果没有则加载
    handleFetchStudents(classId);
  };

  // 添加刷新学生列表的函数
  const refreshStudentList = async () => {
    console.log("正在刷新学生列表");

    if (currentClassId) {
      setLoadingStudents(true);
      try {
        const updatedStudents = await fetchStudents(currentClassId);
        console.log("获取到新的学生数据为:", updatedStudents);
        setStudents(updatedStudents);

        // 更新缓存
        setStudentsCache(prev => ({
          ...prev,
          [currentClassId]: updatedStudents
        }));
        console.log("更新后的缓存数据为", updatedStudents, {
          ...studentsCache,
          [currentClassId]: updatedStudents
        });

      } catch (error) {
        console.error('刷新学生列表失败:', error);
        notification.error('刷新学生列表失败');
      } finally {
        setLoadingStudents(false);
      }
    }
  };

  // 修改处理分配积木的函数
  const handleAssignBlocks = async (studentId: number) => {
    setSelectedStudentId(studentId);
    await fetchTemplates();
    await fetchTemplateUsage();
    setIsAssignBlocksModalVisible(true);
  };

  // 修改处理选择模板的函数
  const handleSelectTemplate = async (templateId: number) => {
    try {
      if (selectedStudentId === null && selectedStudents.length === 0) {
        notification.warning('请先选择学生');
        return;
      }

      const hideLoading = notification.loading('正在分配积木...');
      const userRolesMap = userRoles || [];
      const targetUsers = selectedStudentId ? [selectedStudentId] : selectedStudents;

      // 准备用户数据
      const usersData = targetUsers
        .map(userId => {
          const userInfo = userRolesMap.find(u => u.userId === userId);
          if (!userInfo?.roleId) return null;
          return {
            userId: userId,
            roleId: userInfo.roleId,
            templateId: templateId,
            originalTemplateId: templateId
          } as const;
        })
        .filter((item): item is { userId: number; roleId: number; templateId: number; originalTemplateId: number } => item !== null);

      if (usersData.length === 0) {
        if (hideLoading) {
          hideLoading.close();
        }
        notification.error('无有效用户可分配');
        return;
      }

      // 分批并发处理
      const results = await (usersData.length > 20
        ? Promise.all(
          Array.from(
            { length: Math.ceil(usersData.length / 20) },
            (_, i) => usersData.slice(i * 20, (i + 1) * 20)
          ).map(batchUsers =>
            batchAddUserJoinRole({ users: batchUsers })
              .then(({ data }) => data)
              .catch(() => ({ code: 500, data: { successCount: 0, failCount: batchUsers.length } }))
          )
        ).then(results => ({
          code: results.some(r => r.code !== 200) ? 500 : 200,
          data: {
            successCount: results.reduce((sum, r) => sum + (r.data?.successCount || 0), 0),
            failCount: results.reduce((sum, r) => sum + (r.data?.failCount || 0), 0)
          }
        }))
        : batchAddUserJoinRole({ users: usersData })
          .then(({ data }) => data)
          .catch(() => ({ code: 500, data: { successCount: 0, failCount: usersData.length } }))
      );

      if (hideLoading) {
        hideLoading.close();
      }


      if (results.code === 200) {
        const { successCount, failCount } = results.data;

        if (successCount > 0) {
          notification.success(`成功分配积木给 ${successCount} 个用户`);
        }
        if (failCount > 0) {
          notification.error(`${failCount} 个用户分配失败`);
        }

        setIsAssignBlocksModalVisible(false);
        setSelectedStudentId(null);
        await refreshStudentList();
      } else {
        notification.error('积木分配失败');
      }
    } catch (error) {
      notification.error('积木分配失败');
      console.error('分配积木失败:', error);
    }
  };

  // 处理文件上传
  const handleUpload = async (file: File) => {
    if (!isAllowedFileType(file.type)) {
      notification.error(`不支持的文件类型: ${file.type}`);
      return false;
    }

    if (!isFileSizeAllowed(file.size)) {
      notification.error(`文件大小超过限制: ${formatFileSize(file.size)}，最大允许10MB`);
      return false;
    }

    try {
      setUploading(true);
      const hideLoading = notification.loading('正在上传文件...');

      const url = await uploadApi.uploadToOss(file);

      if (hideLoading) {
        hideLoading.close();
      }
      notification.success('上传成功');

      const newFile = {
        uid: Date.now().toString(),
        name: file.name,
        status: 'done' as const,
        url: url,
        type: file.type,
        size: file.size
      };

      setFileList(prev => [...prev, newFile]);
      return newFile;
    } catch (error) {
      console.error('文件上传失败:', error);
      notification.error('上传失败');
      return false;
    } finally {
      setUploading(false);
    }
  };

  // 处理文件删除
  const handleRemoveFile = async (file: UploadFile) => {
    try {
      if (file.url) {
        await uploadApi.deleteFromOss(file.url);
        // 删除成功后再更新状态
        setFileList(fileList.filter(item => item.uid !== file.uid));
        notification.success('删除成功');
        return true;
      }
      return false;
    } catch (error) {
      console.error('删除文件失败:', error);
      notification.error('删除文件失败');
      return false;
    }
  };

  // 修改处理发布任务的函数
  const handlePublishTask = async (values: any) => {
    try {
      // 检查是否有文件正在上传
      if (uploading) {
        notification.warning('请等待文件上传完成');
        return;
      }

      // 检查所有文件是否都上传成功
      const allFilesUploaded = fileList.every(file => file.status === 'done' && file.url);
      if (!allFilesUploaded) {
        notification.warning('有文件未上传完成');
        return;
      }

      const params = {
        ...values,
        taskType: 1,
        priority: 0,
        startDate: values.startDate?.toDate(),
        endDate: values.endDate?.toDate(),
        studentIds: selectedStudents,
        classId: currentClassId || undefined,
        templateId: values.templateId,
        // 处理多个作品ID的情况，将作品ID数组转换为逗号分隔的字符串
        workIdsStr: values.workIds ?
          (Array.isArray(values.workIds) ? values.workIds.join(',') : String(values.workIds)) :
          undefined,
        attachments: fileList.map(file => file.url).filter(Boolean),
        references: values.references || []
      };

      //console.log('发布任务参数:', params); // 添加日志，检查参数
      const response = await taskApi.publishTask(params);
      if (response.data.code === 200) {
        notification.success('发布任务成功');
        setIsPublishTaskModalVisible(false);
        taskForm.resetFields();
        setSelectedStudents([]);
        setFileList([]);
        setSelectedTemplateId(null);
        // 取消多选模式
        setIsMultiSelect(false);
        setMultiSelectState(createInitialMultiSelectState());
        if (currentClassId) {
          fetchTasks(currentClassId);
        }
      }
    } catch (error) {
      console.error('发布任务失败:', error);
      notification.error('发布任务失败');
    }
  };

  // 获取权限模板列表
  const fetchTemplates = async () => {
    setLoadingTemplates(true);
    try {
      // 同时获取教师自定义模板和官方模板
      const [customResponse, officialResponse] = await Promise.all([
        getRoleTemplateList(userId),
        getOfficialTemplates()
      ]);
      console.log(customResponse);

      if (customResponse.data.code === 200 && officialResponse.data.code === 200) {
        const customTemplates = customResponse.data.data || [];
        const officialTemplates = officialResponse.data.data || [];

        // 为官方模板添加标记
        const markedOfficialTemplates = officialTemplates.map((template: any) => ({
          ...template,
          isOfficial: true
        }));

        // 合并所有模板
        const allTemplates = [...customTemplates, ...markedOfficialTemplates];
        setTemplates(allTemplates);
      }
    } catch (error) {
      console.error('获取模板列表失败:', error);
      notification.error('获取模板列表失败');
    } finally {
      setLoadingTemplates(false);
    }
  };

  // 处理单个学生分配能量
  const handleAssignPoints = async (values: { availablePoints: number; studentExpiries?: { [id: number]: string | undefined }; remark?: string }) => {
    if (!selectedStudent) {
      notification.error('请先选择学生');
      return;
    }

    // 从 studentExpiries 中提取单个学生的过期时间
    const expireTime = values.studentExpiries?.[selectedStudent.userId];

    try {
      await pointsApi.assignPermission({
        studentUserId: selectedStudent.userId,
        availablePoints: values.availablePoints,
        expireTime: expireTime, // 使用提取出的过期时间
        remark: values.remark
      });
      notification.success('分配能量成功');
      setIsAssignPointsModalVisible(false);
      // 刷新学生列表
      await refreshStudentList()
    } catch (error: any) {
      // 增加更具体的错误提示
      notification.error(error.response?.data?.message || '分配能量失败');
    }
  };

  // 添加学生的处理函数
  const handleAddStudent = async (values: AddStudentForm) => {
    try {
      //console.log('添加班级ID:', currentClassId);
      values.password = '123456';
      // //console.log('添加学生参数:', values);
      if (currentClassId == null) {
        notification.error('请先选择班级');
        return;
      }
      const response = await classApi.addStudentToClass(currentClassId, values);

      // 打印完整的响应数据
      console.log('添加学生 API 响应:', response.data);

      // 检查外层状态码
      if (response.data.code === 200) {
        // 检查内层状态码
        if (response.data?.code !== 200) {
          notification.error(response.data?.message);
          return;
        }
        notification.success('添加学生成功');
        setIsAddModalVisible(false);
        form.resetFields();

        // 获取新添加的学生ID (假设API响应中包含)
        const newStudentUserId = response.data.data?.data?.userId; // 修正路径

        // 自动分配特殊模板
        if (newStudentUserId && specialTemplateIdState !== null) {
          try {
            await addUserJoinRole({
              userId: newStudentUserId,
              roleId: 1, // 学生角色ID
              templateId: specialTemplateIdState
            });
            notification.info('已自动为新学生分配默认权限模板', 1.5);
          } catch (templateError) {
            console.error('为新学生分配模板失败:', templateError);
            notification.warning('添加学生成功，但自动分配默认权限失败，请稍后手动分配');
          }
        } else if (!newStudentUserId) {
          console.warn('添加学生响应中未找到userId，无法自动分配模板');
        } else if (specialTemplateIdState === null) {
          console.warn('未获取到特殊模板ID，无法自动分配模板');
        }

        // 刷新数据
        if (selectedSchool) {
          await fetchClasses(selectedSchool.id);
        }
        if (currentClassId) {
          // 获取最新的学生列表
          const updatedStudents = await fetchStudents(currentClassId);
          // 更新缓存和当前显示的学生列表
          setStudentsCache(prev => ({
            ...prev,
            [currentClassId]: updatedStudents
          }));
          setStudents(updatedStudents);
        }
      } else {
        notification.error(response.data.msg || '添加学生失败');
      }
    } catch (error: any) {
      console.error('添加学生失败:', error);
      notification.error('系统错误，请稍后重试');
    }
  };

  // 修改导入学生的处理函数
  const handleImportStudents = async (file: File) => {
    try {
      const reader = new FileReader();

      reader.onload = async (e) => {
        try {
          const data = e.target?.result;
          const workbook = XLSX.read(data, { type: 'binary' });
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];

          const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as string[][];

          const students = rows
            .slice(1)
            .filter(row => row.length >= 2 && row[0] && row[1])
            .map(row => ({
              studentNumber: String(row[0]).trim(),
              nickName: String(row[1]).trim()
            }));

          if (students.length === 0) {
            notification.error('未找到有效的学生数据，请检查文件格式');
            return;
          }

          // 保存当前选中的班级ID
          const targetClassId = currentClassId;
          if (!targetClassId) {
            notification.error('请先选择班级');
            return;
          }

          const response = await classApi.importStudents(targetClassId, students);

          // 打印导入API的响应
          console.log('导入学生 API 响应:', response.data);

          if (response.data.code === 200) {
            // 主要成功路径
            notification.success(`成功导入 ${students.length} 名学生`);
            setIsImportModalVisible(false);

            // 自动为导入的学生分配特殊模板
            const importedStudentsData = response.data.data;
            // 确保返回的是非空数组
            if (Array.isArray(importedStudentsData) && importedStudentsData.length > 0) {
              if (specialTemplateIdState !== null) {
                let templateAssignSuccessCount = 0;
                let templateAssignFailCount = 0;
                const assignPromises = importedStudentsData.map(async (studentData) => {
                  if (studentData?.userId) {
                    try {
                      await addUserJoinRole({
                        userId: studentData.userId,
                        roleId: 1, // 学生角色ID
                        templateId: specialTemplateIdState
                      });
                      templateAssignSuccessCount++;
                    } catch (templateError) {
                      console.error(`为导入学生 ${studentData.userId} 分配模板失败:`, templateError);
                      templateAssignFailCount++;
                    }
                  } else {
                    console.warn('导入响应中缺少userId，跳过分配模板');
                    templateAssignFailCount++; // 也算作失败
                  }
                });

                // 等待所有分配操作完成
                await Promise.all(assignPromises);

                // 显示模板分配结果消息
                if (templateAssignSuccessCount > 0) {
                  notification.info(`已自动为 ${templateAssignSuccessCount} 名导入学生分配默认权限`, 2);
                }
                if (templateAssignFailCount > 0) {
                  notification.warning(`为 ${templateAssignFailCount} 名导入学生自动分配默认权限失败，请稍后手动分配`);
                }
              } else {
                // 如果没有获取到特殊模板ID
                notification.warning('未能获取默认模板ID，无法自动为导入学生分配权限');
              }
            } else {
              // 如果API成功但未返回有效的学生数据数组
              console.warn("导入API成功，但未返回有效的学生数据数组");
            }

            // 在所有消息显示后，再刷新列表
            try {
              if (targetClassId) { // 确保 targetClassId 可用
                // 1. 刷新当前班级的学生列表
                console.log(`刷新学生列表，班级ID: ${targetClassId}`);
                const updatedStudents = await fetchStudents(targetClassId);
                console.log(`获取到 ${updatedStudents.length} 个新学生数据`);

                // 2. 更新缓存和当前显示的学生列表
                setStudentsCache(prev => ({ ...prev, [targetClassId]: updatedStudents }));
                setStudents(updatedStudents);
                console.log('学生缓存和显示列表已更新');

                // 3. 手动更新 classes 状态中的学生数量
                setClasses(prevClasses =>
                  prevClasses.map(cls =>
                    cls.id === targetClassId
                      ? { ...cls, studentCount: updatedStudents.length }
                      : cls
                  )
                );
                console.log(`班级 ${targetClassId} 的学生数量已更新为 ${updatedStudents.length}`);

              } else {
                console.warn('targetClassId 未定义，无法刷新列表');
              }
            } catch (refreshError) {
              console.error("刷新学生列表失败:", refreshError);
              notification.error("导入成功，但刷新列表失败");
            }

          } else {
            // 主要失败路径 1

            const errorMessage = response.data.msg || '导入学生失败，请检查文件或联系管理员';
            notification.error(errorMessage);
          }
        } catch (error) {
          console.error('处理Excel文件失败:', error);
          notification.error('导入失败，请检查文件格式是否正确');
        }
      };

      reader.onerror = () => {
        notification.error('读取文件失败');
      };

      reader.readAsBinaryString(file);
      return false;
    } catch (error) {
      console.error('导入学生失败:', error);
      notification.error('导入失败，请检查文件格式是否正确');
      return false;
    }
  };

  // 修改处理移除学生的函数
  const handleRemoveStudent = async (studentId: number) => {
    try {
      // 先获取学生信息用于显示
      const student = students.find(s => s.userId === studentId);
      if (!student) return;

      // 显示确认对话框
      Modal.confirm({
        title: '确认移出班级',
        content: (
          <div>
            <p>确定要将 {student.nickName} 移出班级吗？</p>
            {student.availablePoints > 0 && (
              <div className="mt-2 p-3 bg-yellow-50 rounded-lg">
                <div className="flex items-center gap-2 text-yellow-600">
                  <InfoCircleOutlined />
                  <span>该学生还有 {student.availablePoints} 点可用能量</span>
                </div>
                <p className="mt-1 text-sm text-yellow-500">
                  移出班级后，可用能量将返还到学生的套餐积分中
                </p>
              </div>
            )}
          </div>
        ),
        okText: '确定移出',
        cancelText: '取消',
        centered: true,
        okButtonProps: { danger: true },
        onOk: async () => {
          const response = await classApi.removeStudentFromClass([studentId]);

          if (response.data.code === 200) {
            if (response.data.data.results && response.data.data.results.length > 0) {
              const result = response.data.data.results[0];
              if (result.success && result.availablePoints > 0) {
                notification.success(
                  `已将学生移出班级！${result.availablePoints} 点可用能量已返还到套餐积分中`
                );
              } else if (result.success) {
                notification.success('已将学生移出班级！');
              } else {
                notification.error(result.message || '移除学生失败');
              }
            } else {
              notification.success('已将学生移出班级！');
            }

            if (selectedSchool) {
              await fetchClasses(selectedSchool.id);
            }
            if (currentClassId) {
              // 更新缓存中的学生列表
              const updatedStudents = students.filter(s => s.userId !== studentId);
              setStudents(updatedStudents);

              // 同步更新 ref
              studentsCacheRef.current[currentClassId] = updatedStudents;

              // 也执行异步的状态更新
              setStudentsCache(prev => {
                const updatedCache = { ...prev };
                updatedCache[currentClassId] = updatedStudents;
                return updatedCache;
              });
            }
          }
        }
      });
    } catch (error: any) {
      if (error.response?.data?.message) {
        notification.error(error.response.data.message);
      } else {
        notification.error('操作失败');
      }
      console.error('移除学生失败:', error);
    }
  };

  // 修改生成邀请码的处理函数
  const handleGenerateInviteCode = async (classId: number) => {
    try {
      const response = await classApi.generateInviteCode(classId);

      if (response.data.code === 200) {
        Modal.success({
          content: <InviteCodeModal inviteCode={response.data.data.inviteCode} />,
          icon: null,
          centered: true,
          footer: null,
          width: 500
        });
      }
    } catch (error) {
      notification.error('生成邀请码失败');
    }
  };

  // 修改创建班级的处理函数
  const handleCreateClass = async (values: { className: string }) => {
    if (!selectedSchool) {
      notification.error('请先选择学校');
      return;
    }

    try {
      const response = await classApi.createClass(selectedSchool.id, values.className, userId);

      if (response.data.code === 200) {
        notification.success('创建班级成功');
        setIsCreateClassModalVisible(false);
        createClassForm.resetFields();
        // 刷新班级列表
        fetchClasses(selectedSchool.id);
      } else if (response.data === "") {
        notification.error('该班级已存在');
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        notification.error(error.response.data.message);
      } else {
        notification.error('创建班级失败');
      }
    }
  };

  // 修改加入班级的处理函数
  const handleJoinClass = async (values: { inviteCode: string }) => {
    try {
      const response = await classApi.joinClassByTeacher(values.inviteCode, userId);

      if (response.data.code === 200) {
        notification.success('成功加入班级');
        setIsJoinClassModalVisible(false);
        // 刷新学校列表
        await handleFetchSchools();
        // 如果当前已选择了学校，刷新班级列表
        if (selectedSchool) {
          await fetchClasses(selectedSchool.id);
        }
      } else if (response.data === "") {
        notification.error("该班级已有协助教师");
      } else {
        notification.error("邀请码已过期");
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        notification.error(error.response.data.message);
      } else {
        notification.error('加入班级失败');
      }
    }
  };

  // 修改编辑班级的处理函数
  const handleEditClass = async (values: { className: string }) => {
    if (!editingClassId) return;

    try {
      const response = await classApi.updateClass(editingClassId, values);

      if (response.data.code === 200) {
        notification.success('更新班级成功');
        setIsEditClassModalVisible(false);
        setEditingClassId(null);
        // 刷新班级列表
        if (selectedSchool) {
          await fetchClasses(selectedSchool.id);
        }
      } else {
        notification.error(response.data.message || '更新班级失败');
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        notification.error(error.response.data.message);
      } else {
        notification.error('更新班级失败');
      }
    }
  };

  // 修改删除班级的处理函数
  const handleDeleteClass = async (classInfo: Class) => {
    // 先检查是否有学生
    if (classInfo.studentCount && classInfo.studentCount > 0) {
      Modal.warning({
        title: '无法删除班级',
        centered: true,
        content: (
          <div>
            <p>班级中还有 {classInfo.studentCount} 名学生，请先移除所有学生后再删除班级。</p>
            <div className="mt-4 text-gray-500 text-sm">
              <p>删除步骤：</p>
              <ol className="list-decimal ml-4 mt-2">
                <li>点击班级查看学生列表</li>
                <li>移除所有学生</li>
                <li>再次尝试删除班级</li>
              </ol>
            </div>
          </div>
        ),
        okText: '知道了'
      });
      return;
    }

    // 如果没有学生，显示删除确认对话框
    Modal.confirm({
      title: '确认删除班级',
      content: (
        <div>
          <p>您确定要删除 {classInfo.grade}{classInfo.className} 吗？</p>
          <p className="text-red-500 mt-2">此操作不可恢复！</p>
        </div>
      ),
      okText: '确定删除',
      cancelText: '取消',
      okButtonProps: { danger: true },
      centered: true,
      onOk: async () => {
        try {
          const response = await classApi.deleteClass(classInfo.id);

          if (response.data.code === 200) {
            notification.success('删除班级成功');
            // 刷新班级列表
            if (selectedSchool) {
              fetchClasses(selectedSchool.id);
            }
          }
        } catch (error: any) {
          if (error.response?.data?.message) {
            notification.error(error.response.data.message);
          } else {
            notification.error('删除班级失败');
          }
        }
      }
    });
  };

  // 添加搜索教师的函数
  const handleSearchTeacher = async (phone: string) => {
    try {
      const response = await classApi.searchTeacherByPhone(phone);
      console.log(response);
      if (response.data.code === 200) {
        setSearchedTeacher(response.data.data);
      }
    } catch (error: any) {
      notification.error(error);
    }
  };

  // 添加转让处理函数
  const handleTransferClass = async (values: { transferType: 'search' | 'assistant'; phone?: string }, currentClass: Class) => {
    try {
      let newTeacherId: number;

      if (values.transferType === 'search') {
        if (!searchedTeacher) {
          notification.error('请先搜索并选择教师');
          return;
        }
        newTeacherId = searchedTeacher.id;
      } else {
        // 检查是否有协助教师
        if (!currentClass.assistantTeacherId) {
          notification.error('该班级没有协助教师');
          return;
        }
        newTeacherId = currentClass.assistantTeacherId;
      }

      const response = await classApi.transferClass(currentClass.id, newTeacherId, values.transferType);

      if (response.data.code === 200) {
        notification.success('转让班级成功');
        setIsTransferModalVisible(false);
        transferForm.resetFields();
        setSearchedTeacher(null);
        // 刷新班级列表
        if (selectedSchool) {
          fetchClasses(selectedSchool.id);
        }
      }
    } catch (error: any) {
      console.error('转让班级失败:', error);
      if (error.response?.data?.message) {
        notification.error(error.response.data.message);
      } else {
        notification.error('转让班级失败');
      }
    }
  };

  // 修改获取任务列表的函数
  const fetchTasks = async (classId: number) => {
    try {

      const response = await taskApi.getTaskList({
        classId,
        page: 1,
        size: 50,
        ...taskFilter,
        teacherId:Number(localStorage.getItem('userId'))
      });
      if (response.data.code === 200) {
        const taskList = response.data.data.list;
        setTasks(taskList);
        // 更新缓存
        setTasksCache(prev => ({
          ...prev,
          [classId]: taskList
        }));
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      notification.error('获取任务列表失败');
    }
  };

  // 添加获取班级项目的函数
  const fetchProjects = async (classId: number) => {
    try {
      setProjectLoading(true);
      const response = await worksApi.searchProjects(
        classId
      );
      if (response.data.code === 200) {
        const projectList = response.data.data?.list || [];
        setProjects(projectList);
        // 更新缓存
        setProjectsCache(prev => ({
          ...prev,
          [classId]: projectList
        }));
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      // notification.error('请检查网络以及登录状态');
    } finally {
      setProjectLoading(false);
    }
  };

  const handleViewWork = async (workId: number) => {
    try {
      const loadingMessage = notification.loading('正在加载作品...');
      const response = await worksApi.getDetail(workId);
      if (response?.data?.code === 200) {
        const content = response.data.data.content;
        if (!content) {
          if (loadingMessage) {
            loadingMessage.close();
          }
          return;
        }
        const newWindow = window.open('', '_blank');
        if (newWindow) {
          newWindow.localStorage.setItem('editWorkContent', content);
          newWindow.localStorage.setItem('editWorkId', String(workId));
          newWindow.location.href = '/logicleap';
        }
      }
      if (loadingMessage) {
        loadingMessage.close();
      }
    } catch (error) {
      console.error('获取作品失败:', error);
      // notification.error('请检查网络以及登录状态');
    }
  };

  // 修改 Tab 切换处理函数
  const handleTabChange = (key: 'students' | 'tasks' | 'projects') => {
    setActiveTab(key);
    setIsMultiSelect(false);
    setSelectedStudents([]);

    // 如果切换到项目标签，加载项目列表
    if (key === 'projects' && currentClassId) {
      fetchProjects(currentClassId);
    }
  };

  // 添加获取展示用的权限模板列表函数
  const fetchDisplayTemplates = async () => {
    setTemplateLoading(true);
    try {
      const response = await getRoleTemplateList(userId);
      if (response.data.code === 200) {
        setDisplayTemplates(response.data.data);
      }
    } catch (error) {
      console.error('获取权限模板列表失败:', error);
    } finally {
      setTemplateLoading(false);
    }
  };

  // 在 useEffect 中添加获取模板列表的调用
  useEffect(() => {
    handleFetchSchools();
  }, []);

  // 添加 useEffect 来处理全局鼠标事件
  useEffect(() => {
    if (isMultiSelect) {
      // 添加全局鼠标事件监听
      window.addEventListener('mouseup', handleMouseUp);
      return () => {
        window.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isMultiSelect]);

  // 添加处理创建模板成功的函数
  const handleTemplateSuccess = () => {
    fetchDisplayTemplates();  // 刷新模板列表
  };

  // Add this function near other handler functions
  const handlePublishTaskClick = (studentId: number) => {
    setSelectedStudents([studentId]);
    setIsTaskListPublish(true);
    setIsPublishTaskModalVisible(true);
  };

  // The existing onPublishTask prop in StudentList component will now work correctly




  const handleOpenAssignPoints = (student: Student) => {
    setSelectedStudent(student);
    setIsAssignPointsModalVisible(true);
  };

  // 添加处理单个学生重置密码的函数
  const handleResetPassword = (studentId: number) => {
    setSelectedStudentId(studentId);
    setIsBatchResetPassword(false);
    setIsResetPasswordModalVisible(true);
  };

  // 添加打开编辑班级模态框的处理函数
  const handleOpenEditClass = (classInfo: Class) => {
    setEditingClassId(classInfo.id);
    setIsEditClassModalVisible(true);
  };

  const [exportLoading, setExportLoading] = useState(false);

  // 导出学生信息
  const handleExportStudents = async () => {
    if (!currentClassId) {
      notification.error('请先选择班级');
      return;
    }

    try {
      setExportLoading(true);
      const response = await studentApi.exportStudents(currentClassId);

      if (response.data.code === 200) {
        // 获取当前班级信息
        const currentClass = classes.find(c => c.id === currentClassId);
        const className = currentClass ? `${currentClass.grade}${currentClass.className}` : '';

        // 将数据转换为CSV格式
        const csvContent = convertToCSV(response.data.data);
        // 创建Blob对象
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        // 创建下载链接
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `${className}_学生名单_${new Date().toLocaleDateString()}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        notification.success('导出成功');
      } else {
        notification.error('导出失败');
      }
    } catch (error) {
      console.error('导出学生信息失败:', error);
      notification.error('导出失败');
    } finally {
      setExportLoading(false);
    }
  };

  // 将数据转换为CSV格式
  const convertToCSV = (data: any[]) => {
    if (!data || data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const rows = [
      headers.join(','),
      ...data.map(item => headers.map(header => `"${item[header] || ''}"`).join(','))
    ];

    return rows.join('\n');
  };

  // 添加实际的密码重置API调用函数
  const checkHasBindPhone = async (selectedStudents: number[]) => {
    if (selectedStudents.length===0) {
       notification.error("请先选中学生后执行操作！")
       return true
    }
    
    const res = await userApi.checkHasBindPhone(selectedStudents)
    console.log("当前检测绑定情况的res",res);
    if (res.code === 200) {
      // 检查是否有数据字段和message字段，表示有学生已绑定手机号
      if (res.data && res.data.data && res.data.data.length > 0) {
        notification.error(res.data.message || `当前选中用户存在已绑定手机号的账号，请取消选中他们后重新重置密码`)
        return true
      }
      // 如果返回格式是 {code: 200, msg: "操作成功"}，表示没有学生绑定手机号
      if (res.msg === "操作成功") {
        return false;
      }
      return false;
    }
    // 接口异常情况下，阻止操作继续
    notification.error("检查手机号绑定状态失败，请稍后再试")
    return true;
  }


  const resetPasswordApi = async (studentIds: number[], password: string) => {
    const results = await (studentIds.length > 20
      ? Promise.all(
        Array.from(
          { length: Math.ceil(studentIds.length / 20) },
          (_, i) => studentIds.slice(i * 20, (i + 1) * 20)
        ).map(batchIds =>
          studentApi.resetPassword(batchIds, password)
            .then(({ data }) => data)
            .catch(() => ({ code: 500, data: { successStudents: [], errorStudents: [] } }))
        )
      ).then(results => ({
        code: results.some(r => r.code !== 200) ? 500 : 200,
        data: {
          successStudents: results.flatMap(r => r.data.successStudents || []),
          errorStudents: results.flatMap(r => r.data.errorStudents || [])
        }
      }))
      : studentApi.resetPassword(studentIds, password)
        .then(({ data }) => data)
        .catch(() => ({ code: 500, data: { successStudents: [], errorStudents: [] } }))
    );

    if (results.code !== 200) throw new Error('重置密码失败');

    const { successStudents, errorStudents } = results.data;
    const successCount = successStudents.length;
    const errorCount = errorStudents.length;

    if (successCount && !errorCount) {
      notification.success(`成功重置 ${successCount} 名学生的密码`);
      return true;
    }

    if (errorCount) {
      Modal.info({
        title: '密码重置结果',
        content: (
          <div>
            <p>重置结果：{successCount}个成功，{errorCount}个失败</p>
            {errorCount > 0 && (
              <div>
                <p>失败学生列表：</p>
                <ul>
                  {errorStudents.map((student: { id: number; reason: string }) => (
                    <li key={student.id}>ID: {student.id}, 原因: {student.reason}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ),
      });
    }

    return true;
  };

  // 添加批量重置密码的API调用函数
  const batchResetPasswordApi = async (studentIds: number[], password: string) => {
    const hideLoading = notification.loading('正在重置密码...');
    await resetPasswordApi(studentIds, password);



    if (hideLoading) {
      hideLoading.close();
    }

  };

  // 修改重置密码模态框的onOk处理函数
  const handleResetPasswordModalOk = async () => {
    try {

      if (isBatchResetPassword) {
        // 这里需要先查询该账户(账户数组ID)是否已绑定了手机号，若已绑定，提示不能修改
        const isExist = await checkHasBindPhone(selectedStudents)
        if (isExist) { return }
        await batchResetPasswordApi(selectedStudents, '123456');
        setSelectedStudents([]);
        setIsMultiSelect(false);
      } else if (selectedStudentId) {
        const isExist = await checkHasBindPhone(selectedStudents)
        if (isExist) { return }
        await batchResetPasswordApi(selectedStudents, '123456');

        notification.success('密码重置成功');
      }
      setIsResetPasswordModalVisible(false);
    } catch (error: any) {
      notification.error(error.message || '重置密码失败');
    }
  };

  // 获取官方模板列表
  const fetchOfficialTemplates = async () => {
    setLoadingOfficialTemplates(true);
    try {
      const response = await getOfficialTemplates();
      if (response.data.code === 200) {
        setOfficialTemplates(response.data.data);
      }
    } catch (error) {
      console.error('获取官方模板列表失败:', error);
      // notification.error('请检查网络以及登录状态');
    } finally {
      setLoadingOfficialTemplates(false);
    }
  };

  // 在原有的 useEffect 中添加获取官方模板
  useEffect(() => {
    if (isTeacherView) {
      fetchTemplates();
      fetchOfficialTemplates(); // 添加这一行
    }
  }, [isTeacherView, userId]);

  // 修改处理使用官方模板的函数
  const handleUseOfficialTemplate = async (template: PermissionTemplateDisplay) => {
    try {
      // 获取官方模板的详细信息
      const response = await getTemplateInfo(template.id);


      if (response.data.code === 200) {
        setIsTemplateModalVisible(true);
        const templateData = response.data.data;
        setSelectedTemplateId(template.id);

      } else {
        notification.error('获取模板详情失败');
      }
    } catch (error) {
      console.error('获取模板详情失败:', error);
      notification.error('获取模板详情失败');
    }
  };

  const handleSearch = async (keyword: string) => {
    if (!currentClassId) return;

    try {
      if (!keyword.trim()) {
        // 当搜索关键字为空时，使用缓存的学生数据
        const cachedStudents = studentsCache[currentClassId];
        if (cachedStudents) {
          setStudents(cachedStudents);
          return;
        }
        // 如果没有缓存，才重新获取
        await refreshStudentList();
        return;
      }

      const response = await studentApi.searchStudents(currentClassId, keyword);
      if (response.data.code === 200) {
        const searchStudents = response.data.data;

        // 提取学生ID列表
        const userIds = searchStudents.map((s: any) => s.userId);

        // 批量获取学生详细信息
        const studentInfoMap = await (userIds.length > 20
          ? Promise.all(
            Array.from({ length: Math.ceil(userIds.length / 20) },
              (_, i) => userIds.slice(i * 20, (i + 1) * 20)
            ).map(batchId =>
              studentApi.getStudentsBatchInfo(batchId)
                .then(({ data: { data } }) => data)
                .catch((error) => {
                  console.error('获取学生批量信息失败:', error);
                  return {};
                })
            )
          ).then(results => Object.assign({}, ...results))
          : studentApi.getStudentsBatchInfo(userIds)
            .then(({ data: { data } }) => data)
            .catch((error) => {
              console.error('获取学生批量信息失败:', error);
              return {};
            })
        );

        // 合并学生数据
        const completeStudents = searchStudents.map((s: any) => ({
          ...s,
          ...studentInfoMap[s.userId],
          totalPoints: studentInfoMap[s.userId]?.totalPoints ?? 0,
          availablePoints: studentInfoMap[s.userId]?.availablePoints ?? 0,
          currentTemplate: studentInfoMap[s.userId]?.currentTemplate ?? null,
          avatarUrl: s.avatarUrl || ''
        }));

        setStudents(completeStudents);
      }
    } catch (error) {
      console.error('搜索失败:', error);
      notification.error('搜索失败');
    }
  };

  // 修改搜索任务函数
  const handleSearchTasks = async (value: string) => {
    if (!currentClassId) return;

    try {
      if (!value.trim()) {
        // 当搜索关键字为空时，使用缓存的任务数据
        const cachedTasks = tasksCache[currentClassId];
        if (cachedTasks) {
          setTasks(cachedTasks);
          return;
        }
        // 如果没有缓存，才重新获取
        await fetchTasks(currentClassId);
        return;
      }

      const response = await taskApi.searchTasks(value, currentClassId);
      if (response.data.code === 200) {
        setTasks(response.data.data);
      }
    } catch (error) {
      console.error('搜索任务失败:', error);
      notification.error('搜索任务失败');
    }
  };

  // 修改搜索项目函数
  const handleSearchProjects = async (value: string) => {
    if (!currentClassId) return;

    try {
      if (!value.trim()) {
        // 当搜索关键字为空时，使用缓存的项目数据
        const cachedProjects = projectsCache[currentClassId];
        if (cachedProjects) {
          setProjects(cachedProjects);
          return;
        }
        // 如果没有缓存，才重新获取
        await fetchProjects(currentClassId);
        return;
      }

      const response = await worksApi.searchProjects(currentClassId, value);
      if (response.data.code === 200) {
        setProjects(response.data.data.list);
      }
    } catch (error) {
      console.error('搜索项目失败:', error);
      notification.error('搜索项目失败');
    }
  };

  // 批量分配能量处理函数
  const handleBatchAssignPoints = async (values: { availablePoints: number; studentExpiries?: { [id: number]: string | undefined }; remark?: string }) => { // 更新参数类型
    if (selectedStudents.length === 0) {
      notification.error('请先选择学生');
      return;
    }
    if (!values.studentExpiries) { // 添加检查
      notification.error('未能获取学生过期时间信息');
      return;
    }

    try {
      const hideLoading = notification.loading('正在批量分配能量...');

      // 调用新的批量分配 API
      const response = await pointsApi.batchAssignPermission({
        availablePoints: values.availablePoints,
        studentExpiries: values.studentExpiries,
        remark: values.remark
      });
      console.log("批量分配积分res", response);

      if (hideLoading) {
        hideLoading.close();
      }

      if (response.data.code === 200) {
        // 后端现在返回处理结果数组，可以根据需要处理
        const results = response.data.data;
        // 可以根据 results 中的信息给出更详细的成功/失败提示，
        // 但为了简单起见，我们仍然使用之前的逻辑
        notification.success(`成功为 ${results.success} 名学生分配能量`);

        setIsAssignPointsModalVisible(false);
        // 刷新学生列表
        if (currentClassId) {

          await refreshStudentList(); // 确保使用 await
        }
        setSelectedStudents([]); // 清空选择
        setIsMultiSelect(false);
      } else {
        // 处理 API 返回的错误信息
        notification.error(response.data.message || '批量分配能量失败');
      }
    } catch (error: any) {
      console.error('批量分配能量失败:', error);
      // 处理请求级别的错误
      notification.error(error.response?.data?.message || '批量分配能量失败，请检查网络连接或稍后重试');
    }
  };



  // 修改批量移除学生的处理
  const handleBatchRemoveStudents = async (studentIds: number[]) => {
    try {
      // 获取选中的学生信息
      const selectedStudentsInfo = students.filter(s => studentIds.includes(s.userId));

      // 计算总可用积分
      const totalAvailablePoints = selectedStudentsInfo.reduce(
        (sum, student) => sum + (student.availablePoints || 0),
        0
      );

      Modal.confirm({
        title: '确认批量移出班级',
        content: (
          <div>
            <p>确定要将选中的 {studentIds.length} 名学生移出班级吗？</p>
            {totalAvailablePoints > 0 && (
              <div className="mt-2 p-3 bg-yellow-50 rounded-lg">
                <div className="flex items-center gap-2 text-yellow-600">
                  <InfoCircleOutlined />
                  <span>选中的学生共有 {totalAvailablePoints} 点可用能量</span>
                </div>
                <p className="mt-1 text-sm text-yellow-500">
                  移出班级后，可用能量将返还到各自的套餐积分中
                </p>
              </div>
            )}
          </div>
        ),
        okText: '确定移出',
        cancelText: '取消',

        onOk: async () => {
          const hideLoading = notification.loading('正在移除学生...');
          try {
            // 直接调用批量接口，传入学生ID数组
            const response = await classApi.removeStudentFromClass(studentIds);
            if (hideLoading) {
              hideLoading.close();
            }

            if (response.data.code === 200) {
              const results = response.data.data.results || [];
              const successCount = results.filter((r: { success: boolean }) => r.success).length;
              const failCount = results.length - successCount;
              const totalReturnedPoints = results.reduce((sum: number, r: { success: boolean, availablePoints?: number }) =>
                sum + (r.success ? (r.availablePoints || 0) : 0), 0);


              if (successCount > 0) {
                notification.success(
                  `成功移除 ${successCount} 名学生${totalReturnedPoints > 0 ?
                    `，共返还 ${totalReturnedPoints} 点可用能量` :
                    ''
                  }`
                );
              }
              if (failCount > 0) {
                notification.error(`${failCount} 名学生移除失败`);
              }
            } else {
              notification.error(response.data.message || '批量移除学生失败');
            }




          } catch (error: any) {
            if (hideLoading) {
              hideLoading.close();
            }
            notification.error(error.response?.data?.message || '批量移除学生失败');
            console.error('批量移除学生失败:', error);
          }

          // 刷新班级列表

          if (currentClassId) {
            // 更新缓存中的学生列表
            console.log("更新缓存中的学生列表");

            const updatedStudents = students.filter(s => !studentIds.includes(s.userId));
            // 同步更新 ref
            studentsCacheRef.current[currentClassId] = updatedStudents;
            setStudents(updatedStudents);
            // 也执行异步的状态更新

            setStudentsCache(prev => {
              const updatedCache = { ...prev };
              updatedCache[currentClassId] = updatedStudents;
              return updatedCache;
            });
            if (selectedSchool) {
              await fetchClasses(selectedSchool.id);
            }



          }

          // 清除选择状态
          setSelectedStudents([]);
          setIsMultiSelect(false);
        }
      });
    } catch (error) {
      notification.error('批量移除学生失败');
      console.error('批量移除学生失败:', error);
    }
  };

  const [isClassCardAssign, setIsClassCardAssign] = useState(false);
  const [isTaskListPublish, setIsTaskListPublish] = useState(false);

  // 2. 添加获取使用情况的函数
  const fetchTemplateUsage = async () => {
    try {
      // 获取教师使用的模板
      const teacherResponse = await getUserCurrentTemplate(userId);
      if (teacherResponse.data.code === 200) {
        setTeacherTemplate(teacherResponse.data.data);
      }

      // 获取学生使用的模板
      const studentResponse = await getStudentTemplates({
        teacherId: userId,
        page: 1,
        size: 200
      });
      if (studentResponse.data.code === 200) {
        // 统计每个模板被使用的次数
        const usage: { [key: number]: number } = {};
        studentResponse.data.data.list.forEach((item: any) => {
          if (item.templateId) {
            usage[item.templateId] = (usage[item.templateId] || 0) + 1;
          }
        });
        setStudentTemplateUsage(usage);
      }
    } catch (error) {
      console.error('获取模板使用情况失败:', error);
    }
  };

  // 添加新的状态
  const [isTemplateUsageModalVisible, setIsTemplateUsageModalVisible] = useState(false);
  const [selectedTemplateForUsage, setSelectedTemplateForUsage] = useState<PermissionTemplateDisplay | null>(null);
  const [templateUsageStudents, setTemplateUsageStudents] = useState<any[]>([]);
  const [loadingUsageStudents, setLoadingUsageStudents] = useState(false);

  // 添加获取模板使用学生列表的函数
  const fetchTemplateUsageStudents = async (templateId: number) => {
    try {
      setLoadingUsageStudents(true);
      const response = await getStudentTemplates({
        teacherId: userId,
        page: 1,
        size: 200
      });

      if (response.data.code === 200) {
        // 过滤出使用该模板的学生
        const studentsWithTemplate = response.data.data.list.filter(
          (item: any) => item.templateId === templateId
        );
        setTemplateUsageStudents(studentsWithTemplate);
      }
    } catch (error) {
      console.error('获取模板使用学生列表失败:', error);
      notification.error('获取模板使用学生列表失败');
    } finally {
      setLoadingUsageStudents(false);
    }
  };

  // 修改模板卡片中的使用情况点击事件
  const handleTemplateUsageClick = async (e: React.MouseEvent, template: PermissionTemplateDisplay) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发模板详情
    setSelectedTemplateForUsage(template);
    setIsTemplateUsageModalVisible(true);
    await fetchTemplateUsageStudents(template.id);
  };

  // 添加模板使用情况模态框
  <Modal
    title={
      <div className="flex items-center gap-2">
        <BlockOutlined className="text-blue-500" />
        <span>模板使用情况</span>
        <Tag color={selectedTemplateForUsage?.isOfficial ? 'gold' : 'success'} className="ml-2 rounded-full">
          {selectedTemplateForUsage?.isOfficial ? '官方' : '自定义'}
        </Tag>
      </div>
    }
    open={isTemplateUsageModalVisible}
    onCancel={() => {
      setIsTemplateUsageModalVisible(false);
      setSelectedTemplateForUsage(null);
      setTemplateUsageStudents([]);
    }}
    footer={null}
    width={700}
  >
    {selectedTemplateForUsage && (
      <div className="space-y-4">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium mb-2">{selectedTemplateForUsage.templateName}</h3>
          <p className="text-gray-600 text-sm">{selectedTemplateForUsage.templateDescription}</p>
        </div>

        <div className="border-t border-gray-100 pt-4">
          <h4 className="text-base font-medium mb-3">使用此模板的学生</h4>
          {loadingUsageStudents ? (
            <div className="flex justify-center py-8">
              <Spin />
            </div>
          ) : templateUsageStudents.length > 0 ? (
            <div className="space-y-2 max-h-[400px] overflow-y-auto pr-2">
              {templateUsageStudents.map(student => (
                <div
                  key={student.studentId}
                  className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100 hover:border-blue-200 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <Avatar
                      size={32}
                      src={student.avatarUrl}
                      icon={!student.avatarUrl && <UserOutlined />}
                    />
                    <div>
                      <div className="font-medium">{student.nickName}</div>
                      <div className="text-xs text-gray-500">{student.studentNumber}</div>
                    </div>
                  </div>
                  <div className="text-sm text-gray-500">
                    {student.className && (
                      <Tag color="blue">{student.className}</Tag>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <Empty description="暂无学生使用此模板" />
          )}
        </div>
      </div>
    )}
  </Modal>

  // 修改返回按钮的处理函数
  const handleBack = () => {
    if (selectedSchool) {
      // 如果当前在班级列表，返回学校列表
      setSelectedSchool(null);
      setClasses([]);
      setStudents([]);
    } else {
      // 如果当前在学校列表，返回主页
      router.push('/home');
    }
  };

  // 添加处理移出协助教师的函数
  const handleRemoveAssistantTeacher = async () => {
    try {
      if (!currentClassId) {
        notification.error('未找到班级信息');
        return;
      }

      const currentClass = classes.find(c => c.id === currentClassId);
      if (!currentClass) {
        notification.error('未找到班级信息');
        return;
      }

      notification.loading('正在移出协助教师...');
      // 调用API移除协助教师
      const response = await classApi.removeAssistantTeacher(currentClassId);
      //console.log(response);

      if (response.data.data.code === 1001) {
        notification.error('班级不存在');
      } else if (response.data.data.code === 1002) {
        notification.error('您不是该班级的主教师，无权执行此操作');
      } else if (response.data.data.code === 1003) {

        // 根据不同的错误码给出不同的提示
        if (response.data.code === 200) {
          // 更新班级列表中的当前班级
          const updatedClasses = classes.map(c => {
            if (c.id === currentClassId) {
              return { ...c, assistantTeacherId: null, assistantTeacherName: null };
            }
            return c;
          });

          setClasses(updatedClasses as Class[]);
          notification.success('已成功移出协助教师');
        } else {
          notification.error(response.data.message || '移出协助教师失败');
        }
      }
    } catch (error) {
      console.error('移出协助教师出错:', error);
      notification.error('移出协助教师失败，请稍后再试');
    }
  };

  // 处理批量/单个兑换密钥 (修改：增加可选 studentId 参数)
  const handleBatchUseKeyPackage = (studentId?: number) => {
    if (studentId) {
      // 单个学生触发
      setSelectedStudents([studentId]);
      setIsBatchUseKeyPackageModalOpen(true);
    } else {
      // 批量操作触发
      if (selectedStudents.length === 0) {
        notification.warning('请先选择学生');
        return;
      }
      setIsBatchUseKeyPackageModalOpen(true);
    }
  };

  // 关闭批量兑换密钥模态框
  const handleCloseBatchUseKeyPackageModal = () => {
    setIsBatchUseKeyPackageModalOpen(false);
  };

  // 批量兑换密钥成功处理
  const handleBatchUseKeyPackageSuccess = () => {
    refreshStudentList();
  };

  // 批量兑换密钥相关的状态
  const [isBatchUseKeyPackageModalOpen, setIsBatchUseKeyPackageModalOpen] = useState(false);

  // 新增处理函数：打开批量兑换模态框并全选学生
  const handleOpenBatchRedeemModalForAllStudents = async (classId: number) => {
    // 确保当前选中的就是这个班级，并且学生列表已加载
    if (currentClassId !== classId) {
      // 如果不是当前班级，需要先切换并加载学生
      setCurrentClassId(classId);
      // await handleFetchStudents(classId); // 等待学生加载完成
    } else if (studentsCache[classId]?.length !== students.length) {
      // 如果是当前班级，但列表可能过期，刷新一下
      await refreshStudentList();
    }

    // 确保students状态已更新
    // 使用setTimeout确保状态更新后的students值被使用
    setTimeout(() => {
      const currentStudents = studentsCache[classId] || students; // 优先使用缓存
      if (currentStudents.length === 0) {
        notification.warning("该班级没有学生，无法执行批量兑换。");
        return;
      }
      const allStudentIds = currentStudents.map(s => s.userId);
      setSelectedStudents(allStudentIds);
      setIsBatchUseKeyPackageModalOpen(true);
      notification.info(`已自动选择班级内 ${allStudentIds.length} 名学生`);
    }, 0); // 延迟执行以确保状态更新
  };

  // 处理前往分配积分的函数
  const handleGoToAssignPoints = (studentIds: number[]) => {
    if (studentIds.length === 0) {
      notification.warning('没有可分配积分的学生');
      return;
    }

    // 更新已选择的学生ID列表
    setSelectedStudents(studentIds);
    // 清除选中的单个学生
    setSelectedStudent(null);
    // 关闭兑换密钥模态框
    setIsBatchUseKeyPackageModalOpen(false);
    // 打开积分分配模态框
    setIsAssignPointsModalVisible(true);

    notification.success(`已选择 ${studentIds.length} 名学生进行积分分配`);
  };

  // 添加处理"前往兑换密钥"的函数
  const handleGoToRedeemKeyFromPoints = (studentIds: number[]) => {
    setIsAssignPointsModalVisible(false); // 关闭当前分配能量模态框
    // handleBatchUseKeyPackage(studentIds[0]); // <<-- 删除这行
    // // 如果需要支持从批量分配能量跳转到批量兑换密钥，可以取消下面的注释
    // // if (isBatch) {
    // //   setSelectedStudents(studentIds);
    // //   setIsBatchUseKeyPackageModalOpen(true);
    // // } else {
    // //   handleBatchUseKeyPackage(studentIds[0]);
    // // }
    // 添加以下两行来正确处理批量跳转
    setSelectedStudents(studentIds);
    setIsBatchUseKeyPackageModalOpen(true);
  };


  if (loading) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }



  return (

    <div className="h-screen flex flex-col overflow-hidden">
      {/* Profile Header Section */}
      <div className="relative bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50">
        {/* 背景图片 */}
        <div className="absolute inset-0 w-full h-full">
          <Image
            src="/images/mywork_background.svg"
            alt="背景图片"
            fill
            className="object-cover opacity-50"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-b from-transparent to-white/80" />
        </div>

        {/* 内容层 - 减小内边距 */}
        <div className="container mx-auto px-4 py-4 relative">
          {/* 用户信息区域 - 减小间距和头像大小 */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
            <div className="flex items-center gap-4">
              <div className="relative group">
                <div className="w-14 h-14 rounded-xl overflow-hidden bg-white border border-gray-200 shadow-lg">
                  {avatarUrl ? (
                    <img
                      src={avatarUrl}
                      alt="用户头像"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.currentTarget as HTMLImageElement;
                        target.style.display = 'none';
                        const parent = target.parentElement;
                        if (parent) {
                          const fallbackDiv = document.createElement('div');
                          fallbackDiv.className = 'w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200';
                          
                          // 创建用户图标
                          const iconDiv = document.createElement('div');
                          iconDiv.className = 'text-2xl text-gray-400 anticon anticon-user';
                          fallbackDiv.appendChild(iconDiv);
                          
                          parent.appendChild(fallbackDiv);
                        }
                      }}
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
                      <UserOutlined className="text-2xl text-gray-400" />
                    </div>
                  )}
                </div>
              </div>
              <div className="space-y-0.5">
                <h1 className="text-xl font-bold text-gray-900">教师空间</h1>
                <p className="text-xs text-gray-500">管理您的学校、班级和学生</p>
              </div>
            </div>
          </div>

          {/* 导航和操作按钮 - 减小上边距 */}
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center gap-3">
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={handleBack}
                className="btn-modern btn-back"
              >
                返回{selectedSchool ? '学校列表' : ''}
              </Button>
              <Button
                type="primary"
                onClick={() => setIsJoinClassModalVisible(true)}
              >
                加入班级
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto">
        <main className="container mx-auto px-4 py-4">  {/* 从 py-6 改为 py-2 */}
          {!selectedSchool ? (
            <div className="grid grid-cols-7 gap-6 relative">
              <div className="col-span-4">
                <SchoolList
                  schools={schools}
                  onSchoolSelect={(school) => {
                    setSelectedSchool(school);
                    fetchClasses(school.id);
                  }}
                  onRefreshSchools={handleFetchSchools}
                  loading={loading}
                />
              </div>
              <div className="col-span-3">
                <TemplateList
                  userId={userId}
                  roleId={roleId}
                  teacherTemplate={teacherTemplate}
                  studentTemplateUsage={studentTemplateUsage}
                  onTemplateUsageClick={handleTemplateUsageClick}
                  onTemplateUseSuccess={() => {
                    // 重新获取教师使用的模板
                    getUserCurrentTemplate(userId).then(response => {
                      if (response.data.code === 200) {
                        setTeacherTemplate(response.data.data);
                      }
                    });
                  }}
                />
              </div>
            </div>
          ) : (
            // 班级和学生列表视图
            <div className="min-h-[500px]">
              <div className="flex items-center mb-6">
                <div className="flex items-center">
                  <div className="text-xl font-semibold text-gray-800">
                    {selectedSchool.schoolName}
                  </div>
                  <span className="mx-3 text-gray-300">|</span>
                  <span className="text-gray-500 text-sm">
                    {selectedSchool.province}
                    {!isMunicipality(selectedSchool.province) && ` ${selectedSchool.city}`}
                    {` ${selectedSchool.district}`}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                {/* 左侧班级列表区域 - 凹陷效果 */}
                <div className="md:col-span-1">
                  <ClassList
                    classes={classes}
                    currentClassId={currentClassId}
                    onClassSelect={(classId) => {
                      setCurrentClassId(classId);
                      setSelectedStudents([]); // 清除选中的学生
                      setIsMultiSelect(false); // 退出多选模式
                      setMultiSelectState(createInitialMultiSelectState()); // 重置多选状态
                      handleFetchStudents(classId);
                      fetchTasks(classId);
                      fetchProjects(classId);
                    }}
                    onCreateClass={() => setIsCreateClassModalVisible(true)}
                    onEditClass={handleOpenEditClass}
                    onAddStudent={(classId) => {
                      setCurrentClassId(classId);
                      setIsAddModalVisible(true);
                    }}
                    onImportStudent={(classId) => {
                      setCurrentClassId(classId);
                      setIsImportModalVisible(true);
                    }}
                    onExportStudents={handleExportStudents}
                    onTransferClass={(classInfo) => {
                      setCurrentClassId(classInfo.id);
                      setIsTransferModalVisible(true);
                      transferForm.resetFields();
                      setSearchedTeacher(null);
                    }}
                    onGenerateInviteCode={handleGenerateInviteCode}
                    onDeleteClass={handleDeleteClass}
                    onAssignBlocks={(classId) => {
                      setCurrentClassId(classId);
                      const allStudentIds = students.map(student => ({
                        userId: student.userId,
                        roleId: 1
                      }));
                      const allUsers = [
                        ...allStudentIds,
                        { userId: userId, roleId: 2 }
                      ];
                      setSelectedStudents(allUsers.map(user => user.userId));
                      setUserRoles(allUsers);
                      setSelectedStudent(null);
                      setIsClassCardAssign(true);
                      fetchTemplates().then(() => fetchTemplateUsage()); // 修改这里
                      setIsAssignBlocksModalVisible(true);
                    }}
                    onAssignPoints={(classId) => {
                      setCurrentClassId(classId);
                      const allStudentIds = students.map(student => student.userId);
                      setSelectedStudents(allStudentIds);
                      setSelectedStudent(null);
                      setIsAssignPointsModalVisible(true);
                    }}
                    onBatchUseKeyPackage={handleOpenBatchRedeemModalForAllStudents} // 添加这一行
                  />
                </div>

                {/* 右侧内容区域 - 凹陷效果 */}
                <div className="md:col-span-3">
                  <div className="bg-gray-50 rounded-xl p-4 shadow-inner min-h-[calc(100vh-240px)]">
                    <div className="flex justify-between items-center ">
                      <div className="flex-1">
                        <Tabs
                          activeKey={activeTab}
                          onChange={(key) => handleTabChange(key as 'students' | 'tasks' | 'projects')}
                          className="custom-tabs w-full"
                          size="small"
                          tabBarGutter={16}
                          items={[
                            {
                              key: 'students',
                              label: (
                                <div className="flex items-center gap-2 w-full">
                                  <div className="flex-none">
                                    <TeamOutlined />
                                  </div>
                                  <div className="2xl:block xl:block lg:block md:block sm:hidden hidden whitespace-nowrap">
                                    学生列表
                                  </div>
                                  {students.length > 0 && (
                                    <div className="2xl:block xl:block lg:block md:hidden sm:hidden hidden">
                                      <span className="text-xs px-2 py-0.5 bg-blue-50 text-blue-500 rounded-full">
                                        {students.length}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              )
                            },
                            {
                              key: 'tasks',
                              label: (
                                <div className="flex items-center gap-2 w-full">
                                  <div className="flex-none">
                                    <OrderedListOutlined />
                                  </div>
                                  <div className="2xl:block xl:block lg:block md:block sm:hidden hidden whitespace-nowrap">
                                    任务列表
                                  </div>
                                  {tasks.length > 0 && (
                                    <div className="2xl:block xl:block lg:block md:hidden sm:hidden hidden">
                                      <span className="text-xs px-2 py-0.5 bg-blue-50 text-blue-500 rounded-full">
                                        {tasks.length}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              )
                            },
                            {
                              key: 'projects',
                              label: (
                                <div className="flex items-center gap-2 w-full">
                                  <div className="flex-none">
                                    <ProjectOutlined />
                                  </div>
                                  <div className="2xl:block xl:block lg:block md:block sm:hidden hidden whitespace-nowrap">
                                    班级项目
                                  </div>
                                  {projects.length > 0 && (
                                    <div className="2xl:block xl:block lg:block md:hidden sm:hidden hidden">
                                      <span className="text-xs px-2 py-0.5 bg-blue-50 text-blue-500 rounded-full">
                                        {projects.length}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              )
                            }
                          ]}
                        />
                      </div>
                      {currentClassId && (activeTab === 'students' || activeTab === 'tasks' || activeTab === 'projects') && (
                        <div className="flex items-center gap-3">
                          {!isMultiSelect ? (
                            <>
                              <ExpandableSearch
                                onSearch={
                                  activeTab === 'students' ? handleSearch :
                                    activeTab === 'tasks' ? handleSearchTasks :
                                      handleSearchProjects
                                }
                              />
                              {activeTab === 'tasks' && (
                                <Button
                                  type="primary"
                                  icon={<PlusOutlined />}
                                  onClick={() => {
                                    const allStudentIds = students.map(student => student.userId);
                                    setSelectedStudents(allStudentIds);
                                    setIsTaskListPublish(true); // 只在这里设置为 true
                                    setIsPublishTaskModalVisible(true);
                                    notification.info(`已为你默认选择全部 ${students.length} 名学生`);
                                  }}
                                  className="bg-gradient-to-r from-blue-500 to-blue-400 border-none hover:shadow-md transition-all duration-300 rounded-full"
                                >
                                  发布任务
                                </Button>
                              )}
                              {activeTab === 'students' && (
                                <Button
                                  type="primary"
                                  icon={<CheckSquareOutlined />}
                                  onClick={() => {
                                    if (isMultiSelect) {
                                      setIsMultiSelect(false);
                                      setSelectedStudents([]);
                                    } else {
                                      setIsMultiSelect(true);
                                      setSelectedStudents(students.map(s => s.userId));
                                    }
                                  }}
                                  className="rounded-full bg-blue-500 hover:bg-blue-600"
                                >
                                  {isMultiSelect ? '退出多选' : '全选'} {selectedStudents.length > 0 && `(${selectedStudents.length})`}
                                </Button>
                              )}
                            </>
                          ) : (
                            <>
                              <Dropdown
                                menu={{
                                  items: [
                                    {
                                      key: 'assign',
                                      label: (
                                        <div className="flex items-center gap-2 py-1">
                                          <BlockOutlined className="text-blue-500" />
                                          <span>分配积木</span>
                                        </div>
                                      ),
                                      onClick: async () => {
                                        setSelectedStudent(null);
                                        await fetchTemplates();
                                        await fetchTemplateUsage(); // 添加这行
                                        setIsAssignBlocksModalVisible(true);
                                      }
                                    },
                                    {
                                      key: 'assignPoints',
                                      label: (
                                        <div className="flex items-center gap-2 py-1">
                                          <ThunderboltOutlined className="text-yellow-500" />
                                          <span>分配能量</span>
                                        </div>
                                      ),
                                      onClick: () => {
                                        setSelectedStudent(null);  // 确保是批量模式
                                        setIsAssignPointsModalVisible(true);
                                      }
                                    },
                                    {
                                      key: 'publish',
                                      label: (
                                        <div className="flex items-center gap-2 py-1">
                                          <FileTextOutlined className="text-green-500" />
                                          <span>发布任务</span>
                                        </div>
                                      ),
                                      onClick: () => {
                                        taskForm.setFieldValue('studentIds', selectedStudents);
                                        setIsPublishTaskModalVisible(true);
                                      }
                                    },
                                    {
                                      key: 'resetPassword',
                                      label: (
                                        <div className="flex items-center gap-2 py-1">
                                          <KeyOutlined className="text-orange-500" />
                                          <span>重置密码</span>
                                        </div>
                                      ),
                                      onClick: () => {
                                        setIsBatchResetPassword(true);
                                        setIsResetPasswordModalVisible(true);
                                      }
                                    },
                                    {
                                      key: 'batchUseKeyPackage',
                                      label: (
                                        <div className="flex items-center gap-2 py-1">
                                          <GiftOutlined className="text-purple-500" />
                                          <span>批量兑换密钥</span>
                                        </div>
                                      ),
                                      onClick: () => {
                                        handleBatchUseKeyPackage();
                                      }
                                    },
                                    {
                                      key: 'remove',
                                      label: (
                                        <div className="flex items-center gap-2 py-1">
                                          <DeleteOutlined className="text-red-500" />
                                          <span>移出班级</span>
                                        </div>
                                      ),
                                      danger: true,
                                      onClick: () => {
                                        Modal.confirm({
                                          title: '确认批量移出班级',
                                          content: `确定要将选中的 ${selectedStudents.length} 名学生移出班级吗？`,
                                          okText: '确定移出',
                                          cancelText: '取消',
                                          centered: true,
                                          okButtonProps: { danger: true },
                                          onOk: () => {
                                            handleBatchRemoveStudents(selectedStudents);
                                          }
                                        });
                                      }

                                    }
                                  ]
                                }}
                                trigger={['click']}
                                placement="bottomRight"
                                disabled={selectedStudents.length === 0}
                              >
                                <Button
                                  type="default"
                                  className={`rounded-full ${selectedStudents.length === 0
                                    ? 'opacity-50 cursor-not-allowed'
                                    : 'hover:bg-gray-100'
                                    }`}
                                >
                                  批量操作
                                </Button>
                              </Dropdown>

                              <Button
                                type="primary"
                                icon={<CheckSquareOutlined />}
                                onClick={() => {
                                  setIsMultiSelect(!isMultiSelect);
                                  setSelectedStudents([]);
                                }}
                                className="rounded-full bg-blue-500 hover:bg-blue-600"
                              >
                                取消多选 {selectedStudents.length > 0 && `(${selectedStudents.length})`}
                              </Button>
                            </>
                          )}
                        </div>
                      )}
                    </div>

                    <div className="overflow-y-auto max-h-[calc(100vh-320px)] px-1 pt-2">
                      {activeTab === 'students' ? (
                        <StudentList
                          students={students}
                          loadingStudents={loadingStudents}
                          isMultiSelect={isMultiSelect}
                          selectedStudents={selectedStudents}
                          onStudentSelect={(studentId) => {
                            if (!isMultiSelect) {
                              setIsMultiSelect(true);
                              setSelectedStudents([studentId]);
                            } else {
                              setSelectedStudents(prev =>
                                prev.includes(studentId)
                                  ? prev.filter(id => id !== studentId)
                                  : [...prev, studentId]
                              );
                            }
                          }}
                          onMultiSelect={handleMultiSelect}
                          onMouseDown={handleMouseDown}
                          onMouseEnter={handleMouseEnter}
                          onAssignBlocks={handleAssignBlocks}
                          onAssignPoints={handleOpenAssignPoints}
                          onPublishTask={handlePublishTaskClick}
                          onResetPassword={(studentId) => handleResetPassword(studentId)}
                          onRemoveStudent={handleRemoveStudent}
                          onUseKeyPackage={handleBatchUseKeyPackage} // 修改这里
                          onRefreshList={refreshStudentList}
                        />
                      ) : activeTab === 'tasks' ? (
                        <TaskListComponent
                          tasks={tasks}
                          onRefresh={() => currentClassId && fetchTasks(currentClassId)}
                          students={students}
                          currentClassId={currentClassId || 0}
                          fetchTasks={fetchTasks}
                          displayTemplates={displayTemplates}
                          officialTemplates={officialTemplates}
                        />
                      ) : (
                        <ProjectList
                          projects={projects}
                          loading={projectLoading}
                          onViewWork={handleViewWork}
                        />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>

      {/* 添加学生模态框 */}
      <AddStudentModal
        visible={isAddModalVisible}
        onCancel={() => {
          setIsAddModalVisible(false);
          form.resetFields();
        }}
        onOk={handleAddStudent}
      />

      {/* 导入学生模态框 */}
      <ImportStudentModal
        visible={isImportModalVisible}
        onCancel={() => setIsImportModalVisible(false)}
        onImport={handleImportStudents}
        classId={currentClassId || 0} // 传递当前选中的班级ID
      />

      {/* 分配积木模态框 */}
      <AssignBlocksModal
        visible={isAssignBlocksModalVisible}
        onCancel={() => {
          setIsAssignBlocksModalVisible(false);
          setSelectedStudentId(null);
        }}
        isClassCardAssign={selectedStudentId !== null}
        loadingTemplates={loadingTemplates}
        templates={templates}
        studentTemplateUsage={studentTemplateUsage}
        teacherTemplate={teacherTemplate}
        onSelectTemplate={(templateId) => {
          //console.log('AssignBlocksModal 选择模板', templateId, '当前多选学生数量:', selectedStudents.length, '单选学生ID:', selectedStudentId);
          handleSelectTemplate(templateId);
        }}
        onTemplateUsageClick={handleTemplateUsageClick}
        userId={userId} // 添加userId参数
      />

      {/* 发布任务模态框 */}
      {isPublishTaskModalVisible && (
        <PublishTaskModal
          visible={isPublishTaskModalVisible}
          onCancel={() => {
            setIsPublishTaskModalVisible(false);
            setIsTaskListPublish(false);
            taskForm.resetFields();
            setSelectedStudents([]);
            setFileList([]);
            setSelectedTemplateId(null);
          }}
          onOk={handlePublishTask}
          students={students}
          selectedStudents={selectedStudents}
          setSelectedStudents={setSelectedStudents}
          fileList={fileList}
          uploading={uploading}
          handleUpload={handleUpload}
          handleRemoveFile={handleRemoveFile}
          displayTemplates={displayTemplates}
          officialTemplates={officialTemplates}
          selectedTemplateId={selectedTemplateId}
          setSelectedTemplateId={setSelectedTemplateId}
          isTaskListPublish={isTaskListPublish}
        />
      )}

      {/* 加入班级模态框 */}
      <JoinClassModal
        visible={isJoinClassModalVisible}
        onCancel={() => {
          setIsJoinClassModalVisible(false);
          joinClassForm.resetFields();
        }}
        onOk={handleJoinClass}
      />

      {/* 编辑班级模态框 */}
      <EditClassModal
        visible={isEditClassModalVisible}
        onCancel={() => {
          setIsEditClassModalVisible(false);
          setEditingClassId(null);
          editClassForm.resetFields();
        }}
        onOk={handleEditClass}
        initialValues={editingClassId ? {
          className: classes.find(c => c.id === editingClassId)?.className || ''
        } : undefined}
      />

      {/* 重置密码模态框 - 在批量操作的地方替换原有的 Modal.confirm */}
      <ResetPasswordModal
        visible={isResetPasswordModalVisible}
        onCancel={() => setIsResetPasswordModalVisible(false)}
        onOk={handleResetPasswordModalOk}
        isBatch={isBatchResetPassword}
        count={selectedStudents.length}
      />

      {/* 转让班级模态框 */}
      {isTransferModalVisible && (
        <TransferClassModal
          visible={isTransferModalVisible}
          onCancel={() => {
            setIsTransferModalVisible(false);
            transferForm.resetFields();
            setSearchedTeacher(null);
          }}
          onOk={(values) => {
            if (values.transferType === 'remove') {
              handleRemoveAssistantTeacher();
              return;
            }
            const currentClass = classes.find(c => c.id === currentClassId);
            if (!currentClass) {
              notification.error('未找到班级信息');
              return;
            }
            handleTransferClass(values as { transferType: 'search' | 'assistant'; phone?: string }, currentClass);
          }}
          onSearchTeacher={handleSearchTeacher}
          searchedTeacher={searchedTeacher}
          hasAssistantTeacher={!!classes.find(c => c.id === currentClassId)?.assistantTeacherId}
          onRemoveAssistant={handleRemoveAssistantTeacher}
        />
      )}

      {/* 创建班级模态框 */}
      {isCreateClassModalVisible && (
        <CreateClassModal
          visible={isCreateClassModalVisible}
          onCancel={() => setIsCreateClassModalVisible(false)}
          onOk={handleCreateClass}
        />
      )}

      {/* 能量分配模态框 */}
      <AssignPointsModal
        visible={isAssignPointsModalVisible}
        onCancel={() => {
          setIsAssignPointsModalVisible(false);
          setSelectedStudent(null);
        }}
        // 根据 selectedStudent 是否存在来决定调用哪个处理函数
        onOk={selectedStudent ? handleAssignPoints : handleBatchAssignPoints}
        studentName={selectedStudent ? `${selectedStudent.nickName}` : `已选择 ${selectedStudents.length} 名学生`}
        studentId={selectedStudent?.id || 0}
        userId={selectedStudent?.userId || 0}
        student={selectedStudent}
        isBatch={!selectedStudent}
        selectedStudents={selectedStudents}
        students={students}
        onSuccess={() => {
          // 移除这里的刷新，因为 onOk 内部已经处理了
        }}
        refreshStudentList={refreshStudentList}
        onGoToRedeemKey={handleGoToRedeemKeyFromPoints} // 传递新的处理函数
      />

      {/* 添加权限模板模态框 */}
      {isTemplateModalVisible && (
        <PermissionTemplateModal
          userId={userId}
          roleId={roleId || 2} // 使用 roleId，默认为教师角色(2)
          visible={isTemplateModalVisible}
          onClose={() => {
            setIsTemplateModalVisible(false);
            setSelectedTemplateId(null); // 清除选中的模板ID
          }}
          onSuccess={handleTemplateSuccess}
          sourceTemplateId={selectedTemplateId} // 传递源模板ID
        />
      )}

      {/* 添加权限查看弹窗 */}
      {isPermissionModalVisible && selectedTemplateId && (
        <PermissionModal
          userId={0}  // 这里传0因为我们只是查看模板
          visible={isPermissionModalVisible}
          onClose={() => {
            setIsPermissionModalVisible(false);
            setSelectedTemplateId(null);
          }}
          templateId={selectedTemplateId}
          readOnly={true}
          onUseTemplate={async (templateId) => {
            try {
              // 使用 addUserJoinRole API 来分配模板
              const response = await addUserJoinRole({
                userId: userId,
                roleId: roleId || 2,
                templateId: templateId
              });

              if (response.data.code === 200) {
                notification.success('模板应用成功');
                // 刷新模板使用情况
                fetchTemplateUsage();
              } else {
                notification.error(response.data.message || '模板应用失败');
              }
            } catch (error) {
              console.error('应用模板失败:', error);
              notification.error('应用模板失败');
            } finally {
              setIsPermissionModalVisible(false);
              setSelectedTemplateId(null);
            }
          }}
        />
      )}

      {/* 添加编辑模板模态框 */}
      {isEditTemplateModalVisible && editingTemplateId && (
        <PermissionTemplateModal
          userId={userId}
          roleId={roleId || 2}
          visible={isEditTemplateModalVisible}
          onClose={() => {
            setIsEditTemplateModalVisible(false);
            setEditingTemplateId(null);
          }}
          onSuccess={handleEditTemplateSuccess}
          templateId={editingTemplateId}
        />
      )}

      {/* 添加模板使用情况模态框 */}
      <TemplateUsageModal
        visible={isTemplateUsageModalVisible}
        onCancel={() => {
          setIsTemplateUsageModalVisible(false);
          setSelectedTemplateForUsage(null);
          setTemplateUsageStudents([]);
        }}
        template={selectedTemplateForUsage}
        students={templateUsageStudents}
        loading={loadingUsageStudents}
      />

      {/* 批量兑换密钥模态框组件 */}
      <BatchUseKeyPackageModal
        open={isBatchUseKeyPackageModalOpen}
        selectedStudentIds={selectedStudents}
        students={students} // 添加这一行，传递学生列表
        onClose={handleCloseBatchUseKeyPackageModal}
        onSuccess={handleBatchUseKeyPackageSuccess}
        onGoToAssignPoints={handleGoToAssignPoints}
      />
    </div>
  );
}

