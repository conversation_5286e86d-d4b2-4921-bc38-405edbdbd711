import React, { useState } from 'react';
import { Modal, Form, Input, DatePicker, Upload, Button, Tag } from 'antd';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import type { UploadFile, RcFile } from 'antd/es/upload/interface';
import { activityApi, ActivityType, type ActivityParams } from '@/lib/api/activity';
import { uploadApi } from '../../../lib/api/upload';
import { GetNotification } from 'logic-common/dist/components/Notification';
import dayjs from 'dayjs';

interface AddActivityModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

interface CustomUploadFile extends UploadFile {
  error?: any;
}

const AddActivityModal: React.FC<AddActivityModalProps> = ({
  visible,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState<CustomUploadFile[]>([]);
  const [promotionImage, setPromotionImage] = useState<CustomUploadFile[]>([]);
  const [attachmentFiles, setAttachmentFiles] = useState<CustomUploadFile[]>([]);
  const [competitionGroups, setCompetitionGroups] = useState<string[]>([]);
  const [newGroupInput, setNewGroupInput] = useState('');
  // 动态配置项状态
  interface DynamicConfigItem {
    id: string;
    name: string;
    fileList: CustomUploadFile[];
    exampleList: CustomUploadFile[];
  }

  const [dynamicConfigs, setDynamicConfigs] = useState<DynamicConfigItem[]>([]);
  const [newConfigName, setNewConfigName] = useState('');

  // 添加参赛组别
  const addCompetitionGroup = () => {
    const trimmedInput = newGroupInput.trim();
    if (trimmedInput && !competitionGroups.includes(trimmedInput)) {
      setCompetitionGroups([...competitionGroups, trimmedInput]);
      setNewGroupInput('');
    }
  };

  // 删除参赛组别
  const removeCompetitionGroup = (groupToRemove: string) => {
    setCompetitionGroups(competitionGroups.filter(group => group !== groupToRemove));
  };

  // 处理回车键添加组别
  const handleGroupInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addCompetitionGroup();
    }
  };

  // 动态配置项管理函数
  const addDynamicConfig = () => {
    if (!newConfigName.trim()) return;

    const newConfig: DynamicConfigItem = {
      id: Date.now().toString(),
      name: newConfigName.trim(),
      fileList: [],
      exampleList: []
    };

    setDynamicConfigs(prev => [...prev, newConfig]);
    setNewConfigName('');
  };

  const removeDynamicConfig = (configId: string) => {
    setDynamicConfigs(prev => prev.filter(config => config.id !== configId));
  };

  const updateConfigFileList = (configId: string, fileList: CustomUploadFile[]) => {
    setDynamicConfigs(prev => prev.map(config =>
      config.id === configId ? { ...config, fileList } : config
    ));
  };

  const updateConfigExampleList = (configId: string, exampleList: CustomUploadFile[]) => {
    setDynamicConfigs(prev => prev.map(config =>
      config.id === configId ? { ...config, exampleList } : config
    ));
  };

  const handleConfigInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addDynamicConfig();
    }
  };

  // 通用上传处理函数
  const createUploadHandler = (setFileListFunc: React.Dispatch<React.SetStateAction<CustomUploadFile[]>>, maxCount: number = 1) => {
    return async (options: any) => {
      const { file, onSuccess, onError } = options;
      const notification = GetNotification();
      try {
        const url = await uploadApi.uploadToOss(file as RcFile);
        const newFile = { ...(file as RcFile), uid: file.uid, name: file.name, status: 'done' as const, url: url };

        if (maxCount === 1) {
          setFileListFunc([newFile]);
        } else {
          setFileListFunc(prev => [...prev, newFile]);
        }

        if (onSuccess) {
          onSuccess(null, file);
        }
        notification.success(`${file.name} 上传成功`);
      } catch (err: any) {
        console.error('上传失败:', err);
        notification.error(`${file.name} 上传失败: ${err.message || '请稍后重试'}`);
        const errorFile = { ...(file as RcFile), uid: file.uid, name: file.name, status: 'error' as const, error: err };

        if (maxCount === 1) {
          setFileListFunc([errorFile]);
        } else {
          setFileListFunc(prev => [...prev, errorFile]);
        }

        if (onError) {
          onError(err);
        }
      }
    };
  };

  // 封面图片上传处理
  const handleCustomRequest = createUploadHandler(setFileList, 1);

  // 通用删除处理函数
  const createRemoveHandler = (setFileListFunc: React.Dispatch<React.SetStateAction<CustomUploadFile[]>>) => {
    return async (file: CustomUploadFile) => {
      if (!file) {
        console.warn('文件对象为空，跳过删除操作');
        return true;
      }

      const notification = GetNotification();
      if (file.url) {
        try {
          await uploadApi.deleteFromOss(file.url);
          notification.success(`${file.name || '文件'} 已从服务器删除`);
        } catch (error) {
          console.error('从 OSS 删除文件失败:', error);
          notification.error(`${file.name || '文件'} 服务器删除失败`);
        }
      }

      try {
        setFileListFunc(prev => prev.filter(f => f.uid !== file.uid));
      } catch (error) {
        console.error('更新文件列表失败:', error);
      }

      return true;
    };
  };

  // 封面图片删除处理
  const handleRemove = createRemoveHandler(setFileList);

  const handleSubmit = async () => {
    const notification = GetNotification();
    try {
      // 检查用户登录状态
      const token = localStorage.getItem('token');
      const userInfo = localStorage.getItem('user');
      console.log('当前登录状态:', { token: !!token, userInfo: !!userInfo });

      if (!token) {
        notification.error('请先登录后再创建活动');
        return;
      }

      console.log('开始表单验证...');
      const values = await form.validateFields();
      console.log('表单验证成功，获取到的值:', values);

      if (!fileList.length || fileList[0].status !== 'done' || !fileList[0].url) {
        console.log('文件上传检查失败:', { fileList, status: fileList[0]?.status, url: fileList[0]?.url });
        notification.error('请上传封面图片');
        return;
      }
      console.log('文件上传检查通过');
      setLoading(true);

      // 处理附件文件：格式为 "文件名1,URL1,文件名2,URL2"
      const attachmentData = attachmentFiles
        .filter(f => f.status === 'done' && f.url)
        .map(f => `${f.name},${f.url}`)
        .join(',');

      // 处理日期时间：开始日期设为00:00:00，结束日期设为23:59:59
      const startDate = dayjs(values.startTime).startOf('day').toDate();
      const endDate = dayjs(values.endTime).endOf('day').toDate();

      // 处理动态配置数据
      let registrationFormData = '';
      if (dynamicConfigs.length > 0) {
        const configDataArray = dynamicConfigs.map(config => {
          const fileUrl = config.fileList.find(f => f.status === 'done' && f.url)?.url || '';
          const exampleUrl = config.exampleList.find(f => f.status === 'done' && f.url)?.url || '';
          return `${config.name},${fileUrl},${exampleUrl}`;
        }).filter(data => {
          const parts = data.split(',');
          return parts.length === 3 && parts[0] && parts[1] && parts[2]; // 确保有完整的数据
        });

        if (configDataArray.length > 0) {
          registrationFormData = configDataArray.join('|'); // 多个配置项用 | 分隔
        }
      }

      const params: ActivityParams = {
        ...values,
        startTime: startDate,
        endTime: endDate,
        coverImage: fileList[0].url,
        promotionImage: promotionImage.find(f => f.status === 'done' && f.url)?.url || '',
        attachmentFiles: attachmentData,
        competitionGroups: competitionGroups.length > 0 ? competitionGroups.join(',') : undefined,
        registrationForm: registrationFormData || undefined,
        activityType: ActivityType.WORK, // 默认设置为作品活动
      };
      console.log('创建活动的参数：', params);
      await activityApi.create(params);

      notification.success('活动创建成功');
      form.resetFields();
      setFileList([]);
      setPromotionImage([]);
      setAttachmentFiles([]);
      setCompetitionGroups([]);
      setNewGroupInput('');
      setDynamicConfigs([]);
      setNewConfigName('');
      onSuccess();
      onCancel();
    } catch (error: any) {
      console.error('创建活动失败:', error);

      // 检查是否是表单验证错误
      if (error.errorFields && Array.isArray(error.errorFields)) {
        console.log('表单验证错误详情:', error.errorFields);
        const errorMessages = error.errorFields.map((field: any) =>
          `${field.name.join('.')}: ${field.errors.join(', ')}`
        ).join('; ');
        notification.error(`表单验证失败: ${errorMessages}`);
      } else {
        notification.error(`创建活动失败: ${error.message || '未知错误'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = async () => {
    try {
      // 清除封面图片
      if (fileList.length && fileList[0]?.url) {
        try {
          await handleRemove(fileList[0]);
        } catch (error) {
          console.warn('清除封面图片失败:', error);
        }
      }

      // 清除宣传图片
      if (promotionImage.length && promotionImage[0]?.url) {
        try {
          await createRemoveHandler(setPromotionImage)(promotionImage[0]);
        } catch (error) {
          console.warn('清除宣传图片失败:', error);
        }
      }

      // 清除附件文件
      for (const file of attachmentFiles) {
        if (file?.url) {
          try {
            await createRemoveHandler(setAttachmentFiles)(file);
          } catch (error) {
            console.warn('清除附件文件失败:', error);
          }
        }
      }

      // 清除动态配置文件
      for (const config of dynamicConfigs) {
        if (config.fileList?.length && config.fileList[0]?.url) {
          try {
            await uploadApi.deleteFromOss(config.fileList[0].url);
          } catch (error) {
            console.warn('清除报名表文件失败:', error);
          }
        }
        if (config.exampleList?.length && config.exampleList[0]?.url) {
          try {
            await uploadApi.deleteFromOss(config.exampleList[0].url);
          } catch (error) {
            console.warn('清除示例图片失败:', error);
          }
        }
      }
    } catch (error) {
      console.warn('清理文件时出现错误:', error);
    }

    // 重置表单和状态
    form.resetFields();
    setFileList([]);
    setPromotionImage([]);
    setAttachmentFiles([]);
    setCompetitionGroups([]);
    setNewGroupInput('');
    setDynamicConfigs([]);
    setNewConfigName('');
    onCancel();
  };



  return (
    <Modal
      title="创建活动"
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={loading}
      width={800}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          status: 0,
        }}
      >
        <Form.Item
          name="name"
          label="活动名称"
          rules={[{ required: true, message: '请输入活动名称' }]}
        >
          <Input placeholder="请输入活动名称" />
        </Form.Item>

        <Form.Item
          name="organizer"
          label="主办方"
          rules={[{ required: true, message: '请输入主办方' }]}
        >
          <Input placeholder="请输入主办方" />
        </Form.Item>

        <Form.Item
          label="参赛组别配置"
          help="为活动配置可选的参赛组别，学生提交时可从中选择"
        >
          <div className="space-y-2">
            <div className="flex gap-2">
              <Input
                placeholder="输入组别名称，如：小学组、初中组等"
                value={newGroupInput}
                onChange={(e) => setNewGroupInput(e.target.value)}
                onKeyDown={handleGroupInputKeyPress}
                style={{ flex: 1 }}
              />
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={addCompetitionGroup}
                disabled={!newGroupInput.trim()}
              >
                添加
              </Button>
            </div>
            {competitionGroups.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {competitionGroups.map((group, index) => (
                  <Tag
                    key={index}
                    closable
                    onClose={() => removeCompetitionGroup(group)}
                    color="blue"
                  >
                    {group}
                  </Tag>
                ))}
              </div>
            )}
            {competitionGroups.length === 0 && (
              <div className="text-gray-400 text-sm">
                暂未配置参赛组别，学生提交时将无法选择组别
              </div>
            )}
          </div>
        </Form.Item>

        <Form.Item
          name="startTime"
          label="开始日期"
          rules={[{ required: true, message: '请选择开始日期' }]}
        >
          <DatePicker
            format="YYYY-MM-DD"
            placeholder="请选择开始日期"
            style={{ width: '100%' }}
            onChange={() => {
              // 当开始日期改变时，清除结束日期的验证错误
              form.validateFields(['endTime']);
            }}
            disabledDate={(current) => {
              // 禁用今天之前的日期
              return current && current < dayjs().startOf('day');
            }}
          />
        </Form.Item>

        <Form.Item
          name="endTime"
          label="结束日期"
          rules={[
            { required: true, message: '请选择结束日期' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                const startTime = getFieldValue('startTime');
                if (!value || !startTime) {
                  return Promise.resolve();
                }
                if (dayjs(value).isBefore(dayjs(startTime), 'day')) {
                  return Promise.reject(new Error('结束日期不能早于开始日期'));
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <DatePicker
            format="YYYY-MM-DD"
            placeholder="请选择结束日期"
            style={{ width: '100%' }}
            disabledDate={(current) => {
              const startTime = form.getFieldValue('startTime');
              if (!startTime) {
                // 如果没有选择开始日期，禁用今天之前的日期
                return current && current < dayjs().startOf('day');
              }
              // 禁用开始日期之前的日期
              return current && current < dayjs(startTime).startOf('day');
            }}
          />
        </Form.Item>

        <Form.Item
          label="封面图片"
          required
        >
          <Upload<CustomUploadFile>
            listType="picture-card"
            maxCount={1}
            fileList={fileList}
            customRequest={handleCustomRequest}
            onRemove={handleRemove}
          >
            {fileList.length >= 1 ? null : (
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>上传</div>
              </div>
            )}
          </Upload>
        </Form.Item>

        <Form.Item
          label="宣传长图"
          help="用于活动推广展示的长图"
        >
          <Upload<CustomUploadFile>
            listType="picture-card"
            maxCount={1}
            fileList={promotionImage}
            customRequest={createUploadHandler(setPromotionImage, 1)}
            onRemove={createRemoveHandler(setPromotionImage)}
            accept="image/*"
          >
            {promotionImage.length >= 1 ? null : (
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>上传宣传长图</div>
              </div>
            )}
          </Upload>
        </Form.Item>

        <Form.Item
          label="上传附件"
          help="可上传活动相关的附件文件，如报名表、活动说明等"
        >
          <Upload
            fileList={attachmentFiles}
            customRequest={createUploadHandler(setAttachmentFiles, 10)}
            onRemove={createRemoveHandler(setAttachmentFiles)}
            accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar"
            maxCount={10}
          >
            <button type="button" style={{ border: 'none', background: 'none' }}>
              <UploadOutlined /> 选择附件
            </button>
          </Upload>
        </Form.Item>

        <Form.Item
          label="报名表配置"
          help="为活动配置需要的报名表文件，如参赛协议、家长同意书等"
        >
          <div className="space-y-4">
            {/* 添加新配置项 */}
            <div className="flex gap-2">
              <Input
                placeholder="输入配置项名称，如：参赛协议、家长同意书"
                value={newConfigName}
                onChange={(e) => setNewConfigName(e.target.value)}
                onKeyDown={handleConfigInputKeyPress}
                style={{ flex: 1 }}
              />
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={addDynamicConfig}
                disabled={!newConfigName.trim()}
              >
                添加
              </Button>
            </div>

            {/* 显示已添加的配置项 */}
            {dynamicConfigs.map((config) => (
              <div key={config.id} className="border border-gray-200 rounded-lg p-4 space-y-3">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium text-gray-900">{config.name}</h4>
                  <Button
                    type="text"
                    danger
                    size="small"
                    onClick={() => removeDynamicConfig(config.id)}
                  >
                    删除
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* 文件上传 */}
                  <div>
                    <label className="block text-sm font-medium mb-2">{config.name}文件</label>
                    <Upload
                      fileList={config.fileList}
                      customRequest={async (options: any) => {
                        const { file, onSuccess, onError } = options;
                        const notification = GetNotification();
                        try {
                          const url = await uploadApi.uploadToOss(file as RcFile);
                          const newFile = { ...(file as RcFile), uid: file.uid, name: file.name, status: 'done' as const, url: url };
                          updateConfigFileList(config.id, [newFile]);
                          if (onSuccess) {
                            onSuccess(null, file);
                          }
                          notification.success(`${file.name} 上传成功`);
                        } catch (err: any) {
                          console.error('上传失败:', err);
                          notification.error(`${file.name} 上传失败: ${err.message || '请稍后重试'}`);
                          const errorFile = { ...(file as RcFile), uid: file.uid, name: file.name, status: 'error' as const, error: err };
                          updateConfigFileList(config.id, [errorFile]);
                          if (onError) {
                            onError(err);
                          }
                        }
                      }}
                      onRemove={async (file: any) => {
                        if (file?.url) {
                          try {
                            await uploadApi.deleteFromOss(file.url);
                          } catch (error) {
                            console.error('从 OSS 删除文件失败:', error);
                          }
                        }
                        updateConfigFileList(config.id, []);
                        return true;
                      }}
                      accept=".pdf,.doc,.docx,.xls,.xlsx"
                      maxCount={1}
                    >
                      <button type="button" style={{ border: 'none', background: 'none' }}>
                        <UploadOutlined /> 选择{config.name}文件
                      </button>
                    </Upload>
                  </div>

                  {/* 示例图上传 */}
                  <div>
                    <label className="block text-sm font-medium mb-2">示例图片</label>
                    <Upload
                      listType="picture-card"
                      maxCount={1}
                      fileList={config.exampleList}
                      customRequest={async (options: any) => {
                        const { file, onSuccess, onError } = options;
                        const notification = GetNotification();
                        try {
                          const url = await uploadApi.uploadToOss(file as RcFile);
                          const newFile = { ...(file as RcFile), uid: file.uid, name: file.name, status: 'done' as const, url: url };
                          updateConfigExampleList(config.id, [newFile]);
                          if (onSuccess) {
                            onSuccess(null, file);
                          }
                          notification.success(`${file.name} 上传成功`);
                        } catch (err: any) {
                          console.error('上传失败:', err);
                          notification.error(`${file.name} 上传失败: ${err.message || '请稍后重试'}`);
                          const errorFile = { ...(file as RcFile), uid: file.uid, name: file.name, status: 'error' as const, error: err };
                          updateConfigExampleList(config.id, [errorFile]);
                          if (onError) {
                            onError(err);
                          }
                        }
                      }}
                      onRemove={async (file: any) => {
                        if (file?.url) {
                          try {
                            await uploadApi.deleteFromOss(file.url);
                          } catch (error) {
                            console.error('从 OSS 删除文件失败:', error);
                          }
                        }
                        updateConfigExampleList(config.id, []);
                        return true;
                      }}
                      accept="image/*"
                    >
                      {config.exampleList.length >= 1 ? null : (
                        <div>
                          <PlusOutlined />
                          <div style={{ marginTop: 8 }}>上传示例图</div>
                        </div>
                      )}
                    </Upload>
                  </div>
                </div>
              </div>
            ))}

            {dynamicConfigs.length === 0 && (
              <div className="text-gray-400 text-sm text-center py-4 border border-dashed border-gray-200 rounded-lg">
                暂未配置报名表文件，学生提交时将无需上传相关文件
              </div>
            )}
          </div>
        </Form.Item>

        <Form.Item name="status" label="状态" hidden>
          <Input type="hidden" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddActivityModal; 