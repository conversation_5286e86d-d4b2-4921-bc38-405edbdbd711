import { Modal, Form, Input, Button } from 'antd';
import { useEffect } from 'react';

interface EditClassModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: { className: string }) => Promise<void>;
  initialValues?: {
    className: string;
  };
}

export const EditClassModal: React.FC<EditClassModalProps> = ({
  visible,
  onCancel,
  onOk,
  initialValues
}) => {
  const [form] = Form.useForm();

  // 只在Modal打开时设置一次初始值
  useEffect(() => {
    if (visible && initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [visible]);  // 只依赖visible，避免initialValues变化时重置表单

  return (
    <Modal
      title="编辑班级"
      open={visible}
      onCancel={() => {
        onCancel();
        form.resetFields();
      }}
      footer={null}
      centered
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={onOk}
      >
        <Form.Item
          label="班级名称"
          name="className"
          rules={[{ required: true, message: '请输入班级名称' }]}
        >
          <Input placeholder="请输入班级名称" maxLength={8} showCount />
        </Form.Item>

        <Form.Item className="mb-0 text-right">
          <Button type="default" className="mr-2" onClick={() => {
            onCancel();
            form.resetFields();
          }}>
            取消
          </Button>
          <Button type="primary" htmlType="submit">
            确定
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
}; 