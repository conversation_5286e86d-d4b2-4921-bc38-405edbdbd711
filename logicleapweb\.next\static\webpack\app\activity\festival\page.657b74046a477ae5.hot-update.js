"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/activity/festival/page",{

/***/ "(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx":
/*!*************************************************************!*\
  !*** ./app/activity/festival/components/ActivityHeader.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Share2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Share2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./lib/store.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ActivityMediaGallery */ \"(app-pages-browser)/./app/activity/festival/components/ActivityMediaGallery.tsx\");\n/* harmony import */ var _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Modal!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _components_login_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/login-dialog */ \"(app-pages-browser)/./components/login-dialog.tsx\");\n/* harmony import */ var _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/activity */ \"(app-pages-browser)/./lib/api/activity/index.ts\");\n/* harmony import */ var _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/activity/events-task */ \"(app-pages-browser)/./lib/api/activity/events-task.ts\");\n/* harmony import */ var _lib_api_user__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/user */ \"(app-pages-browser)/./lib/api/user.ts\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 临时实现formatDate函数，以解决模块导入问题\nconst formatDate = (dateString)=>{\n    // 在服务端渲染时，直接返回原始字符串，避免水合错误\n    if (false) {}\n    const date = new Date(dateString);\n    // 检查日期是否有效\n    if (isNaN(date.getTime())) {\n        return dateString; // 如果无效，返回原始字符串\n    }\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, \"0\");\n    const day = String(date.getDate()).padStart(2, \"0\");\n    return \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n};\nconst ActivityHeader = (param)=>{\n    let { title: initialTitle, startTime: initialStartTime, endTime: initialEndTime, bannerImage: initialBannerImage, expanded, setExpanded, activityId, tags = [], organizer: initialOrganizer = \"\", activityType = _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.ActivityType.WORK, onRefreshWorks, // 媒体字段\n    promotionImage, backgroundImage, galleryImages, attachmentFiles } = param;\n    _s();\n    console.log(\"\\uD83D\\uDD0D [DEBUG] ActivityHeader 组件渲染\", {\n        activityId,\n        initialTitle\n    });\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_9__.GetNotification)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.usePathname)();\n    // 显示用的标题和时间\n    const title = initialTitle;\n    const startTime = initialStartTime;\n    const endTime = initialEndTime;\n    const bannerImage = initialBannerImage;\n    const organizer = initialOrganizer;\n    // 获取当前用户信息和dispatch\n    const userState = (0,react_redux__WEBPACK_IMPORTED_MODULE_11__.useSelector)(_lib_store__WEBPACK_IMPORTED_MODULE_3__.selectUserState);\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_11__.useDispatch)();\n    const isAdmin = (userState === null || userState === void 0 ? void 0 : userState.roleId) === 4;\n    // 同步用户状态的函数 - 增强版，从API获取最新用户信息\n    const syncUserState = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 开始同步用户状态\");\n            const localUser = localStorage.getItem(\"user\");\n            if (localUser) {\n                const userData = JSON.parse(localUser);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中的用户数据\", userData);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] Redux 中的用户数据\", userState);\n                // 如果用户已登录但phone为空，尝试从API获取最新用户信息\n                if (userData.isLoggedIn && userData.userId && (!userData.phone || userData.phone.trim() === \"\")) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户phone为空，从API获取最新用户信息\");\n                    try {\n                        const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_8__.userApi.getUserInfo(userData.userId);\n                        if (response.code === 200 && response.data) {\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取到最新用户信息\", response.data);\n                            // 构建更新后的用户数据\n                            const updatedUserData = {\n                                ...userData,\n                                phone: response.data.phone || \"\",\n                                nickName: response.data.nickName || userData.nickName,\n                                avatarUrl: response.data.avatarUrl || userData.avatarUrl,\n                                gender: response.data.gender || userData.gender,\n                                roleId: response.data.roleId || userData.roleId\n                            };\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 更新用户数据\", {\n                                oldPhone: userData.phone,\n                                newPhone: updatedUserData.phone,\n                                updated: updatedUserData\n                            });\n                            // 更新localStorage和Redux\n                            localStorage.setItem(\"user\", JSON.stringify(updatedUserData));\n                            dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(updatedUserData));\n                            return;\n                        }\n                    } catch (error) {\n                        console.error(\"\\uD83D\\uDD0D [DEBUG] 从API获取用户信息失败:\", error);\n                    }\n                }\n                // 检查关键字段是否有变化\n                const hasPhoneChanged = userData.phone !== (userState === null || userState === void 0 ? void 0 : userState.phone);\n                const hasRoleChanged = userData.roleId !== (userState === null || userState === void 0 ? void 0 : userState.roleId);\n                const hasLoginStatusChanged = userData.isLoggedIn !== (userState === null || userState === void 0 ? void 0 : userState.isLoggedIn);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态变化检查\", {\n                    phoneChanged: hasPhoneChanged,\n                    roleChanged: hasRoleChanged,\n                    loginStatusChanged: hasLoginStatusChanged,\n                    oldPhone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                    newPhone: userData.phone,\n                    oldRole: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                    newRole: userData.roleId\n                });\n                // 如果localStorage中的用户状态与Redux中的不一致，说明需要更新\n                if (hasPhoneChanged || hasRoleChanged || hasLoginStatusChanged) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户状态变化，同步Redux状态\");\n                    dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(userData));\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态无变化，无需同步\");\n                }\n            } else {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中无用户数据\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD0D [DEBUG] 检查用户状态失败:\", error);\n        }\n    };\n    // 监听页面焦点变化和路由变化，重新检查用户状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 页面加载时立即检查一次\n        syncUserState().catch(console.error);\n        const handleVisibilityChange = ()=>{\n            if (document.visibilityState === \"visible\") {\n                syncUserState().catch(console.error);\n            }\n        };\n        const handleFocus = ()=>{\n            syncUserState().catch(console.error);\n        };\n        // 添加多种事件监听确保状态同步\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n        window.addEventListener(\"focus\", handleFocus);\n        return ()=>{\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n            window.removeEventListener(\"focus\", handleFocus);\n        };\n    }, [\n        userState === null || userState === void 0 ? void 0 : userState.phone,\n        dispatch\n    ]);\n    // 工具函数：检查用户是否为学生身份\n    const checkIsStudent = ()=>{\n        try {\n            const userStr = localStorage.getItem(\"user\");\n            const localUser = userStr ? JSON.parse(userStr) : null;\n            const userRoleId = (localUser === null || localUser === void 0 ? void 0 : localUser.roleId) || (userState === null || userState === void 0 ? void 0 : userState.roleId);\n            return userRoleId === 1; // 学生身份的roleId为1\n        } catch (error) {\n            console.error(\"解析localStorage中的用户信息失败:\", error);\n            return false;\n        }\n    };\n    // 工具函数：显示身份验证失败提示\n    const showStudentOnlyModal = ()=>{\n        _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].confirm({\n            title: \"身份验证\",\n            content: \"抱歉，只有学生身份的用户才能参加此活动报名。如果您是学生，请联系管理员更新您的身份信息。\",\n            okText: \"我知道了\",\n            cancelText: \"联系管理员\",\n            onCancel: ()=>{\n                notification.info(\"如需帮助，请联系客服：400-123-4567\");\n            }\n        });\n    };\n    // 获取报名状态文本\n    const getRegistrationStatusText = ()=>{\n        switch(registrationStatus){\n            case 0:\n                return \"已取消\";\n            case 1:\n                return \"已报名\";\n            case 2:\n                return \"已审核通过\";\n            case 3:\n                return \"已拒绝\";\n            case 4:\n                return \"评审中\";\n            case 5:\n                return \"已获奖\";\n            case 6:\n                return \"审核中\";\n            default:\n                return \"已报名\";\n        }\n    };\n    // 登录和报名相关状态\n    const [showLoginDialog, setShowLoginDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSubmitted, setHasSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [registrationStatus, setRegistrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 报名状态：0-已取消 1-已报名 2-已审核通过 3-已拒绝 4-评审中 5-已获奖 6-审核中\n    const [showUI, setShowUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 添加客户端检测来解决水合错误\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    // 页面加载时检查报名状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkRegistrationStatus = async ()=>{\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态 - useEffect 触发\", {\n                isLoggedIn: userState.isLoggedIn,\n                roleId: userState.roleId,\n                phone: userState.phone,\n                isClient,\n                activityId\n            });\n            // 只有在用户已登录且是学生身份时才检查报名状态\n            const isStudent = checkIsStudent();\n            if (!userState.isLoggedIn || !isStudent) {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录或不是学生身份，重置报名状态\", {\n                    isLoggedIn: userState.isLoggedIn,\n                    isStudent\n                });\n                // 如果用户未登录或不是学生，重置状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n                return;\n            }\n            try {\n                var _response_data, _response_data1;\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 开始检查报名状态 API 调用\");\n                setIsChecking(true);\n                const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.checkRegistration(Number(activityId));\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 页面加载检查报名状态响应:\", response);\n                if ((response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200 && ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data)) {\n                    const { submitted, submit } = response.data.data;\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态检查结果\", {\n                        submitted,\n                        submit\n                    });\n                    setHasSubmitted(submitted);\n                    if (submit && submit.status !== undefined) {\n                        setRegistrationStatus(submit.status);\n                    } else {\n                        setRegistrationStatus(null);\n                    }\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态响应不符合预期，设置为未报名状态\");\n                    // 如果响应不符合预期，设置为未报名状态\n                    setHasSubmitted(false);\n                    setRegistrationStatus(null);\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态失败:\", error);\n                // 出错时设置为未报名状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n            } finally{\n                setIsChecking(false);\n            }\n        };\n        // 只在客户端执行，避免服务端渲染问题\n        if (isClient) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 客户端环境，执行报名状态检查\");\n            checkRegistrationStatus();\n        } else {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 非客户端环境，跳过报名状态检查\");\n        }\n    }, [\n        activityId,\n        userState.isLoggedIn,\n        userState.roleId,\n        userState.phone,\n        isClient\n    ]);\n    // 前往任务函数\n    const handleGoToTask = async ()=>{\n        try {\n            const loadingMessage = notification.loading(\"正在跳转到任务...\");\n            // 获取当前用户的赛事任务列表\n            const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n            if (eventsTaskResponse.data.code === 200) {\n                const eventsTasks = eventsTaskResponse.data.data;\n                // 查找与当前活动相关的赛事任务\n                const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                if (relatedTask) {\n                    // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                    window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                    notification.success(\"正在跳转到任务页面...\");\n                } else {\n                    // 如果没有找到对应任务，在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                    notification.info(\"未找到对应任务，已打开班级空间\");\n                }\n            } else {\n                // 如果获取任务失败，在新标签页中打开班级空间\n                window.open(\"/class-space\", \"_blank\");\n                notification.info(\"获取任务失败，已打开班级空间\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"跳转任务失败:\", error);\n            notification.error(\"跳转任务失败，请稍后重试\");\n        }\n    };\n    // 处理报名按钮点击\n    const handleSubmitClick = async ()=>{\n        var _userState_phone, _localUser_phone;\n        // 获取localStorage中的用户信息进行对比\n        const localUserStr = localStorage.getItem(\"user\");\n        const localUser = localUserStr ? JSON.parse(localUserStr) : null;\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 报名按钮点击 - 详细用户状态检查\", {\n            hasSubmitted,\n            \"Redux userState\": {\n                isLoggedIn: userState === null || userState === void 0 ? void 0 : userState.isLoggedIn,\n                phone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                phoneLength: userState === null || userState === void 0 ? void 0 : (_userState_phone = userState.phone) === null || _userState_phone === void 0 ? void 0 : _userState_phone.length,\n                phoneType: typeof (userState === null || userState === void 0 ? void 0 : userState.phone),\n                roleId: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                userId: userState === null || userState === void 0 ? void 0 : userState.userId,\n                nickName: userState === null || userState === void 0 ? void 0 : userState.nickName\n            },\n            \"localStorage user\": {\n                phone: localUser === null || localUser === void 0 ? void 0 : localUser.phone,\n                phoneLength: localUser === null || localUser === void 0 ? void 0 : (_localUser_phone = localUser.phone) === null || _localUser_phone === void 0 ? void 0 : _localUser_phone.length,\n                phoneType: typeof (localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                userId: localUser === null || localUser === void 0 ? void 0 : localUser.userId,\n                nickName: localUser === null || localUser === void 0 ? void 0 : localUser.nickName\n            },\n            \"phone validation\": {\n                \"userState.phone exists\": !!(userState === null || userState === void 0 ? void 0 : userState.phone),\n                \"userState.phone not empty\": (userState === null || userState === void 0 ? void 0 : userState.phone) && userState.phone.trim() !== \"\",\n                \"localUser.phone exists\": !!(localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                \"localUser.phone not empty\": (localUser === null || localUser === void 0 ? void 0 : localUser.phone) && (localUser === null || localUser === void 0 ? void 0 : localUser.phone.trim()) !== \"\"\n            }\n        });\n        // 临时调用API获取最新用户信息进行对比\n        if (userState === null || userState === void 0 ? void 0 : userState.userId) {\n            try {\n                var _response_data, _response_data_phone, _response_data1, _response_data2, _response_data3, _response_data4;\n                const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_8__.userApi.getUserInfo(userState.userId);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取的最新用户信息:\", {\n                    code: response.code,\n                    phone: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.phone,\n                    phoneLength: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_phone = _response_data1.phone) === null || _response_data_phone === void 0 ? void 0 : _response_data_phone.length,\n                    phoneType: typeof ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.phone),\n                    nickName: (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.nickName,\n                    userId: (_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : _response_data4.id\n                });\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 获取用户信息失败:\", error);\n            }\n        }\n        // 如果用户已报名，跳转到任务页面\n        if (hasSubmitted) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已报名，跳转到任务页面\");\n            handleGoToTask();\n            return;\n        }\n        // 检查用户是否登录\n        if (!userState || !userState.isLoggedIn) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录，跳转到登录页面\");\n            // 未登录，显示登录对话框\n            const redirectUrl = pathname || \"/home\";\n            router.push(\"/login?redirect=\".concat(encodeURIComponent(redirectUrl)));\n            return;\n        }\n        // 检查用户身份：只有学生（roleId为1）才能报名\n        const isStudent = checkIsStudent();\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 检查用户身份\", {\n            isStudent,\n            roleId: userState === null || userState === void 0 ? void 0 : userState.roleId\n        });\n        if (!isStudent) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户不是学生身份，显示身份验证弹窗\");\n            showStudentOnlyModal();\n            return;\n        }\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 身份验证通过，开始报名流程\");\n        // 身份验证通过，直接进行报名\n        handleDirectRegistration();\n    };\n    // 处理登录成功\n    const handleLoginSuccess = ()=>{\n        notification.success(\"登录成功\");\n    // 登录成功后，页面的useEffect会自动检查报名状态\n    // 不需要在这里重复检查，让useEffect处理状态更新\n    };\n    // 处理报名成功\n    const handleSubmitSuccess = ()=>{\n        // 更新提交状态\n        setHasSubmitted(true);\n        setRegistrationStatus(1); // 设置为已报名状态\n        notification.success(\"报名成功\");\n        // 通知页面刷新参赛作品列表\n        if (onRefreshWorks) {\n            onRefreshWorks();\n        }\n    };\n    // 直接报名处理函数\n    const handleDirectRegistration = async ()=>{\n        try {\n            var _response_data;\n            // 检查用户是否绑定了手机号\n            if (!userState.phone || userState.phone.trim() === \"\") {\n                // 显示确认对话框\n                _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].confirm({\n                    title: \"需要绑定手机号\",\n                    content: \"报名活动需要绑定手机号，是否前往绑定？\",\n                    okText: \"前往绑定\",\n                    cancelText: \"取消\",\n                    onOk: ()=>{\n                        // 跳转到登录页面，并在URL中添加参数表示需要绑定手机号\n                        const currentUrl = window.location.href;\n                        router.push(\"/login?needBindPhone=true&redirect=\".concat(encodeURIComponent(currentUrl)));\n                    },\n                    onCancel: ()=>{\n                        notification.info(\"未绑定手机号，无法报名活动\");\n                    }\n                });\n                return;\n            }\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已绑定手机号，继续报名流程\");\n            // 显示加载提示\n            const loadingMessage = notification.loading(\"正在提交报名...\");\n            // 提交报名数据（使用默认值，跳过协议确认步骤）\n            const submitData = {\n                activityId: Number(activityId),\n                agreementAccepted: true,\n                parentConsentAccepted: true,\n                parentSignaturePath: \"\",\n                signatureTime: new Date().toISOString(),\n                remark: \"快速报名时间：\".concat(new Date().toLocaleString(\"zh-CN\"))\n            };\n            const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.submitRegistration(submitData);\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                // 更新UI状态\n                setHasSubmitted(true);\n                setRegistrationStatus(1);\n                notification.success(\"报名成功！正在跳转到班级空间...\");\n                // 通知页面刷新参赛作品列表\n                if (onRefreshWorks) {\n                    onRefreshWorks();\n                }\n                // 报名成功后，获取对应的赛事任务并跳转到班级空间\n                try {\n                    // 等待一小段时间确保后端创建赛事任务完成\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    // 获取当前用户的赛事任务列表\n                    const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n                    if (eventsTaskResponse.data.code === 200) {\n                        const eventsTasks = eventsTaskResponse.data.data;\n                        // 查找与当前活动相关的赛事任务\n                        const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                        if (relatedTask) {\n                            // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                            window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                        } else {\n                            // 如果没有找到对应任务，在新标签页中打开班级空间\n                            window.open(\"/class-space\", \"_blank\");\n                        }\n                    } else {\n                        // 如果获取任务失败，在新标签页中打开班级空间\n                        window.open(\"/class-space\", \"_blank\");\n                    }\n                } catch (taskError) {\n                    console.error(\"获取赛事任务失败:\", taskError);\n                    // 即使获取任务失败，也要在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                }\n            } else {\n                var _response_data1;\n                throw new Error(((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message) || \"报名失败\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"报名失败:\", error);\n            notification.error(error instanceof Error ? error.message : \"报名失败，请稍后重试\");\n        }\n    };\n    // 处理分享按钮点击事件\n    const handleShare = ()=>{\n        if (true) {\n            // 获取当前页面URL\n            const currentUrl = window.location.href;\n            // 复制到剪贴板\n            navigator.clipboard.writeText(currentUrl).then(()=>{\n                notification.success(\"活动链接已复制，快去分享吧！\");\n            }).catch((err)=>{\n                console.error(\"复制失败:\", err);\n                notification.error(\"复制链接失败，请手动复制\");\n            });\n        }\n    };\n    // 如果还没有在客户端挂载，返回一个简单的占位符\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-64 bg-gray-200 animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 607,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-300 rounded mb-2 w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded mb-1 w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 608,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 606,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n            lineNumber: 605,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-[300px] overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-transparent to-black/30 z-10 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 628,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-blue-500/10 animate-pulse z-0 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: bannerImage,\n                                            alt: title,\n                                            fill: true,\n                                            priority: true,\n                                            style: {\n                                                objectFit: \"cover\"\n                                            },\n                                            className: \"z-10 hover:scale-105 transition-transform duration-700\",\n                                            onError: (e)=>{\n                                                const target = e.target;\n                                                target.style.background = \"linear-gradient(120deg, #f3f4f6, #e5e7eb)\";\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-4 left-4 right-4 z-20 flex justify-between items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 626,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-800 flex items-center\",\n                                        children: [\n                                            title,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"ml-2 w-5 h-5 text-yellow-400 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    organizer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: [\n                                            \"主办方: \",\n                                            organizer\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm mt-2 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-500 to-indigo-500 h-1.5 w-1.5 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"活动时间：\",\n                                            formatDate(startTime),\n                                            \" - \",\n                                            formatDate(endTime)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 720,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3 mt-4 md:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSubmitClick,\n                                        disabled: isChecking,\n                                        className: \"flex items-center px-5 py-2.5 text-sm font-medium rounded-full \".concat(hasSubmitted ? \"bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\" : \"bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\", \" transition-all duration-200\"),\n                                        children: isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"loading-spinner mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"检查状态中\"\n                                            ]\n                                        }, void 0, true) : hasSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"已报名 前往任务\"\n                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"立即报名\"\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex items-center p-2.5 text-gray-500 hover:text-blue-500 hover:bg-gray-50 rounded-full hover:scale-110 active:scale-95 transition-all duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 777,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 735,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 622,\n                columnNumber: 7\n            }, undefined),\n            (promotionImage || backgroundImage || galleryImages || attachmentFiles) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    promotionImage: promotionImage,\n                    backgroundImage: backgroundImage,\n                    galleryImages: galleryImages,\n                    attachmentFiles: attachmentFiles,\n                    activityTitle: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                    lineNumber: 786,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 785,\n                columnNumber: 9\n            }, undefined),\n            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_login_dialog__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showLoginDialog,\n                onClose: ()=>setShowLoginDialog(false),\n                onSuccess: handleLoginSuccess\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 798,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ActivityHeader, \"LrQsrruGHwY+vh0xTSNWkYGLl0w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.usePathname,\n        react_redux__WEBPACK_IMPORTED_MODULE_11__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_11__.useDispatch\n    ];\n});\n_c = ActivityHeader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ActivityHeader);\nvar _c;\n$RefreshReg$(_c, \"ActivityHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx\n"));

/***/ })

});