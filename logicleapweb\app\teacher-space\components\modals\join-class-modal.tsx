import { Modal, Form, Input, Button } from 'antd';

interface JoinClassModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: { inviteCode: string }) => Promise<void>;
}

export const JoinClassModal: React.FC<JoinClassModalProps> = ({
  visible,
  onCancel,
  onOk,
}) => {
  const [form] = Form.useForm();

  return (
    <Modal
      title="加入班级"
      open={visible}
      onCancel={() => {
        onCancel();
        form.resetFields();
      }}
      footer={null}
      centered
      width={500}
      style={{
        maxWidth: '90vw',
        margin: '10vh auto',
        padding: 0,
        top: 0
      }}
      bodyStyle={{
        padding: '20px'
      }}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={onOk}
      >
        <Form.Item
          label="邀请码"
          name="inviteCode"
          rules={[{ required: true, message: '请输入邀请码' }]}
        >
          <Input placeholder="请输入班级邀请码" />
        </Form.Item>

        <Form.Item className="mb-0 text-right">
          <Button type="default" className="mr-2" onClick={() => {
            onCancel();
            form.resetFields();
          }}>
            取消
          </Button>
          <Button type="primary" htmlType="submit">
            确定
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
}; 