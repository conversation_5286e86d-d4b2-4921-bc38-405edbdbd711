'use client'

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card, Table, Tag, Space, Button, Tooltip, Dropdown, Menu, Input, Modal, Tabs } from 'antd';
import TextArea from 'antd/lib/input/TextArea';
import { ArrowLeftOutlined, EyeOutlined, FileTextOutlined, DownOutlined, SearchOutlined, DownloadOutlined, CloudDownloadOutlined } from '@ant-design/icons';
import { activityApi } from '@/lib/api/activity';
import { eventsTaskApi } from '@/lib/api/activity/events-task';
import { userApi } from '@/lib/api/user';
import { worksApi } from '@/lib/api/works';
import { viewWork } from '@/lib/utils/view-work-modal';
import { GetNotification } from 'logic-common/dist/components/Notification';
import dayjs from 'dayjs';
import JSZip from 'jszip';



interface Activity {
  id: number;
  name: string;
  organizer: string;
  startTime: string;
  endTime: string;
}

interface EventsTask {
  id: number;
  userId: number;
  activityId: number;
  eventName: string;
  startTime: string;
  endTime: string;
  workId?: number;
  workFile?: string;
  workDescription?: string;
  instructorName?: string;
  schoolName?: string;
  contactPerson?: string;
  contactPhone?: string;
  status: number; // 0-待开始 1-进行中 2-已完成 3-已取消
  createTime: string;
  updateTime: string;
  remark?: string;
  // 新增字段
  realName?: string;
  idNumber?: string;
  affiliatedSchool?: string;
  organization?: string;
  instructorPhone?: string;
  competitionGroup?: string;
  registrationFormFile?: string;
}

function ActivityWorksContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const activityId = searchParams.get('activityId');

  // 添加自定义样式
  const customStyles = `
    .custom-events-table .ant-table-thead > tr > th {
      background: #fafafa;
      color: #262626;
      font-weight: 600;
      border-bottom: 2px solid #e8e8e8;
      text-align: left;
    }
    .custom-events-table .ant-table-tbody > tr:hover > td {
      background-color: #f0f9ff !important;
    }
    .custom-events-table .ant-table-tbody > tr > td {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 12px;
    }
    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  `;

  const [activity, setActivity] = useState<Activity | null>(null);
  const [eventsTasks, setEventsTasks] = useState<EventsTask[]>([]);
  const [eventsTasksLoading, setEventsTasksLoading] = useState(false);
  const [userInfoMap, setUserInfoMap] = useState<Record<number, any>>({});

  // 筛选状态
  const [workFilter, setWorkFilter] = useState<string>('all'); // all, hasWork, hasFile, noWork
  const [statusFilter, setStatusFilter] = useState<string>('all'); // all, inProgress, submitted, reviewed
  const [searchText, setSearchText] = useState<string>(''); // 学生姓名和学校搜索
  const [schoolFilter, setSchoolFilter] = useState<string>(''); // 学校筛选
  const [groupFilter, setGroupFilter] = useState<string>(''); // 参赛组别筛选
  const [activeTab, setActiveTab] = useState<string>('all'); // 标签页状态

  // 审核相关状态
  const [reviewModalVisible, setReviewModalVisible] = useState(false);
  const [reviewTaskId, setReviewTaskId] = useState<number | null>(null);
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject'>('approve');
  const [reviewReason, setReviewReason] = useState('');

  const notification = GetNotification();



  // 查看作品
  const handleViewWork = async (workId: number) => {
    if (!workId) {
      notification.error('作品ID无效');
      return;
    }

    try {
      const loadingMessage = notification.loading('正在加载作品...');
      const response = await worksApi.getDetail(workId);

      if (response?.data?.code === 200 && response.data.data) {
        const work = response.data.data;
        viewWork({
          content: work.content,
          workId: workId,
          userId: work.userId
        });
        notification.success('加载成功');
      } else {
        notification.error(response?.data?.message || '获取作品详情失败');
      }

      if (loadingMessage) {
        loadingMessage.close();
      }
    } catch (error) {
      console.error('获取作品失败:', error);
      notification.error('加载作品失败');
    }
  };

  // 下载作品
  const handleDownloadWork = async (workId: number, record: EventsTask) => {
    if (!workId) {
      notification.error('作品ID无效');
      return;
    }

    try {
      const loadingMessage = notification.loading('正在下载作品...');
      const response = await worksApi.getDetail(workId);

      if (response?.data?.code === 200 && response.data.data) {
        const work = response.data.data;
        const workContent = work.content;
        const workTitle = work.title || '未命名作品';
        // 从userInfoMap中获取学生姓名，优先使用真实姓名
        const userInfo = userInfoMap[record.userId];
        const studentName = record.realName || (userInfo ? userInfo.nickName || '未知用户' : `用户${record.userId}`);

        if (!workContent) {
          notification.error('作品内容为空');
          return;
        }

        // 判断作品类型并设置相应的文件扩展名
        let fileExtension = 'json';
        let mimeType = 'application/json';

        // 根据作品类型设置文件扩展名
        if (work.type === 1) {
          // 图形化编程作品 (Scratch)
          fileExtension = 'sb3';
          mimeType = 'application/json';
        } else if (work.type === 2) {
          // 节点式编程作品
          fileExtension = 'json';
          mimeType = 'application/json';
        } else if (work.type === 3) {
          // AI应用作品
          fileExtension = 'json';
          mimeType = 'application/json';
        }

        // 构建文件名：比赛名称+学校+姓名
        const eventName = record.eventName || '未知比赛';
        const schoolName = record.schoolName || '未知学校';

        // 清理文件名中的特殊字符，避免文件系统问题
        const cleanEventName = eventName.replace(/[<>:"/\\|?*]/g, '_');
        const cleanSchoolName = schoolName.replace(/[<>:"/\\|?*]/g, '_');
        const cleanStudentName = studentName.replace(/[<>:"/\\|?*]/g, '_');

        const fileName = `${cleanEventName}_${cleanSchoolName}_${cleanStudentName}.${fileExtension}`;

        // 创建Blob并下载
        const blob = new Blob([workContent], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        notification.success(`作品下载成功: ${workTitle}`);
      } else {
        notification.error(response?.data?.message || '获取作品详情失败');
      }

      if (loadingMessage) {
        loadingMessage.close();
      }
    } catch (error) {
      console.error('下载作品失败:', error);
      notification.error('下载作品失败');
    }
  };

  // 下载附件
  const handleDownloadAttachment = async (record: EventsTask) => {
    if (!record.workFile) {
      notification.error('附件文件无效');
      return;
    }

    try {
      const loadingMessage = notification.loading('正在下载附件...');

      // 从userInfoMap中获取学生姓名，优先使用真实姓名
      const userInfo = userInfoMap[record.userId];
      const studentName = record.realName || (userInfo ? userInfo.nickName || '未知用户' : `用户${record.userId}`);

      // 构建文件名：比赛名称+学校+姓名
      const eventName = record.eventName || '未知比赛';
      const schoolName = record.schoolName || '未知学校';

      // 清理文件名中的特殊字符，避免文件系统问题
      const cleanEventName = eventName.replace(/[<>:"/\\|?*]/g, '_');
      const cleanSchoolName = schoolName.replace(/[<>:"/\\|?*]/g, '_');
      const cleanStudentName = studentName.replace(/[<>:"/\\|?*]/g, '_');

      // 获取原始文件的扩展名
      const originalFileName = record.workFile.split('/').pop() || 'attachment';
      const fileExtension = originalFileName.includes('.') ?
        originalFileName.split('.').pop() : 'file';

      const fileName = `${cleanEventName}_${cleanSchoolName}_${cleanStudentName}_附件.${fileExtension}`;

      // 下载文件
      const response = await fetch(record.workFile);
      if (!response.ok) {
        throw new Error('下载附件失败');
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      notification.success('附件下载成功');

      if (loadingMessage) {
        loadingMessage.close();
      }
    } catch (error) {
      console.error('下载附件失败:', error);
      notification.error('下载附件失败');
    }
  };

  // 打开审核模态框
  const openReviewModal = (taskId: number, action: 'approve' | 'reject') => {
    setReviewTaskId(taskId);
    setReviewAction(action);
    setReviewReason('');
    setReviewModalVisible(true);
  };

  // 审核任务
  const handleReviewTask = async () => {
    if (!reviewTaskId) return;

    try {
      const status = reviewAction === 'approve' ? 3 : 4; // 3=已审核，4=审核不通过
      const remark = reviewAction === 'reject' ? reviewReason : undefined;

      const response = await eventsTaskApi.adminReviewTask(reviewTaskId, status, remark);
      if (response.data.code === 200) {
        notification.success(reviewAction === 'approve' ? '审核通过成功' : '审核不通过成功');
        setReviewModalVisible(false);
        setReviewTaskId(null);
        setReviewReason('');
        fetchEventsTasks(); // 重新获取数据
      } else {
        notification.error(response.data.message || '审核失败');
      }
    } catch (error) {
      console.error('审核失败:', error);
      notification.error('审核失败，请重试');
    }
  };

  // 下载全部作品
  const handleDownloadAllWorks = async () => {
    // 获取所有有作品的任务
    const tasksWithWorks = filteredEventsTasks.filter(task => task.workId);

    if (tasksWithWorks.length === 0) {
      notification.warning('没有可下载的作品');
      return;
    }

    try {
      const loadingMessage = notification.loading(`正在打包下载 ${tasksWithWorks.length} 个作品...`);
      const zip = new JSZip();
      let successCount = 0;
      let failCount = 0;

      // 获取活动名称作为压缩包名称
      const activityName = activity?.name || '未知活动';
      const cleanActivityName = activityName.replace(/[<>:"/\\|?*]/g, '_');

      for (const task of tasksWithWorks) {
        try {
          // 获取作品详情
          const response = await worksApi.getDetail(task.workId!);

          if (response?.data?.code === 200 && response.data.data) {
            const work = response.data.data;
            const workContent = work.content;

            if (workContent) {
              // 获取学生信息，优先使用真实姓名
              const userInfo = userInfoMap[task.userId];
              const studentName = task.realName || (userInfo ? userInfo.nickName || '未知用户' : `用户${task.userId}`);
              const eventName = task.eventName || '未知比赛';
              const schoolName = task.schoolName || '未知学校';

              // 清理文件名
              const cleanEventName = eventName.replace(/[<>:"/\\|?*]/g, '_');
              const cleanSchoolName = schoolName.replace(/[<>:"/\\|?*]/g, '_');
              const cleanStudentName = studentName.replace(/[<>:"/\\|?*]/g, '_');

              // 确定文件扩展名
              let fileExtension = 'json';
              if (work.type === 1) {
                fileExtension = 'sb3';
              }

              const fileName = `${cleanEventName}_${cleanSchoolName}_${cleanStudentName}.${fileExtension}`;

              // 添加到压缩包
              zip.file(fileName, workContent);
              successCount++;
            } else {
              failCount++;
            }
          } else {
            failCount++;
          }
        } catch (error) {
          console.error(`下载作品 ${task.workId} 失败:`, error);
          failCount++;
        }
      }

      if (successCount > 0) {
        // 生成压缩包
        const content = await zip.generateAsync({ type: 'blob' });

        // 下载压缩包
        const url = URL.createObjectURL(content);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${cleanActivityName}_作品集.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        notification.success(`成功打包下载 ${successCount} 个作品${failCount > 0 ? `，${failCount} 个作品下载失败` : ''}`);
      } else {
        notification.error('所有作品下载失败');
      }

      if (loadingMessage) {
        loadingMessage.close();
      }
    } catch (error) {
      console.error('批量下载作品失败:', error);
      notification.error('批量下载作品失败');
    }
  };

  // 获取活动信息
  const fetchActivity = async () => {
    if (!activityId) return;

    try {
      const response = await activityApi.getActivityInfo(Number(activityId));
      if (response?.data && response.status === 200) {
        const data = response.data.data || response.data;
        setActivity(data);
      }
    } catch (error) {
      console.error('获取活动信息失败:', error);
      notification.error('获取活动信息失败');
    }
  };



  // 获取用户信息
  const fetchUserInfo = async (userId: number) => {
    if (userInfoMap[userId]) return userInfoMap[userId];

    try {
      console.log('正在获取用户信息，用户ID:', userId);
      const response = await userApi.getUserInfo(userId);
      console.log('用户信息响应:', response);
      if (response?.code === 200 && response.data) {
        console.log('用户信息数据:', response.data);
        setUserInfoMap(prev => ({
          ...prev,
          [userId]: response.data
        }));
        return response.data;
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
    return null;
  };

  // 获取活动的赛事任务列表
  const fetchEventsTasks = async () => {
    if (!activityId) return;

    try {
      setEventsTasksLoading(true);
      const response = await eventsTaskApi.getActivityTasks(Number(activityId));

      if (response?.data && response.status === 200) {
        const data = response.data.data || response.data;
        console.log('获取到的赛事任务数据:', data);
        const tasks = Array.isArray(data) ? data : [];
        setEventsTasks(tasks);

        // 获取所有用户信息
        const userIds = tasks.map(task => task.userId).filter(Boolean);
        const uniqueUserIds = Array.from(new Set(userIds));

        for (const userId of uniqueUserIds) {
          await fetchUserInfo(userId);
        }
      }
    } catch (error) {
      console.error('获取赛事任务失败:', error);
      notification.error('获取赛事任务失败');
      setEventsTasks([]);
    } finally {
      setEventsTasksLoading(false);
    }
  };

  useEffect(() => {
    if (activityId) {
      fetchActivity();
      fetchEventsTasks();
    }
  }, [activityId]);
  // 获取所有学校选项
  const getSchoolOptions = () => {
    const schools = new Set<string>();
    eventsTasks.forEach(task => {
      if (task.schoolName) schools.add(task.schoolName);
      if (task.affiliatedSchool) schools.add(task.affiliatedSchool);
    });
    return Array.from(schools).sort();
  };

  // 获取所有参赛组别选项
  const getGroupOptions = () => {
    const groups = new Set<string>();
    eventsTasks.forEach(task => {
      if (task.competitionGroup) groups.add(task.competitionGroup);
    });
    return Array.from(groups).sort();
  };





  // 获取赛事任务状态标签
  const getEventsTaskStatusTag = (status: number) => {
    switch (status) {
      case 0:
        return <Tag color="blue">待开始</Tag>;
      case 1:
        return <Tag color="orange">进行中</Tag>;
      case 2:
        return <Tag color="gold">已提交</Tag>;
      case 3:
        return <Tag color="green">已审核</Tag>;
      case 4:
        return <Tag color="red">审核不通过</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  // 筛选数据
  const filteredEventsTasks = eventsTasks.filter(task => {
    // 标签页筛选（优先级最高）
    if (activeTab !== 'all') {
      if (activeTab === 'submitted' && task.status !== 2) return false;
      if (activeTab === 'reviewed' && task.status !== 3) return false;
      if (activeTab === 'rejected' && task.status !== 4) return false;
    }

    // 姓名和学校搜索
    if (searchText.trim()) {
      const userInfo = userInfoMap[task.userId];
      const realName = task.realName || '';
      const nickName = userInfo ? userInfo.nickName || '' : '';
      const schoolName = task.schoolName || task.affiliatedSchool || '';
      const searchTerm = searchText.toLowerCase();

      // 搜索真实姓名、账号名字、学校名称
      if (!realName.toLowerCase().includes(searchTerm) &&
          !nickName.toLowerCase().includes(searchTerm) &&
          !schoolName.toLowerCase().includes(searchTerm)) {
        return false;
      }
    }

    // 学校筛选
    if (schoolFilter.trim()) {
      const taskSchool = task.schoolName || task.affiliatedSchool || '';
      if (taskSchool !== schoolFilter) {
        return false;
      }
    }

    // 参赛组别筛选
    if (groupFilter.trim()) {
      if (task.competitionGroup !== groupFilter) {
        return false;
      }
    }

    // 作品筛选
    if (workFilter !== 'all') {
      if (workFilter === 'hasWork' && !task.workId) return false;
      if (workFilter === 'hasFile' && !task.workFile) return false;
      if (workFilter === 'noWork' && (task.workId || task.workFile)) return false;
    }

    // 状态筛选（仅在"全部"标签页时生效）
    if (activeTab === 'all' && statusFilter !== 'all') {
      if (statusFilter === 'inProgress' && task.status !== 1) return false;
      if (statusFilter === 'submitted' && task.status !== 2) return false;
      if (statusFilter === 'reviewed' && task.status !== 3) return false;
      if (statusFilter === 'rejected' && task.status !== 4) return false;
    }

    return true;
  });



  // 赛事任务表格列定义
  const eventsTaskColumns = [
    {
      title: <div className="text-left">学生信息</div>,
      dataIndex: 'userId',
      key: 'userId',
      width: 140,
      render: (userId: number, record: EventsTask) => {
        const userInfo = userInfoMap[userId];
        const studentName = record.realName || (userInfo ? userInfo.nickName || '未知用户' : `用户${userId}`);
        const schoolName = record.schoolName || '未填写学校';

        return (
          <div className="space-y-1">
            <div className="font-medium text-gray-900">{studentName}</div>
            <div className="text-sm text-gray-500">{schoolName}</div>
          </div>
        );
      },
    },
    {
      title: <div className="text-left">个人信息</div>,
      dataIndex: 'personalInfo',
      key: 'personalInfo',
      width: 180,
      render: (_: any, record: EventsTask) => (
        <div className="space-y-1 text-sm">
          {record.idNumber && (
            <div>
              <span className="text-gray-600">证件号：</span>
              <span>{record.idNumber}</span>
            </div>
          )}
          {record.affiliatedSchool && (
            <div>
              <span className="text-gray-600">所属学校：</span>
              <span>{record.affiliatedSchool}</span>
            </div>
          )}
          {record.organization && (
            <div>
              <span className="text-gray-600">所属单位：</span>
              <span>{record.organization}</span>
            </div>
          )}
          {record.competitionGroup && (
            <div>
              <span className="text-gray-600">参赛组别：</span>
              <Tag color="blue">{record.competitionGroup}</Tag>
            </div>
          )}
        </div>
      ),
    },
    {
      title: (
        <div className="flex items-center gap-2 justify-start">
          <span>作品提交</span>
          <Dropdown
            overlay={
              <Menu
                selectedKeys={[workFilter]}
                onClick={({ key }) => setWorkFilter(key as string)}
              >
                <Menu.Item key="all">全部</Menu.Item>
                <Menu.Item key="hasWork">有作品</Menu.Item>
                <Menu.Item key="hasFile">有附件</Menu.Item>
                <Menu.Item key="noWork">未提交</Menu.Item>
              </Menu>
            }
            trigger={['click']}
          >
            <Button type="text" size="small" icon={<DownOutlined />} />
          </Dropdown>
          <Tooltip title="下载所有已提交的作品">
            <Button
              type="primary"
              size="small"
              icon={<CloudDownloadOutlined />}
              onClick={handleDownloadAllWorks}
              className="bg-green-500 hover:bg-green-600 border-green-500 hover:border-green-600"
            >
              下载全部
            </Button>
          </Tooltip>
        </div>
      ),
      dataIndex: 'workId',
      key: 'workId',
      width: 140,
      render: (workId: number, record: EventsTask) => (
        <div className="space-y-1">
          {workId ? (
            <div className="flex items-center gap-1 flex-wrap">
              <Button
                type="primary"
                size="small"
                icon={<EyeOutlined />}
                onClick={() => handleViewWork(workId)}
              >
                查看作品
              </Button>
              <Button
                type="default"
                size="small"
                icon={<DownloadOutlined />}
                onClick={() => handleDownloadWork(workId, record)}
                className="text-green-600 border-green-300 hover:text-green-700 hover:border-green-400"
              >
                下载作品
              </Button>
            </div>
          ) : (
            <span className="text-gray-400 text-sm">未提交作品</span>
          )}
          {record.workFile && (
            <div className="mt-1">
              <Button
                type="link"
                size="small"
                icon={<FileTextOutlined />}
                onClick={() => handleDownloadAttachment(record)}
                className="p-0 h-auto text-blue-600 hover:text-blue-700"
              >
                下载附件
              </Button>
            </div>
          )}
        </div>
      ),
    },
    {
      title: <div className="text-left">作品描述</div>,
      dataIndex: 'workDescription',
      key: 'workDescription',
      width: 200,
      ellipsis: true,
      render: (text: string) => (
        <div className="text-sm">
          {text ? (
            <div className="space-y-2">
              <Tooltip title={text}>
                <div className="line-clamp-3 max-w-[180px]">{text}</div>
              </Tooltip>
              <Button
                type="link"
                size="small"
                className="p-0 h-auto text-xs text-blue-600 hover:text-blue-800"
                onClick={() => {
                  Modal.info({
                    title: '作品描述详情',
                    content: (
                      <div className="py-2">
                        <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                          {text}
                        </p>
                      </div>
                    ),
                    width: 500,
                    okText: '关闭',
                    centered: true,
                  });
                }}
              >
                查看详细
              </Button>
            </div>
          ) : (
            <span className="text-gray-400">无描述</span>
          )}
        </div>
      ),
    },
    {
      title: <div className="text-left">指导信息</div>,
      dataIndex: 'instructorName',
      key: 'instructorName',
      width: 180,
      render: (instructorName: string, record: EventsTask) => (
        <div className="space-y-1">
          <div className="text-sm">
            <span className="text-gray-600">指导老师：</span>
            <span className="font-medium">{instructorName || '未填写'}</span>
          </div>
          {record.instructorPhone && (
            <div className="text-sm">
              <span className="text-gray-600">老师电话：</span>
              <span>{record.instructorPhone}</span>
            </div>
          )}
          <div className="text-sm">
            <span className="text-gray-600">联系人：</span>
            <span>{record.contactPerson || '未填写'}</span>
          </div>
          <div className="text-sm">
            <span className="text-gray-600">联系电话：</span>
            <span>{record.contactPhone || '未填写'}</span>
          </div>
        </div>
      ),
    },
    {
      title: <div className="text-left">报名表</div>,
      dataIndex: 'registrationFormFile',
      key: 'registrationFormFile',
      width: 120,
      render: (registrationFormFile: string) => (
        <div className="space-y-1">
          {registrationFormFile ? (
            <Button
              type="primary"
              size="small"
              icon={<DownloadOutlined />}
              onClick={() => {
                window.open(registrationFormFile, '_blank');
              }}
            >
              下载报名表
            </Button>
          ) : (
            <span className="text-gray-400 text-sm">未上传</span>
          )}
        </div>
      ),
    },
    {
      title: (
        <div className="flex items-center gap-1 justify-start">
          <span>提交状态</span>
          <Dropdown
            overlay={
              <Menu
                selectedKeys={[statusFilter]}
                onClick={({ key }) => setStatusFilter(key as string)}
              >
                <Menu.Item key="all">全部</Menu.Item>
                <Menu.Item key="inProgress">进行中</Menu.Item>
                <Menu.Item key="submitted">已提交</Menu.Item>
                <Menu.Item key="reviewed">已审核</Menu.Item>
                <Menu.Item key="rejected">审核不通过</Menu.Item>
              </Menu>
            }
            trigger={['click']}
          >
            <Button type="text" size="small" icon={<DownOutlined />} />
          </Dropdown>
        </div>
      ),
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: number, record: EventsTask) => (
        <div className="space-y-2">
          <div>{getEventsTaskStatusTag(status)}</div>
          <div className="text-xs text-gray-500">
            {dayjs(record.createTime).format('MM-DD HH:mm')}
          </div>
          {status === 2 && (
            <div className="space-x-2">
              <Button
                type="primary"
                size="small"
                onClick={() => openReviewModal(record.id, 'approve')}
                className="bg-green-500 hover:bg-green-600 border-green-500 hover:border-green-600"
              >
                审核通过
              </Button>
              <Button
                danger
                size="small"
                onClick={() => openReviewModal(record.id, 'reject')}
              >
                审核不通过
              </Button>
            </div>
          )}
          {status === 4 && record.remark && (
            <div className="text-xs text-red-500 max-w-xs">
              <div className="font-medium">不通过原因：</div>
              <div className="break-words">{record.remark}</div>
            </div>
          )}
        </div>
      ),
    },

  ];

  if (!activityId) {
    return (
      <div className="p-6">
        <div className="text-center">
          <p className="text-gray-500">缺少活动ID参数</p>
          <Button onClick={() => router.back()}>返回</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* 添加自定义样式 */}
      <style jsx>{customStyles}</style>

      {/* 页面头部 */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => router.back()}
          >
            返回
          </Button>
          <div>
            <h1 className="text-2xl font-bold">活动作品管理</h1>
            {activity && (
              <p className="text-gray-500">
                {activity.name} - {activity.organizer}
              </p>
            )}
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card size="small">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{eventsTasks.length}</div>
              <div className="text-gray-500">总参赛学生</div>
            </div>
          </Card>
          <Card size="small">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{filteredEventsTasks.length}</div>
              <div className="text-gray-500">筛选结果</div>
            </div>
          </Card>
          <Card size="small">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {filteredEventsTasks.filter(t => t.status === 2).length}
              </div>
              <div className="text-gray-500">已提交作品</div>
            </div>
          </Card>
          <Card size="small">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {filteredEventsTasks.filter(t => t.status === 1).length}
              </div>
              <div className="text-gray-500">进行中任务</div>
            </div>
          </Card>
        </div>
      </div>



      {/* 筛选区域 */}
      <Card className="mb-4">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">筛选条件</h3>
            {(searchText || schoolFilter || groupFilter) && (
              <Button
                type="link"
                onClick={() => {
                  setSearchText('');
                  setSchoolFilter('');
                  setGroupFilter('');
                }}
                className="text-red-500 hover:text-red-700"
              >
                清除所有筛选
              </Button>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索学生姓名或学校
              </label>
              <Input
                placeholder="输入学生姓名或学校名称"
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                allowClear
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                筛选学校
              </label>
              <select
                value={schoolFilter}
                onChange={(e) => setSchoolFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">全部学校</option>
                {getSchoolOptions().map(school => (
                  <option key={school} value={school}>{school}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                筛选参赛组别
              </label>
              <select
                value={groupFilter}
                onChange={(e) => setGroupFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">全部组别</option>
                {getGroupOptions().map(group => (
                  <option key={group} value={group}>{group}</option>
                ))}
              </select>
            </div>
          </div>

          {(searchText || schoolFilter || groupFilter) && (
            <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-md">
              <span className="font-medium">当前筛选条件：</span>
              {searchText && <span className="ml-2 px-2 py-1 bg-blue-100 rounded">搜索: {searchText}</span>}
              {schoolFilter && <span className="ml-2 px-2 py-1 bg-green-100 rounded">学校: {schoolFilter}</span>}
              {groupFilter && <span className="ml-2 px-2 py-1 bg-purple-100 rounded">组别: {groupFilter}</span>}
            </div>
          )}
        </div>
      </Card>

      {/* 赛事任务表格 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'all',
              label: `全部 (${eventsTasks.length})`,
              children: (
                <Table
                  columns={eventsTaskColumns}
                  dataSource={filteredEventsTasks}
                  rowKey="id"
                  loading={eventsTasksLoading}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                    pageSizeOptions: ['10', '20', '50'],
                  }}
                  scroll={{ x: 1400 }}
                  size="middle"
                  className="custom-events-table"
                  rowClassName={(record, index) =>
                    index % 2 === 0 ? 'bg-gray-50/50' : 'bg-white'
                  }
                />
              ),
            },
            {
              key: 'submitted',
              label: `已提交 (${eventsTasks.filter(task => task.status === 2).length})`,
              children: (
                <Table
                  columns={eventsTaskColumns}
                  dataSource={filteredEventsTasks}
                  rowKey="id"
                  loading={eventsTasksLoading}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                    pageSizeOptions: ['10', '20', '50'],
                  }}
                  scroll={{ x: 1400 }}
                  size="middle"
                  className="custom-events-table"
                  rowClassName={(record, index) =>
                    index % 2 === 0 ? 'bg-gray-50/50' : 'bg-white'
                  }
                />
              ),
            },
            {
              key: 'reviewed',
              label: `已审核 (${eventsTasks.filter(task => task.status === 3).length})`,
              children: (
                <Table
                  columns={eventsTaskColumns}
                  dataSource={filteredEventsTasks}
                  rowKey="id"
                  loading={eventsTasksLoading}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                    pageSizeOptions: ['10', '20', '50'],
                  }}
                  scroll={{ x: 1400 }}
                  size="middle"
                  className="custom-events-table"
                  rowClassName={(record, index) =>
                    index % 2 === 0 ? 'bg-gray-50/50' : 'bg-white'
                  }
                />
              ),
            },
            {
              key: 'rejected',
              label: `审核不通过 (${eventsTasks.filter(task => task.status === 4).length})`,
              children: (
                <Table
                  columns={eventsTaskColumns}
                  dataSource={filteredEventsTasks}
                  rowKey="id"
                  loading={eventsTasksLoading}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                    pageSizeOptions: ['10', '20', '50'],
                  }}
                  scroll={{ x: 1400 }}
                  size="middle"
                  className="custom-events-table"
                  rowClassName={(record, index) =>
                    index % 2 === 0 ? 'bg-gray-50/50' : 'bg-white'
                  }
                />
              ),
            },
          ]}
        />
      </Card>

      {/* 审核模态框 */}
      <Modal
        title={reviewAction === 'approve' ? '审核通过' : '审核不通过'}
        open={reviewModalVisible}
        onOk={handleReviewTask}
        onCancel={() => {
          setReviewModalVisible(false);
          setReviewTaskId(null);
          setReviewReason('');
        }}
        okText={reviewAction === 'approve' ? '确认通过' : '确认不通过'}
        cancelText="取消"
        okButtonProps={{
          danger: reviewAction === 'reject'
        }}
      >
        {reviewAction === 'approve' ? (
          <p>确认审核通过此任务吗？</p>
        ) : (
          <div className="space-y-4">
            <p>请填写审核不通过的原因：</p>
            <TextArea
              value={reviewReason}
              onChange={(e) => setReviewReason(e.target.value)}
              placeholder="请输入审核不通过的原因..."
              rows={4}
              maxLength={500}
              showCount
            />
          </div>
        )}
      </Modal>

    </div>
  );
}

export default function ActivityWorksPage() {
  return (
    <Suspense fallback={<div className="p-6 text-center">加载中...</div>}>
      <ActivityWorksContent />
    </Suspense>
  );
}
