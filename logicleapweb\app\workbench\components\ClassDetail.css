.class-detail-container {
  height: calc(100vh - var(--main-header-height, 80px) - 48px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.class-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 8px 16px;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}



.class-detail-content {
  display: flex;
  gap: 16px;
  flex: 1;
  overflow: hidden;
}

.left-section {
  flex: 0 0 300px;
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 左侧头部区域 */
.left-section-header {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 16px;
}

.settings-btn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  position: relative;
}

.settings-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.settings-btn.active {
  background: #f3f4f6;
  border-color: #3b82f6;
  color: #3b82f6;
}

/* 班级信息卡片样式 */
.class-info-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 16px;
  flex-shrink: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.class-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.class-card-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.class-card-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #3b82f6;
  flex-shrink: 0;
}

.class-card-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.class-card-title {
  font-size: 15px;
  font-weight: 500;
  color: #111827;
  margin: 0;
  line-height: 1.4;
}

.class-card-subtitle {
  font-size: 13px;
  color: #9ca3af;
  margin: 0;
  line-height: 1.4;
}

.class-card-badge {
  background: #fef3c7;
  color: #d97706;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #fbbf24;
  flex-shrink: 0;
}

/* 旧的班级头像样式已移除，使用新的卡片样式 */

.search-section {
  margin-bottom: 20px;
}

.search-box {
  position: relative;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 10px 14px;
  display: flex;
  align-items: center;
}

.search-icon {
  color: #9ca3af;
}

.students-section {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.students-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  flex-shrink: 0;
}

.students-header h3 {
  color: #111827;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.students-actions {
  display: flex;
  gap: 8px;
}

.more-actions-container,
.settings-container {
  position: relative;
}

.add-student-btn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  position: relative;
}

.add-student-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

/* 全选按钮样式 */
.select-all-btn {
  height: 32px;
  padding: 0 12px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  font-size: 14px;
}

.select-all-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.select-all-btn.active {
  background: #eff6ff;
  border-color: #3b82f6;
  color: #3b82f6;
}

/* 批量操作按钮样式 */
.batch-actions-container {
  position: relative;
  display: inline-block;
}

.batch-actions-btn {
  height: 32px;
  padding: 0 12px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  font-size: 14px;
  gap: 6px;
  margin-left: 8px;
}

.batch-actions-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

/* 批量操作下拉菜单 */
.batch-actions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 4px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 1000;
  min-width: 160px;
  overflow: hidden;
}

.batch-action-item {
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: none;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: #374151;
  font-size: 14px;
  text-align: left;
}

.batch-action-item:hover {
  background: #f9fafb;
}

.batch-action-item:first-child:hover {
  color: #dc2626;
}

/* 下拉菜单样式 */
.dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 180px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  z-index: 100;
  overflow: hidden;
  animation: fadeIn 0.2s ease;
}

/* 设置按钮的下拉菜单向右对齐 */
.settings-container .dropdown-menu {
  left: 0;
  right: auto;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.dropdown-menu-items {
  padding: 6px;
}

.dropdown-menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #374151;
  font-size: 14px;
}

.dropdown-menu-item:hover {
  background: #f3f4f6;
}

.dropdown-menu-item.danger {
  color: #ef4444;
}

.dropdown-menu-item.danger:hover {
  background: #fef2f2;
}

.dropdown-menu-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: inherit;
}

.dropdown-menu-divider {
  height: 1px;
  background: #e5e7eb;
  margin: 6px 0;
}

.students-list {
  flex: 1;
  overflow-y: auto;
}

.student-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 10px;
  border-bottom: 1px solid #f3f4f6;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 2px;
}

.student-item:last-child {
  border-bottom: 1px solid #f3f4f6;
}

.student-item:hover {
  background: #f9fafb;
}

.student-item.selected {
  background: #eff6ff;
  border: 1px solid #3b82f6;
  border-bottom: 1px solid #3b82f6;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.student-item.selected .student-name {
  color: #1d4ed8;
  font-weight: 600;
}

.student-item.selected .student-avatar {
  box-shadow: 0 0 0 2px #3b82f6;
}

.student-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
}

.student-info {
  flex: 1;
}

.student-name {
  color: #111827;
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 1px;
}

.student-id {
  color: #6b7280;
  font-size: 12px;
}

.student-template {
  margin-top: 4px;
}

.template-tag {
  display: inline-block;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-tag.official {
  background-color: #fef3c7;
  color: #d97706;
  border: 1px solid #fbbf24;
}

.template-tag.custom {
  background-color: #dbeafe;
  color: #2563eb;
  border: 1px solid #60a5fa;
}

.template-tag.loading {
  background-color: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.template-tag.no-template {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fca5a5;
}

.student-checkbox {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  color: #9ca3af;
  transition: all 0.2s ease;
  padding: 0;
}

.student-checkbox:hover {
  background: #f3f4f6;
}

.student-checkbox.checked {
  color: #3b82f6;
}

.right-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
  overflow: hidden;
}

/* 学生信息头部 */
.student-info-header {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

.student-avatar-large {
  flex-shrink: 0;
}

.student-avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
}

.student-details {
  flex: 1;
}

.student-name-large {
  color: #111827;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
  transition: all 0.3s ease;
}

.student-id-large {
  color: #6b7280;
  font-size: 12px;
  transition: all 0.3s ease;
}

.delete-student-btn {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: 1px solid #fecaca;
  background: #fef2f2;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #ef4444;
}

.delete-student-btn:hover:not(:disabled) {
  background: #fee2e2;
  border-color: #fca5a5;
}

.delete-student-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f9fafb;
  border-color: #e5e7eb;
  color: #9ca3af;
}

/* 功能区域容器 */
.functions-container {
  display: flex;
  gap: 16px;
  flex: 0 0 auto;
  height: 60%;
  align-items: stretch;
  overflow: hidden;
}

.functions-section {
  flex: 0 0 240px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.functions-section h3 {
  color: #6b7280;
  font-size: 13px;
  font-weight: 500;
  margin: 0 0 12px 0;
}

.function-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  justify-content: flex-start;
}

.function-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border: none;
  border-radius: 28px;
  background: #3b82f6;
  color: white;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
  min-height: 56px;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.function-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

/* 统一使用蓝色主题 */
.function-btn.publish-task,
.function-btn.distribute-blocks,
.function-btn.distribute-energy,
.function-btn.exchange-tokens,
.function-btn.reset-password {
  background: #3b82f6;
}

.function-btn.publish-task:hover,
.function-btn.distribute-blocks:hover,
.function-btn.distribute-energy:hover,
.function-btn.exchange-tokens:hover,
.function-btn.reset-password:hover {
  background: #2563eb;
}

.function-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.learning-status {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.learning-status h3 {
  color: #6b7280;
  font-size: 13px;
  font-weight: 500;
  margin: 0 0 12px 0;
  flex-shrink: 0;
}

.status-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  min-height: 200px;
  height: auto;
}

/* 底部区域 */
.bottom-section {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 16px;
  min-height: 200px;
}

/* 能量进度区域 */
.energy-progress-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.energy-progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 15px;
  color: #374151;
  font-weight: 600;
}

.energy-progress-bar {
  width: 100%;
  height: 12px;
  background: #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.energy-progress-fill {
  height: 100%;
  background: #10b981;
  border-radius: 6px;
  transition: width 0.3s ease;
}

/* 模板卡片区域 */
.template-card-section {
  margin-top: 16px;
}

.template-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px 16px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.template-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.template-card[style*="cursor: default"] {
  opacity: 0.6;
}

.template-card[style*="cursor: default"]:hover {
  border-color: #e5e7eb;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transform: none;
}

.template-card-header {
  display: flex ;
  justify-content: space-between;
  align-items: flex-start;
  min-height: 24px;
  gap:24px;
}

.template-card-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.template-card-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #3b82f6;
  flex-shrink: 0;
  margin-top: 0;
}

.template-card-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
  min-width: 0;
}

.template-card-title {
  font-size: 15px;
  font-weight: 500;
  color: #111827;
  margin: 0;
  line-height: 24px;
  height: 24px;
  display: flex;
  align-items: center;
}

.template-card-subtitle {
  font-size: 13px;
  color: #9ca3af;
  margin: 0;
  line-height: 1.4;
}

.template-card-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.template-card-badge {
  background: #fef3c7;
  color: #d97706;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #fbbf24;
  flex-shrink: 0;
  height: 24px;
  display: flex;
  align-items: center;
  margin-top: 0;
}

.template-card-swap-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.template-card-swap-icon:hover {
  background: #f3f4f6;
  color: #3b82f6;
  transform: scale(1.1);
}

.template-action:hover {
  text-decoration: underline;
}

.energy-section {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
}

.energy-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.energy-label {
  color: #9ca3af;
  font-size: 14px;
  font-weight: 400;
  margin: 0;
}

.energy-display {
  text-align: right;
}

.energy-available-number {
  color: #10b981;
  font-size: 16px;
  font-weight: 600;
}

.energy-total-number {
  color: #9ca3af;
  font-size: 16px;
  font-weight: 400;
}

.energy-bar {
  height: 12px;
  background: #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.energy-progress {
  height: 100%;
  background: #10b981;
  border-radius: 6px;
  transition: width 0.3s ease;
}

.template-section {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;
  color: #6b7280;
  font-weight: 400;
}

.template-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  /* 默认样式（官方模板） */
  background: #fef3c7;
  color: #d97706;
}

/* 自定义模板样式 */
.template-badge.custom {
  background: #e0f2fe;
  color: #0277bd;
}

.template-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.template-icon {
  font-size: 20px;
  color: #f59e0b;
}

.template-info span {
  color: #111827;
  font-size: 16px;
  font-weight: 600;
}

.template-info span {
  color: #111827;
  font-size: 14px;
  font-weight: 500;
}

/* 加载和错误状态样式 */
.loading-message,
.error-message,
.no-students-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
}

.loading-message p,
.error-message p,
.no-students-message p {
  margin: 0 0 16px 0;
  font-size: 14px;
}

.retry-button {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: #2563eb;
}

.template-description {
  margin-top: 8px;
  color: #9ca3af;
  font-size: 12px;
  line-height: 1.4;
}

.template-loading {
  color: #9ca3af !important;
  font-style: italic;
}

/* 分配能量弹窗样式 */
.assign-points-modal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
}

.assign-points-modal .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px 24px;
}

.assign-points-modal .ant-modal-body {
  padding: 24px;
}

.assign-points-modal .ant-input-number {
  border-radius: 12px;
  border: 1px solid #d1d5db;
}

.assign-points-modal .ant-input-number:focus,
.assign-points-modal .ant-input-number-focused {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.assign-points-modal .ant-alert {
  border-radius: 12px;
}

.assign-points-modal .ant-btn {
  border-radius: 8px;
  font-weight: 500;
}

.assign-points-modal .ant-btn-primary {
  background: #3b82f6;
  border-color: #3b82f6;
}

.assign-points-modal .ant-btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

/* 兑换密钥弹窗样式 */
.key-exchange-modal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
}

.key-exchange-modal .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px 24px;
}

.key-exchange-modal .ant-modal-body {
  padding: 24px;
}

.key-exchange-modal .ant-upload-dragger {
  border-radius: 12px;
  border: 2px dashed #d1d5db;
  background: linear-gradient(to bottom, rgba(59, 130, 246, 0.05), white);
}

.key-exchange-modal .ant-upload-dragger:hover {
  border-color: #3b82f6;
}

.key-exchange-modal .ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.key-exchange-modal .ant-table-thead > tr > th {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 600;
}

.key-exchange-modal .ant-btn {
  border-radius: 8px;
  font-weight: 500;
}

.key-exchange-modal .ant-btn-primary {
  background: #3b82f6;
  border-color: #3b82f6;
}

.key-exchange-modal .ant-btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.key-exchange-modal .ant-input {
  border-radius: 6px;
}

.key-exchange-modal .ant-input:focus,
.key-exchange-modal .ant-input-focused {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.key-exchange-modal .ant-alert {
  border-radius: 12px;
}

.key-exchange-modal .ant-progress-circle {
  color: #3b82f6;
}

/* 任务发布成功提示样式 */
.ant-modal-confirm .ant-modal-confirm-title {
  color: #059669;
  font-weight: 600;
  font-size: 18px;
}

.ant-modal-confirm .ant-modal-confirm-content {
  margin-top: 12px;
  color: #374151;
  line-height: 1.6;
}

.ant-modal-confirm .ant-modal-confirm-content p {
  margin-bottom: 8px;
}

.ant-modal-confirm .ant-modal-confirm-content strong {
  color: #1f2937;
  font-weight: 600;
}

.ant-modal-confirm .ant-modal-confirm-btns {
  margin-top: 24px;
}

.ant-modal-confirm .ant-btn-primary {
  background: #059669;
  border-color: #059669;
  border-radius: 8px;
  font-weight: 500;
  padding: 8px 24px;
  height: auto;
}

.ant-modal-confirm .ant-btn-primary:hover {
  background: #047857;
  border-color: #047857;
}

/* 兑换密令弹窗样式 */
.redeem-key-modal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
}

.redeem-key-modal .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px 24px;
}

.redeem-key-modal .ant-modal-body {
  padding: 24px;
}

.redeem-key-modal .ant-input {
  border-radius: 12px;
  border: 1px solid #d1d5db;
}

.redeem-key-modal .ant-input:focus,
.redeem-key-modal .ant-input-focused {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.redeem-key-modal .ant-btn {
  border-radius: 8px;
  font-weight: 500;
}

.redeem-key-modal .ant-btn-primary {
  background: #3b82f6;
  border-color: #3b82f6;
}

.redeem-key-modal .ant-btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.redeem-key-modal .ant-result-title {
  color: #1f2937;
}

.redeem-key-modal .ant-result-subtitle {
  color: #6b7280;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .functions-container {
    flex-direction: column;
    gap: 12px;
  }

  .learning-status {
    min-height: 200px;
  }
}

@media (max-width: 1200px) {
  .class-detail-content {
    flex-direction: column;
    gap: 16px;
    height: auto;
    overflow: visible;
  }

  .left-section {
    flex: none;
    max-height: 350px;
    overflow-y: auto;
  }

  .right-section {
    flex: 1;
    height: auto;
    overflow: visible;
  }

  .functions-container {
    flex-direction: column;
    gap: 12px;
    overflow: visible;
  }

  .functions-section {
    min-width: auto;
  }

  .learning-status {
    min-height: 200px;
    height: auto;
    overflow: visible;
  }
}

@media (max-width: 768px) {
  .class-detail-content {
    gap: 12px;
  }

  .left-section {
    padding: 12px;
    max-height: 300px;
  }

  .student-info-header {
    padding: 10px 12px;
    gap: 10px;
  }

  .functions-section,
  .learning-status {
    padding: 12px;
  }
}

/* 超小屏幕适配 */
@media (max-height: 700px) {
  .class-detail-container {
    height: calc(100vh - var(--main-header-height, 80px) - 48px);
  }
}

/* 小屏幕适配 */
@media (max-width: 480px) {
  .class-info-card {
    padding: 12px;
    margin-bottom: 12px;
  }

  .class-card-icon {
    width: 28px;
    height: 28px;
    font-size: 16px;
  }

  .class-card-title {
    font-size: 14px;
  }

  .class-card-subtitle {
    font-size: 12px;
  }

  .class-card-badge {
    font-size: 11px;
    padding: 3px 6px;
  }

  .template-card {
    padding: 10px 12px;
  }

  .template-card-icon {
    width: 20px;
    height: 20px;
    font-size: 14px;
  }

  .template-card-title {
    font-size: 14px;
  }

  .template-card-subtitle {
    font-size: 12px;
  }

  .template-card-badge {
    font-size: 11px;
    padding: 3px 6px;
  }

  .template-card-right {
    gap: 8px;
  }

  .template-card-swap-icon {
    width: 20px;
    height: 20px;
    font-size: 14px;
  }

  .student-item {
    padding: 4px 6px;
  }

  .student-avatar {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .function-btn {
    padding: 6px 10px;
    font-size: 11px;
  }

  .status-placeholder {
    min-height: 100px;
    font-size: 12px;
  }
}

/* 确保内容不会溢出 */
.class-detail-content * {
  box-sizing: border-box;
}

/* 滚动条样式优化 */
.students-list::-webkit-scrollbar {
  width: 4px;
}

.students-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.students-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.students-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
