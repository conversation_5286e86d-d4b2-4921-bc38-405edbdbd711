"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/activity/festival/page",{

/***/ "(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx":
/*!*************************************************************!*\
  !*** ./app/activity/festival/components/ActivityHeader.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Share2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Share2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./lib/store.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ActivityMediaGallery */ \"(app-pages-browser)/./app/activity/festival/components/ActivityMediaGallery.tsx\");\n/* harmony import */ var _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Modal!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _components_login_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/login-dialog */ \"(app-pages-browser)/./components/login-dialog.tsx\");\n/* harmony import */ var _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/activity */ \"(app-pages-browser)/./lib/api/activity/index.ts\");\n/* harmony import */ var _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/activity/events-task */ \"(app-pages-browser)/./lib/api/activity/events-task.ts\");\n/* harmony import */ var _lib_api_user__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/user */ \"(app-pages-browser)/./lib/api/user.ts\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 临时实现formatDate函数，以解决模块导入问题\nconst formatDate = (dateString)=>{\n    // 在服务端渲染时，直接返回原始字符串，避免水合错误\n    if (false) {}\n    const date = new Date(dateString);\n    // 检查日期是否有效\n    if (isNaN(date.getTime())) {\n        return dateString; // 如果无效，返回原始字符串\n    }\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, \"0\");\n    const day = String(date.getDate()).padStart(2, \"0\");\n    return \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n};\nconst ActivityHeader = (param)=>{\n    let { title: initialTitle, startTime: initialStartTime, endTime: initialEndTime, bannerImage: initialBannerImage, expanded, setExpanded, activityId, tags = [], organizer: initialOrganizer = \"\", activityType = _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.ActivityType.WORK, onRefreshWorks, // 媒体字段\n    promotionImage, backgroundImage, galleryImages, attachmentFiles } = param;\n    _s();\n    console.log(\"\\uD83D\\uDD0D [DEBUG] ActivityHeader 组件渲染\", {\n        activityId,\n        initialTitle\n    });\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_9__.GetNotification)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.usePathname)();\n    // 显示用的标题和时间\n    const title = initialTitle;\n    const startTime = initialStartTime;\n    const endTime = initialEndTime;\n    const bannerImage = initialBannerImage;\n    const organizer = initialOrganizer;\n    // 获取当前用户信息和dispatch\n    const userState = (0,react_redux__WEBPACK_IMPORTED_MODULE_11__.useSelector)(_lib_store__WEBPACK_IMPORTED_MODULE_3__.selectUserState);\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_11__.useDispatch)();\n    const isAdmin = (userState === null || userState === void 0 ? void 0 : userState.roleId) === 4;\n    // 同步用户状态的函数 - 增强版，从API获取最新用户信息\n    const syncUserState = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 开始同步用户状态\");\n            const localUser = localStorage.getItem(\"user\");\n            if (localUser) {\n                const userData = JSON.parse(localUser);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中的用户数据\", userData);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] Redux 中的用户数据\", userState);\n                // 如果用户已登录但phone为空，尝试从API获取最新用户信息\n                if (userData.isLoggedIn && userData.userId && (!userData.phone || userData.phone.trim() === \"\")) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户phone为空，从API获取最新用户信息\");\n                    try {\n                        const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_8__.userApi.getUserInfo(userData.userId);\n                        if (response.code === 200 && response.data) {\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取到最新用户信息\", response.data);\n                            // 构建更新后的用户数据\n                            const updatedUserData = {\n                                ...userData,\n                                phone: response.data.phone || \"\",\n                                nickName: response.data.nickName || userData.nickName,\n                                avatarUrl: response.data.avatarUrl || userData.avatarUrl,\n                                gender: response.data.gender || userData.gender,\n                                roleId: response.data.roleId || userData.roleId\n                            };\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 更新用户数据\", {\n                                oldPhone: userData.phone,\n                                newPhone: updatedUserData.phone,\n                                updated: updatedUserData\n                            });\n                            // 更新localStorage和Redux\n                            localStorage.setItem(\"user\", JSON.stringify(updatedUserData));\n                            dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(updatedUserData));\n                            return;\n                        }\n                    } catch (error) {\n                        console.error(\"\\uD83D\\uDD0D [DEBUG] 从API获取用户信息失败:\", error);\n                    }\n                }\n                // 检查关键字段是否有变化\n                const hasPhoneChanged = userData.phone !== (userState === null || userState === void 0 ? void 0 : userState.phone);\n                const hasRoleChanged = userData.roleId !== (userState === null || userState === void 0 ? void 0 : userState.roleId);\n                const hasLoginStatusChanged = userData.isLoggedIn !== (userState === null || userState === void 0 ? void 0 : userState.isLoggedIn);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态变化检查\", {\n                    phoneChanged: hasPhoneChanged,\n                    roleChanged: hasRoleChanged,\n                    loginStatusChanged: hasLoginStatusChanged,\n                    oldPhone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                    newPhone: userData.phone,\n                    oldRole: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                    newRole: userData.roleId\n                });\n                // 如果localStorage中的用户状态与Redux中的不一致，说明需要更新\n                if (hasPhoneChanged || hasRoleChanged || hasLoginStatusChanged) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户状态变化，同步Redux状态\");\n                    dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(userData));\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态无变化，无需同步\");\n                }\n            } else {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中无用户数据\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD0D [DEBUG] 检查用户状态失败:\", error);\n        }\n    };\n    // 监听页面焦点变化和路由变化，重新检查用户状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 页面加载时立即检查一次\n        syncUserState().catch(console.error);\n        const handleVisibilityChange = ()=>{\n            if (document.visibilityState === \"visible\") {\n                syncUserState().catch(console.error);\n            }\n        };\n        const handleFocus = ()=>{\n            syncUserState().catch(console.error);\n        };\n        // 添加多种事件监听确保状态同步\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n        window.addEventListener(\"focus\", handleFocus);\n        return ()=>{\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n            window.removeEventListener(\"focus\", handleFocus);\n        };\n    }, [\n        userState === null || userState === void 0 ? void 0 : userState.phone,\n        dispatch\n    ]);\n    // 工具函数：检查用户是否为学生身份\n    const checkIsStudent = ()=>{\n        try {\n            const userStr = localStorage.getItem(\"user\");\n            const localUser = userStr ? JSON.parse(userStr) : null;\n            const userRoleId = (localUser === null || localUser === void 0 ? void 0 : localUser.roleId) || (userState === null || userState === void 0 ? void 0 : userState.roleId);\n            return userRoleId === 1; // 学生身份的roleId为1\n        } catch (error) {\n            console.error(\"解析localStorage中的用户信息失败:\", error);\n            return false;\n        }\n    };\n    // 工具函数：显示身份验证失败提示\n    const showStudentOnlyModal = ()=>{\n        _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].confirm({\n            title: \"身份验证\",\n            content: \"抱歉，只有学生身份的用户才能参加此活动报名。如果您是学生，请联系管理员更新您的身份信息。\",\n            okText: \"我知道了\",\n            cancelText: \"联系管理员\",\n            onCancel: ()=>{\n                notification.info(\"如需帮助，请联系客服：400-123-4567\");\n            }\n        });\n    };\n    // 获取报名状态文本\n    const getRegistrationStatusText = ()=>{\n        switch(registrationStatus){\n            case 0:\n                return \"已取消\";\n            case 1:\n                return \"已报名\";\n            case 2:\n                return \"已审核通过\";\n            case 3:\n                return \"已拒绝\";\n            case 4:\n                return \"评审中\";\n            case 5:\n                return \"已获奖\";\n            case 6:\n                return \"审核中\";\n            default:\n                return \"已报名\";\n        }\n    };\n    // 可编辑的导航标签文本\n    const [navTags, setNavTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"tag1\",\n            text: \"官方活动\"\n        },\n        {\n            id: \"tag2\",\n            text: \"AIGC作品征集\"\n        }\n    ]);\n    // 活动标签ID列表\n    const [tagIds, setTagIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 标签编辑状态\n    const [editingTagId, setEditingTagId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingTagText, setEditingTagText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 登录和报名相关状态\n    const [showLoginDialog, setShowLoginDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSubmitted, setHasSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [registrationStatus, setRegistrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 报名状态：0-已取消 1-已报名 2-已审核通过 3-已拒绝 4-评审中 5-已获奖 6-审核中\n    const [uploadLoading, setUploadLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fileList, setFileList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUI, setShowUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 添加客户端检测来解决水合错误\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    // 页面加载时检查报名状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkRegistrationStatus = async ()=>{\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态 - useEffect 触发\", {\n                isLoggedIn: userState.isLoggedIn,\n                roleId: userState.roleId,\n                phone: userState.phone,\n                isClient,\n                activityId\n            });\n            // 只有在用户已登录且是学生身份时才检查报名状态\n            const isStudent = checkIsStudent();\n            if (!userState.isLoggedIn || !isStudent) {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录或不是学生身份，重置报名状态\", {\n                    isLoggedIn: userState.isLoggedIn,\n                    isStudent\n                });\n                // 如果用户未登录或不是学生，重置状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n                return;\n            }\n            try {\n                var _response_data, _response_data1;\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 开始检查报名状态 API 调用\");\n                setIsChecking(true);\n                const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.checkRegistration(Number(activityId));\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 页面加载检查报名状态响应:\", response);\n                if ((response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200 && ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data)) {\n                    const { submitted, submit } = response.data.data;\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态检查结果\", {\n                        submitted,\n                        submit\n                    });\n                    setHasSubmitted(submitted);\n                    if (submit && submit.status !== undefined) {\n                        setRegistrationStatus(submit.status);\n                    } else {\n                        setRegistrationStatus(null);\n                    }\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态响应不符合预期，设置为未报名状态\");\n                    // 如果响应不符合预期，设置为未报名状态\n                    setHasSubmitted(false);\n                    setRegistrationStatus(null);\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态失败:\", error);\n                // 出错时设置为未报名状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n            } finally{\n                setIsChecking(false);\n            }\n        };\n        // 只在客户端执行，避免服务端渲染问题\n        if (isClient) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 客户端环境，执行报名状态检查\");\n            checkRegistrationStatus();\n        } else {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 非客户端环境，跳过报名状态检查\");\n        }\n    }, [\n        activityId,\n        userState.isLoggedIn,\n        userState.roleId,\n        userState.phone,\n        isClient\n    ]);\n    // 检查用户是否已提交作品\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkUserSubmitted = async ()=>{\n            try {\n                // 仅针对已登录用户进行检查\n                if (userState && userState.isLoggedIn) {\n                    var _response_data;\n                    setIsChecking(true);\n                    const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityApi.checkUserSubmitted(Number(activityId));\n                    console.log(\"页面加载时检查用户提交状态响应:\", response);\n                    if ((response === null || response === void 0 ? void 0 : response.status) === 200 && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data)) {\n                        setHasSubmitted(response.data.data.submitted);\n                    } else {\n                        // 如果响应不符合预期，默认为未提交\n                        setHasSubmitted(false);\n                    }\n                    setIsChecking(false);\n                } else {\n                    // 未登录用户，直接设置为未提交\n                    setHasSubmitted(false);\n                    setIsChecking(false);\n                }\n            } catch (error) {\n                console.error(\"检查用户提交状态失败:\", error);\n                // 出错时默认设置为未提交\n                setHasSubmitted(false);\n                setIsChecking(false);\n            }\n        };\n        // 页面加载时执行检查\n        if (isClient) {\n            checkUserSubmitted();\n        }\n    }, [\n        activityId,\n        userState,\n        isClient\n    ]);\n    // 加载活动标签\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchActivityTags = async ()=>{\n            if (activityId) {\n                try {\n                    const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityTagApi.getActivityTags(Number(activityId));\n                    if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n                        setTagIds(response.data.data || []);\n                    }\n                } catch (error) {\n                    console.error(\"获取活动标签失败:\", error);\n                }\n            }\n        };\n        fetchActivityTags();\n    }, [\n        activityId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 初始化文件列表\n        if (bannerImage) {\n            setFileList([\n                {\n                    uid: \"-1\",\n                    name: \"当前封面\",\n                    status: \"done\",\n                    url: bannerImage\n                }\n            ]);\n        } else {\n            setFileList([]);\n        }\n    }, [\n        bannerImage\n    ]);\n    // 前往任务函数\n    const handleGoToTask = async ()=>{\n        try {\n            const loadingMessage = notification.loading(\"正在跳转到任务...\");\n            // 获取当前用户的赛事任务列表\n            const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n            if (eventsTaskResponse.data.code === 200) {\n                const eventsTasks = eventsTaskResponse.data.data;\n                // 查找与当前活动相关的赛事任务\n                const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                if (relatedTask) {\n                    // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                    window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                    notification.success(\"正在跳转到任务页面...\");\n                } else {\n                    // 如果没有找到对应任务，在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                    notification.info(\"未找到对应任务，已打开班级空间\");\n                }\n            } else {\n                // 如果获取任务失败，在新标签页中打开班级空间\n                window.open(\"/class-space\", \"_blank\");\n                notification.info(\"获取任务失败，已打开班级空间\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"跳转任务失败:\", error);\n            notification.error(\"跳转任务失败，请稍后重试\");\n        }\n    };\n    // 处理报名按钮点击\n    const handleSubmitClick = async ()=>{\n        var _userState_phone, _localUser_phone;\n        // 获取localStorage中的用户信息进行对比\n        const localUserStr = localStorage.getItem(\"user\");\n        const localUser = localUserStr ? JSON.parse(localUserStr) : null;\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 报名按钮点击 - 详细用户状态检查\", {\n            hasSubmitted,\n            \"Redux userState\": {\n                isLoggedIn: userState === null || userState === void 0 ? void 0 : userState.isLoggedIn,\n                phone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                phoneLength: userState === null || userState === void 0 ? void 0 : (_userState_phone = userState.phone) === null || _userState_phone === void 0 ? void 0 : _userState_phone.length,\n                phoneType: typeof (userState === null || userState === void 0 ? void 0 : userState.phone),\n                roleId: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                userId: userState === null || userState === void 0 ? void 0 : userState.userId,\n                nickName: userState === null || userState === void 0 ? void 0 : userState.nickName\n            },\n            \"localStorage user\": {\n                phone: localUser === null || localUser === void 0 ? void 0 : localUser.phone,\n                phoneLength: localUser === null || localUser === void 0 ? void 0 : (_localUser_phone = localUser.phone) === null || _localUser_phone === void 0 ? void 0 : _localUser_phone.length,\n                phoneType: typeof (localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                userId: localUser === null || localUser === void 0 ? void 0 : localUser.userId,\n                nickName: localUser === null || localUser === void 0 ? void 0 : localUser.nickName\n            },\n            \"phone validation\": {\n                \"userState.phone exists\": !!(userState === null || userState === void 0 ? void 0 : userState.phone),\n                \"userState.phone not empty\": (userState === null || userState === void 0 ? void 0 : userState.phone) && userState.phone.trim() !== \"\",\n                \"localUser.phone exists\": !!(localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                \"localUser.phone not empty\": (localUser === null || localUser === void 0 ? void 0 : localUser.phone) && (localUser === null || localUser === void 0 ? void 0 : localUser.phone.trim()) !== \"\"\n            }\n        });\n        // 临时调用API获取最新用户信息进行对比\n        if (userState === null || userState === void 0 ? void 0 : userState.userId) {\n            try {\n                var _response_data, _response_data_phone, _response_data1, _response_data2, _response_data3, _response_data4;\n                const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_8__.userApi.getUserInfo(userState.userId);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取的最新用户信息:\", {\n                    code: response.code,\n                    phone: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.phone,\n                    phoneLength: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_phone = _response_data1.phone) === null || _response_data_phone === void 0 ? void 0 : _response_data_phone.length,\n                    phoneType: typeof ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.phone),\n                    nickName: (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.nickName,\n                    userId: (_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : _response_data4.id\n                });\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 获取用户信息失败:\", error);\n            }\n        }\n        // 如果用户已报名，跳转到任务页面\n        if (hasSubmitted) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已报名，跳转到任务页面\");\n            handleGoToTask();\n            return;\n        }\n        // 检查用户是否登录\n        if (!userState || !userState.isLoggedIn) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录，跳转到登录页面\");\n            // 未登录，显示登录对话框\n            const redirectUrl = pathname || \"/home\";\n            router.push(\"/login?redirect=\".concat(encodeURIComponent(redirectUrl)));\n            return;\n        }\n        // 检查用户身份：只有学生（roleId为1）才能报名\n        const isStudent = checkIsStudent();\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 检查用户身份\", {\n            isStudent,\n            roleId: userState === null || userState === void 0 ? void 0 : userState.roleId\n        });\n        if (!isStudent) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户不是学生身份，显示身份验证弹窗\");\n            showStudentOnlyModal();\n            return;\n        }\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 身份验证通过，开始报名流程\");\n        // 身份验证通过，直接进行报名\n        handleDirectRegistration();\n    };\n    // 处理登录成功\n    const handleLoginSuccess = ()=>{\n        notification.success(\"登录成功\");\n    // 登录成功后，页面的useEffect会自动检查报名状态\n    // 不需要在这里重复检查，让useEffect处理状态更新\n    };\n    // 处理报名成功\n    const handleSubmitSuccess = ()=>{\n        // 更新提交状态\n        setHasSubmitted(true);\n        setRegistrationStatus(1); // 设置为已报名状态\n        notification.success(\"报名成功\");\n        // 通知页面刷新参赛作品列表\n        if (onRefreshWorks) {\n            onRefreshWorks();\n        }\n    };\n    // 直接报名处理函数\n    const handleDirectRegistration = async ()=>{\n        try {\n            var _response_data;\n            // 检查用户是否绑定了手机号\n            if (!userState.phone || userState.phone.trim() === \"\") {\n                // 显示确认对话框\n                _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].confirm({\n                    title: \"需要绑定手机号\",\n                    content: \"报名活动需要绑定手机号，是否前往绑定？\",\n                    okText: \"前往绑定\",\n                    cancelText: \"取消\",\n                    onOk: ()=>{\n                        // 跳转到登录页面，并在URL中添加参数表示需要绑定手机号\n                        const currentUrl = window.location.href;\n                        router.push(\"/login?needBindPhone=true&redirect=\".concat(encodeURIComponent(currentUrl)));\n                    },\n                    onCancel: ()=>{\n                        notification.info(\"未绑定手机号，无法报名活动\");\n                    }\n                });\n                return;\n            }\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已绑定手机号，继续报名流程\");\n            // 显示加载提示\n            const loadingMessage = notification.loading(\"正在提交报名...\");\n            // 提交报名数据（使用默认值，跳过协议确认步骤）\n            const submitData = {\n                activityId: Number(activityId),\n                agreementAccepted: true,\n                parentConsentAccepted: true,\n                parentSignaturePath: \"\",\n                signatureTime: new Date().toISOString(),\n                remark: \"快速报名时间：\".concat(new Date().toLocaleString(\"zh-CN\"))\n            };\n            const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.submitRegistration(submitData);\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                // 更新UI状态\n                setHasSubmitted(true);\n                setRegistrationStatus(1);\n                notification.success(\"报名成功！正在跳转到班级空间...\");\n                // 通知页面刷新参赛作品列表\n                if (onRefreshWorks) {\n                    onRefreshWorks();\n                }\n                // 报名成功后，获取对应的赛事任务并跳转到班级空间\n                try {\n                    // 等待一小段时间确保后端创建赛事任务完成\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    // 获取当前用户的赛事任务列表\n                    const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n                    if (eventsTaskResponse.data.code === 200) {\n                        const eventsTasks = eventsTaskResponse.data.data;\n                        // 查找与当前活动相关的赛事任务\n                        const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                        if (relatedTask) {\n                            // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                            window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                        } else {\n                            // 如果没有找到对应任务，在新标签页中打开班级空间\n                            window.open(\"/class-space\", \"_blank\");\n                        }\n                    } else {\n                        // 如果获取任务失败，在新标签页中打开班级空间\n                        window.open(\"/class-space\", \"_blank\");\n                    }\n                } catch (taskError) {\n                    console.error(\"获取赛事任务失败:\", taskError);\n                    // 即使获取任务失败，也要在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                }\n            } else {\n                var _response_data1;\n                throw new Error(((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message) || \"报名失败\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"报名失败:\", error);\n            notification.error(error instanceof Error ? error.message : \"报名失败，请稍后重试\");\n        }\n    };\n    // 处理分享按钮点击事件\n    const handleShare = ()=>{\n        if (true) {\n            // 获取当前页面URL\n            const currentUrl = window.location.href;\n            // 复制到剪贴板\n            navigator.clipboard.writeText(currentUrl).then(()=>{\n                notification.success(\"活动链接已复制，快去分享吧！\");\n            }).catch((err)=>{\n                console.error(\"复制失败:\", err);\n                notification.error(\"复制链接失败，请手动复制\");\n            });\n        }\n    };\n    // 如果还没有在客户端挂载，返回一个简单的占位符\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-64 bg-gray-200 animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 695,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-300 rounded mb-2 w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded mb-1 w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 700,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 697,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 696,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 694,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n            lineNumber: 693,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-[300px] overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-transparent to-black/30 z-10 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 716,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-blue-500/10 animate-pulse z-0 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: bannerImage,\n                                            alt: title,\n                                            fill: true,\n                                            priority: true,\n                                            style: {\n                                                objectFit: \"cover\"\n                                            },\n                                            className: \"z-10 hover:scale-105 transition-transform duration-700\",\n                                            onError: (e)=>{\n                                                const target = e.target;\n                                                target.style.background = \"linear-gradient(120deg, #f3f4f6, #e5e7eb)\";\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 721,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 718,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-4 left-4 right-4 z-20 flex justify-between items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 739,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 714,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-800 flex items-center\",\n                                        children: [\n                                            title,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"ml-2 w-5 h-5 text-yellow-400 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 811,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 809,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    organizer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: [\n                                            \"主办方: \",\n                                            organizer\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 814,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm mt-2 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-500 to-indigo-500 h-1.5 w-1.5 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 818,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"活动时间：\",\n                                            formatDate(startTime),\n                                            \" - \",\n                                            formatDate(endTime)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 817,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 808,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3 mt-4 md:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSubmitClick,\n                                        disabled: isChecking,\n                                        className: \"flex items-center px-5 py-2.5 text-sm font-medium rounded-full \".concat(hasSubmitted ? \"bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\" : \"bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\", \" transition-all duration-200\"),\n                                        children: isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"loading-spinner mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                    lineNumber: 851,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"检查状态中\"\n                                            ]\n                                        }, void 0, true) : hasSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"已报名 前往任务\"\n                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"立即报名\"\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 841,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex items-center p-2.5 text-gray-500 hover:text-blue-500 hover:bg-gray-50 rounded-full hover:scale-110 active:scale-95 transition-all duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 865,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 861,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 823,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 807,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 710,\n                columnNumber: 7\n            }, undefined),\n            (promotionImage || backgroundImage || galleryImages || attachmentFiles) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    promotionImage: promotionImage,\n                    backgroundImage: backgroundImage,\n                    galleryImages: galleryImages,\n                    attachmentFiles: attachmentFiles,\n                    activityTitle: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                    lineNumber: 874,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 873,\n                columnNumber: 9\n            }, undefined),\n            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_login_dialog__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showLoginDialog,\n                onClose: ()=>setShowLoginDialog(false),\n                onSuccess: handleLoginSuccess\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 886,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ActivityHeader, \"9puqDtaYsDo7+fZ2UWLRshIk+yw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.usePathname,\n        react_redux__WEBPACK_IMPORTED_MODULE_11__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_11__.useDispatch\n    ];\n});\n_c = ActivityHeader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ActivityHeader);\nvar _c;\n$RefreshReg$(_c, \"ActivityHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx\n"));

/***/ })

});