"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/activity/festival/page",{

/***/ "(app-pages-browser)/./app/activity/festival/components/ActivitySubmitModal.tsx":
/*!******************************************************************!*\
  !*** ./app/activity/festival/components/ActivitySubmitModal.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ActivitySubmitModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Carousel,Form,Input,Modal,Popconfirm,Tag!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Carousel,Form,Input,Modal,Popconfirm,Tag!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Carousel,Form,Input,Modal,Popconfirm,Tag!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Carousel,Form,Input,Modal,Popconfirm,Tag!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Carousel,Form,Input,Modal,Popconfirm,Tag!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Carousel,Form,Input,Modal,Popconfirm,Tag!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Carousel,Form,Input,Modal,Popconfirm,Tag!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/carousel/index.js\");\n/* harmony import */ var _barrel_optimize_names_CheckOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckOutlined.js\");\n/* harmony import */ var _lib_api_works__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/works */ \"(app-pages-browser)/./lib/api/works.ts\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./lib/store.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/activity */ \"(app-pages-browser)/./lib/api/activity/index.ts\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n// 引入CarouselArrow组件\nconst CustomArrow = (param)=>{\n    let { type, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"custom-arrow \".concat(type, \"-arrow\"),\n        onClick: onClick,\n        style: {\n            position: \"absolute\",\n            zIndex: 1,\n            top: \"50%\",\n            transform: \"translateY(-50%)\",\n            width: \"32px\",\n            height: \"32px\",\n            backgroundColor: \"rgba(255, 255, 255, 0.8)\",\n            borderRadius: \"12px\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            cursor: \"pointer\",\n            boxShadow: \"0 2px 6px rgba(0, 0, 0, 0.1)\",\n            ...type === \"prev\" ? {\n                left: \"8px\"\n            } : {\n                right: \"8px\"\n            }\n        },\n        children: type === \"prev\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            width: \"16\",\n            height: \"16\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"15 18 9 12 15 6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            width: \"16\",\n            height: \"16\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"9 18 15 12 9 6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n        lineNumber: 15,\n        columnNumber: 3\n    }, undefined);\n};\n_c = CustomArrow;\n// 图片标签组件\nconst ImageLabel = (param)=>{\n    let { text } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"absolute\",\n            bottom: \"8px\",\n            right: \"8px\",\n            backgroundColor: \"rgba(0, 0, 0, 0.7)\",\n            color: \"white\",\n            padding: \"2px 8px\",\n            borderRadius: \"12px\",\n            fontSize: \"0.75rem\"\n        },\n        children: text\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n        lineNumber: 49,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = ImageLabel;\nconst AddDescriptionModal = (param)=>{\n    let { visible, workId, workTitle, onClose, onSaved } = param;\n    _s();\n    const [form] = _barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_5__.GetNotification)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (visible) {\n            form.resetFields();\n        }\n    }, [\n        visible,\n        form\n    ]);\n    const handleSubmit = async ()=>{\n        try {\n            var _response_data;\n            const values = await form.validateFields();\n            setSaving(true);\n            // 调用更新作品的API\n            const response = await _lib_api_works__WEBPACK_IMPORTED_MODULE_2__.worksApi.updateWork(workId, {\n                description: values.description\n            });\n            if ((response === null || response === void 0 ? void 0 : response.status) === 200 || (response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                notification.success(\"作品描述添加成功\");\n                onSaved();\n            } else {\n                var _response_data1;\n                throw new Error((response === null || response === void 0 ? void 0 : (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message) || \"保存失败\");\n            }\n        } catch (error) {\n            console.error(\"保存描述失败:\", error);\n            notification.error(\"保存描述失败，请稍后重试\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-500 rounded-lg p-1.5 shadow-sm flex items-center justify-center w-6 h-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-white text-xs\",\n                        children: \"✏️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 11\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-semibold text-blue-700\",\n                    children: \"添加作品描述\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 11\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n            lineNumber: 116,\n            columnNumber: 9\n        }, void 0),\n        open: visible,\n        onCancel: onClose,\n        centered: true,\n        footer: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                onClick: onClose,\n                className: \"rounded-full px-4 py-1 h-auto hover:bg-gray-50 font-medium\",\n                children: \"取消\"\n            }, \"cancel\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, void 0),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                type: \"primary\",\n                loading: saving,\n                onClick: handleSubmit,\n                className: \"rounded-full px-4 py-1 h-auto bg-blue-500 hover:bg-blue-600 border-none font-medium\",\n                children: \"保存描述\"\n            }, \"save\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, void 0)\n        ],\n        width: 450,\n        className: \"description-modal\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-3 bg-blue-50 p-3 rounded-lg border border-blue-100 shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium mb-1 text-xs text-blue-700\",\n                            children: [\n                                \"作品: \",\n                                workTitle\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-600 leading-relaxed\",\n                            children: \"添加作品描述有助于评委更好地了解您的创作理念和作品亮点\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    form: form,\n                    layout: \"vertical\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                        name: \"description\",\n                        rules: [\n                            {\n                                required: true,\n                                message: \"请输入作品描述\"\n                            }\n                        ],\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].TextArea, {\n                            rows: 3,\n                            placeholder: \"请描述您的作品，例如创作灵感、功能特点等...\",\n                            maxLength: 200,\n                            showCount: true,\n                            className: \"rounded-lg text-sm\",\n                            style: {\n                                resize: \"none\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AddDescriptionModal, \"NwW+VLUiEzJ6MXtcJgbxcG3Df1s=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm\n    ];\n});\n_c2 = AddDescriptionModal;\nfunction ActivitySubmitModal(param) {\n    let { visible, activityId, activityTitle, activityType = _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ActivityType.WORK, onClose, onSubmitted, onRefreshWorks } = param;\n    _s1();\n    const [form] = _barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    const [selectedWorkId, setSelectedWorkId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [works, setWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const userState = (0,react_redux__WEBPACK_IMPORTED_MODULE_10__.useSelector)(_lib_store__WEBPACK_IMPORTED_MODULE_3__.selectUserState);\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_5__.GetNotification)();\n    // 添加内容类型选择状态，根据活动类型设置默认值\n    const [contentType, setContentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(activityType === _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ActivityType.IMAGE ? _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ContentType.IMAGE : _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ContentType.WORK);\n    // 添加描述模态框状态\n    const [descriptionModalVisible, setDescriptionModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWork, setSelectedWork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 用户报名状态\n    const [userSubmission, setUserSubmission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingSubmission, setLoadingSubmission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cancelling, setCancelling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 重置表单状态\n    const resetForm = ()=>{\n        setSelectedWorkId(null);\n        setSelectedWork(null);\n    };\n    // 获取内容列表（根据contentType决定获取作品或图片）\n    const fetchContentList = async ()=>{\n        try {\n            if (contentType === 1) {\n                // 获取作品列表\n                const { data: response } = await _lib_api_works__WEBPACK_IMPORTED_MODULE_2__.worksApi.getList({\n                    type: 1,\n                    page: 1,\n                    size: 100\n                });\n                if (response.code === 200) {\n                    setWorks(response.data.list || []);\n                }\n            } else if (contentType === 2) {\n                // 获取图片列表，添加userId参数只获取当前用户的图片\n                console.log(\"userState\", userState);\n                const userId = userState === null || userState === void 0 ? void 0 : userState.userId;\n                if (!userId) {\n                    notification.error(\"用户未登录\");\n                    return;\n                }\n                const { data: response } = await _lib_api_works__WEBPACK_IMPORTED_MODULE_2__.worksApi.getAllWorks({\n                    page: 1,\n                    size: 100,\n                    userId\n                });\n                if (response.code === 200) {\n                    setWorks(response.data.list || []);\n                }\n            }\n        } catch (error) {\n            console.error(\"获取内容列表失败:\", error);\n            notification.error(\"获取内容列表失败\");\n        }\n    };\n    // 获取用户的报名状态\n    const fetchUserSubmission = async ()=>{\n        if (!activityId || !(userState === null || userState === void 0 ? void 0 : userState.isLoggedIn)) return;\n        try {\n            var _response_data;\n            setLoadingSubmission(true);\n            const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.activityApi.getUserSubmissionStatus(activityId);\n            if ((response === null || response === void 0 ? void 0 : response.status) === 200 && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data)) {\n                // 检查是否已提交\n                const submitted = response.data.data.submitted;\n                if (submitted) {\n                    var _submissionResponse_data;\n                    // 获取用户在此活动的提交详情\n                    const submissionResponse = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.activityApi.getActivityWorks(activityId, {\n                        userId: userState.id,\n                        status: _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.SubmissionStatus.SUBMITTED // 查找已提交状态的记录\n                    });\n                    if ((submissionResponse === null || submissionResponse === void 0 ? void 0 : submissionResponse.status) === 200 && ((_submissionResponse_data = submissionResponse.data) === null || _submissionResponse_data === void 0 ? void 0 : _submissionResponse_data.length) > 0) {\n                        const submission = submissionResponse.data[0];\n                        // 设置内容类型和已选择的作品ID\n                        setContentType(submission.contentType || 1);\n                        setSelectedWorkId(submission.workId);\n                        // 设置用户报名状态\n                        setUserSubmission({\n                            id: submission.id,\n                            status: submission.status,\n                            activityId: activityId,\n                            workId: submission.workId,\n                            contentType: submission.contentType\n                        });\n                        // 根据contentType获取对应类型的内容列表\n                        await fetchContentList();\n                    }\n                } else {\n                    setUserSubmission(null);\n                }\n            } else {\n                setUserSubmission(null);\n            }\n        } catch (error) {\n            console.error(\"获取用户报名状态失败:\", error);\n            setUserSubmission(null);\n        } finally{\n            setLoadingSubmission(false);\n        }\n    };\n    // 当模态框打开时获取作品列表和用户报名状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (visible) {\n            resetForm();\n            fetchUserSubmission();\n        }\n    }, [\n        visible,\n        activityId,\n        userState\n    ]);\n    // 当内容类型变更时，获取对应的内容列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (visible && !userSubmission) {\n            fetchContentList();\n        }\n    }, [\n        contentType,\n        visible,\n        userSubmission\n    ]);\n    // 当活动类型改变时重置内容类型\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activityType === _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ActivityType.WORK) {\n            setContentType(_lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ContentType.WORK);\n        } else if (activityType === _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ActivityType.IMAGE) {\n            setContentType(_lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ContentType.IMAGE);\n        }\n    // OTHER类型活动允许任意内容类型，保持用户选择\n    }, [\n        activityType\n    ]);\n    // 处理内容类型切换\n    const handleContentTypeChange = (e)=>{\n        setContentType(e.target.value);\n        // 切换类型时重置选择\n        setSelectedWorkId(null);\n        setSelectedWork(null);\n    };\n    // 处理作品选择\n    const handleWorkSelect = (work)=>{\n        // 如果用户已报名，不允许选择其他作品\n        if (userSubmission && userSubmission.status !== _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.SubmissionStatus.CANCELLED) {\n            return;\n        }\n        if (selectedWorkId === work.id) {\n            setSelectedWorkId(null);\n            setSelectedWork(null);\n        } else {\n            setSelectedWorkId(work.id);\n            setSelectedWork(work);\n        }\n    };\n    // 描述保存后的回调\n    const handleDescriptionSaved = async ()=>{\n        setDescriptionModalVisible(false);\n        // 重新获取作品列表，更新描述\n        await fetchContentList();\n    };\n    // 处理取消报名\n    const handleCancelSubmission = async ()=>{\n        if (!userSubmission) {\n            notification.error(\"未找到报名记录\");\n            return;\n        }\n        try {\n            setCancelling(true);\n            // 调用取消报名API\n            const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.activityApi.cancelSubmission(userSubmission.id);\n            if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n                notification.success(\"已成功取消报名\");\n                // 重置状态\n                setUserSubmission(null);\n                onSubmitted();\n                // 调用刷新作品列表的回调\n                if (onRefreshWorks) {\n                    onRefreshWorks();\n                }\n                onClose();\n            } else {\n                var _response_data;\n                throw new Error((response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.message) || \"取消失败\");\n            }\n        } catch (error) {\n            console.error(\"取消报名失败:\", error);\n            notification.error(\"取消报名失败，请稍后重试\");\n        } finally{\n            setCancelling(false);\n        }\n    };\n    // 提交报名\n    const handleSubmit = async ()=>{\n        if (!selectedWorkId) {\n            notification.error(\"请选择要报名的作品\");\n            return;\n        }\n        // 只有作品类型才需要检查描述\n        if (contentType === 1) {\n            const selectedWork = works.find((work)=>work.id === selectedWorkId);\n            if (!(selectedWork === null || selectedWork === void 0 ? void 0 : selectedWork.description)) {\n                // 只显示警告，不再阻止提交\n                notification.warning(\"建议为作品添加描述，这有助于评委更好地了解您的作品\");\n            }\n        }\n        try {\n            setSubmitting(true);\n            // 调用API提交活动报名，添加contentType\n            const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.activityApi.submitWork({\n                activityId,\n                workId: selectedWorkId,\n                contentType: contentType\n            });\n            if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n                // notification.success('提交成功');\n                onSubmitted();\n                // 调用刷新作品列表的回调\n                if (onRefreshWorks) {\n                    onRefreshWorks();\n                }\n                onClose();\n            } else {\n                var _response_data;\n                throw new Error(((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.message) || \"提交失败\");\n            }\n        } catch (error) {\n            console.error(\"提交失败:\", error);\n            notification.error(\"提交失败，请稍后重试\");\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    // 渲染模态框标题\n    const renderModalTitle = ()=>{\n        if (userSubmission && userSubmission.status !== _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.SubmissionStatus.CANCELLED) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-3xl\",\n                                children: \"\\uD83D\\uDCCB\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-semibold text-blue-700\",\n                                children: \"我的报名信息\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        color: \"success\",\n                        className: \"rounded-full px-3 text-sm ml-2\",\n                        children: \"已报名\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                lineNumber: 441,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-3xl\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xl font-semibold text-blue-700\",\n                        children: \"活动报名\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                lineNumber: 452,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n            lineNumber: 451,\n            columnNumber: 7\n        }, this);\n    };\n    // 渲染模态框底部按钮\n    const renderModalFooter = ()=>{\n        // 如果正在加载报名状态，显示加载中\n        if (loadingSubmission) {\n            return [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    onClick: onClose,\n                    className: \"rounded-full px-5 py-1.5 h-auto hover:bg-gray-50 font-medium\",\n                    children: \"关闭\"\n                }, \"cancel\", false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this)\n            ];\n        }\n        // 如果用户已报名，显示取消报名按钮\n        if (userSubmission && userSubmission.status !== _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.SubmissionStatus.CANCELLED) {\n            return [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    onClick: onClose,\n                    className: \"rounded-full px-5 py-1.5 h-auto hover:bg-gray-50 font-medium\",\n                    children: \"关闭\"\n                }, \"cancel\", false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                    lineNumber: 474,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    title: \"确认取消报名\",\n                    description: \"您确定要取消活动报名吗？取消后可以重新报名。\",\n                    onConfirm: handleCancelSubmission,\n                    okText: \"确认取消\",\n                    okButtonProps: {\n                        danger: true,\n                        className: \"rounded-full\"\n                    },\n                    cancelText: \"返回\",\n                    cancelButtonProps: {\n                        className: \"rounded-full\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        danger: true,\n                        loading: cancelling,\n                        className: \"rounded-full px-5 py-1.5 h-auto font-medium\",\n                        children: \"取消报名\"\n                    }, \"cancelSubmit\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 11\n                    }, this)\n                }, \"cancelPopconfirm\", false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, this)\n            ];\n        }\n        // 默认显示报名按钮\n        return [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                onClick: onClose,\n                className: \"rounded-full px-5 py-1.5 h-auto hover:bg-gray-50 font-medium\",\n                children: \"取消\"\n            }, \"cancel\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                lineNumber: 501,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                type: \"primary\",\n                loading: submitting,\n                onClick: handleSubmit,\n                className: \"rounded-full px-5 py-1.5 h-auto bg-blue-500 hover:bg-blue-600 border-none font-medium\",\n                disabled: !selectedWorkId,\n                children: \"提交报名\"\n            }, \"submit\", false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                lineNumber: 504,\n                columnNumber: 7\n            }, this)\n        ];\n    };\n    // 渲染内容类型选择区域\n    const renderContentTypeSelector = ()=>{\n        // 如果用户已报名或已取消，不显示选择器\n        if (userSubmission && userSubmission.status !== _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.SubmissionStatus.CANCELLED) {\n            return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                title: renderModalTitle(),\n                open: visible,\n                onCancel: onClose,\n                centered: true,\n                footer: renderModalFooter(),\n                width: 900,\n                className: \"activity-submit-modal\",\n                modalRender: (modal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            margin: \"2vh 0\",\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            height: \"fit-content\",\n                            maxHeight: \"96vh\"\n                        },\n                        children: modal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 11\n                    }, void 0),\n                style: {\n                    maxWidth: \"90vw\",\n                    top: 0,\n                    paddingBottom: 0\n                },\n                bodyStyle: {\n                    padding: \"20px\",\n                    maxHeight: \"calc(96vh - 110px)\",\n                    overflow: \"hidden\",\n                    borderRadius: \"16px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-[30%] flex flex-col\",\n                            style: {\n                                maxHeight: \"calc(96vh - 180px)\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-blue-50 rounded-xl border border-blue-100 p-4 overflow-y-auto relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-500 rounded-xl p-1.5 shadow-sm flex items-center justify-center w-6 h-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3.5 h-3.5 text-white\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12 16V12M12 8H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-blue-700 font-semibold text-sm\",\n                                                children: \"活动报名指南\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2.5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl p-2.5 shadow-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs shrink-0 mt-0.5 shadow-sm\",\n                                                            children: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs font-medium text-blue-700 mb-1\",\n                                                                    children: \"活动要求\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 leading-relaxed\",\n                                                                    children: [\n                                                                        activityType === _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ActivityType.WORK && \"本活动仅支持提交作品，请从您的作品中选择一个参赛\",\n                                                                        activityType === _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ActivityType.IMAGE && \"本活动仅支持提交图片，请从您的图片中选择一张参赛\",\n                                                                        activityType === _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ActivityType.OTHER && \"请选择要参赛的内容类型及作品，每人限选一个\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl p-2.5 shadow-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs shrink-0 mt-0.5 shadow-sm\",\n                                                            children: \"2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs font-medium text-blue-700 mb-1\",\n                                                                    children: \"参赛规则\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                    lineNumber: 595,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                className: \"flex items-start\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-blue-500 mr-1.5 font-bold\",\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                        lineNumber: 599,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"每个用户仅限提交一个作品\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                        lineNumber: 600,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                lineNumber: 598,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                className: \"flex items-start\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-blue-500 mr-1.5 font-bold\",\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                        lineNumber: 603,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"提交后可在个人中心查看报名状态\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                        lineNumber: 604,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                lineNumber: 602,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                className: \"flex items-start\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-blue-500 mr-1.5 font-bold\",\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                        lineNumber: 607,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"获奖结果将通过站内消息通知\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                        lineNumber: 608,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                lineNumber: 606,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                        lineNumber: 597,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                    lineNumber: 596,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                lineNumber: 591,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-amber-50 rounded-xl p-2.5 shadow-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-amber-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs shrink-0 mt-0.5 shadow-sm\",\n                                                            children: \"3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs font-medium text-amber-700 mb-1\",\n                                                                    children: \"作品提示\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                    lineNumber: 621,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-amber-800\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                className: \"flex items-start\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-amber-500 mr-1.5 font-bold\",\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                        lineNumber: 625,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"参赛期间请保持作品公开状态\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                        lineNumber: 626,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                lineNumber: 624,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                className: \"flex items-start\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-amber-500 mr-1.5 font-bold\",\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                        lineNumber: 629,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"添加详细的作品描述有助于评委了解\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                        lineNumber: 630,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                lineNumber: 628,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                className: \"flex items-start\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-amber-500 mr-1.5 font-bold\",\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                        lineNumber: 633,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"获得更多社区互动提高获奖机会\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                        lineNumber: 634,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                                lineNumber: 632,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                        lineNumber: 623,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                    lineNumber: 622,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 17\n                                            }, this),\n                                            userSubmission && userSubmission.status !== _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.SubmissionStatus.CANCELLED && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 rounded-xl p-2.5 shadow-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs shrink-0 mt-0.5 shadow-sm\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs font-medium text-green-700 mb-1\",\n                                                                    children: \"报名状态\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                    lineNumber: 650,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-green-600 leading-relaxed\",\n                                                                    children: \"您已成功报名此活动，可随时取消报名并重新选择\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                    lineNumber: 651,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-[70%] flex flex-col\",\n                            style: {\n                                maxHeight: \"calc(96vh - 180px)\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-indigo-50 rounded-xl border border-indigo-100 p-4 overflow-y-auto relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-indigo-500 rounded-xl p-1.5 shadow-sm flex items-center justify-center w-6 h-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xs\",\n                                                    children: \"\\uD83C\\uDFAF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-indigo-700 font-semibold text-sm\",\n                                                children: userSubmission && userSubmission.status !== _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.SubmissionStatus.CANCELLED ? \"您的参赛内容\" : \"选择参赛作品\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 15\n                                    }, this),\n                                    renderContentTypeSelector(),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 pr-1\",\n                                        style: {\n                                            maxHeight: \"calc(96vh - 250px)\"\n                                        },\n                                        children: works.length > 0 ? works.map((work)=>{\n                                            const isSelected = selectedWorkId === work.id;\n                                            const hasNoDescription = contentType === _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ContentType.WORK && !work.description;\n                                            // 如果已报名，只显示已报名的作品\n                                            if (userSubmission && userSubmission.status !== _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.SubmissionStatus.CANCELLED && work.id !== userSubmission.workId) {\n                                                return null;\n                                            }\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-xl transition-all duration-300 shadow-sm bg-white\\n                          \".concat(isSelected ? \"border-indigo-400 bg-indigo-50/80 shadow-md\" : \"border border-gray-200 hover:border-indigo-300 hover:shadow\", \"\\n                          \").concat(userSubmission && userSubmission.status !== _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.SubmissionStatus.CANCELLED ? \"\" : \"cursor-pointer\"),\n                                                onClick: ()=>handleWorkSelect(work),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-4 rounded-full border transition-colors flex items-center justify-center\\n                              \".concat(isSelected ? \"border-indigo-500 bg-indigo-500\" : \"border-gray-300\"),\n                                                                        children: isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"text-[8px] text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                            lineNumber: 708,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                        lineNumber: 704,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium \".concat(isSelected ? \"text-indigo-600\" : \"text-gray-600\"),\n                                                                        children: userSubmission && userSubmission.status !== _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.SubmissionStatus.CANCELLED ? \"已报名内容\" : isSelected ? \"已选择\" : \"点击选择\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                        lineNumber: 711,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                lineNumber: 703,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-1.5\",\n                                                                children: [\n                                                                    hasNoDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        color: \"blue\",\n                                                                        className: \"rounded-full text-xs px-2 py-0\",\n                                                                        children: \"建议添加描述\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                        lineNumber: 720,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    contentType === _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ContentType.IMAGE && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        color: \"purple\",\n                                                                        className: \"rounded-full text-xs px-2 py-0\",\n                                                                        children: \"图片\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                        lineNumber: 726,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    userSubmission && userSubmission.status !== _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.SubmissionStatus.CANCELLED && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        color: \"success\",\n                                                                        className: \"rounded-full text-xs px-2 py-0\",\n                                                                        children: \"参赛中\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                        lineNumber: 732,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                lineNumber: 718,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                        lineNumber: 702,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative mb-2 h-[160px] bg-gray-50 rounded-xl overflow-hidden border border-gray-100\",\n                                                        children: contentType === _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ContentType.WORK ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            dots: {\n                                                                className: \"custom-dots !z-[1]\"\n                                                            },\n                                                            arrows: true,\n                                                            prevArrow: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomArrow, {\n                                                                type: \"prev\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                lineNumber: 744,\n                                                                columnNumber: 42\n                                                            }, void 0),\n                                                            nextArrow: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomArrow, {\n                                                                type: \"next\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                lineNumber: 745,\n                                                                columnNumber: 42\n                                                            }, void 0),\n                                                            children: [\n                                                                work.coverImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-[160px] relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: work.coverImage,\n                                                                            alt: \"\".concat(work.title, \" 封面\"),\n                                                                            className: \"w-full h-full object-contain rounded-xl\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                            lineNumber: 749,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageLabel, {\n                                                                            text: \"作品封面\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                            lineNumber: 754,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                work.screenShotImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-[160px] relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: work.screenShotImage,\n                                                                            alt: \"\".concat(work.title, \" 截图\"),\n                                                                            className: \"w-full h-full object-contain rounded-xl\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                            lineNumber: 759,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageLabel, {\n                                                                            text: \"作品截图\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                            lineNumber: 764,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-[160px] relative\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: work.backupImagePath,\n                                                                alt: work.name || \"图片\",\n                                                                className: \"w-full h-full object-contain rounded-xl\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                lineNumber: 770,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1.5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium \".concat(isSelected ? \"text-indigo-700\" : \"text-gray-700\"),\n                                                                children: contentType === _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ContentType.WORK ? work.title : work.name || \"未命名图片\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                lineNumber: 780,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            contentType === _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ContentType.WORK && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1.5\",\n                                                                children: [\n                                                                    !work.description && isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        type: \"primary\",\n                                                                        size: \"small\",\n                                                                        className: \"rounded-full px-2 py-0 h-auto text-xs bg-indigo-500 hover:bg-indigo-600 border-none\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            setSelectedWork(work);\n                                                                            setDescriptionModalVisible(true);\n                                                                        },\n                                                                        children: \"添加描述\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                        lineNumber: 786,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        color: work.status === 0 ? \"default\" : \"success\",\n                                                                        className: \"rounded-full text-xs px-2 py-0\",\n                                                                        children: work.status === 0 ? \"未发布\" : \"已发布\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                        lineNumber: 799,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                lineNumber: 784,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    contentType === _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ContentType.WORK ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-1.5 rounded-xl border border-gray-100 max-h-[40px] overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 line-clamp-2\",\n                                                            children: work.description || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-orange-500\",\n                                                                children: \"暂无描述（建议添加描述以便评委更好了解）\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                                lineNumber: 810,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                            lineNumber: 808,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                        lineNumber: 807,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-1.5 rounded-xl border border-gray-100 max-h-[40px] overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 line-clamp-2\",\n                                                            children: work.prompt || \"图片内容\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                            lineNumber: 816,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                        lineNumber: 815,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1.5\",\n                                                        children: [\n                                                            \"创建时间：\",\n                                                            new Date(work.createTime).toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, work.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 23\n                                            }, this);\n                                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center justify-center h-[calc(96vh-350px)] text-center bg-white rounded-xl border border-indigo-100 p-8 shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl mb-4\",\n                                                    children: contentType === _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ContentType.WORK ? \"\\uD83C\\uDFA8\" : \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                    lineNumber: 830,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-indigo-700 mb-2 font-medium text-base\",\n                                                    children: contentType === _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ContentType.WORK ? \"您还没有创建任何作品\" : \"您还没有创作任何图片\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-500 mb-6 text-sm max-w-md\",\n                                                    children: contentType === _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ContentType.WORK ? \"请先创建作品后再参加活动，去创建一个精彩的作品吧！\" : \"请先在创作页面生成图片后再参加活动，去创建一幅精美的图片吧！\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                    lineNumber: 834,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    type: \"primary\",\n                                                    className: \"rounded-full px-5 bg-indigo-500 border-none h-9 shadow-md\",\n                                                    onClick: ()=>{\n                                                        window.location.href = \"/scratch\";\n                                                    },\n                                                    children: \"立即创作\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                                    lineNumber: 839,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                            lineNumber: 829,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                                lineNumber: 664,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                            lineNumber: 663,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                lineNumber: 528,\n                columnNumber: 7\n            }, this),\n            selectedWork && contentType === _lib_api_activity__WEBPACK_IMPORTED_MODULE_4__.ContentType.WORK && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddDescriptionModal, {\n                visible: descriptionModalVisible,\n                workId: selectedWork.id,\n                workTitle: selectedWork.title,\n                onClose: ()=>setDescriptionModalVisible(false),\n                onSaved: handleDescriptionSaved\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivitySubmitModal.tsx\",\n                lineNumber: 858,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(ActivitySubmitModal, \"EvCsmS0JICfnFm68h7c5qQcg9Ms=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Carousel_Form_Input_Modal_Popconfirm_Tag_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm,\n        react_redux__WEBPACK_IMPORTED_MODULE_10__.useSelector\n    ];\n});\n_c3 = ActivitySubmitModal;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"CustomArrow\");\n$RefreshReg$(_c1, \"ImageLabel\");\n$RefreshReg$(_c2, \"AddDescriptionModal\");\n$RefreshReg$(_c3, \"ActivitySubmitModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/activity/festival/components/ActivitySubmitModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/userAuth/teacherAuthModal.tsx":
/*!******************************************************!*\
  !*** ./app/components/userAuth/teacherAuthModal.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/theme/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/auto-complete/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Form,Input,Modal,Select,Spin,Typography,Upload,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,FileExcelOutlined,FileImageOutlined,FileOutlined,FilePdfOutlined,FileWordOutlined,LoadingOutlined,PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/FileWordOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,FileExcelOutlined,FileImageOutlined,FileOutlined,FilePdfOutlined,FileWordOutlined,LoadingOutlined,PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/FileExcelOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,FileExcelOutlined,FileImageOutlined,FileOutlined,FilePdfOutlined,FileWordOutlined,LoadingOutlined,PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/FilePdfOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,FileExcelOutlined,FileImageOutlined,FileOutlined,FilePdfOutlined,FileWordOutlined,LoadingOutlined,PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/FileImageOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,FileExcelOutlined,FileImageOutlined,FileOutlined,FilePdfOutlined,FileWordOutlined,LoadingOutlined,PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/FileOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,FileExcelOutlined,FileImageOutlined,FileOutlined,FilePdfOutlined,FileWordOutlined,LoadingOutlined,PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,FileExcelOutlined,FileImageOutlined,FileOutlined,FilePdfOutlined,FileWordOutlined,LoadingOutlined,PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,FileExcelOutlined,FileImageOutlined,FileOutlined,FilePdfOutlined,FileWordOutlined,LoadingOutlined,PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _public_pca_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../public/pca.json */ \"(app-pages-browser)/./public/pca.json\");\n/* harmony import */ var _lib_api_school__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/school */ \"(app-pages-browser)/./lib/api/school.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var _lib_api_teacher_auth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/teacher-auth */ \"(app-pages-browser)/./lib/api/teacher-auth.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// 定义滚动条样式\nconst scrollbarStyles = \"\\n  .file-scroll-container::-webkit-scrollbar {\\n    height: 8px;\\n    background-color: #f1f1f1;\\n  }\\n  \\n  .file-scroll-container::-webkit-scrollbar-thumb {\\n    background-color: #888;\\n    border-radius: 4px;\\n  }\\n  \\n  .file-scroll-container::-webkit-scrollbar-thumb:hover {\\n    background-color: #555;\\n  }\\n  \\n  .file-scroll-container::-webkit-scrollbar-track {\\n    background-color: #f1f1f1;\\n    border-radius: 4px;\\n  }\\n  \\n  /* 添加模态框自适应样式 */\\n  @media screen and (max-width: 768px) {\\n    .teacher-auth-modal {\\n      width: 90% !important;\\n      margin: 0 auto;\\n    }\\n  }\\n  \\n  /* 模态框内容滚动样式 */\\n  .teacher-auth-modal-body {\\n    max-height: calc(80vh - 72px); /* 预留底部按钮的空间 */\\n    overflow-y: auto;\\n    padding-right: 8px;\\n  }\\n  \\n  .teacher-auth-modal-body::-webkit-scrollbar {\\n    width: 6px;\\n    background-color: #f1f1f1;\\n  }\\n  \\n  .teacher-auth-modal-body::-webkit-scrollbar-thumb {\\n    background-color: #888;\\n    border-radius: 3px;\\n  }\\n  \\n  .teacher-auth-modal-body::-webkit-scrollbar-thumb:hover {\\n    background-color: #555;\\n  }\\n  \\n  /* 左右布局容器 */\\n  .auth-flex-container {\\n    display: flex;\\n    flex-direction: row;\\n    gap: 16px;\\n    width: 100%;\\n    flex-wrap: wrap;\\n    margin-bottom: 24px; /* 增加底部边距，确保不会与提交按钮重叠 */\\n  }\\n  \\n  /* 左侧和右侧列 */\\n  .auth-column-left, .auth-column-right {\\n    flex: 1 1 calc(50% - 8px);\\n    min-width: 0;\\n    display: flex;\\n    flex-direction: column;\\n  }\\n  \\n  /* 底部横跨列 */\\n  .auth-column-bottom {\\n    flex: 1 1 100%;\\n    margin-top: 16px;\\n  }\\n  \\n  /* 响应式布局 */\\n  @media screen and (max-width: 768px) {\\n    .auth-flex-container {\\n      flex-direction: column;\\n      gap: 16px;\\n    }\\n   \\n    .auth-column-left, .auth-column-right {\\n      flex: 1 1 100%;\\n    }\\n  }\\n  \\n  /* 固定底部按钮样式 */\\n  .teacher-auth-modal-footer {\\n    position: absolute;\\n    bottom: 0;\\n    left: 0;\\n    width: 100%;\\n    padding: 16px 0;\\n    background-color: white;\\n    text-align: center;\\n    border-top: 1px solid #f0f0f0;\\n    border-radius: 0 0 16px 16px;\\n    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\\n  }\\n\";\n// 组件的dom,箭头函数的形参传入成功执行后的回调函数\nconst TeacherAuthModal = (param)=>{\n    let { onSuccess, visible, handleCloseTeacherAuthModal } = param;\n    _s();\n    // 逻辑层\n    // 获取主题变量\n    const { token } = _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useToken();\n    // 添加窗口大小状态\n    const [windowSize, setWindowSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width:  true ? window.innerWidth : 0,\n        height:  true ? window.innerHeight : 0\n    });\n    // 监听窗口大小变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        function handleResize() {\n            setWindowSize({\n                width: window.innerWidth,\n                height: window.innerHeight\n            });\n        }\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    // 根据窗口大小计算模态窗宽度\n    const getModalWidth = ()=>{\n        if (windowSize.width <= 576) return \"95%\";\n        if (windowSize.width <= 768) return 460;\n        return 780; // 增加宽度以适应左右布局\n    };\n    // 获取提示框\n    const nt = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 文件列表\n    const [fileList, setFileList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 本地文件对象列表，用于存储实际文件对象\n    const [localFiles, setLocalFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // 从redux中获取userId\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_8__.useSelector)((state)=>state.user.userState);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [schoolName, setSchoolName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredSchools, setFilteredSchools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [province, setProvince] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [city, setCity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [district, setDistrict] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [cities, setCities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [districts, setDistricts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 学校名词搜索框\n    const [schoolSearchText, setSchoolSearchText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 学校下拉框的展示\n    const [showSchoolDropdown, setShowSchoolDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 表单\n    const [form] = _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm();\n    // 提交状态\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 上传状态 - 新增\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 上传提示文字 - 新增\n    const [uploadingText, setUploadingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 钩子\n    // 省市区发生变动的时候更新下拉框的值\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (province) {\n            const selectedProvince = _public_pca_json__WEBPACK_IMPORTED_MODULE_3__.find((p)=>p.name === province);\n            if (selectedProvince && selectedProvince.children) {\n                if (isMunicipality(province)) {\n                    // 直辖市的情况下，城市列表为空，直接设置区县列表\n                    setCities([]);\n                    setDistricts(selectedProvince.children[0].children || []);\n                    if (!city) {\n                        setCity(province); // 直接将省份名称设为城市名称\n                    }\n                } else {\n                    // 普通省份的处理\n                    setCities(selectedProvince.children);\n                    if (city) {\n                        const selectedCity = selectedProvince.children.find((c)=>c.name === city);\n                        if (selectedCity && selectedCity.children) {\n                            setDistricts(selectedCity.children);\n                        }\n                    }\n                }\n            }\n        } else {\n            setCities([]);\n            setDistricts([]);\n            setCity(\"\");\n            setDistrict(\"\");\n        }\n    }, [\n        province,\n        city,\n        _public_pca_json__WEBPACK_IMPORTED_MODULE_3__\n    ]);\n    // 判断是否是直辖市\n    const isMunicipality = (provinceName)=>{\n        return [\n            \"北京市\",\n            \"上海市\",\n            \"天津市\",\n            \"重庆市\"\n        ].includes(provinceName);\n    };\n    const handleSubmit = async ()=>{\n        try {\n            // 表单验证\n            const values = await form.validateFields();\n            if (!schoolName) {\n                setError(\"请输入或选择学校\");\n                nt.error(\"请输入或选择学校\");\n                return;\n            }\n            if (!values.nickName) {\n                setError(\"请输入您的姓名\");\n                nt.error(\"请输入您的姓名\");\n                return;\n            }\n            // 设置提交状态\n            setSubmitting(true);\n            setUploading(true);\n            setUploadingText(\"提交认证申请中，请稍候...\");\n            // 拼接学校信息\n            const schoolInfo = \"\".concat(schoolName, \"|\").concat(province, \"|\").concat(city, \"|\").concat(district);\n            try {\n                // 上传所有本地文件到OSS\n                const attachments = [];\n                for(const uid in localFiles){\n                    if (fileList.some((file)=>file.uid === uid)) {\n                        try {\n                            const file = localFiles[uid];\n                            // 上传文件到OSS\n                            console.log(\"上传ing\");\n                            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_5__.uploadApi.uploadToOss(file);\n                            // 添加到附件列表\n                            attachments.push({\n                                url: url,\n                                name: file.name,\n                                type: file.type || \"application/octet-stream\",\n                                size: file.size || 0\n                            });\n                        } catch (error) {\n                            console.error(\"上传文件失败:\", error);\n                            nt.error(\"文件 \".concat(localFiles[uid].name, \" 上传失败\"));\n                            setSubmitting(false);\n                            setUploading(false);\n                            return;\n                        }\n                    }\n                }\n                // 准备提交数据\n                const submitData = {\n                    teacherId: user.userId,\n                    teacherName: values.nickName,\n                    schoolInfo: schoolInfo,\n                    attachments: attachments\n                };\n                console.log(\"提交教师认证信息:\", submitData);\n                // 调用提交接口\n                const response = await _lib_api_teacher_auth__WEBPACK_IMPORTED_MODULE_6__[\"default\"].submitAuth(submitData);\n                console.log(\"API响应:\", response);\n                // 检查响应状态\n                if (response.data && response.data.code === 200) {\n                    nt.success(\"认证申请提交成功，请等待审核\");\n                    // 关闭模态框并执行成功回调\n                    handleCloseTeacherAuthModal();\n                    onSuccess();\n                } else {\n                    var _response_data;\n                    // 处理业务错误响应 (HTTP 200但业务错误)\n                    const errorMessage = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.message) || \"认证申请提交失败\";\n                    nt.error(errorMessage);\n                    console.log(\"业务错误:\", response.data);\n                }\n            } catch (apiError) {\n                // 处理HTTP异常，这里可以捕获到后端抛出的HttpException\n                console.error(\"API错误:\", apiError);\n                // 在控制台打印出完整的错误对象，方便调试\n                console.log(\"完整错误对象:\", JSON.stringify(apiError, null, 2));\n                if (apiError.response) {\n                    // 处理后端返回的结构化错误\n                    const errorData = apiError.response.data;\n                    console.log(\"后端错误数据:\", errorData);\n                    // 直接显示完整的错误消息，确保用户能看到\n                    if (typeof errorData === \"object\" && errorData !== null) {\n                        console.log(\"错误消息是对象\");\n                        nt.error(errorData.message || \"提交失败，请稍后重试\");\n                    } else if (typeof errorData === \"string\") {\n                        console.log(\"错误消息是字符串\");\n                        nt.error(errorData || \"提交失败，请稍后重试\");\n                    } else {\n                        console.log(\"错误消息格式未知\");\n                        nt.error(\"请求失败 (\".concat(apiError.response.status, \"): 请稍后重试\"));\n                    }\n                } else if (apiError.request) {\n                    // 请求已发送但没有收到响应\n                    nt.error(\"服务器无响应，请检查网络连接\");\n                } else {\n                    // 请求配置错误\n                    nt.error(apiError.message || \"网络错误，请检查网络连接\");\n                }\n            }\n        } catch (error) {\n            console.error(\"表单验证或其他错误:\", error);\n            nt.error(error.message || \"提交失败，请检查表单信息\");\n        } finally{\n            setSubmitting(false);\n            setUploading(false);\n        }\n    };\n    // 认证信息包括的提示信息For列表\n    const handleCancel = ()=>{\n        // 上传中不允许关闭\n        if (uploading) return;\n        handleCloseTeacherAuthModal();\n    };\n    const handleSchoolSelect = (value, option)=>{\n        // 1. 尝试从option.data获取学校数据\n        // 2. 如果option本身是学校对象，则使用option\n        // 3. 如果以上都失败，则通过id在filteredSchools中查找学校\n        // 4. 如果找不到学校，则使用手动输入的值创建一个新学校对象\n        let selectedSchool = option === null || option === void 0 ? void 0 : option.data;\n        console.log(\"zww:\", selectedSchool);\n        if (!selectedSchool && (option === null || option === void 0 ? void 0 : option.label) && typeof option.value !== \"undefined\") {\n            selectedSchool = {\n                id: option.value,\n                schoolName: option.label\n            };\n        }\n        if (!selectedSchool && typeof value !== \"undefined\") {\n            if (typeof value === \"string\") {\n                // 用户输入的自定义学校，创建一个新的学校对象\n                const currentProvince = province || \"\";\n                const currentCity = city || \"\";\n                const currentDistrict = district || \"\";\n                selectedSchool = {\n                    id: \"custom-\".concat(Date.now()),\n                    schoolName: value,\n                    province: currentProvince,\n                    city: currentCity,\n                    district: currentDistrict\n                };\n            } else {\n                // 通过id在已过滤的学校列表中查找\n                selectedSchool = filteredSchools.find((school)=>school.id === value);\n            }\n        }\n        if (!selectedSchool) {\n            console.error(\"无法找到所选学校的数据:\", {\n                value,\n                option\n            });\n            return;\n        }\n        // 从filteredSchools中找到完整的学校信息（包括省市区）\n        const completeSchoolInfo = filteredSchools.find((school)=>school.id === selectedSchool.id || school.schoolName === selectedSchool.schoolName) || selectedSchool;\n        console.log(\"选中学校数据:\", completeSchoolInfo);\n        setSchoolName(completeSchoolInfo.schoolName);\n        // 处理省市区信息，确保没有null字符串\n        const selectedProvince = completeSchoolInfo.province && completeSchoolInfo.province !== \"null\" ? completeSchoolInfo.province : \"\";\n        if (selectedProvince) {\n            setProvince(selectedProvince);\n            // 设置城市（如果是直辖市，则城市名与省份名相同）\n            const selectedCity = isMunicipality(selectedProvince) ? selectedProvince : completeSchoolInfo.city && completeSchoolInfo.city !== \"null\" ? completeSchoolInfo.city : \"\";\n            setCity(selectedCity);\n            // 设置区县\n            const selectedDistrict = completeSchoolInfo.district && completeSchoolInfo.district !== \"null\" ? completeSchoolInfo.district : \"\";\n            setDistrict(selectedDistrict);\n        }\n        // 隐藏学校下拉列表\n        setShowSchoolDropdown(false);\n    };\n    const fetchSchools = async (searchText)=>{\n        // 如果有搜索文本，则不需要考虑地区筛选条件\n        // 如果没有搜索文本，且没有完整的地区信息，则不搜索\n        if (!searchText && (!province || !isMunicipality(province) && !city || !district)) {\n            setFilteredSchools([]);\n            return;\n        }\n        try {\n            var _response_data;\n            const params = {};\n            if (searchText) {\n                params.keyword = searchText;\n            // 有搜索文本时可以不限制地区\n            } else {\n                // 无搜索文本时，必须有地区筛选\n                if (province) {\n                    params.province = province;\n                    if (!isMunicipality(province) && city) {\n                        params.city = city;\n                    }\n                    if (district) {\n                        params.district = district;\n                    }\n                }\n            }\n            console.log(\"搜索学校参数:\", params);\n            const response = await _lib_api_school__WEBPACK_IMPORTED_MODULE_4__.schoolApi.getList(params);\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                var _response_data1, _response_data2;\n                // 获取学校列表\n                let schools = ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data) || ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.list) || response.data || [];\n                // 处理可能的null字符串\n                schools = schools.map((school)=>({\n                        ...school,\n                        province: school.province && school.province !== \"null\" ? school.province : \"\",\n                        city: school.city && school.city !== \"null\" ? school.city : \"\",\n                        district: school.district && school.district !== \"null\" ? school.district : \"\"\n                    }));\n                console.log(\"获取到学校列表:\", schools);\n                setFilteredSchools(schools);\n            } else {\n                console.warn(\"搜索学校接口返回错误:\", response);\n                setFilteredSchools([]);\n            }\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            setFilteredSchools([]);\n        }\n    };\n    // 获取文件图标\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes(\"word\") || fileType.includes(\"docx\") || fileType.includes(\"doc\")) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                style: {\n                    fontSize: \"28px\",\n                    color: \"#2B5797\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                lineNumber: 509,\n                columnNumber: 14\n            }, undefined);\n        } else if (fileType.includes(\"excel\") || fileType.includes(\"xlsx\") || fileType.includes(\"xls\") || fileType.includes(\"csv\")) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                style: {\n                    fontSize: \"28px\",\n                    color: \"#1D6F42\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                lineNumber: 511,\n                columnNumber: 14\n            }, undefined);\n        } else if (fileType.includes(\"pdf\")) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                style: {\n                    fontSize: \"28px\",\n                    color: \"#FF0000\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                lineNumber: 513,\n                columnNumber: 14\n            }, undefined);\n        } else if (fileType.includes(\"image/\")) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                style: {\n                    fontSize: \"28px\",\n                    color: \"#FFB400\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                lineNumber: 515,\n                columnNumber: 14\n            }, undefined);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                style: {\n                    fontSize: \"28px\",\n                    color: \"#8C8C8C\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                lineNumber: 517,\n                columnNumber: 14\n            }, undefined);\n        }\n    };\n    // 获取文件扩展名\n    const getFileExtension = (fileName)=>{\n        const parts = fileName.split(\".\");\n        if (parts.length > 1) {\n            return parts[parts.length - 1].toUpperCase();\n        }\n        return \"\";\n    };\n    // 自定义文件项渲染\n    const customFileItemRender = (originNode, file, fileList)=>{\n        var _file_type;\n        const isImage = (_file_type = file.type) === null || _file_type === void 0 ? void 0 : _file_type.startsWith(\"image/\");\n        // 如果是图片，使用默认渲染\n        if (isImage && file.thumbUrl) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"custom-upload-item\",\n                style: {\n                    position: \"relative\"\n                },\n                children: [\n                    originNode,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"file-delete-icon\",\n                        style: {\n                            position: \"absolute\",\n                            top: \"0\",\n                            right: \"0\",\n                            background: \"rgba(0,0,0,0.65)\",\n                            width: \"22px\",\n                            height: \"22px\",\n                            borderRadius: \"0 0 0 8px\",\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            cursor: \"pointer\"\n                        },\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            // 直接调用上传组件的onRemove方法\n                            handleRemoveFile(file);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            style: {\n                                color: \"#fff\",\n                                fontSize: \"14px\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                            lineNumber: 559,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                lineNumber: 537,\n                columnNumber: 9\n            }, undefined);\n        }\n        // 非图片类型，自定义渲染\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"custom-file-card\",\n            style: {\n                width: \"104px\",\n                height: \"104px\",\n                border: \"1px solid \".concat(token.colorBorderSecondary),\n                borderRadius: \"12px\",\n                padding: \"8px\",\n                display: \"inline-flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                position: \"relative\",\n                background: token.colorFillQuaternary,\n                boxShadow: \"0 2px 8px \".concat(token.colorBgElevated),\n                transition: \"all 0.3s\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"file-delete-icon\",\n                    style: {\n                        position: \"absolute\",\n                        top: \"0\",\n                        right: \"0\",\n                        background: \"rgba(0,0,0,0.65)\",\n                        width: \"22px\",\n                        height: \"22px\",\n                        borderRadius: \"0 0 0 12px\",\n                        display: \"flex\",\n                        justifyContent: \"center\",\n                        alignItems: \"center\",\n                        cursor: \"pointer\",\n                        transition: \"background 0.2s\"\n                    },\n                    onClick: (e)=>{\n                        e.stopPropagation();\n                        handleRemoveFile(file);\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        style: {\n                            color: \"#fff\",\n                            fontSize: \"14px\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                        lineNumber: 603,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                    lineNumber: 583,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        marginTop: \"10px\"\n                    },\n                    children: getFileIcon(file.type || \"\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                    lineNumber: 606,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: \"center\",\n                        width: \"100%\",\n                        marginTop: \"4px\",\n                        overflow: \"hidden\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Text, {\n                            ellipsis: {\n                                tooltip: file.name\n                            },\n                            style: {\n                                fontSize: \"12px\",\n                                lineHeight: \"1.2\"\n                            },\n                            children: file.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"11px\",\n                                color: token.colorTextSecondary,\n                                marginTop: \"2px\",\n                                background: token.colorFillSecondary,\n                                padding: \"0 4px\",\n                                borderRadius: \"4px\",\n                                display: \"inline-block\"\n                            },\n                            children: getFileExtension(file.name)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                    lineNumber: 610,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n            lineNumber: 567,\n            columnNumber: 7\n        }, undefined);\n    };\n    // 处理文件删除\n    const handleRemoveFile = (file)=>{\n        // 从文件列表中移除\n        setFileList(fileList.filter((item)=>item.uid !== file.uid));\n        // 从本地文件映射中移除\n        setLocalFiles((prev)=>{\n            const newLocalFiles = {\n                ...prev\n            };\n            delete newLocalFiles[file.uid];\n            return newLocalFiles;\n        });\n        // 如果有临时URL，需要释放\n        if (file.thumbUrl && file.thumbUrl.startsWith(\"blob:\")) {\n            URL.revokeObjectURL(file.thumbUrl);\n        }\n    };\n    // 样式对象\n    const modalStyles = {\n        header: {\n            marginBottom: \"24px\",\n            textAlign: \"center\",\n            fontSize: windowSize.width <= 576 ? \"20px\" : \"24px\",\n            fontWeight: \"bold\",\n            color: token.colorTextHeading\n        },\n        sectionTitle: {\n            fontSize: windowSize.width <= 576 ? \"14px\" : \"16px\",\n            fontWeight: \"bold\",\n            marginBottom: \"12px\",\n            color: token.colorTextHeading,\n            position: \"relative\",\n            paddingLeft: \"12px\",\n            display: \"flex\",\n            alignItems: \"center\"\n        },\n        sectionTitleBefore: {\n            content: '\"\"',\n            position: \"absolute\",\n            left: 0,\n            top: \"50%\",\n            transform: \"translateY(-50%)\",\n            width: \"4px\",\n            height: \"16px\",\n            backgroundColor: token.colorPrimary,\n            borderRadius: \"2px\"\n        },\n        formContainer: {\n            padding: \"0 8px\"\n        },\n        listItem: {\n            margin: \"6px 0\",\n            fontSize: \"14px\",\n            color: token.colorTextSecondary\n        },\n        uploadSection: {\n            marginBottom: \"24px\",\n            padding: \"16px\",\n            borderRadius: \"12px\",\n            background: token.colorFillTertiary\n        },\n        submitButton: {\n            borderRadius: \"32px\",\n            height: \"40px\",\n            padding: \"0 32px\",\n            fontSize: \"16px\",\n            boxShadow: \"0 4px 12px \".concat(token.colorPrimaryBg),\n            transition: \"all 0.3s\"\n        }\n    };\n    //  视图层\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                children: scrollbarStyles\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                lineNumber: 713,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                open: visible,\n                zIndex: 1000,\n                onCancel: handleCancel,\n                footer: null,\n                centered: true,\n                maskClosable: !uploading,\n                closable: !uploading,\n                keyboard: !uploading,\n                width: getModalWidth(),\n                style: {\n                    borderRadius: \"16px\",\n                    overflow: \"hidden\",\n                    top: windowSize.width <= 576 ? 20 : undefined // 小屏幕时调整位置\n                },\n                bodyStyle: {\n                    padding: windowSize.width <= 576 ? \"16px 20px 88px\" : \"24px 32px 88px\",\n                    maxHeight: \"80vh\",\n                    overflowX: \"hidden\" // 防止水平滚动条\n                },\n                maskStyle: {\n                    backdropFilter: \"blur(4px)\",\n                    background: \"rgba(0, 0, 0, 0.45)\"\n                },\n                className: \"teacher-auth-modal\",\n                children: [\n                    uploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            left: 0,\n                            top: 0,\n                            width: \"100%\",\n                            height: \"100%\",\n                            background: \"rgba(0, 0, 0, 0.7)\",\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            zIndex: 10,\n                            borderRadius: \"16px\",\n                            color: \"white\",\n                            textAlign: \"center\",\n                            backdropFilter: \"blur(4px)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                indicator: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    style: {\n                                        fontSize: 36,\n                                        color: token.colorPrimary\n                                    },\n                                    spin: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                    lineNumber: 756,\n                                    columnNumber: 30\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                lineNumber: 756,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: 16,\n                                    fontSize: \"16px\",\n                                    fontWeight: 500\n                                },\n                                children: uploadingText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                lineNumber: 757,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                        lineNumber: 739,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"teacher-auth-modal-body\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: modalStyles.header,\n                                children: \"教师身份认证\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                lineNumber: 763,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                form: form,\n                                onFinish: handleSubmit,\n                                layout: \"vertical\",\n                                style: modalStyles.formContainer,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"auth-flex-container\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"auth-column-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: modalStyles.sectionTitle,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: modalStyles.sectionTitleBefore\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"个人信息\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                    label: \"姓名\",\n                                                    name: \"nickName\",\n                                                    rules: [\n                                                        {\n                                                            required: true,\n                                                            message: \"请输入您的姓名\"\n                                                        }\n                                                    ],\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        placeholder: \"请输入您的姓名\",\n                                                        style: {\n                                                            borderRadius: \"8px\",\n                                                            height: \"40px\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: modalStyles.sectionTitle,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: modalStyles.sectionTitleBefore\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 795,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"学校信息\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                    lineNumber: 794,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            style: {\n                                                                margin: \"8px 0 16px\",\n                                                                borderRadius: \"8px\",\n                                                                width: \"100%\"\n                                                            },\n                                                            placeholder: \"请输入学校名称搜索，也可直接输入自定义学校名\",\n                                                            value: schoolName,\n                                                            onChange: (value)=>{\n                                                                if (typeof value === \"string\") {\n                                                                    setSchoolName(value);\n                                                                }\n                                                            },\n                                                            onSelect: (value, option)=>handleSchoolSelect(value, option),\n                                                            onSearch: (value)=>{\n                                                                setSchoolSearchText(value);\n                                                                if (value && value.length >= 1) {\n                                                                    fetchSchools(value);\n                                                                    setShowSchoolDropdown(true);\n                                                                }\n                                                            },\n                                                            onDropdownVisibleChange: (open)=>{\n                                                                setShowSchoolDropdown(open);\n                                                                if (open && province && (isMunicipality(province) ? true : city) && district) {\n                                                                    fetchSchools();\n                                                                }\n                                                            },\n                                                            open: showSchoolDropdown,\n                                                            filterOption: (inputValue, option)=>{\n                                                                if (!(option === null || option === void 0 ? void 0 : option.value)) return false;\n                                                                return String(option.value).toLowerCase().includes(inputValue.toLowerCase());\n                                                            },\n                                                            defaultActiveFirstOption: false,\n                                                            notFoundContent: filteredSchools.length === 0 ? \"未找到相关学校，可直接输入学校名\" : !province && !schoolSearchText ? \"请先选择省份或直接搜索学校名称\" : !isMunicipality(province) && !city && !schoolSearchText ? \"请先选择城市\" : !district && !schoolSearchText ? \"请先选择区县\" : \"请选择学校\",\n                                                            options: filteredSchools.map((school)=>{\n                                                                const hasDuplicateName = filteredSchools.some((s)=>s.schoolName === school.schoolName && (s.province !== school.province || s.city !== school.city || s.district !== school.district));\n                                                                const formatLocation = ()=>{\n                                                                    const schoolProvince = school.province && school.province !== \"null\" ? school.province : \"\";\n                                                                    const schoolCity = schoolProvince && !isMunicipality(schoolProvince) ? school.city && school.city !== \"null\" ? school.city : \"\" : \"\";\n                                                                    const schoolDistrict = school.district && school.district !== \"null\" ? school.district : \"\";\n                                                                    if (!schoolProvince && !schoolCity && !schoolDistrict) {\n                                                                        return \"\";\n                                                                    }\n                                                                    return \"（\".concat(schoolProvince).concat(schoolCity).concat(schoolDistrict, \"）\");\n                                                                };\n                                                                return {\n                                                                    value: hasDuplicateName ? \"\".concat(school.schoolName).concat(formatLocation()) : school.schoolName,\n                                                                    label: hasDuplicateName ? \"\".concat(school.schoolName).concat(formatLocation()) : school.schoolName,\n                                                                    data: school\n                                                                };\n                                                            }),\n                                                            dropdownStyle: {\n                                                                maxHeight: 200,\n                                                                overflow: \"auto\",\n                                                                borderRadius: \"12px\",\n                                                                padding: \"8px\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"12px\",\n                                                                flexWrap: windowSize.width <= 576 ? \"wrap\" : \"nowrap\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    placeholder: \"省\",\n                                                                    value: province || undefined,\n                                                                    onChange: (value)=>{\n                                                                        setProvince(value);\n                                                                        setCity(\"\");\n                                                                        setDistrict(\"\");\n                                                                        setSchoolName(\"\");\n                                                                        setFilteredSchools([]);\n                                                                    },\n                                                                    style: {\n                                                                        flex: windowSize.width <= 576 ? \"1 1 100%\" : 1,\n                                                                        borderRadius: \"8px\"\n                                                                    },\n                                                                    dropdownStyle: {\n                                                                        borderRadius: \"12px\"\n                                                                    },\n                                                                    children: _public_pca_json__WEBPACK_IMPORTED_MODULE_3__.map((p)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Option, {\n                                                                            value: p.name,\n                                                                            children: p.name\n                                                                        }, p.code, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                            lineNumber: 893,\n                                                                            columnNumber: 25\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                    lineNumber: 879,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                !isMunicipality(province) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    placeholder: \"市\",\n                                                                    value: city || undefined,\n                                                                    onChange: (value)=>{\n                                                                        setCity(value);\n                                                                        setDistrict(\"\");\n                                                                        setSchoolName(\"\");\n                                                                        setFilteredSchools([]);\n                                                                    },\n                                                                    disabled: !province,\n                                                                    style: {\n                                                                        flex: windowSize.width <= 576 ? \"1 1 48%\" : 1,\n                                                                        borderRadius: \"8px\"\n                                                                    },\n                                                                    dropdownStyle: {\n                                                                        borderRadius: \"12px\"\n                                                                    },\n                                                                    children: cities.map((c)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Option, {\n                                                                            value: c.name,\n                                                                            children: c.name\n                                                                        }, c.code, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                            lineNumber: 914,\n                                                                            columnNumber: 27\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                    lineNumber: 900,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    placeholder: \"区\",\n                                                                    onChange: (value)=>{\n                                                                        setDistrict(value);\n                                                                        setSchoolName(\"\");\n                                                                        setFilteredSchools([]);\n                                                                    },\n                                                                    disabled: !province || !isMunicipality(province) && !city,\n                                                                    value: district,\n                                                                    style: {\n                                                                        flex: windowSize.width <= 576 ? \"1 1 48%\" : 1,\n                                                                        borderRadius: \"8px\"\n                                                                    },\n                                                                    dropdownStyle: {\n                                                                        borderRadius: \"12px\"\n                                                                    },\n                                                                    children: districts.map((d)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Option, {\n                                                                            value: d.name,\n                                                                            children: d.name\n                                                                        }, d.code, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                            lineNumber: 934,\n                                                                            columnNumber: 25\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                    lineNumber: 921,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 878,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: modalStyles.uploadSection,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: modalStyles.sectionTitle,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: modalStyles.sectionTitleBefore\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                    lineNumber: 945,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \"附加材料\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"12px\",\n                                                                marginTop: \"16px\",\n                                                                width: \"100%\",\n                                                                position: \"relative\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: \"flex\",\n                                                                    gap: \"12px\",\n                                                                    width: \"100%\",\n                                                                    overflowX: \"auto\",\n                                                                    paddingBottom: \"8px\",\n                                                                    WebkitOverflowScrolling: \"touch\",\n                                                                    scrollbarWidth: \"auto\",\n                                                                    whiteSpace: \"nowrap\"\n                                                                },\n                                                                className: \"file-scroll-container\",\n                                                                children: [\n                                                                    fileList.map((file)=>{\n                                                                        var _file_type;\n                                                                        const isImage = (_file_type = file.type) === null || _file_type === void 0 ? void 0 : _file_type.startsWith(\"image/\");\n                                                                        if (isImage && file.thumbUrl) {\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"custom-upload-item\",\n                                                                                style: {\n                                                                                    position: \"relative\",\n                                                                                    width: \"104px\",\n                                                                                    height: \"104px\",\n                                                                                    borderRadius: \"12px\",\n                                                                                    display: \"inline-block\",\n                                                                                    flexShrink: 0,\n                                                                                    /* 防止图片收缩 */ overflow: \"hidden\"\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                        src: file.thumbUrl,\n                                                                                        alt: file.name,\n                                                                                        style: {\n                                                                                            width: \"100%\",\n                                                                                            height: \"100%\",\n                                                                                            objectFit: \"cover\"\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                        lineNumber: 982,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"file-delete-icon\",\n                                                                                        style: {\n                                                                                            position: \"absolute\",\n                                                                                            top: \"0\",\n                                                                                            right: \"0\",\n                                                                                            background: \"rgba(0,0,0,0.65)\",\n                                                                                            width: \"22px\",\n                                                                                            height: \"22px\",\n                                                                                            borderRadius: \"0 0 0 8px\",\n                                                                                            display: \"flex\",\n                                                                                            justifyContent: \"center\",\n                                                                                            alignItems: \"center\",\n                                                                                            cursor: \"pointer\"\n                                                                                        },\n                                                                                        onClick: ()=>handleRemoveFile(file),\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                            style: {\n                                                                                                color: \"#fff\",\n                                                                                                fontSize: \"14px\"\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                            lineNumber: 1007,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                        lineNumber: 991,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, file.uid, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                lineNumber: 973,\n                                                                                columnNumber: 29\n                                                                            }, undefined);\n                                                                        } else {\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"custom-file-card\",\n                                                                                style: {\n                                                                                    width: \"104px\",\n                                                                                    height: \"104px\",\n                                                                                    border: \"1px solid \".concat(token.colorBorderSecondary),\n                                                                                    borderRadius: \"12px\",\n                                                                                    padding: \"8px\",\n                                                                                    display: \"inline-flex\",\n                                                                                    flexDirection: \"column\",\n                                                                                    alignItems: \"center\",\n                                                                                    justifyContent: \"space-between\",\n                                                                                    position: \"relative\",\n                                                                                    flexShrink: 0,\n                                                                                    /* 防止文件卡片收缩 */ background: token.colorFillQuaternary,\n                                                                                    boxShadow: \"0 2px 8px \".concat(token.colorBgElevated),\n                                                                                    transition: \"all 0.3s\"\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"file-delete-icon\",\n                                                                                        style: {\n                                                                                            position: \"absolute\",\n                                                                                            top: \"0\",\n                                                                                            right: \"0\",\n                                                                                            background: \"rgba(0,0,0,0.65)\",\n                                                                                            width: \"22px\",\n                                                                                            height: \"22px\",\n                                                                                            borderRadius: \"0 0 0 12px\",\n                                                                                            display: \"flex\",\n                                                                                            justifyContent: \"center\",\n                                                                                            alignItems: \"center\",\n                                                                                            cursor: \"pointer\",\n                                                                                            transition: \"background 0.2s\"\n                                                                                        },\n                                                                                        onClick: ()=>handleRemoveFile(file),\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                            style: {\n                                                                                                color: \"#fff\",\n                                                                                                fontSize: \"14px\"\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                            lineNumber: 1047,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                        lineNumber: 1030,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        style: {\n                                                                                            marginTop: \"10px\"\n                                                                                        },\n                                                                                        children: getFileIcon(file.type || \"\")\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                        lineNumber: 1050,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        style: {\n                                                                                            textAlign: \"center\",\n                                                                                            width: \"100%\",\n                                                                                            marginTop: \"4px\",\n                                                                                            overflow: \"hidden\"\n                                                                                        },\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].Text, {\n                                                                                                ellipsis: {\n                                                                                                    tooltip: file.name\n                                                                                                },\n                                                                                                style: {\n                                                                                                    fontSize: \"12px\",\n                                                                                                    lineHeight: \"1.2\"\n                                                                                                },\n                                                                                                children: file.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                                lineNumber: 1060,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                style: {\n                                                                                                    fontSize: \"11px\",\n                                                                                                    color: token.colorTextSecondary,\n                                                                                                    marginTop: \"2px\",\n                                                                                                    background: token.colorFillSecondary,\n                                                                                                    padding: \"0 4px\",\n                                                                                                    borderRadius: \"4px\",\n                                                                                                    display: \"inline-block\"\n                                                                                                },\n                                                                                                children: getFileExtension(file.name)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                                lineNumber: 1066,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                        lineNumber: 1054,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, file.uid, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                lineNumber: 1013,\n                                                                                columnNumber: 29\n                                                                            }, undefined);\n                                                                        }\n                                                                    }),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        showUploadList: false,\n                                                                        beforeUpload: (file)=>{\n                                                                            try {\n                                                                                const uid = Date.now().toString();\n                                                                                const newFile = {\n                                                                                    uid: uid,\n                                                                                    name: file.name,\n                                                                                    status: \"done\",\n                                                                                    thumbUrl: URL.createObjectURL(file),\n                                                                                    type: file.type,\n                                                                                    size: file.size\n                                                                                };\n                                                                                setFileList([\n                                                                                    ...fileList,\n                                                                                    newFile\n                                                                                ]);\n                                                                                setLocalFiles((prev)=>({\n                                                                                        ...prev,\n                                                                                        [uid]: file\n                                                                                    }));\n                                                                            } catch (error) {\n                                                                                nt.error(\"文件处理失败，请重试\");\n                                                                            }\n                                                                            return false;\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            style: {\n                                                                                border: 0,\n                                                                                background: \"none\",\n                                                                                cursor: \"pointer\",\n                                                                                borderRadius: \"12px\",\n                                                                                padding: \"16px\",\n                                                                                transition: \"all 0.3s\",\n                                                                                width: \"104px\",\n                                                                                height: \"104px\",\n                                                                                display: \"inline-flex\",\n                                                                                flexShrink: 0,\n                                                                                /* 防止按钮收缩 */ flexDirection: \"column\",\n                                                                                justifyContent: \"center\",\n                                                                                alignItems: \"center\",\n                                                                                backgroundColor: token.colorBgContainer,\n                                                                                boxShadow: \"0 2px 8px \".concat(token.colorBorderSecondary)\n                                                                            },\n                                                                            type: \"button\",\n                                                                            disabled: uploading,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_FileExcelOutlined_FileImageOutlined_FileOutlined_FilePdfOutlined_FileWordOutlined_LoadingOutlined_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    style: {\n                                                                                        fontSize: \"24px\",\n                                                                                        color: token.colorPrimary\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                    lineNumber: 1129,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    style: {\n                                                                                        marginTop: 8,\n                                                                                        color: token.colorTextSecondary\n                                                                                    },\n                                                                                    children: \"点击上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                                    lineNumber: 1130,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                            lineNumber: 1108,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                        lineNumber: 1084,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                lineNumber: 956,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 949,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                    lineNumber: 943,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"auth-column-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        padding: \"16px\",\n                                                        borderRadius: \"12px\",\n                                                        background: token.colorInfoBg,\n                                                        border: \"2px solid \".concat(token.colorInfoBorder),\n                                                        height: \"fit-content\",\n                                                        /* 高度适应内容 */ marginBottom: \"6px\",\n                                                        marginTop: \"16px\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: modalStyles.sectionTitle,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        ...modalStyles.sectionTitleBefore,\n                                                                        backgroundColor: token.colorInfo\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                    lineNumber: 1152,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \"注意事项\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 1151,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            style: {\n                                                                listStyle: \"none\",\n                                                                padding: 0,\n                                                                margin: 0\n                                                            },\n                                                            children: [\n                                                                \"同一个身份信息仅支持实名认证3个洛基飞跃账号。\",\n                                                                \"提交审核过后可在沟通群内联系管理员跟进认证。\",\n                                                                \"审核结果将于1个工作日以短信和站内信的方式通知您。审核不通过，请根据页面提示，修改认证证明材料并再次提交审核。\"\n                                                            ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    style: modalStyles.listItem,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: token.colorInfo,\n                                                                                marginRight: \"8px\"\n                                                                            },\n                                                                            children: \"•\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                            lineNumber: 1165,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        item\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                    lineNumber: 1164,\n                                                                    columnNumber: 23\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 1158,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                    lineNumber: 1141,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        marginTop: \"16px\",\n                                                        padding: \"16px\",\n                                                        borderRadius: \"12px\",\n                                                        background: token.colorSuccessBg,\n                                                        border: \"2px solid \".concat(token.colorSuccessBorder),\n                                                        height: \"fit-content\" /* 高度适应内容 */ \n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: modalStyles.sectionTitle,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        ...modalStyles.sectionTitleBefore,\n                                                                        backgroundColor: token.colorSuccess\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                    lineNumber: 1182,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \"认证信息可包括\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 1181,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            style: {\n                                                                listStyle: \"none\",\n                                                                padding: 0,\n                                                                margin: 0\n                                                            },\n                                                            children: [\n                                                                \"教师资格证\",\n                                                                \"教师工作证\",\n                                                                \"中华人民共和国居民身份证信息\",\n                                                                \"学校或教育机构授权\"\n                                                            ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    style: modalStyles.listItem,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: token.colorSuccess,\n                                                                                marginRight: \"8px\"\n                                                                            },\n                                                                            children: \"•\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                            lineNumber: 1191,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        item\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                                    lineNumber: 1190,\n                                                                    columnNumber: 23\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                            lineNumber: 1188,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                                    lineNumber: 1173,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                            lineNumber: 1139,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                    lineNumber: 774,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                                lineNumber: 768,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                        lineNumber: 761,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"teacher-auth-modal-footer\",\n                        style: {\n                            borderTop: \"1px solid #f0f0f0\" /* 使用浅色边框替代红线 */ \n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            type: \"primary\",\n                            onClick: ()=>form.submit(),\n                            loading: submitting,\n                            disabled: submitting,\n                            style: modalStyles.submitButton,\n                            children: submitting ? \"提交中...\" : \"确认提交\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                            lineNumber: 1204,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                        lineNumber: 1203,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\components\\\\userAuth\\\\teacherAuthModal.tsx\",\n                lineNumber: 714,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(TeacherAuthModal, \"+FxDNWxv1HLtDIlYvaHMrUbsrWg=\", false, function() {\n    return [\n        _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useToken,\n        react_redux__WEBPACK_IMPORTED_MODULE_8__.useSelector,\n        _barrel_optimize_names_AutoComplete_Button_Form_Input_Modal_Select_Spin_Typography_Upload_theme_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].useForm\n    ];\n});\n_c = TeacherAuthModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TeacherAuthModal);\nvar _c;\n$RefreshReg$(_c, \"TeacherAuthModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/userAuth/teacherAuthModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api/school.ts":
/*!***************************!*\
  !*** ./lib/api/school.ts ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   schoolApi: function() { return /* binding */ schoolApi; }\n/* harmony export */ });\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../request */ \"(app-pages-browser)/./lib/request.ts\");\n/*\r\n * @Author: Zwww <EMAIL>\r\n * @Date: 2025-04-29 11:54:36\r\n * @LastEditors: Zwww <EMAIL>\r\n * @LastEditTime: 2025-05-06 17:27:15\r\n * @FilePath: \\sourceCode\\logicleapweb\\lib\\api\\school.ts\r\n * @Description: \r\n * \r\n * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. \r\n */ \nconst schoolBaseUrl = \"/api/user-school\";\nconst schoolRelationBaseUrl = \"/api/user-school-relation\";\nconst schoolApi = {\n    // 获取学校列表  okok\n    getList: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(schoolBaseUrl, {\n            params\n        });\n    },\n    // 添加学校   okok\n    add: (school)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(schoolBaseUrl, school);\n    },\n    // 更新学校信息   okok\n    update: (school, id)=>{\n        console.log(school);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(schoolBaseUrl + id, school);\n    },\n    // 删除学校    okok\n    delete: (id)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(schoolBaseUrl + \"/\" + id);\n    },\n    // 获取省市区数据    没有用到 直接okok\n    getRegions: ()=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/app/user/school/regions\");\n    },\n    // 绑定学校   okok\n    bindSchool: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(schoolRelationBaseUrl, params);\n    },\n    // 解绑学校  okok\n    unbindSchool: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"\".concat(schoolRelationBaseUrl, \"/user/\").concat(params.userId, \"/school/\").concat(params.schoolId));\n    },\n    // 获取用户关联的学校列表  \n    getUserSchools: ()=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/user-school/listByUserId\");\n    },\n    // 获取学校的教师列表    okok\n    getSchoolTeachers: (schoolId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(schoolRelationBaseUrl, \"/school/\").concat(schoolId, \"/teachers\"));\n    },\n    // 获取学校的班级列表 \n    getSchoolClasses: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/user/class/school/\".concat(params.schoolId, \"/all\"), {\n            params\n        });\n    },\n    // 获取学校的省市区信息  没人调用他。okok\n    getSchoolRegion: (schoolId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/app/user/school/\".concat(schoolId, \"/region\"));\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/school.ts\n"));

/***/ })

});