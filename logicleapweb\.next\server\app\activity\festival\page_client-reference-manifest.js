globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/activity/festival/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/login/page.tsx":{"*":{"id":"(ssr)/./app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/providers.tsx":{"*":{"id":"(ssr)/./app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(main)/home/<USER>":{"*":{"id":"(ssr)/./app/(main)/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(main)/layout.tsx":{"*":{"id":"(ssr)/./app/(main)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin-space/page.tsx":{"*":{"id":"(ssr)/./app/admin-space/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/activity/page.tsx":{"*":{"id":"(ssr)/./app/activity/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/activity/festival/page.tsx":{"*":{"id":"(ssr)/./app/activity/festival/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./app/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\app\\providers.tsx":{"id":"(app-pages-browser)/./app/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\node_modules\\antd\\dist\\reset.css":{"id":"(app-pages-browser)/./node_modules/antd/dist/reset.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\app\\(main)\\home\\page.tsx":{"id":"(app-pages-browser)/./app/(main)/home/<USER>","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\app\\(main)\\layout.tsx":{"id":"(app-pages-browser)/./app/(main)/layout.tsx","name":"*","chunks":["app/(main)/layout","static/chunks/app/(main)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\app\\admin-space\\page.tsx":{"id":"(app-pages-browser)/./app/admin-space/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\app\\activity\\page.tsx":{"id":"(app-pages-browser)/./app/activity/page.tsx","name":"*","chunks":["app/activity/page","static/chunks/app/activity/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\app\\activity\\festival\\page.tsx":{"id":"(app-pages-browser)/./app/activity/festival/page.tsx","name":"*","chunks":["app/activity/festival/page","static/chunks/app/activity/festival/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\":[],"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\app\\(main)\\layout":["static/css/app/(main)/layout.css"],"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\app\\activity\\page":[],"C:\\Users\\<USER>\\Desktop\\LL\\logicleapweb\\app\\activity\\festival\\page":["static/css/app/activity/festival/page.css"]}}