2025-07-30 09:47:41.000 [ERROR] [WebWeixinScanService] 清理过期记录失败: connect ECONNABORTED ************:13306 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:47:41.081 [ERROR] [WebWeixinScanService] 清理过期记录失败: connect ECONNABORTED ************:13306 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:47:41.120 [ERROR] [WebWeixinScanService] 清理过期记录失败: read ECONNRESET {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:51:04.035 [ERROR] [GlobalExceptionFilter] GET /api/encryption/publicKey - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 09:51:04.036 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
{"url":"/api/encryption/publicKey","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T01:51:04.036Z"}
2025-07-30 09:51:04.044 [ERROR] [GlobalExceptionFilter] GET /api/user-school/listByUserId - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 09:51:04.044 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
{"url":"/api/user-school/listByUserId","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T01:51:04.044Z"}
2025-07-30 09:51:04.050 [ERROR] [GlobalExceptionFilter] GET /api/teacher-task/list?teacherId=2896&page=1&size=50&orderBy=createTime:DESC - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 09:51:04.050 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
{"url":"/api/teacher-task/list?teacherId=2896&page=1&size=50&orderBy=createTime:DESC","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T01:51:04.050Z"}
2025-07-30 09:51:04.056 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/current/2896 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 09:51:04.056 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
{"url":"/api/user/srch/templates/current/2896","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T01:51:04.056Z"}
2025-07-30 09:51:04.060 [ERROR] [GlobalExceptionFilter] GET /api/user-school/listByUserId - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 09:51:04.061 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
{"url":"/api/user-school/listByUserId","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T01:51:04.061Z"}
2025-07-30 09:51:04.065 [ERROR] [GlobalExceptionFilter] GET /api/encryption/publicKey - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 09:51:04.066 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
{"url":"/api/encryption/publicKey","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T01:51:04.066Z"}
2025-07-30 09:51:04.073 [ERROR] [GlobalExceptionFilter] GET /api/teacher-task/list?teacherId=2896&page=1&size=50&orderBy=createTime:DESC - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 09:51:04.074 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
{"url":"/api/teacher-task/list?teacherId=2896&page=1&size=50&orderBy=createTime:DESC","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T01:51:04.073Z"}
2025-07-30 09:51:04.081 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/current/2896 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 09:51:04.081 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
{"url":"/api/user/srch/templates/current/2896","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T01:51:04.081Z"}
2025-07-30 09:51:04.091 [ERROR] [GlobalExceptionFilter] POST /api/router-guard/refresh-token - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.refreshToken (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:223:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async RouterGuardController.refreshToken (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.controller.ts:63:22)
2025-07-30 09:51:04.092 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
{"url":"/api/router-guard/refresh-token","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"刷新token无效","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T01:51:04.091Z"}
2025-07-30 09:51:04.099 [ERROR] [GlobalExceptionFilter] GET /api/user/class/teacher/2896/stats - Cannot GET /api/user/class/teacher/2896/stats {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
NotFoundException: Cannot GET /api/user/class/teacher/2896/stats
    at callback (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:342:13)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:297:9
    at processParams (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (C:\Users\<USER>\Desktop\LL\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-30 09:51:04.100 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
{"url":"/api/user/class/teacher/2896/stats","method":"GET","statusCode":404,"message":"Cannot GET /api/user/class/teacher/2896/stats","details":{"message":"Cannot GET /api/user/class/teacher/2896/stats","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T01:51:04.100Z"}
2025-07-30 09:51:04.120 [ERROR] [GlobalExceptionFilter] GET /api/user-point/total - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 09:51:04.121 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
{"url":"/api/user-point/total","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T01:51:04.120Z"}
2025-07-30 09:51:07.062 [ERROR] [GlobalExceptionFilter] GET /api/user-school/listByUserId - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 09:51:07.062 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai"}
{"url":"/api/user-school/listByUserId","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T01:51:07.062Z"}
2025-07-30 09:51:13.639 [ERROR] [WebSocketGateway] 连接处理异常: 没有上传token {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41236,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.420 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Injector.resolveConstructorParams (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 09:52:07.477 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.477 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.477 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.477 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.478 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.478 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.478 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.478 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.479 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.479 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.480 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.480 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.480 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.480 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.480 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.481 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.481 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.481 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.481 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:07.481 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:54.803 [ERROR] [EncryptionService] 【会话】会话ID不存在或已过期: 1753840070128.ffff127001.xxmqy2cmzf {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:52:54.805 [ERROR] [EncryptionService] 【会话】获取会话密钥失败: 会话ID不存在或已过期: 1753840070128.ffff127001.xxmqy2cmzf {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":["Error: 会话ID不存在或已过期: 1753840070128.ffff127001.xxmqy2cmzf\n    at EncryptionService.getSessionKey (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\encryption.service.ts:355:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async EncryptionService.encrypt (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\encryption.service.ts:266:26)\n    at async <anonymous> (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\encrypt.interceptor.ts:182:19)"]}
2025-07-30 09:52:54.806 [ERROR] [EncryptionService] 【加密】加密数据失败: 获取会话密钥失败 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":["Error: 获取会话密钥失败\n    at EncryptionService.getSessionKey (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\encryption.service.ts:373:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async EncryptionService.encrypt (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\encryption.service.ts:266:26)\n    at async <anonymous> (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\encrypt.interceptor.ts:182:19)"]}
2025-07-30 09:53:25.143 [ERROR] [WebSocketGateway] 连接处理异常: 没有上传token {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 09:53:55.898 [ERROR] [GlobalExceptionFilter] GET /api/user/class/teacher/2896/stats - Cannot GET /api/user/class/teacher/2896/stats {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
NotFoundException: Cannot GET /api/user/class/teacher/2896/stats
    at callback (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:342:13)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:297:9
    at processParams (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (C:\Users\<USER>\Desktop\LL\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-30 09:53:55.899 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
{"url":"/api/user/class/teacher/2896/stats","method":"GET","statusCode":404,"message":"Cannot GET /api/user/class/teacher/2896/stats","details":{"message":"Cannot GET /api/user/class/teacher/2896/stats","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T01:53:55.899Z"}
2025-07-30 10:31:37.574 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/list/null - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 10:31:37.575 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
{"url":"/api/user/srch/templates/list/null","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T02:31:37.575Z"}
2025-07-30 10:31:37.581 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/current/null - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 10:31:37.582 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
{"url":"/api/user/srch/templates/current/null","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T02:31:37.582Z"}
2025-07-30 10:31:37.585 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/folders/list - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 10:31:37.586 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
{"url":"/api/user/srch/templates/folders/list","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T02:31:37.586Z"}
2025-07-30 10:31:37.588 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/list/null - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 10:31:37.589 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
{"url":"/api/user/srch/templates/list/null","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T02:31:37.588Z"}
2025-07-30 10:31:37.592 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/current/null - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 10:31:37.592 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
{"url":"/api/user/srch/templates/current/null","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T02:31:37.592Z"}
2025-07-30 10:31:37.595 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/folders/list - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 10:31:37.595 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
{"url":"/api/user/srch/templates/folders/list","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T02:31:37.595Z"}
2025-07-30 10:31:37.597 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/1 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 10:31:37.598 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
{"url":"/api/user/srch/templates/1","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T02:31:37.597Z"}
2025-07-30 10:31:37.600 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/1 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-07-30 10:31:37.601 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai"}
{"url":"/api/user/srch/templates/1","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-07-30T02:31:37.600Z"}
2025-07-30 10:31:40.200 [ERROR] [WebSocketGateway] 连接处理异常: 没有上传token {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 10:31:46.013 [ERROR] [WeixinApiUtilService] 创建二维码异常: 创建二维码失败: {"errcode":40001,"errmsg":"invalid credential, access_token is invalid or not latest, could get access_token by getStableAccessToken, more details at https://mmbizurl.cn/s/JtxxFh33r rid: 68898413-001435c9-4dfeeab7"} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 10:31:46.013 [ERROR] [WeixinQrCodeService] 生成二维码失败: 创建二维码失败: {"errcode":40001,"errmsg":"invalid credential, access_token is invalid or not latest, could get access_token by getStableAccessToken, more details at https://mmbizurl.cn/s/JtxxFh33r rid: 68898413-001435c9-4dfeeab7"} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 10:31:46.013 [ERROR] [WeixinQrCodeService] 生成登录二维码失败: 生成二维码失败 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 10:31:46.014 [ERROR] [WebWeixinScanService] 生成二维码失败: 生成登录二维码失败 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 10:31:46.059 [ERROR] [WeixinApiUtilService] 创建二维码异常: 创建二维码失败: {"errcode":40001,"errmsg":"invalid credential, access_token is invalid or not latest, could get access_token by getStableAccessToken, more details at https://mmbizurl.cn/s/JtxxFh33r rid: 68898413-47a1b3fa-6d611981"} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 10:31:46.059 [ERROR] [WeixinQrCodeService] 生成二维码失败: 创建二维码失败: {"errcode":40001,"errmsg":"invalid credential, access_token is invalid or not latest, could get access_token by getStableAccessToken, more details at https://mmbizurl.cn/s/JtxxFh33r rid: 68898413-47a1b3fa-6d611981"} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 10:31:46.060 [ERROR] [WeixinQrCodeService] 生成登录二维码失败: 生成二维码失败 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
2025-07-30 10:31:46.060 [ERROR] [WebWeixinScanService] 生成二维码失败: 生成登录二维码失败 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":20268,"hostname":"zhulezai","stack":[null]}
