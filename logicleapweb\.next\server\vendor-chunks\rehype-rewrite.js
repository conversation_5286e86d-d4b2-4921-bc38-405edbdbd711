"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-rewrite";
exports.ids = ["vendor-chunks/rehype-rewrite"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-rewrite/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/rehype-rewrite/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getCodeString: () => (/* binding */ getCodeString)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var hast_util_select__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-select */ \"(ssr)/./node_modules/hast-util-select/lib/index.js\");\n\n\n/** Get the node tree source code string */\nconst getCodeString = (data = [], code = '') => {\n    data.forEach((node) => {\n        if (node.type === 'text') {\n            code += node.value;\n        }\n        else if (node.type === 'element' && node.children && Array.isArray(node.children)) {\n            code += getCodeString(node.children);\n        }\n    });\n    return code;\n};\nconst remarkRewrite = (options) => {\n    const { selector, rewrite } = options || {};\n    return (tree) => {\n        if (!rewrite || typeof rewrite !== 'function')\n            return;\n        if (selector && typeof selector === 'string') {\n            const selected = (0,hast_util_select__WEBPACK_IMPORTED_MODULE_0__.selectAll)(selector, tree);\n            if (selected && selected.length > 0) {\n                (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.visit)(tree, selected, (node, index, parent) => {\n                    rewrite(node, index, parent);\n                });\n            }\n            return;\n        }\n        (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.visit)(tree, (node, index, parent) => {\n            rewrite(node, index, parent);\n        });\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (remarkRewrite);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-rewrite/lib/index.js\n");

/***/ })

};
;