'use client'

import { Mo<PERSON>, Button, Tabs, Tag, Progress, Form, Rate, Input, InputNumber, Tooltip, Empty, Collapse, Avatar, Spin } from 'antd';
import { Task, Student, Assignment } from '../types/index';
import taskApi from '@/lib/api/task';
import studentSelfAssessmentSubmissionApi from '@/lib/api/studentSelfAssessmentSubmission';
import { useEffect, useState, useRef, useCallback } from 'react';
import { FileTextOutlined, EyeOutlined, DownloadOutlined, CheckCircleOutlined, LoadingOutlined, CloseOutlined, UserOutlined } from '@ant-design/icons';
import Image from 'next/image';
import { worksApi } from '@/lib/api/works';
import { viewWork } from '../../../lib/utils/view-work-modal';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
import { GetNotification } from 'logic-common/dist/components/Notification';
import ossImageProcessor from '@/lib/utils/OssImageProcessor';

interface TaskDetailModalProps {
  task: Task | null;
  visible: boolean;
  onClose: () => void;
  students: Student[];
  onRefresh: () => void;
  currentClassId: number;
}

// 将 SubmissionContent 组件提取为单独的组件
const SubmissionContent: React.FC<{
  currentSubmission: any;
  handleViewWork: (workId: number) => void;
}> = ({ currentSubmission, handleViewWork }) => {
  const [activeTab, setActiveTab] = useState(() => {
    // 确定初始标签
    if (!currentSubmission) return 'work';
    
    if (currentSubmission.submissionType === 'image' || 
        (!currentSubmission.workInfo && currentSubmission.imageInfo)) {
      return 'image';
    } else if (currentSubmission.submissionType === 'files' || 
              (!currentSubmission.workInfo && !currentSubmission.imageInfo && 
               currentSubmission.submissionFiles?.length > 0)) {
      return 'files';
    } 
    return 'work';
  });
  
  if (!currentSubmission) return null;
  
  return (
    <div className="flex flex-col h-full min-h-0 relative">
      {/* 头部信息区域 */}
      <div className="flex-none p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl mb-4">
        <div className="flex flex-wrap items-center gap-2">
          <Tag color="blue" className="rounded-full px-3 py-1">
            {currentSubmission.submissionType === 'work' ? '作品提交' :
              currentSubmission.submissionType === 'image' ? '图片提交' :
                currentSubmission.submissionType === 'work_and_image' ? '作品和图片提交' :
                  '文件提交'}
          </Tag>
          {currentSubmission.submitTime && (
            <span className="text-gray-600 text-xs sm:text-sm">
              提交时间：{new Date(currentSubmission.submitTime).toLocaleString()}
            </span>
          )}
        </div>
      </div>

      {/* 切换按钮组 */}
      {(currentSubmission.workInfo || currentSubmission.imageInfo || currentSubmission.submissionFiles?.length > 0) && (
        <div className="flex-none p-4 bg-gradient-to-r from-gray-50 to-slate-50 border border-gray-100 rounded-xl mb-4">
          <div className="flex flex-wrap gap-2">
            {currentSubmission.workInfo && (
              <Button
                type={activeTab === 'work' ? "primary" : "default"}
                onClick={() => setActiveTab('work')}
                className="rounded-full flex items-center gap-2"
                icon={<span className="text-lg">🎨</span>}
              >
                作品
              </Button>
            )}
            {currentSubmission.imageInfo && (
              <Button
                type={activeTab === 'image' ? "primary" : "default"}
                onClick={() => setActiveTab('image')}
                className="rounded-full flex items-center gap-2"
                icon={<span className="text-lg">📸</span>}
              >
                图片
              </Button>
            )}
            {currentSubmission.submissionFiles?.length > 0 && (
              <Button
                type={activeTab === 'files' ? "primary" : "default"}
                onClick={() => setActiveTab('files')}
                className="rounded-full flex items-center gap-2"
                icon={<span className="text-lg">📁</span>}
              >
                文件
                <Tag className="rounded-full" color={activeTab === 'files' ? "blue" : "default"}>
                  {currentSubmission.submissionFiles.length}
                </Tag>
              </Button>
            )}
          </div>
        </div>
      )}

      {/* 内容区域 */}
      <div className="flex-1 min-h-0 overflow-auto">
        <div className="h-full bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-4">
          {/* 作品内容 */}
          {activeTab === 'work' && currentSubmission.workInfo && (
            <div className="h-full flex flex-col">
              <div className="flex-none flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-3">
                <div>
                  <h4 className="text-lg font-medium mb-1">{currentSubmission.workInfo.title || '未命名作品'}</h4>
                  <p className="text-sm text-gray-600">{currentSubmission.workInfo.description || '暂无描述'}</p>
                </div>
                <Button
                  type="primary"
                  icon={<EyeOutlined />}
                  className="rounded-full w-full sm:w-auto"
                  onClick={() => {
                    Modal.destroyAll();
                    handleViewWork(currentSubmission.workId);
                  }}
                >
                  查看作品详情
                </Button>
              </div>
              {currentSubmission.workInfo.coverImage && (
                <div className="flex-1 min-h-0 flex items-center justify-center p-4 bg-white rounded-xl">
                  <img
                    src={currentSubmission.workInfo.coverImage}
                    alt="作品封面"
                    className="max-w-full max-h-500px object-contain rounded-lg shadow-lg"
                  />
                </div>
              )}
            </div>
          )}

          {/* 图片内容 */}
          {activeTab === 'image' && currentSubmission.imageInfo && (
            <div className="h-full flex flex-col">
              <div className="flex-none flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-3">
                <h4 className="text-lg font-medium">提交的图片</h4>
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  className="rounded-full w-full sm:w-auto"
                  onClick={() => {
                    if (currentSubmission.imageInfo) {
                      window.open(currentSubmission.imageInfo.backupImagePath || currentSubmission.imageInfo.originalImagePath, '_blank');
                    }
                  }}
                >
                  下载原图
                </Button>
              </div>
              <div className="flex-1 min-h-0 flex items-center justify-center p-4 bg-white rounded-xl">
                <img
                  src={currentSubmission.imageInfo.backupImagePath || currentSubmission.imageInfo.originalImagePath}
                  alt="提交的图片"
                  className="max-w-full max-h-500px object-contain rounded-lg shadow-lg"
                  onError={(e) => {
                    const target = e.currentTarget;
                    if (currentSubmission.imageInfo?.originalImagePath && target.src !== currentSubmission.imageInfo.originalImagePath) {
                      target.src = currentSubmission.imageInfo.originalImagePath;
                    }
                  }}
                />
              </div>
            </div>
          )}

          {/* 文件列表 */}
          {activeTab === 'files' && currentSubmission.submissionFiles?.length > 0 && (
            <div className="h-full flex flex-col">
              <h4 className="text-lg font-medium mb-4">提交的文件</h4>
              <div className="flex-1 min-h-0 overflow-auto">
                <div className="grid grid-cols-1 gap-3">
                  {currentSubmission.submissionFiles.map((fileUrl: string, index: number) => {
                    const fileName = fileUrl.split('/').pop() || '未命名文件';
                    const fileExt = fileName.split('.').pop()?.toLowerCase() || '';

                    return (
                      <div key={index}
                        className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 bg-white rounded-xl hover:shadow-md transition-all gap-3">
                        <div className="flex items-center gap-3">
                          <div className="text-2xl">
                            {['jpg', 'jpeg', 'png', 'gif'].includes(fileExt) ? '🖼️' :
                              fileExt === 'pdf' ? '📄' :
                                fileExt === 'doc' || fileExt === 'docx' ? '📝' :
                                  '📎'}
                          </div>
                          <div>
                            <div className="font-medium text-gray-800">{fileName}</div>
                            <div className="text-sm text-gray-500">{fileExt.toUpperCase()}</div>
                          </div>
                        </div>
                        <div className="flex gap-2 w-full sm:w-auto justify-end">
                          <Button
                            type="primary"
                            icon={<DownloadOutlined />}
                            className="rounded-full w-full sm:w-auto"
                            onClick={() => {
                              const link = document.createElement('a');
                              link.href = fileUrl;
                              link.download = fileName;
                              document.body.appendChild(link);
                              link.click();
                              document.body.removeChild(link);
                            }}
                          >
                            下载
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* 如果没有任何提交内容 */}
                      {!currentSubmission.workInfo && !currentSubmission.imageInfo && !currentSubmission.submissionFiles?.length && (
              <div className="h-full min-h-[200px] flex flex-col items-center justify-center">
                <span className="text-5xl mb-4">🤔</span>
              <Empty description={
                <span className="text-gray-500">该学生尚未提交任何内容</span>
              } />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// 将 PieChart 组件提取为单独的组件
const PieChart: React.FC<{
  completedCount: number;
  inProgressCount: number;
  returnedCount: number;
  notStartedCount: number;
  expiredCount: number;
  totalStudents: number;
}> = ({ completedCount, inProgressCount, returnedCount, notStartedCount, expiredCount, totalStudents }) => {
  const calculatePercentage = (count: number, total: number) => {
    if (total === 0) return 0;
    return (count / total) * 100;
  };
  
  const statusColors = {
    completed: "#3B82F6",
    inProgress: "#FBBF24",
    returned: "#F97316",
    notStarted: "#10B981",
    expired: "#EF4444",
    border: "#4B5563"
  };

  const statuses = [
    { name: '已完成', count: completedCount, color: statusColors.completed },
    { name: '进行中', count: inProgressCount, color: statusColors.inProgress },
    { name: '已打回', count: returnedCount, color: statusColors.returned },
    { name: '未开始', count: notStartedCount, color: statusColors.notStarted },
    { name: '已过期', count: expiredCount, color: statusColors.expired },
  ].filter(s => s.count > 0);

  const hasSingleStatus = statuses.length === 1 && statuses[0].count === totalStudents && totalStudents > 0;
  const singleStatus = hasSingleStatus ? statuses[0] : null;

  const centerX = 120;
  const centerY = 120;
  const pieRadius = 45;
  const viewboxSize = 240;

  let startAngle = -90;
  const statusData = statuses.map(status => {
    const percentage = calculatePercentage(status.count, totalStudents);
    const angle = (percentage / 100) * 360;
    const endAngle = startAngle + angle;
    const midAngle = startAngle + angle / 2;
    const data = { ...status, percentage, angle, startAngle, endAngle, midAngle };
    startAngle = endAngle;
    return data;
  });

  const renderLabels = (labels: typeof statusData, isRight: boolean) => {
    if (labels.length === 0) return null;

    const minGap = 12;
    const labelData = labels
      .map(label => ({
        ...label,
        y: centerY + Math.sin(label.midAngle * Math.PI / 180) * (pieRadius + 10),
      }))
      .sort((a, b) => a.y - b.y);

    for (let i = 1; i < labelData.length; i++) {
      if (labelData[i].y < labelData[i - 1].y + minGap) {
        labelData[i].y = labelData[i - 1].y + minGap;
      }
    }

    for (let i = labelData.length - 2; i >= 0; i--) {
      if (labelData[i].y > labelData[i + 1].y - minGap) {
        labelData[i].y = labelData[i + 1].y - minGap;
      }
    }

    return labelData.map(data => {
      const startRadius = pieRadius * 0.8;
      const lineStartX = centerX + startRadius * Math.cos(data.midAngle * Math.PI / 180);
      const lineStartY = centerY + startRadius * Math.sin(data.midAngle * Math.PI / 180);

      const elbowX = centerX + (isRight ? 1 : -1) * (pieRadius + 2);
      const horizontalLineEndX = elbowX + (isRight ? 1 : -1) * 8;

      const path = `M ${lineStartX} ${lineStartY} L ${elbowX} ${data.y} H ${horizontalLineEndX}`;

      return (
        <g key={data.name + "-label"}>
                <path 
            d={path}
            fill="none"
            stroke={data.color}
            strokeWidth="1.2"
                />
          <circle cx={lineStartX} cy={lineStartY} r="2" fill={data.color} />
                    <text 
            x={horizontalLineEndX + (isRight ? 4 : -4)}
            y={data.y}
            dy="0.35em"
            textAnchor={isRight ? "start" : "end"}
            fontSize="10"
            fontWeight="500"
            fill="#4B5563"
          >
            {`${data.name} (${data.count}人)`}
                    </text>
        </g>
      );
    });
  };

  const rightSideLabels = statusData.filter(d => (d.midAngle > -90 && d.midAngle < 90));
  const leftSideLabels = statusData.filter(d => !(d.midAngle > -90 && d.midAngle < 90));

  return (
    <div className="relative mx-auto w-80 h-80 flex items-center justify-center">
      <svg viewBox={`0 0 ${viewboxSize} ${viewboxSize}`} className="w-full h-full">
        {totalStudents === 0 ? (
          <circle
            cx={centerX}
            cy={centerY}
            r={pieRadius}
            fill="#E5E7EB"
            stroke="white"
            strokeWidth="2"
          />
        ) : hasSingleStatus && singleStatus ? (
          <g>
            <circle
              cx={centerX}
              cy={centerY}
              r={pieRadius}
              fill={singleStatus.color}
              stroke="white"
              strokeWidth="2"
            />
            {/* 为单状态添加曲线引导线 */}
            <path 
              d={`M ${centerX + pieRadius * 0.6} ${centerY - pieRadius * 0.6} 
                  C ${centerX + pieRadius * 0.75} ${centerY - pieRadius * 0.7}, 
                    ${centerX + pieRadius * 0.9} ${centerY - pieRadius * 0.8}, 
                    ${centerX + pieRadius * 1.05} ${centerY - pieRadius * 0.9}`}
              fill="none"
              stroke={singleStatus.color}
              strokeWidth="1.2"
            />
            <circle cx={centerX + pieRadius * 0.6} cy={centerY - pieRadius * 0.6} r="2" fill={singleStatus.color} />
            <text 
              x={centerX + pieRadius * 1.1}
              y={centerY - pieRadius * 0.9}
              dy="0.35em"
              textAnchor="start"
              fontSize="10"
              fontWeight="500"
              fill="#4B5563"
            >
              {`${singleStatus.name} (${singleStatus.count}人)`}
            </text>
          </g>
        ) : (
              <>
            {statusData.map((data) => (
                <path 
                  key={data.name}
                  d={(() => {
                    const startRad = data.startAngle * Math.PI / 180;
                    const endRad = data.endAngle * Math.PI / 180;
                    const startX = centerX + pieRadius * Math.cos(startRad);
                    const startY = centerY + pieRadius * Math.sin(startRad);
                    const endX = centerX + pieRadius * Math.cos(endRad);
                    const endY = centerY + pieRadius * Math.sin(endRad);
                    const largeArcFlag = data.angle > 180 ? 1 : 0;
                    return `M ${centerX},${centerY} L ${startX},${startY} A ${pieRadius},${pieRadius} 0 ${largeArcFlag} 1 ${endX},${endY} Z`;
                  })()}
                  fill={data.color}
                  stroke="white"
                  strokeWidth="1.5"
                />
            ))}
            {renderLabels(rightSideLabels, true)}
            {renderLabels(leftSideLabels, false)}
                  </>
                )}
        <circle cx={centerX} cy={centerY} r={pieRadius * 0.65} fill="white" />
        <text x={centerX} y={centerY - 5} textAnchor="middle" fontSize="20" fontWeight="bold" fill={statusColors.border}>
          {totalStudents}
        </text>
        <text x={centerX} y={centerY + 10} textAnchor="middle" fontSize="10" fill="#6B7280">
          总人数
        </text>
      </svg>
    </div>
  );
};

export const TaskDetailModal: React.FC<TaskDetailModalProps> = ({
  task: initialTask,
  visible,
  onClose,
  students,
  onRefresh,
  currentClassId
}) => {
  const notification = GetNotification();
  
  const [gradeForm] = Form.useForm();
  const [gradeModalVisible, setGradeModalVisible] = useState(false);
  const [currentAssignment, setCurrentAssignment] = useState<Assignment | null>(null);
  const [publishedWorks, setPublishedWorks] = useState<Set<number>>(new Set());
  const [downloadedAssignments, setDownloadedAssignments] = useState<Set<number>>(new Set());
  const [downloadingAssignments, setDownloadingAssignments] = useState<Set<number>>(new Set());
  const [hasDownloadableContent, setHasDownloadableContent] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [activeTabKey, setActiveTabKey] = useState<string>('completed');
  const [task, setTask] = useState<Task | null>(initialTask);
  const [detailedSubmissions, setDetailedSubmissions] = useState<Record<number, any[]>>({});
  const [loadingSubmissions, setLoadingSubmissions] = useState<Record<number, boolean>>({});
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string>('completed'); // Changed to non-nullable string
  const [isLoadingTask, setIsLoadingTask] = useState(false);
  const descriptionRef = useRef<HTMLParagraphElement>(null);
  const hasMultipleLines = useRef(false);
  const lineCount = useRef(0);
  const isLongDescription = useRef(false);
  
  // 添加用于自评项目的状态
  const [selectedAssessmentItem, setSelectedAssessmentItem] = useState<any>(null);
  
  // 在组件顶层声明所有 Hook，确保每次渲染调用数量一致
  const [assignmentPreviews, setAssignmentPreviews] = useState<Record<number, PreviewData | null>>({});
  const [loadingPreviews, setLoadingPreviews] = useState<Record<number, boolean>>({});
  
  // 新增：用于提交内容模态框的状态
  const [submissionModalVisible, setSubmissionModalVisible] = useState(false);
  const [currentSubmission, setCurrentSubmission] = useState<any>(null);
  
  // 定义预览数据类型
  type PreviewData = {
    type: 'work' | 'image' | 'files' | 'none';
    url?: string;
    fileCount?: number;
    isScratchWork?: boolean;
  };
  
  // 修改API用于获取作业预览图 - 确保先定义这个函数
  const getAssignmentPreview = async (assignmentId: number) => {
    try {
      const response = await taskApi.getStudentWork(assignmentId);
      if (response.data.code === 200 && response.data.data) {
        const submission = response.data.data;
        
        // 返回带有类型信息的对象，而不仅仅是URL
        const result: PreviewData = { type: 'none' };
        
        // 定义缩略图尺寸
        const thumbnailWidth = 185;
        const thumbnailHeight = 112;

        // 检查是否是Scratch作品
        if (submission.workInfo?.workId) {
          // 如果有作品ID，说明可能是scratch作品
          try {
            const workDetailResponse = await worksApi.getDetail(submission.workInfo.workId);
                         const workType = workDetailResponse?.data?.data?.type;
             if (workType === 1 || workType === 'scratch') {
               result.type = 'work';
               result.isScratchWork = true;
               if (submission.workInfo?.coverImage) {
                 result.url = ossImageProcessor.getHDFillThumbnail(submission.workInfo.coverImage, thumbnailWidth, thumbnailHeight);
               }
               return result;
             }
          } catch (err) {
            // 获取作品类型失败，继续后续判断
            console.error("获取作品类型失败", err);
          }
        }
        
        // 优先返回作品封面图
        if (submission.workInfo?.coverImage) {
          result.type = 'work';
          result.url = ossImageProcessor.getHDFillThumbnail(submission.workInfo.coverImage, thumbnailWidth, thumbnailHeight);
          return result;
        }
        
        // 其次返回提交图片
        if (submission.imageInfo?.backupImagePath || submission.imageInfo?.originalImagePath) {
          result.type = 'image';
          result.url = ossImageProcessor.getHDFillThumbnail(submission.imageInfo.backupImagePath || submission.imageInfo.originalImagePath, thumbnailWidth, thumbnailHeight);
          return result;
        }
        
        // 最后检查是否有图片文件
        if (submission.submissionFiles?.length > 0) {
          let hasImageFile = false;
          for (const fileUrl of submission.submissionFiles) {
            const fileExt = fileUrl.split('.').pop()?.toLowerCase() || '';
            if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
              result.type = submission.submissionFiles.length > 1 ? 'files' : 'image';
              result.url = ossImageProcessor.getHDFillThumbnail(fileUrl, thumbnailWidth, thumbnailHeight);
              result.fileCount = submission.submissionFiles.length;
              hasImageFile = true;
              break;
            }
          }
          
          // 如果没有图片文件但有其他文件
          if (!hasImageFile && submission.submissionFiles.length > 0) {
            result.type = 'files';
            result.fileCount = submission.submissionFiles.length;
          }
          
          return result;
        }
      }
      return { type: 'none' as const };
    } catch (error) {
      console.error("获取作业预览图失败", error);
      return { type: 'none' as const };
    }
  };

  // 改为使用 useCallback 包装函数，避免不必要的重新创建
  const loadAssignmentPreview = useCallback(async (assignmentId: number) => {
    // 如果已经在加载或已有缓存，则跳过
    if (loadingPreviews[assignmentId] || assignmentPreviews[assignmentId] !== undefined) {
      return;
    }

    setLoadingPreviews(prev => ({ ...prev, [assignmentId]: true }));
    try {
      const previewData = await getAssignmentPreview(assignmentId);
      setAssignmentPreviews(prev => {
        const newPreviews = { ...prev };
        newPreviews[assignmentId] = previewData;
        return newPreviews;
      });
    } catch (error) {
      console.error(`加载预览图失败: ${assignmentId}`, error);
      setAssignmentPreviews(prev => {
        const newPreviews = { ...prev };
        newPreviews[assignmentId] = { type: 'none' as const };
        return newPreviews;
      });
    } finally {
      setLoadingPreviews(prev => ({ ...prev, [assignmentId]: false }));
    }
  }, [loadingPreviews, assignmentPreviews]);

  useEffect(() => {
    const fetchTaskDetails = async () => {
      if (!initialTask?.id) {
        setTask(initialTask);
        return;
      }
      
      setIsLoadingTask(true);
      try {
        const response = await taskApi.getTaskDetail(initialTask.id);
        if (response?.data?.code === 200 && response.data.data) {
          console.log('Task details fetched:', response.data.data);
          setTask(response.data.data);
          
          // Set selected status to "completed" by default
          setSelectedStatus('completed');
        } else {
          console.warn('Failed to fetch task details, using initial data:', initialTask);
          setTask(initialTask);
          notification.warning('获取任务详情失败');
        }
      } catch (error) {
        console.error("获取任务详情失败", error);
        setTask(initialTask);
        notification.error('获取任务详情失败');
      } finally {
        setIsLoadingTask(false);
      }
    };

    if (visible) {
      fetchTaskDetails();
      // Reset states when opening the modal
      setDetailedSubmissions({});
      setLoadingSubmissions({});
      setIsDescriptionExpanded(false);
      
      // Set selected status to "completed" by default when opening the modal
      setSelectedStatus('completed');
    }
  }, [visible, initialTask, notification]);

  // 检查任务描述是否有多行
  useEffect(() => {
    if (descriptionRef.current && visible) {
      const element = descriptionRef.current;
      const text = element.textContent || '';
      
      // 检查文本长度
      isLongDescription.current = (text.length > 100);
      
      // 创建一个临时元素用于测量
      const tempElement = document.createElement('p');
      tempElement.style.width = `${element.clientWidth}px`;
      tempElement.style.position = 'absolute';
      tempElement.style.visibility = 'hidden';
      tempElement.style.whiteSpace = 'normal';
      tempElement.style.wordBreak = 'break-word';
      tempElement.style.fontSize = window.getComputedStyle(element).fontSize;
      tempElement.style.fontFamily = window.getComputedStyle(element).fontFamily;
      tempElement.style.lineHeight = window.getComputedStyle(element).lineHeight;
      tempElement.style.margin = '0';
      tempElement.style.padding = '0';
      tempElement.textContent = text;
      document.body.appendChild(tempElement);
      
      // 获取行高
      const lineHeight = parseInt(window.getComputedStyle(element).lineHeight) || 20;
      
      // 计算行数
      const height = tempElement.clientHeight;
      const calculatedLineCount = Math.ceil(height / lineHeight);
      lineCount.current = calculatedLineCount;
      
      // 如果超过2行，则显示展开/收起按钮
      hasMultipleLines.current = calculatedLineCount > 2;
      
      // 移除临时元素
      document.body.removeChild(tempElement);

      console.log('Task description line count:', calculatedLineCount, 'length:', text.length);
    }
  }, [visible, task?.taskDescription]);

  // 保存下载状态，以便在刷新后恢复
  useEffect(() => {
    // 当任务列表发生变化时，保持已下载的状态
    const savedDownloadedAssignments = localStorage.getItem('downloadedAssignments');
    if (savedDownloadedAssignments) {
      setDownloadedAssignments(new Set(JSON.parse(savedDownloadedAssignments)));
    }
  }, []);

  // 保存下载状态到本地存储
  useEffect(() => {
    if (downloadedAssignments.size > 0) {
      localStorage.setItem('downloadedAssignments',
        JSON.stringify(Array.from(downloadedAssignments)));
    }
  }, [downloadedAssignments]);

  // 添加检查作品发布状态的函数
  const checkPublishedStatus = async () => {
    if (!task?.assignments) return;

    const publishedSet = new Set<number>();
    for (const assignment of task.assignments as Assignment[]) {
      if (assignment.workId) {
        try {
          const response = await worksApi.getDetail(assignment.workId);
          if (response?.data.code === 200 && response.data.data) {
            if (response.data.data.isPublishedToClass === 1) {
              publishedSet.add(assignment.id);
            }
          }
        } catch (error) {
          handleError('获取作品状态失败:', error);
        }
      }
    }
    setPublishedWorks(publishedSet);
  };

  // 在弹窗显示时检查发布状态
  useEffect(() => {
    const checkPublishedStatusIfNeeded = () => {
      if (visible && task) {
        checkPublishedStatus();
      }
    };
    
    checkPublishedStatusIfNeeded();
  }, [visible, task]);

  // 检查是否有可下载的内容
  useEffect(() => {
    const checkDownloadableContentAsync = async () => {
      if (!task?.assignments) return;

      const completedAssignments = task.assignments.filter(a => a.taskStatus === 2);
      if (completedAssignments.length === 0) {
        setHasDownloadableContent(false);
        return;
      }

      // 检查至少一个学生是否有可下载内容
      let hasContent = false;
      for (const assignment of completedAssignments) {
        try {
          const response = await taskApi.getStudentWork(assignment.id);
          console.log(response);

          if (response.data.code !== 200) continue;

          const submission = response.data.data;

          // 检查是否有图片或可下载文件
          if (submission.imageInfo?.backupImagePath || submission.imageInfo?.originalImagePath) {
            hasContent = true;
            break;
          }

          if (submission.workInfo?.coverImage) {
            hasContent = true;
            break;
          }

          if (submission.submissionFiles?.length > 0) {
            for (const fileUrl of submission.submissionFiles) {
              const fileExt = fileUrl.split('.').pop()?.toLowerCase() || '';
              if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
                hasContent = true;
                break;
              }
            }
            if (hasContent) break;
          }
        } catch {
          continue;
        }
      }

      setHasDownloadableContent(hasContent);
    };

    if (visible && task) {
      checkDownloadableContentAsync();
    }
  }, [visible, task]);

  // Ensure we load completed students data when the component mounts
  useEffect(() => {
    const loadCompletedStudentsData = () => {
      if (visible && selectedStatus === 'completed' && task) {
        // If we have task data and the modal is visible, ensure completed students are shown
        console.log('Showing completed students by default');
      }
    };
    
    loadCompletedStudentsData();
  }, [visible, task, selectedStatus]);

  // Add useEffect to load previews for completed assignments
  useEffect(() => {
    const loadPreviewsForCompletedAssignments = () => {
      if (visible && task?.assignments && (selectedStatus === 'completed' || selectedStatus === 'returned')) {
        const assignmentsToLoad = selectedStatus === 'completed' 
          ? task.assignments.filter(a => a.taskStatus === 2)
          : task.assignments.filter(a => a.taskStatus === 4);
          
        console.log(`Loading previews for ${selectedStatus} assignments:`, assignmentsToLoad.length);
        
        assignmentsToLoad.forEach(assignment => {
          loadAssignmentPreview(assignment.id);
        });
      }
    };
    
    loadPreviewsForCompletedAssignments();
  }, [visible, task, selectedStatus, loadAssignmentPreview]);

  if (!task) return null;

  const completedStudents = task.assignments?.filter(a => a.taskStatus === 2) || [];
  const inProgressStudents = task.assignments?.filter(a => a.taskStatus === 1 || a.taskStatus === 4) || [];
  const actualInProgress = task.assignments?.filter(a => a.taskStatus === 1) || [];
  const returnedStudents = task.assignments?.filter(a => a.taskStatus === 4) || [];
  const notStartedStudents = task.assignments?.filter(a => a.taskStatus === 0) || [];
  const expiredStudents = task.assignments?.filter(a => a.taskStatus === 3) || [];

  // 从students列表中获取学生信息的函数
  const getStudentName = (studentId: number) => {
    const student = students.find(s => s.userId === studentId);
    return student ? student.nickName : `未知学生(${studentId})`;
  };

  const getStudentNumber = (studentId: number) => {
    const student = students.find(s => s.userId === studentId);
    return student ? student.studentNumber : `${studentId}`;
  };

  // 修改查看作品的处理函数
  const handleViewWork = async (workId: number) => {
    try {
      const loadingKey = 'viewWork';
      const loadingMessage = notification.loading('正在加载作品...');
      const response = await worksApi.getDetail(workId);

      if (response?.data?.code === 200 && response.data.data) {
        const work = response.data.data;
        viewWork({
          content: work.content,
          workId: workId,
          userId: work.userId
        });
        notification.success('加载成功');
      }
      if (loadingMessage) {
        loadingMessage.close();
      }
    } catch (error) {
      handleError('获取作品失败:', error);
      notification.error('加载作品失败');
    }
  };

  // 评分
  const handleGrade = async (values: { score: number; feedback?: string }) => {
    if (!currentAssignment) {
      notification.error('未选择要评分的作业');
      return;
    }

    try {
      const response = await taskApi.gradeTask({
        assignmentId: currentAssignment.id,
        score: values.score,
        feedback: values.feedback
      });
      if (response.data.code === 200) {
        notification.success('评分成功');
        setGradeModalVisible(false);
        // 更新当前任务中的作业状态
        if (task && task.assignments) {
          const updatedAssignments = task.assignments.map(a =>
            a.id === currentAssignment.id
              ? { ...a, taskScore: values.score, feedback: values.feedback }
              : a
          );
          task.assignments = updatedAssignments;
        }
        onRefresh();
      }
    } catch (error) {
      handleError('评分失败', error);
    }
  };

  // 打回修改
  const handleReturnForRevision = async (assignment: any) => {
    Modal.confirm({
      centered: true,
      title: '打回修改',
      content: (
        <div className="py-4">
          <p className="mb-3 text-gray-600">确定要将该作业打回修改吗？</p>
          <Input.TextArea
            placeholder="请输入修改意见"
            rows={4}
            onChange={(e) => {
              (Modal.confirm as any).feedback = e.target.value;
            }}
          />
        </div>
      ),
      width: 500,
      style: {
        maxWidth: '90vw',
        margin: '10vh auto',
        padding: 0,
        top: 0
      },
      bodyStyle: {
        padding: '20px'
      },
      onOk: async () => {
        const feedback = (Modal.confirm as any).feedback;
        if (!feedback) {
          notification.error('请输入修改意见');
          return Promise.reject();
        }
        try {
          const response = await taskApi.returnForRevision({
            assignmentId: assignment.id,
            feedback
          });
          if (response.data.code === 200) {
            notification.success('已打回修改');

            // 更新本地任务状态
            if (task && task.assignments) {
              const updatedAssignments = task.assignments.map(a =>
                a.id === assignment.id
                  ? { ...a, taskStatus: 4, feedback, taskScore: undefined }
                  : a
              );

              // 更新状态，确保视图立即刷新
              task.assignments = updatedAssignments;

              // 重新计算各状态学生数量
              const newReturnedStudents = updatedAssignments.filter(a => a.taskStatus === 4);

              // 如果当前在已完成页面，自动切换到进行中/已打回页面并高亮提示
              if (activeTabKey === 'completed') {
                setActiveTabKey('inProgress');
              }
            }

            onRefresh();
          }
        } catch (error) {
          notification.error('操作失败');
        }
      }
    });
  };

  // 修改查看提交内容的函数
  const handleViewSubmission = async (assignment: any) => {
    try {
      const response = await taskApi.getStudentWork(assignment.id);
      console.log('获取到的提交详情:', response.data);

      if (response.data.code !== 200) {
        throw new Error('获取提交内容失败');
      }

      const submission = response.data.data;

      // 设置当前提交内容并显示模态框
      setCurrentSubmission(submission);
      setSubmissionModalVisible(true);
    } catch (error) {
      handleError('查看提交内容失败:', error);
      notification.error('查看提交内容失败');
    }
  };

  // 下载所有图片为ZIP压缩包
  const downloadAllImagesAsZip = async () => {
    if (!task || !task.assignments) return;

    setIsDownloading(true);

    try {
      const zip = new JSZip();
      const completedAssignments = task.assignments.filter(a => a.taskStatus === 2);
      let totalImages = 0;

      for (const assignment of completedAssignments) {
        try {
          const response = await taskApi.getStudentWork(assignment.id);
          if (response.data.code !== 200) continue;

          const submission = response.data.data;
          const studentName = getStudentName(assignment.studentId);
          const studentFolder = zip.folder(studentName);

          if (!studentFolder) continue;

          // 添加图片到ZIP文件
          let studentImageCount = 0;

          // 处理主图片
          if (submission.imageInfo?.backupImagePath || submission.imageInfo?.originalImagePath) {
            const imageUrl = submission.imageInfo.backupImagePath || submission.imageInfo.originalImagePath;
            const fileName = imageUrl.split('/').pop() || '未命名图片.jpg';

            try {
              const response = await fetch(imageUrl);
              const blob = await response.blob();
              studentFolder.file(fileName, blob);
              studentImageCount++;
            } catch {
              // 单个图片下载失败不应阻止整个过程
            }
          }

          // 处理作品封面图
          if (submission.workInfo?.coverImage) {
            const imageUrl = submission.workInfo.coverImage;
            const fileName = `作品封面_${imageUrl.split('/').pop() || '未命名图片.jpg'}`;

            try {
              const response = await fetch(imageUrl);
              const blob = await response.blob();
              studentFolder.file(fileName, blob);
              studentImageCount++;
            } catch {
              // 单个图片下载失败不应阻止整个过程
            }
          }

          // 处理提交文件中的图片
          if (submission.submissionFiles?.length > 0) {
            for (const fileUrl of submission.submissionFiles) {
              const fileExt = fileUrl.split('.').pop()?.toLowerCase() || '';
              if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
                const fileName = fileUrl.split('/').pop() || '未命名图片.jpg';

                try {
                  const response = await fetch(fileUrl);
                  const blob = await response.blob();
                  studentFolder.file(fileName, blob);
                  studentImageCount++;
                } catch {
                  // 单个图片下载失败不应阻止整个过程
                }
              }
            }
          }

          if (studentImageCount > 0) {
            totalImages += studentImageCount;
            setDownloadedAssignments(prev => new Set(Array.from(prev).concat([assignment.id])));
          }
        } catch {
          // 单个学生的处理失败不应阻止整个过程
          continue;
        }
      }

      if (totalImages > 0) {
        // 生成并下载ZIP文件
        const content = await zip.generateAsync({ type: 'blob' });
        saveAs(content, `${task.taskName || '任务'}_学生作品图片.zip`);
        notification.success(`已将${totalImages}张图片打包下载完成`);
      } else {
        notification.info('没有找到可下载的图片');
      }
    } catch (error) {
      handleError('下载过程中发生错误', error);
    } finally {
      setIsDownloading(false);
    }
  };

  const handlePanelChange = async (key: string | string[]) => {
    const panelKey = Array.isArray(key) ? key[key.length - 1] : key;
    if (!panelKey) return;

    const itemId = Number(panelKey);
    if (detailedSubmissions[itemId]) {
      return; 
    }

    setLoadingSubmissions(prev => ({ ...prev, [itemId]: true }));
    try {
      const response = await studentSelfAssessmentSubmissionApi.getSubmissionsByItemId(itemId);
      if (response.data?.code === 200 && response.data.data) {
        setDetailedSubmissions(prev => ({ ...prev, [itemId]: response.data.data }));
      } else {
        throw new Error(response.data?.message || '获取自评详情失败');
      }
    } catch (error) {
      console.error("获取自评详情失败", error);
      notification.error("获取自评详情失败");
      // 出现错误时，设置为空数组，防止界面崩溃
      setDetailedSubmissions(prev => ({ ...prev, [itemId]: [] }));
    } finally {
      setLoadingSubmissions(prev => ({ ...prev, [itemId]: false }));
    }
  };

  // 修改单个学生下载函数以适配不同类型的提交内容
  const downloadStudentImages = async (assignment: Assignment) => {
    if (downloadingAssignments.has(assignment.id)) return;

    setDownloadingAssignments(prev => new Set(Array.from(prev).concat([assignment.id])));

    try {
      const response = await taskApi.getStudentWork(assignment.id);
      if (response.data.code !== 200) {
        throw new Error('获取学生作品失败');
      }

      const submission = response.data.data;
      const studentName = getStudentName(assignment.studentId);

      // 如果是作品项目提交
      if (submission.workInfo?.content) {
        try {
          // 下载项目内容
          const workContent = submission.workInfo.content;
          const workTitle = submission.workInfo.title || '未命名作品';
          
          // 创建Blob并下载
          const blob = new Blob([workContent], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${studentName}_${workTitle}.json`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
          
          setDownloadedAssignments(prev => new Set(Array.from(prev).concat([assignment.id])));
          notification.success(`已下载作品项目: ${workTitle}`);
          return;
        } catch (error) {
          console.error('下载项目内容失败', error);
        }
      }

      // 如果是文件提交
      if (submission.submissionFiles?.length > 0) {
        // 检查是否有非图片文件
        const hasNonImageFiles = submission.submissionFiles.some((fileUrl: string) => {
          const fileExt = fileUrl.split('.').pop()?.toLowerCase() || '';
          return !['jpg', 'jpeg', 'png', 'gif'].includes(fileExt);
        });

        // 如果有非图片文件，优先下载文件
        if (hasNonImageFiles) {
          // 如果只有一个文件，直接下载
          if (submission.submissionFiles.length === 1) {
            const fileUrl = submission.submissionFiles[0];
            const fileName = fileUrl.split('/').pop() || '未命名文件';
            
            const link = document.createElement('a');
            link.href = fileUrl;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            setDownloadedAssignments(prev => new Set(Array.from(prev).concat([assignment.id])));
            notification.success(`已下载文件: ${fileName}`);
            return;
          } 
          // 有多个文件，创建ZIP
          else {
            // 创建ZIP文件
            const zip = new JSZip();
            
            for (let i = 0; i < submission.submissionFiles.length; i++) {
              const fileUrl = submission.submissionFiles[i];
              const fileName = fileUrl.split('/').pop() || `未命名文件${i+1}`;

              try {
                const response = await fetch(fileUrl);
                const blob = await response.blob();
                zip.file(fileName, blob);
              } catch (error) {
                console.error(`下载文件 ${fileName} 失败`, error);
              }
            }

            // 生成并下载ZIP文件
            const content = await zip.generateAsync({ type: 'blob' });
            saveAs(content, `${studentName}_提交文件.zip`);
            
            setDownloadedAssignments(prev => new Set(Array.from(prev).concat([assignment.id])));
            notification.success(`已打包下载 ${submission.submissionFiles.length} 个文件`);
            return;
          }
        }
      }

      // 如果是图片提交或者没有检测到项目/文件
      const imageUrls: string[] = [];

      // 收集所有图片URL
      if (submission.imageInfo?.backupImagePath || submission.imageInfo?.originalImagePath) {
        imageUrls.push(submission.imageInfo.backupImagePath || submission.imageInfo.originalImagePath);
      }

      if (submission.workInfo?.coverImage) {
        imageUrls.push(submission.workInfo.coverImage);
      }

      // 收集提交文件中的图片
      if (submission.submissionFiles?.length > 0) {
        submission.submissionFiles.forEach((fileUrl: string) => {
          const fileExt = fileUrl.split('.').pop()?.toLowerCase() || '';
          if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
            imageUrls.push(fileUrl);
          }
        });
      }

      if (imageUrls.length > 0) {
        // 下载单张图片
        if (imageUrls.length === 1) {
          const imageUrl = imageUrls[0];
          const fileName = `${studentName}_${imageUrl.split('/').pop() || '图片.jpg'}`;
          
          const link = document.createElement('a');
          link.href = imageUrl;
          link.download = fileName;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          
          setDownloadedAssignments(prev => new Set(Array.from(prev).concat([assignment.id])));
          notification.success(`已下载图片: ${fileName}`);
        }
        // 创建ZIP文件下载多张图片
        else {
          const zip = new JSZip();

          for (let i = 0; i < imageUrls.length; i++) {
            const imageUrl = imageUrls[i];
            const fileName = `${i + 1}_${imageUrl.split('/').pop() || '未命名图片.jpg'}`;

            try {
              const response = await fetch(imageUrl);
              const blob = await response.blob();
              zip.file(fileName, blob);
            } catch (error) {
              console.error(`下载图片 ${fileName} 失败`, error);
            }
          }

          // 生成并下载ZIP文件
          const content = await zip.generateAsync({ type: 'blob' });
          saveAs(content, `${studentName}_作品图片.zip`);
          
          setDownloadedAssignments(prev => new Set(Array.from(prev).concat([assignment.id])));
          notification.success(`已打包下载 ${imageUrls.length} 张图片`);
        }
      } else {
        notification.info('没有找到可下载的内容');
      }
    } catch (error) {
      handleError('下载失败', error);
    } finally {
      setDownloadingAssignments(prev => {
        const newSet = new Set(prev);
        newSet.delete(assignment.id);
        return newSet;
      });
    }
  };

  // 处理错误但不丢失错误信息
  const handleError = (errorMsg: string, error?: unknown) => {
    console.error(errorMsg, error);
    notification.error(errorMsg);
  };

  // 计算各状态的百分比
  const calculatePercentage = (count: number, total: number) => {
    if (total === 0) return 0;
    return Math.round((count / total) * 100);
  };

  // 确保我们有有效的学生数据
  const validCompletedStudents = Array.isArray(completedStudents) ? completedStudents : [];
  const validInProgressStudents = Array.isArray(actualInProgress) ? actualInProgress : [];
  const validReturnedStudents = Array.isArray(returnedStudents) ? returnedStudents : [];
  const validNotStartedStudents = Array.isArray(notStartedStudents) ? notStartedStudents : [];
  const validExpiredStudents = Array.isArray(expiredStudents) ? expiredStudents : [];

  const totalStudents = validCompletedStudents.length + validInProgressStudents.length + 
    validReturnedStudents.length + validNotStartedStudents.length + validExpiredStudents.length;

  const completedPercentage = calculatePercentage(validCompletedStudents.length, totalStudents);
  const inProgressPercentage = calculatePercentage(validInProgressStudents.length, totalStudents);
  const returnedPercentage = calculatePercentage(validReturnedStudents.length, totalStudents);
  const notStartedPercentage = calculatePercentage(validNotStartedStudents.length, totalStudents);
  const expiredPercentage = calculatePercentage(validExpiredStudents.length, totalStudents);
  
  // 设置各状态的颜色 - 与左侧按钮颜色保持一致
  const statusColors = {
    completed: "#3B82F6",  // 蓝色 - 已完成（对应 bg-blue-500）
    inProgress: "#FBBF24", // 黄色 - 进行中（对应 bg-amber-400）
    returned: "#F97316",   // 橙色 - 已打回（对应 bg-orange-500）
    notStarted: "#10B981", // 绿色 - 未开始（对应 bg-emerald-500）
    expired: "#EF4444",    // 红色 - 已过期（对应 bg-red-500）
    border: "#374151"      // 深灰色边框（对应 bg-gray-700）
  };

  // 修改预加载作品图片的函数
  const preloadAssignmentImages = async () => {
    if (!task?.assignments) return;
    
    // 预先加载已完成和进行中的学生作业图片
    const assignmentsToPreload = task.assignments.filter(a => 
      a.taskStatus === 2 || a.taskStatus === 1 || a.taskStatus === 4
    ).slice(0, 8); // 限制预加载数量，避免过多请求
    
    for (const assignment of assignmentsToPreload) {
      try {
        const response = await taskApi.getStudentWork(assignment.id);
        if (response.data.code === 200 && response.data.data) {
          const submission = response.data.data;
          if (submission.workInfo?.coverImage || 
              submission.imageInfo?.backupImagePath || 
              submission.imageInfo?.originalImagePath) {
            // 标记为已获取图片
            setDownloadedAssignments(prev => new Set([...Array.from(prev), assignment.id]));
          }
        }
      } catch (error) {
        console.error("预加载图片失败", error);
      }
    }
  };

  // 筛选显示有预览图的已完成学生作业
  const getAssignmentsWithPreview = (assignments: Assignment[]) => {
    if (!assignments) return [];
    // Return all assignments instead of filtering only those with previews
    return assignments;
  };

  // 获取学生自评项目详情
  const fetchAssessmentItemDetails = async (itemId: number) => {
    if (loadingSubmissions[itemId] || detailedSubmissions[itemId]) {
      return detailedSubmissions[itemId];
    }

    setLoadingSubmissions(prev => ({ ...prev, [itemId]: true }));
    try {
      const response = await studentSelfAssessmentSubmissionApi.getSubmissionsByItemId(itemId);
      if (response.data?.code === 200 && response.data.data) {
        const submissions = response.data.data;
        setDetailedSubmissions(prev => ({ ...prev, [itemId]: submissions }));
        return submissions;
      } else {
        throw new Error(response.data?.message || '获取自评详情失败');
      }
    } catch (error) {
      console.error("获取自评详情失败", error);
      notification.error("获取自评详情失败");
      // 出现错误时，设置为空数组，防止界面崩溃
      setDetailedSubmissions(prev => ({ ...prev, [itemId]: [] }));
      return [];
    } finally {
      setLoadingSubmissions(prev => ({ ...prev, [itemId]: false }));
    }
  };

  // 处理点击自评项目
  const handleAssessmentItemClick = async (item: any) => {
    // 已选中则取消选择
    if (selectedAssessmentItem?.id === item.id) {
      setSelectedAssessmentItem(null);
      return;
    }
    
    setSelectedAssessmentItem(item);
    
    // 获取详情
    if (!detailedSubmissions[item.id]) {
      await fetchAssessmentItemDetails(item.id);
    }
  };

  // 渲染评分星星
  const renderStars = (score: number) => {
    return (
      <div className="flex">
        {Array(score)
          .fill(0)
          .map((_, i) => (
            <span key={i} className="text-amber-400">★</span>
          ))}
        {Array(5 - score)
          .fill(0)
          .map((_, i) => (
            <span key={i} className="text-gray-200">★</span>
          ))}
      </div>
    );
  };

  // 获取头像缩略图的函数
  const getAvatarThumbnail = (url: string, size: number = 24) => {
    if (!url) return undefined;

    try {
      // 使用OSS图片处理器获取头像缩略图
      return ossImageProcessor.getAvatarThumbnail(url, size, 85);
    } catch (error) {
      console.error('获取头像缩略图失败:', error);
      return url;
    }
  };

  // Add the new function after handleViewSubmission

  // 直接查看作品的函数
  const handleDirectViewWork = async (assignment: any) => {
    try {
      // 先检查预览类型，如果已经知道是图片类型，直接展示提交内容
      if (assignmentPreviews[assignment.id]?.type === 'image') {
        const response = await taskApi.getStudentWork(assignment.id);
        if (response.data.code === 200) {
          setCurrentSubmission(response.data.data);
          setSubmissionModalVisible(true);
          return;
        }
      }
      
      if (assignment.workId) {
        // 如果有workId，直接查看作品
        await handleViewWork(assignment.workId);
      } else {
        // 如果没有workId，回退到查看提交内容
        const response = await taskApi.getStudentWork(assignment.id);
        
        if (response.data.code !== 200) {
          throw new Error('获取提交内容失败');
        }
        
        const submission = response.data.data;
        
        // 检查是否是图片提交
        if (submission.submissionType === 'image' || 
            (!submission.workInfo && submission.imageInfo)) {
          // 如果是图片提交，直接显示提交内容模态框
          setCurrentSubmission(submission);
          setSubmissionModalVisible(true);
        }
        // 如果提交包含作品信息和workId，查看作品
        else if (submission.workInfo?.workId) {
          await handleViewWork(submission.workInfo.workId);
        } else {
          // 否则显示提交内容模态框
          setCurrentSubmission(submission);
          setSubmissionModalVisible(true);
        }
      }
    } catch (error) {
      handleError('查看作品失败:', error);
      notification.error('查看作品失败');
    }
  };

  return (
    <>
      <Modal
        title={null}
        open={visible}
        onCancel={onClose}
        footer={null}
        centered
        width={1200}
        className="task-detail-modal"
        closeIcon={null}
        styles={{
          mask: {
            backgroundColor: 'rgba(0, 0, 0, 0.2)',
            backdropFilter: 'blur(4px)'
          },
          content: {
            borderRadius: '24px',
            padding: '24px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
            overflow: 'hidden',
            border: 'none',
            background: '#ffffff',
            height: '80vh',
            width: '1200px',
            margin: '0 auto'
          },
          body: {
            padding: '0',
            height: '100%',
            overflow: 'hidden'
          },
          header: {
            display: 'none'
          }
        }}
      >
        <div className="h-full flex flex-col relative pt-6">
          {/* Close button - absolute positioned in top-right corner */}
          <Button 
            type="text" 
            className="absolute top-4 right-4 z-10" 
            icon={<CloseOutlined className="text-gray-500 hover:text-gray-700 transition-colors" />} 
            onClick={onClose}
          />
          
          <div className="flex flex-1 h-full overflow-hidden flex-col lg:flex-row mt-4">
              {/* Left content area - task description and student information */}
              <div className="w-full lg:w-2/3 lg:pr-6 flex flex-col h-full overflow-hidden">
                {/* Title and date display */}
                <div className="flex justify-between items-center mb-4 flex-shrink-0">
                  {isLoadingTask ? (
                    <div className="w-full">
                      <div className="h-8 bg-gray-200 rounded animate-pulse mb-2 w-1/3"></div>
                      <div className="h-4 bg-gray-100 rounded animate-pulse w-1/2"></div>
                    </div>
                  ) : (
                    <>
                      <h2 className="text-xl font-bold text-gray-800">{task?.taskName || '任务详情'}</h2>
                      <div className="text-base text-gray-500">
                        {task?.endDate ? new Date(task.endDate).toLocaleDateString('zh-CN', {
                          year: 'numeric',
                          month: 'numeric',
                          day: 'numeric'
                        }).replace(/\//g, '.') : ''}
                      </div>
                    </>
                  )}
                </div>
                
                {/* Task description */}
                <div className="mb-5 flex-shrink-0">
                  <div className="relative">
                    {isLoadingTask ? (
                      <div className="space-y-2">
                        <div className="h-4 bg-gray-100 rounded animate-pulse w-full"></div>
                        <div className="h-4 bg-gray-100 rounded animate-pulse w-5/6"></div>
                      </div>
                    ) : (
                      <>
                        <p 
                          ref={descriptionRef}
                          className="text-gray-600 text-base"
                          style={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: isLongDescription.current && !isDescriptionExpanded ? '3' : 'unset',
                            WebkitBoxOrient: 'vertical'
                          }}
                        >
                          {task?.taskDescription || '暂无任务描述'}
                        </p>
                        {isLongDescription.current && (
                          <div className="flex justify-center mt-2">
                            <Button 
                              type="default" 
                              size="small" 
                              className="rounded-full px-6 border-gray-300 text-gray-600"
                              onClick={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
                            >
                              {isDescriptionExpanded ? '收起' : '展开'}
                            </Button>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>

                {/* Divider line */}
                <div className="border-t border-gray-200 mb-5 flex-shrink-0"></div>
                
                {/* Student completion statistics cards */}
                <div className="grid grid-cols-5 gap-2 mb-6 flex-shrink-0">
                  <div 
                    className={`p-4 ${selectedStatus === 'completed' ? 'bg-blue-100' : 'bg-blue-50'} rounded-2xl text-center cursor-pointer hover:shadow-md transition-all`}
                    onClick={() => {
                      setSelectedStatus('completed');
                    }}
                  >
                    <div className="text-xl font-semibold text-blue-600">
                      {validCompletedStudents.length}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">已完成</div>
                  </div>
                  
                  <div 
                    className={`p-4 ${selectedStatus === 'inProgress' ? 'bg-yellow-100' : 'bg-yellow-50'} rounded-2xl text-center cursor-pointer hover:shadow-md transition-all`}
                    onClick={() => {
                      setSelectedStatus('inProgress');
                    }}
                  >
                    <div className="text-xl font-semibold text-yellow-600">
                      {validInProgressStudents.length}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">进行中</div>
                  </div>
                  
                  <div 
                    className={`p-4 ${selectedStatus === 'returned' ? 'bg-orange-100' : 'bg-orange-50'} rounded-2xl text-center cursor-pointer hover:shadow-md transition-all`}
                    onClick={() => {
                      setSelectedStatus('returned');
                    }}
                  >
                    <div className="text-xl font-semibold text-orange-600">
                      {validReturnedStudents.length}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">已打回</div>
                  </div>
                  
                  <div 
                    className={`p-4 ${selectedStatus === 'notStarted' ? 'bg-green-100' : 'bg-green-50'} rounded-2xl text-center cursor-pointer hover:shadow-md transition-all`}
                    onClick={() => {
                      setSelectedStatus('notStarted');
                    }}
                  >
                    <div className="text-xl font-semibold text-green-600">
                      {validNotStartedStudents.length}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">未开始</div>
                  </div>
                  
                  <div 
                    className={`p-4 ${selectedStatus === 'expired' ? 'bg-red-100' : 'bg-red-50'} rounded-2xl text-center cursor-pointer hover:shadow-md transition-all`}
                    onClick={() => {
                      setSelectedStatus('expired');
                    }}
                  >
                    <div className="text-xl font-semibold text-red-600">
                      {validExpiredStudents.length}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">已过期</div>
                  </div>
                </div>
                
                {/* Display students with selected status */}
                {selectedStatus && (
                  <div className="flex flex-col flex-grow min-h-0 overflow-hidden">
                    <h3 className="text-lg font-medium mb-4 flex-shrink-0">
                      {selectedStatus === 'completed' ? '已完成的学生' : 
                       selectedStatus === 'inProgress' ? '进行中的学生' :
                       selectedStatus === 'returned' ? '已打回的学生' :
                       selectedStatus === 'notStarted' ? '未开始的学生' : '已过期的学生'}
                    </h3>
                    
                    {/* Empty state when no students */}
                    {((selectedStatus === 'completed' && validCompletedStudents.length === 0) ||
                      (selectedStatus === 'inProgress' && validInProgressStudents.length === 0) ||
                      (selectedStatus === 'returned' && validReturnedStudents.length === 0) ||
                      (selectedStatus === 'notStarted' && validNotStartedStudents.length === 0) ||
                      (selectedStatus === 'expired' && validExpiredStudents.length === 0)) && (
                      <div className="flex flex-col items-center justify-center py-8 sm:py-10 bg-gray-50 rounded-xl flex-1 min-h-[200px] sm:min-h-[250px] md:min-h-[300px] m-auto mt-4 mb-8 w-[80%] max-w-[600px] shadow-sm border border-gray-100">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-gray-300 mb-4">
                          <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM9.5 16.5L7 14L9.5 11.5L7 9L9.5 6.5L12 9L14.5 6.5L17 9L14.5 11.5L17 14L14.5 16.5L12 14L9.5 16.5Z" fill="currentColor"/>
                        </svg>
                        <p className="text-gray-500 text-base sm:text-lg font-medium">暂无学生数据</p>
                      </div>
                    )}
                    
                    <div className="flex-grow min-h-0 overflow-y-auto pr-2">
                      <div className="grid grid-cols-3 gap-3 pb-4">
                        {selectedStatus === 'completed' && getAssignmentsWithPreview(validCompletedStudents).map((assignment, index) => (
                          <div key={assignment.id || `completed-${index}`}
                            className="border border-gray-200 rounded-2xl overflow-hidden hover:shadow-md transition-all bg-white h-auto"
                          >
                            <div className="h-28 bg-gray-100 overflow-hidden relative border-b border-gray-200">
                              {loadingPreviews[assignment.id] && (
                                <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
                                  <Spin indicator={<LoadingOutlined style={{ fontSize: 12 }} spin />} />
                                </div>
                              )}
                              {assignmentPreviews[assignment.id] ? (
                                <>
                                  {/* Add type badge in top-left corner only for work and image types with adaptive color */}
                                  <div className="absolute top-2 left-2 z-10">
                                    {assignmentPreviews[assignment.id]?.type === 'work' && (
                                      <div className="flex items-center justify-center bg-white bg-opacity-70 backdrop-blur-sm p-1.5 rounded-full shadow-sm">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-800">
                                          <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                                          <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                                          <line x1="12" y1="22.08" x2="12" y2="12"></line>
                                        </svg>
                                      </div>
                                    )}
                                    {assignmentPreviews[assignment.id]?.type === 'image' && (
                                      <div className="flex items-center justify-center bg-white bg-opacity-70 backdrop-blur-sm p-1.5 rounded-full shadow-sm">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-800">
                                          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                          <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                          <polyline points="21 15 16 10 5 21"></polyline>
                                        </svg>
                                      </div>
                                    )}
                                    {/* Files and none types don't show icons */}
                                  </div>
                                  
                                  {assignmentPreviews[assignment.id]?.type === 'work' && assignmentPreviews[assignment.id]?.url && (
                                    <img 
                                      src={assignmentPreviews[assignment.id]?.url}
                                      alt={getStudentName(assignment.studentId)}
                                      className="w-full h-full object-cover"
                                      onError={(e) => {
                                        const target = e.currentTarget as HTMLImageElement;
                                        target.style.display = 'none';
                                        // 添加加载动画容器
                                        const parent = target.parentElement;
                                        if (parent) {
                                          const loadingDiv = document.createElement('div');
                                          loadingDiv.className = 'w-full h-full flex flex-col items-center justify-center bg-gray-50';
                                          
                                          // 创建加载动画元素
                                          const spinContainer = document.createElement('div');
                                          spinContainer.className = 'mb-2';
                                          
                                          // 添加文本提示
                                          const textDiv = document.createElement('div');
                                          textDiv.className = 'text-gray-400 text-xs';
                                          textDiv.innerText = '图片加载失败';
                                          
                                          loadingDiv.appendChild(spinContainer);
                                          loadingDiv.appendChild(textDiv);
                                          parent.appendChild(loadingDiv);
                                          
                                          // 使用React的Spin组件渲染到容器中
                                          const antIcon = document.createElement('span');
                                          antIcon.className = 'anticon anticon-loading anticon-spin';
                                          antIcon.style.fontSize = '24px';
                                          antIcon.style.color = '#1890ff';
                                          spinContainer.appendChild(antIcon);
                                        }
                                      }}
                                    />
                                  )}
                                  {assignmentPreviews[assignment.id]?.type === 'image' && assignmentPreviews[assignment.id]?.url && (
                                    <img 
                                      src={assignmentPreviews[assignment.id]?.url}
                                      alt={getStudentName(assignment.studentId)}
                                      className="w-full h-full object-cover"
                                      onError={(e) => {
                                        const target = e.currentTarget as HTMLImageElement;
                                        target.style.display = 'none';
                                        // 添加加载动画容器
                                        const parent = target.parentElement;
                                        if (parent) {
                                          const loadingDiv = document.createElement('div');
                                          loadingDiv.className = 'w-full h-full flex flex-col items-center justify-center bg-gray-50';
                                          
                                          // 创建加载动画元素
                                          const spinContainer = document.createElement('div');
                                          spinContainer.className = 'mb-2';
                                          
                                          // 添加文本提示
                                          const textDiv = document.createElement('div');
                                          textDiv.className = 'text-gray-400 text-xs';
                                          textDiv.innerText = '图片加载失败';
                                          
                                          loadingDiv.appendChild(spinContainer);
                                          loadingDiv.appendChild(textDiv);
                                          parent.appendChild(loadingDiv);
                                          
                                          // 使用React的Spin组件渲染到容器中
                                          const antIcon = document.createElement('span');
                                          antIcon.className = 'anticon anticon-loading anticon-spin';
                                          antIcon.style.fontSize = '24px';
                                          antIcon.style.color = '#1890ff';
                                          spinContainer.appendChild(antIcon);
                                        }
                                      }}
                                    />
                                  )}
                                  {assignmentPreviews[assignment.id]?.type === 'files' && (
                                    <div className="w-full h-full flex items-center justify-center bg-white">
                                      <div className="text-4xl">📁</div>
                                    </div>
                                  )}
                                  {assignmentPreviews[assignment.id]?.type === 'none' && (
                                    <div className="w-full h-full flex items-center justify-center bg-gray-50">
                                      <div className="text-gray-400 text-xs">无预览</div>
                                    </div>
                                  )}
                                </>
                              ) : (
                                <div className="w-full h-full flex items-center justify-center bg-gray-50">
                                  <Spin indicator={<LoadingOutlined style={{ fontSize: 12 }} spin />} />
                                </div>
                              )}
                            </div>
                            <div className="p-2 flex justify-between items-center border-b border-gray-100">
                              <span className="font-medium text-gray-800 text-xs">{getStudentName(assignment.studentId)}</span>
                              {assignment?.taskScore !== undefined && assignment?.taskScore !== null ? (
                                <span className="text-green-500 font-bold text-xs">{assignment.taskScore}分</span>
                              ) : (
                                <span className="text-gray-400 text-xs">等待评分</span>
                              )}
                            </div>
                            {(assignment?.taskScore === undefined || assignment?.taskScore === null) && (
                              <div className="flex justify-evenly items-center p-2">
                                <button 
                                  className="p-1 group relative"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    downloadStudentImages(assignment);
                                  }}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-700">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="7 10 12 15 17 10"></polyline>
                                    <line x1="12" y1="15" x2="12" y2="3"></line>
                                  </svg>
                                  <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                    下载作品
                                  </span>
                                </button>
                                <button 
                                  className="p-1 group relative"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDirectViewWork(assignment);
                                  }}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-700">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                    <circle cx="12" cy="12" r="3"></circle>
                                  </svg>
                                  <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                    查看作品详情
                                  </span>
                                </button>
                                <button 
                                  className="p-1"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setCurrentAssignment(assignment);
                                    setGradeModalVisible(true);
                                  }}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-600">
                                    <path d="M12 20h9"></path>
                                    <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                                  </svg>
                                </button>
                                <button 
                                  className="p-1"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleReturnForRevision(assignment);
                                  }}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-red-400">
                                    <path d="M23 4v6h-6"></path>
                                    <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"></path>
                                  </svg>
                                </button>
                              </div>
                            )}
                            {(assignment?.taskScore !== undefined && assignment?.taskScore !== null) && (
                              <div className="flex justify-evenly items-center p-2">
                                <button 
                                  className="p-1 group relative"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    downloadStudentImages(assignment);
                                  }}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-700">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="7 10 12 15 17 10"></polyline>
                                    <line x1="12" y1="15" x2="12" y2="3"></line>
                                  </svg>
                                  <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                    下载作品
                                  </span>
                                </button>
                                <button 
                                  className="p-1 group relative"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDirectViewWork(assignment);
                                  }}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-700">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                    <circle cx="12" cy="12" r="3"></circle>
                                  </svg>
                                  <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                    查看作品详情
                                  </span>
                                </button>
                                <button 
                                  className="p-1 group relative"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    
                                    // 先验证是否有workId，如果没有就直接返回提示
                                    if (!assignment.workId) {
                                      notification.info('正在开发中，敬请期待');
                                      return;
                                    }
                                    
                                    if (assignment.workId) {
                                      // 检查作品是否已发布到班级项目
                                      const isAlreadyPublished = publishedWorks.has(assignment.id);

                                      // 根据发布状态显示不同的确认对话框
                                      Modal.confirm({
                                        title: isAlreadyPublished ? '取消发布到班级项目' : '发布到班级项目',
                                        content: isAlreadyPublished 
                                          ? '该作品已发送到班级项目，是否取消？取消后学生将无法在班级项目中查看该作品。'
                                          : '确定将该作品发布到班级项目展示区吗？学生将可以在班级项目中查看该作品。',
                                        okText: '确定',
                                        cancelText: '取消',
                                        onOk: async () => {
                                          try {
                                            // 首先更新作品状态为发布到班级
                                            const workId = assignment.workId || 0; // 确保 workId 是数字类型
                                            if (workId === 0) {
                                              notification.error('作品ID无效');
                                              return;
                                            }
                                            
                                            // 获取提交详情，检查hasWork属性
                                            const submissionResponse = await taskApi.getStudentWork(assignment.id);
                                            const submissionData = submissionResponse.data.data;
                                            
                                            // 如果不是scratch作品（根据hasWork判断）且要发布（不是取消发布）
                                            if (!isAlreadyPublished && !(submissionData.hasWork === true && submissionData.workId)) {
                                              notification.info('正在开发中，敬请期待');
                                              return;
                                            }
                                            
                                            // 获取作品详情用于展示
                                            const workDetailResponse = await worksApi.getDetail(workId);
                                            const workDetail = workDetailResponse?.data?.data;
                                            
                                            if (isAlreadyPublished) {
                                              // 取消发布 - 更新作品信息
                                              const updateResponse = await worksApi.updateWork(workId, {
                                                isPublishedToClass: 0,
                                                publishedClassId: null,
                                                publishToClassTime: null,
                                                publishedTeacherId: null
                                              });
                                              
                                              if (updateResponse?.data?.code === 200) {
                                                // 更新发布状态 - 从集合中移除
                                                setPublishedWorks(prev => {
                                                  const newSet = new Set(Array.from(prev));
                                                  newSet.delete(assignment.id);
                                                  return newSet;
                                                });
                                                
                                                // 获取学生信息
                                                const studentName = getStudentName(assignment.studentId);
                                                const workTitle = workDetail?.title || submissionData.workInfo?.title || '未命名作品';
                                                
                                                notification.success(`已取消发布学生 ${studentName} 的作品 ${workTitle} 到班级项目`);
                                              } else {
                                                console.error('取消发布失败:', updateResponse?.data);
                                                notification.error('取消发布失败: ' + (updateResponse?.data?.message || '未知错误'));
                                              }
                                            } else {
                                              // 发布作品到班级项目
                                              const response = await taskApi.publishToClass({
                                                assignmentId: assignment.id,
                                                classId: currentClassId
                                              });
                                              
                                              if (response?.data?.code === 200) {
                                                // 更新发布状态
                                                setPublishedWorks(prev => new Set([...Array.from(prev), assignment.id]));
                                                
                                                // 获取学生信息
                                                const studentName = getStudentName(assignment.studentId);
                                                const workTitle = workDetail?.title || submissionData.workInfo?.title || '未命名作品';
                                                
                                                // 显示成功提示，并提供查看选项
                                                Modal.success({
                                                  title: '发布成功',
                                                  content: (
                                                    <div>
                                                      <p>学生 <strong>{studentName}</strong> 的作品 <strong>{workTitle}</strong> 已成功发布到班级项目页面。</p>
                                                      <p>所有学生可以在班级项目空间查看该作品。</p>
                                                    </div>
                                                  ),
                                                  okText: '确定',
                                                  onOk: () => {
                                                    // 如果需要导航到班级项目页面，可以在这里添加导航代码
                                                    // window.open(`/class-space/${currentClassId}/projects`, '_blank');
                                                  }
                                                });
                                              } else {
                                                console.error('发布失败:', response?.data);
                                                notification.error('发布失败: ' + (response?.data?.message || '未知错误'));
                                              }
                                            }
                                          } catch (error) {
                                            console.error('操作失败', error);
                                            notification.error('操作失败，请稍后重试');
                                          }
                                        }
                                      });
                                    } else {
                                      notification.info('该提交内容没有关联作品，无法发布到班级项目');
                                    }
                                  }}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={`${publishedWorks.has(assignment.id) ? 'text-green-500' : 'text-gray-800'}`}>
                                    <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path>
                                    <polyline points="16 6 12 2 8 6"></polyline>
                                    <line x1="12" y1="2" x2="12" y2="15"></line>
                                  </svg>
                                  <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                    {publishedWorks.has(assignment.id) ? '已发布到班级项目' : '发布到班级项目'}
                                  </span>
                                </button>
                              </div>
                            )}
                          </div>
                        ))}
                        
                        {selectedStatus === 'inProgress' && validInProgressStudents.map((assignment, index) => (
                          <div key={assignment.id || `inProgress-${index}`}
                            className="border border-gray-200 rounded-2xl overflow-hidden hover:shadow-sm transition-all bg-white p-3"
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="w-9 h-9 rounded-full bg-yellow-50 flex items-center justify-center text-yellow-400 text-sm font-medium">
                                  {getStudentName(assignment.studentId).charAt(0)}
                                </div>
                                <div>
                                  <span className="font-medium text-gray-800 block text-sm leading-tight">{getStudentName(assignment.studentId)}</span>
                                  <span className="text-gray-500 text-xs block mt-0.5">{`学号: ${getStudentNumber(assignment.studentId)}`}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                        
                        {selectedStatus === 'returned' && validReturnedStudents.map((assignment, index) => (
                          <div key={assignment.id || `returned-${index}`}
                            className="border border-gray-200 rounded-2xl overflow-hidden hover:shadow-md transition-all bg-white relative"
                          >
                            <div className="flex flex-col">
                              {/* 学生头像和基本信息 */}
                              <div className="flex items-center p-3 sm:p-4 pb-2 sm:pb-3">
                                <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-orange-500 flex items-center justify-center text-white text-xs sm:text-sm font-medium shadow-sm mr-2 sm:mr-3">
                                  {getStudentName(assignment.studentId).charAt(0)}
                                </div>
                                <div>
                                  <span className="font-medium text-gray-800 block text-xs sm:text-sm leading-tight">{getStudentName(assignment.studentId)}</span>
                                  <span className="text-gray-500 text-[10px] sm:text-xs block mt-0.5">{`学号: ${getStudentNumber(assignment.studentId)}`}</span>
                                </div>
                              </div>
                              
                              {/* 分隔线 */}
                              <div className="border-t border-gray-100 mx-3 sm:mx-4"></div>
                              
                              {/* 修改意见 */}
                              <div className="px-3 sm:px-4 pt-3 sm:pt-4 pb-1 sm:pb-2">
                                <span className="text-[10px] sm:text-xs font-medium text-gray-500 block mb-1">修改意见</span>
                                <div className="bg-orange-50 text-xs sm:text-sm text-gray-700 p-2.5 sm:p-3.5 rounded-xl leading-relaxed h-24 sm:h-28 overflow-y-auto scrollbar-thin scrollbar-thumb-orange-400 scrollbar-track-orange-50 border border-orange-100 shadow-sm">
                                  {assignment.feedback || '未提供修改意见'}
                                </div>
                              </div>

                              {/* 查看作品详情按钮 */}
                              <div className="flex justify-end px-3 sm:px-4 py-2 pb-3 sm:pb-4">
                                <button 
                                  className="flex items-center text-blue-600 text-[10px] sm:text-xs hover:text-blue-700 transition-colors bg-blue-50 hover:bg-blue-100 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full"
                                  onClick={() => {
                                    if (assignment.workId) {
                                      handleViewWork(assignment.workId);
                                    }
                                  }}
                                >
                                  <FileTextOutlined className="mr-1 sm:mr-1.5" />
                                  查看作品详情
                                </button>
                              </div>
                            </div>
                          </div>
                        ))}
                        
                        {selectedStatus === 'notStarted' && (validNotStartedStudents.length > 0 ? validNotStartedStudents : []).map((assignment, index) => (
                          <div key={assignment.id || `notStarted-${index}`}
                            className="border border-gray-200 rounded-2xl overflow-hidden hover:shadow-sm transition-all bg-white p-3"
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="w-9 h-9 rounded-full bg-green-100 flex items-center justify-center text-green-600 text-sm font-medium">
                                  {getStudentName(assignment.studentId).charAt(0)}
                                </div>
                                <div>
                                  <span className="font-medium text-gray-800 block text-sm leading-tight">{getStudentName(assignment.studentId)}</span>
                                  <span className="text-gray-500 text-xs block mt-0.5">{`学号: ${getStudentNumber(assignment.studentId)}`}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                        
                        {selectedStatus === 'expired' && validExpiredStudents.map((assignment, index) => (
                          <div key={assignment.id || `expired-${index}`}
                            className="border border-gray-200 rounded-2xl overflow-hidden hover:shadow-sm transition-all bg-white p-3"
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="w-9 h-9 rounded-full bg-red-50 flex items-center justify-center text-red-400 text-sm font-medium">
                                  {getStudentName(assignment.studentId).charAt(0)}
                                </div>
                                <div>
                                  <span className="font-medium text-gray-800 block text-sm leading-tight">{getStudentName(assignment.studentId)}</span>
                                  <span className="text-gray-500 text-xs block mt-0.5">{`学号: ${getStudentNumber(assignment.studentId)}`}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
                
                {/* Bottom download button - only display in "completed" status */}
                {selectedStatus === 'completed' && validCompletedStudents.length > 0 && (
                  <div className="flex justify-end mt-4 flex-shrink-0">
                    <Button
                      type="primary"
                      icon={<DownloadOutlined />}
                      onClick={downloadAllImagesAsZip}
                      loading={isDownloading}
                      className="rounded-full"
                      disabled={!hasDownloadableContent}
                    >
                      {isDownloading ? '正在打包...' : '下载所有图片'}
                    </Button>
                  </div>
                )}
              </div>
              
              {/* Right content area - statistics */}
              <div className="border-t lg:border-t-0 lg:border-l border-gray-200 pt-6 lg:pt-0 lg:pl-6 flex flex-col h-full overflow-hidden w-full lg:w-1/3 mt-4 lg:mt-0">
                {/* Completion statistics */}
                <div className="mb-6 flex-shrink-0">
                  <h3 className="text-lg font-medium text-gray-700 mb-4">完成统计</h3>
                  <div className="flex flex-col items-center mb-4">
                    {/* Pie chart - keeping the fixed size */}
                    <PieChart 
                      completedCount={validCompletedStudents.length}
                      inProgressCount={validInProgressStudents.length}
                      returnedCount={validReturnedStudents.length}
                      notStartedCount={validNotStartedStudents.length}
                      expiredCount={validExpiredStudents.length}
                      totalStudents={totalStudents}
                    />
                    

                  </div>
                </div>
                
                {/* Self-assessment statistics */}
                <div className="flex-grow min-h-0 overflow-hidden flex flex-col">
                  <h3 className="text-lg font-medium text-gray-700 mb-2 sm:mb-4 flex-shrink-0">自评统计</h3>
                  <div className="flex-grow min-h-0 overflow-y-auto pr-0 sm:pr-2">
                    {/* Self-assessment item list */}
                    {task?.selfAssessmentItems && task.selfAssessmentItems.length > 0 ? (
                      <div className="space-y-2 sm:space-y-3">
                        {task.selfAssessmentItems.map((item) => (
                          <div key={item.id} className="border border-gray-100 rounded-xl overflow-hidden">
                            {/* 自评项目标题行 */}
                            <div 
                              className={`flex items-center justify-between py-1.5 px-3 cursor-pointer hover:bg-gray-50 transition ${selectedAssessmentItem?.id === item.id ? 'bg-blue-50 border-b border-blue-100' : ''}`}
                              onClick={() => handleAssessmentItemClick(item)}
                            >
                              <div className="flex items-center gap-1 flex-grow overflow-hidden mr-2">
                                <div className={`flex ${selectedAssessmentItem?.id === item.id ? 'text-blue-600' : 'text-gray-600'} flex-shrink-0 items-center mr-1`}>
                                  <span className={`${selectedAssessmentItem?.id === item.id ? 'transform rotate-90' : ''} inline-block mr-1`} style={{fontSize: '8px'}}>▶</span>
                                </div>
                                <div className={`truncate ${selectedAssessmentItem?.id === item.id ? 'font-medium' : ''} text-xs`} title={item.content}>
                                  {item.content}
                                </div>
                              </div>
                              <div className="flex items-center gap-1.5 flex-shrink-0">
                                <div className="text-amber-400 flex">
                                  {Array(5)
                                    .fill(0)
                                    .map((_, i) => (
                                      <span key={i} className="text-xs">
                                        {i < Math.round(item.ratedCount > 0 ? Number(item.scoreSum) / item.ratedCount : 0) ? "★" : "☆"}
                                      </span>
                                    ))}
                                </div>
                                <span className="inline-block px-1 py-0 rounded-full bg-blue-50 border border-blue-200 text-blue-500" style={{fontSize: '10px'}}>
                                  平均分:{item.ratedCount > 0 ? (Number(item.scoreSum) / item.ratedCount).toFixed(2) : '0.00'}
                                </span>
                                <span className="px-1 py-0 rounded-full bg-green-50 border border-green-200 text-green-500" style={{fontSize: '10px'}}>
                                  {item.ratedCount}人
                                </span>
                              </div>
                            </div>
                            
                            {/* 学生自评详情 - 仅在选中时显示 */}
                            {selectedAssessmentItem?.id === item.id && (
                              <div className="px-3 py-2 bg-white">
                                {loadingSubmissions[item.id] ? (
                                  <div className="flex justify-center py-3">
                                    <Spin size="small" />
                                  </div>
                                ) : (
                                  <div className="space-y-1 max-h-[200px] overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-blue-200 scrollbar-track-gray-100">
                                    {detailedSubmissions[item.id]?.length > 0 ? (
                                      detailedSubmissions[item.id].map((submission: any) => (
                                        <div key={submission.id} className="bg-white rounded-xl border border-gray-100 shadow-sm flex items-center justify-between w-full py-1.5 px-3 hover:shadow-md transition-shadow">
                                          <div className="flex items-center">
                                            <div className="flex-shrink-0 mr-2">
                                              <Avatar 
                                                src={submission.avatarUrl ? getAvatarThumbnail(submission.avatarUrl, 24) : undefined}
                                                size={20}
                                                icon={!submission.avatarUrl && <UserOutlined />}
                                                className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-gray-100 shadow-sm"
                                                style={{
                                                  display: 'flex',
                                                  alignItems: 'center',
                                                  justifyContent: 'center',
                                                  fontSize: '10px',
                                                  fontWeight: 'bold',
                                                  color: '#4B91F1'
                                                }}
                                                onError={() => {
                                                  // 当头像加载失败时显示用户名首字母或用户图标
                                                  return true; // 返回true表示使用fallback内容（即icon属性）
                                                }}
                                              >
                                                {!submission.avatarUrl && (submission.studentName?.[0] || getStudentName(submission.studentId).charAt(0))}
                                              </Avatar>
                                            </div>
                                            <span className="text-xs text-gray-700 font-medium truncate max-w-[80px]">
                                              {submission.studentName || getStudentName(submission.studentId)}
                                            </span>
                                          </div>
                                          <div className="text-amber-400 flex items-center">
                                            {Array(5)
                                              .fill(0)
                                              .map((_, i) => (
                                                <span key={i} className="leading-none flex items-center text-[10px]">
                                                  {i < submission.score ? "★" : "☆"}
                                                </span>
                                              ))}
                                          </div>
                                        </div>
                                      ))
                                    ) : (
                                      <div className="py-4 text-center text-gray-500 text-xs bg-white border border-gray-100 rounded-xl shadow-sm w-full">暂无学生提交自评</div>
                                    )}
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-8 sm:py-10 rounded-xl bg-gradient-to-br from-blue-50/50 to-indigo-50/50 border border-blue-100/30 shadow-sm min-h-[250px] mt-4 mb-8 w-[90%] max-w-[500px] mx-auto">
                        <div className="text-6xl mb-3 opacity-60">📋</div>
                        <Empty 
                          description={
                            <span className="text-gray-500 text-base sm:text-lg font-medium">暂无自评项目</span>
                          } 
                          image={Empty.PRESENTED_IMAGE_SIMPLE}
                          imageStyle={{ display: 'none' }}
                        />
                        <div className="text-sm text-gray-400 mt-2">教师可在创建任务时添加自评项目</div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
        </div>
      </Modal>

      {/* Submission content modal - updating to fixed size */}
      <Modal
        title="查看提交内容"
        open={submissionModalVisible}
        onCancel={() => setSubmissionModalVisible(false)}
        footer={null}
        width={1200}
        className="submission-modal"
        styles={{
          mask: {
            backgroundColor: 'rgba(0, 0, 0, 0.2)',
            backdropFilter: 'blur(4px)'
          },
          content: {
            borderRadius: '16px',
            padding: '0',
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.08)',
            overflow: 'hidden',
            height: '80vh',
            width: '1200px',
            display: 'flex',
            flexDirection: 'column',
            margin: '0 auto'
          },
          header: {
            padding: '16px 24px',
            marginBottom: '0',
            borderBottom: '1px solid #f0f0f0',
            flex: 'none',
            fontSize: '16px',
            fontWeight: '500',
            color: '#1f2937'
          },
          body: {
            padding: '24px',
            flex: '1 1 auto',
            minHeight: '0',
            overflow: 'auto'
          }
        }}
        centered
        maskClosable
        closeIcon={<CloseOutlined className="text-gray-500 hover:text-gray-700 transition-colors" />}
      >
        <SubmissionContent currentSubmission={currentSubmission} handleViewWork={handleViewWork} />
      </Modal>

      {/* Grading modal */}
      <Modal
        title="评分"
        open={gradeModalVisible}
        onCancel={() => setGradeModalVisible(false)}
        onOk={() => gradeForm.submit()}
        centered
        style={{
          width: '500px',
          margin: '0 auto',
          padding: 0,
          top: 0
        }}
        width={500}
        styles={{
          mask: {
            backgroundColor: 'rgba(0, 0, 0, 0.3)',
            backdropFilter: 'blur(8px)'
          },
          content: {
            borderRadius: '24px',
            padding: '28px',
            boxShadow: '0 12px 32px rgba(0, 0, 0, 0.12)',
            overflow: 'hidden',
            border: 'none'
          },
          header: {
            marginBottom: '20px',
            padding: '0',
            border: 'none',
            fontSize: '20px',
            fontWeight: '600'
          },
          body: {
            padding: '0 0 20px 0'
          },
          footer: {
            marginTop: '16px',
            padding: '0',
            border: 'none'
          }
        }}
      >
        <Form
          form={gradeForm}
          onFinish={handleGrade}
          layout="vertical"
        >
          <Form.Item
            name="score"
            label="分数"
            rules={[
              { required: true, message: '请输入分数' },
              { type: 'number', min: 0, max: 100, message: '分数范围为0-100' }
            ]}
          >
            <InputNumber min={0} max={100} className="w-full" />
          </Form.Item>
          <Form.Item
            name="feedback"
            label="评语"
          >
            <Input.TextArea rows={4} placeholder="请输入评语（选填）" className="rounded-xl" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}; 