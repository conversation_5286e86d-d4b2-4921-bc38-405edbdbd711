import request from '../request';

// 任务相关常量
export enum TaskType {
  GRAPHIC = 1,  // 图形化任务
  NODE = 2,     // 节点式任务
  AI = 3        // AI应用任务
}

export enum Priority {
  NORMAL = 0,   // 普通
  IMPORTANT = 1, // 重要
  URGENT = 2    // 紧急
}

export enum TaskStatus {
  NOT_STARTED = 0, // 未开始
  IN_PROGRESS = 1, // 进行中
  COMPLETED = 2,   // 已完成
  EXPIRED = 3,     // 已过期
  RE_DO = 4        // 重新修改
}

// 任务相关类型定义
export interface TeacherTaskParams {
  taskName: string;
  taskDescription?: string;
  taskType: TaskType;
  startDate?: Date;
  endDate?: Date;
  taskContent?: string;
  attachments?: string[];
  priority?: Priority;
  tags?: string[];
  isPublic?: number;
  repeatConf?: Record<string, any>;
  studentIds?: number[];
  classId?: number;
  allowLateSubmission?: boolean;
  lateSubmissionPolicy?: {
    deductionPerDay: number;
    maxDeductionDays: number;
    minScore: number;
  };
  references?: Array<{
    title: string;
    description: string;
    url: string;
    type: string;
  }>;
  templateId?: number;
  workIdsStr?: string;
}

export interface TaskQueryParams {
  type?: number;
  priority?: number;
  status?: number;
  orderBy?: string;
  page?: number;
  size?: number;
  classId?: number;
  studentId?: number;
  roleId?: number;
  teacherId?: number;
}

export interface TaskAssignmentParams {
  taskId: number;
  studentIds: number[];
}

export interface SubmitTaskParams {
  taskId: number;
  workId?: number | null;  // 作品ID
  imageId?: number | null;  // 图片ID
  files?: string[] | null;  // 文件列表
  submissionType: 'work' | 'image' | 'files' | 'work_and_image';  // 添加新的提交类型
}

export interface TaskGradeParams {
  assignmentId: number;
  score: number;
  feedback?: string;
}

export interface Task {
  id: number;
  taskName: string;
  taskDescription: string;
  taskType: number;
  priority: number;
  startDate: Date;
  endDate: Date;
  taskContent: string;
  templateId: number;
  attachments: Array<{
    name: string;
    url: string;
    type: string;
    size: number;
  }>;
  allowLateSubmission: boolean;
  lateSubmissionPolicy?: {
    deductionPerDay: number;
    maxDeductionDays: number;
    minScore: number;
  };
  references?: Array<{
    title: string;
    description: string;
    url: string;
    type: string;
  }>;
  assignments: {
    id: number;
    taskId: number;
    studentId: number;
    taskStatus: number;
    submitTime?: Date;
    taskScore?: number;
    feedback?: string;
  }[];
  getStudentWork?: (assignmentId: number) => Promise<any>;
  gradeTask?: (params: TaskGradeParams) => Promise<any>;
}

export interface TaskStatusUpdateParams {
  assignmentId: number;
  taskStatus: number;  // 使用number类型代替TaskStatus枚举
}

export interface PublishToClassParams {
  assignmentId: number;
  classId: number;
}

// 任务相关 API
const taskApi = {
  /**
   * 获取任务统计
   */

  // 没人用它   okok
  getTaskStats() {
    return request.get('/app/user/teacher_task/stats');
  },

  /**
   * 获取任务列表   okok
   */
  getTaskList(params: TaskQueryParams) {
    return request.get('/api/teacher-task/list', { params });
  },

  /**
   * 发布任务    okok
   */
  publishTask(params: TeacherTaskParams) {
    return request.post('/api/teacher-task/publish', params);
  },

  /**
   * 获取任务完成统计   没人用 ，okok
   */
  getAssignmentStats() {
    return request.get('/app/user/teacher_task/assignment/stats');
  },

  /**
   * 批量分配任务  没人用okok
   */
  batchAssignTask(params: TaskAssignmentParams) {
    return request.post('/app/user/teacher_task/assignment/batchAssign', params);
  },

  /**
   * 提交任务   okok
   */
  submitTask(params: SubmitTaskParams) {
    return request.post('/api/teacher-task-assignment/submitWork', params);
  },

  /**
   * 评分   okok
   */
  gradeTask(params: TaskGradeParams) {
    return request.post('/api/teacher-task-assignment/grade', params);
  },

  /**
   * 获取任务详情  okok
   */
  getTaskDetail(taskId: number) {
    return request.get(`/api/teacher-task/${taskId}`);
  },

  /**
   * 获取学生提交的作品    okok
   */
  getStudentWork(assignmentId: number) {
    return request.get('/api/teacher-task-assignment/studentCommitWork', {
      params: { assignmentId }
    });
  },

  /**
   * 打回修改   okok
   */
  returnForRevision(params: {
    assignmentId: number;
    feedback: string;
  }) {
    return request.post('/api/teacher-task-assignment/return-revision', params);
  },

  /**
   * 更新任务状态   okok
   */
  updateTaskStatus(params: TaskStatusUpdateParams) {
    return request.post('/api/teacher-task-assignment/update-status', params);
  },

  /**
   * 更新任务     okok
   */
  updateTask(params: TeacherTaskParams & { taskId: number }) {
    return request.post('/api/teacher-task/update-task', params);
  },

  /**
   * 发布学生作品到班级项目  okok
   */
  publishToClass(params: PublishToClassParams) {
    return request.post('/api/teacher-task-assignment/publish-to-class', params);
  },

  /**
   * 删除任务  okok
   */
  deleteTask(taskId: number) {
    return request.post('/api/teacher-task/delete-task', { taskId });
  },

  /**
   * 搜索任务   okok
   */
  searchTasks(keyword: string, classId: number) {
    return request.get('/api/teacher-task/search', {
      params: { keyword, classId }
    });
  },

  /**
   * 获取作品的提交记录   okok
   */
  getWorkSubmissions(workId: number) {
    return request.get('/api/teacher-task-assignment/workSubmissionsRecord', {
      params: { workId }
    });
  },

  // 获取学生任务列表      没有人调用他  okok
  getStudentTasks: (params: {
    studentId: number;
    status: number;
    isPublic: boolean;
    hasSubmitted: boolean;
  }) => {
    return request.get('/app/user/teacher_task/list', {
      params: {
        studentId: params.studentId,
        status: params.status,
        roleId: 1, // 学生角色
        page: 1,
        size: 100
      }
    });
  },

  // 添加标记已读的方法   okok
  markAsRead: (params: {
    taskId: number;
    studentId: number;
  }) => {
    return request.post('/api/teacher-task-assignment/mark-read', params);
  },

  // 根据任务ID获取自评项
  getSelfAssessmentItemsByTaskId: (taskId: number) => {
    return request.get(`/api/self-assessment-item/task/${taskId}`);
  },
  
  // 搜索自评项
  searchSelfAssessmentItems(keyword: string) {
    return request.get('/api/task-self-assessment-item/search', {
      params: { keyword }
    });
  },

  /**
   * 检查自评项是否有学生自评记录
   */
  checkSelfAssessmentRecords(itemIds: number[]) {
    return request.post('/api/task-self-assessment-item/check-records', { itemIds });
  },

  /**
   * 更新任务的自评项
   */
  updateSelfAssessmentItems(taskId: number, selfAssessmentItems: string[]) {
    return request.post('/api/task-self-assessment-item/update-by-task', { 
      taskId, 
      selfAssessmentItems 
    });
  }
};

export default taskApi; 