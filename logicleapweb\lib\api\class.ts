import request from "../request";

/**
 * 移除班级协助教师
 */

export const classApi = {

  baseUrl: 'api/user/class',
  getClassStudents: (classId: number) => {
    return request.get(`${classApi.baseUrl}/${classId}/students`);
  },
  transferClass: (classId: number, newTeacherId: number, transferType: string) => {
    console.log(classId, newTeacherId, transferType);
    return request.post(`${classApi.baseUrl}/transfer`, {
      id: classId,
      newTeacherId,
      transferType
    });
  },
  importStudentsSimple: (students: Array<{ studentNumber: string; nickName: string }>) => {
    return request.post(`${classApi.baseUrl}/import`, {
      students
    });
  },

  // updateClassSimple: (id: number, values: Record<string, any>) => {
  updateClassSimple: (values: Record<string, any>) => {
    return request.post(`${classApi.baseUrl}/update/${values.id}`, values);
  },
  createClassSimple: (schoolId: number, grade: string, className: string) => {
    return request.post(`${classApi.baseUrl}/create`, {
      schoolId,
      grade,
      className
    });
  },


  removeStudentFromClass: async (studentIds: number[]) => {
    const results = await (studentIds.length > 20
      ? Promise.all(
        Array.from(
          { length: Math.ceil(studentIds.length / 20) },
          (_, i) => studentIds.slice(i * 20, (i + 1) * 20)
        ).map(batchId =>
          request.post(`${classApi.baseUrl}/student/remove`, {
            studentIds: batchId
          }).then(({ data }) => data)
        )
      ).then(results => ({
        data: {
          code: results.some(r => r.code !== 200) ? 500 : 200,
          data: {
            results: results.flatMap(r => r.data.results || [])
          }
        }
      }))
      : request.post(`${classApi.baseUrl}/student/remove`, {
        studentIds
      })
    );

    return results;
  },



  deleteClass: (classId: number) => {
    return request.delete(`${classApi.baseUrl}/${classId}`);
  },
  updateClass: (classId: number, values: Record<string, any>) => {
    return request.put(`${classApi.baseUrl}/update/${classId}`, {
      ...values
    });
  },
  joinClassByTeacher: (inviteCode: string, userId: number) => {
    return request.post(`${classApi.baseUrl}/invite_join`, {
      inviteCode,
      userId
    });
  },
  createClass: (schoolId: number, className: string, teacherId: number) => {
    console.log(schoolId, className, teacherId);
    return request.post(`${classApi.baseUrl}/create`, {
      schoolId,
      className,
      teacherId
    });
  },
  generateInviteCode: (classId: number) => {
    return request.post(`${classApi.baseUrl}/${classId}/invite`);
  },

  importStudents: (classId: number, students: Array<{ studentNumber: string; nickName: string }>) => {
    return request.post(`${classApi.baseUrl}/import`, {
      classId,
      students
    });
  },
  addStudentToClass: (classId: number, values: Record<string, any>) => {
    return request.post(`${classApi.baseUrl}/student/create`, {
      classId,
      ...values
    });
  },
  joinClassByInvite: (userId: number, inviteCode: string) => {
    return request.post(`${classApi.baseUrl}/invite_join`, {
      userId,
      inviteCode
    });
  },

  searchTeacherByPhone: (phone: string) => {
    console.log(phone);
    return request.get(`${classApi.baseUrl}/teacher/search/${phone}`);
  },
  getTeacherClassesSimple: (schoolId: number) => {
    return request.get(`${classApi.baseUrl}/school/${schoolId}`);
  },

  getTeacherClasses: (schoolId: number, teacherId: number) => {
    return request.get(`${classApi.baseUrl}/teacher/classes/${schoolId}`, {
      params: { teacherId }
    });
  },
  removeAssistantTeacher: (classId: number) => {
    return request.post(`${classApi.baseUrl}/assistant/remove`, {
      classId
    });
  },
  getClassInfo: (classId: number) => {
    return request.get(`${classApi.baseUrl}/info/${classId}`);
  }
}
