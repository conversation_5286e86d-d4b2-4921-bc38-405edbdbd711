"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/ClassDetail.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/ClassDetail.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/blocks.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _lib_api_student__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/api/student */ \"(app-pages-browser)/./lib/api/student.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/TemplateContext */ \"(app-pages-browser)/./app/workbench/contexts/TemplateContext.tsx\");\n/* harmony import */ var _AddStudentModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AddStudentModal */ \"(app-pages-browser)/./app/workbench/components/AddStudentModal.tsx\");\n/* harmony import */ var _teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../teacher-space/components/modals */ \"(app-pages-browser)/./app/teacher-space/components/modals/index.ts\");\n/* harmony import */ var _AssignBlocksModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AssignBlocksModal */ \"(app-pages-browser)/./app/workbench/components/AssignBlocksModal.tsx\");\n/* harmony import */ var _TransferClassModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TransferClassModal */ \"(app-pages-browser)/./app/workbench/components/TransferClassModal.tsx\");\n/* harmony import */ var _AssignPointsModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./AssignPointsModal */ \"(app-pages-browser)/./app/workbench/components/AssignPointsModal.tsx\");\n/* harmony import */ var _BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./BatchUseKeyPackageModal */ \"(app-pages-browser)/./app/workbench/components/BatchUseKeyPackageModal.tsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./app/workbench/components/StudentList/index.tsx\");\n/* harmony import */ var _ClassDetail_css__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ClassDetail.css */ \"(app-pages-browser)/./app/workbench/components/ClassDetail.css\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 类型定义已移至 ./types/index.ts\nconst ClassDetail = (param)=>{\n    let { classInfo: initialClassInfo, selectedSchool, onBack, onClassInfoUpdate, onClassDeleted } = param;\n    var _selectedStudent_nickName, _selectedStudent_currentTemplate;\n    _s();\n    // 创建内部状态来管理班级信息，这样可以在编辑后更新显示\n    const [classInfo, setClassInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialClassInfo);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 同步外部传入的classInfo变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setClassInfo(initialClassInfo);\n    }, [\n        initialClassInfo\n    ]);\n    const [isAddStudentModalVisible, setIsAddStudentModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSettingsDropdownOpen, setIsSettingsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchActionsDropdownOpen, setIsBatchActionsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const settingsDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const batchActionsDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 模态框状态\n    const [isEditClassModalVisible, setIsEditClassModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isImportStudentModalVisible, setIsImportStudentModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTransferClassModalVisible, setIsTransferClassModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isInviteCodeModalVisible, setIsInviteCodeModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAssignBlocksModalVisible, setIsAssignBlocksModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [inviteCode, setInviteCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 新增功能模态框状态\n    const [isPublishTaskModalVisible, setIsPublishTaskModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResetPasswordModalVisible, setIsResetPasswordModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAssignPointsModalVisible, setIsAssignPointsModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchUseKeyPackageModalVisible, setIsBatchUseKeyPackageModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [isRedeemKeyModalVisible, setIsRedeemKeyModalVisible] = useState(false); // 未使用，已移除\n    // 转让管理相关状态\n    const [searchedTeacher, setSearchedTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [transferLoading, setTransferLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // PublishTaskModal 相关状态\n    const [fileList, setFileList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [officialTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 保留以避免引用错误\n    const [selectedTemplateId, setSelectedTemplateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // AssignBlocksModal 相关状态\n    const [loadingTemplates, setLoadingTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [studentTemplateUsage, setStudentTemplateUsage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [teacherTemplate, setTeacherTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedStudentId, setSelectedStudentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 添加userRoles状态，与teacher-space保持一致\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 多选状态\n    const [selectedStudentIds, setSelectedStudentIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSelectAll, setIsSelectAll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 使用全局模板状态\n    const { currentTemplate, globalTemplateChangeVersion, refreshCurrentTemplate } = (0,_contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__.useTemplate)();\n    // 全局当前模板信息（用于没有个人模板的学生）\n    const [globalCurrentTemplate, setGlobalCurrentTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 获取教师当前模板（从班级接口获取）\n    const fetchTeacherCurrentTemplate = async ()=>{\n        try {\n            if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id) || !(classInfo === null || classInfo === void 0 ? void 0 : classInfo.schoolId)) return null;\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.getTeacherClasses(classInfo.schoolId, userId);\n            if (response.data.code === 200 && response.data.data) {\n                // 找到当前班级的模板信息\n                const currentClass = response.data.data.find((cls)=>cls.id === classInfo.id);\n                if (currentClass && currentClass.templateName) {\n                    const templateInfo = {\n                        templateId: currentClass.templateId || 0,\n                        templateName: currentClass.templateName,\n                        isOfficial: currentClass.isOfficial || false\n                    };\n                    setGlobalCurrentTemplate(templateInfo);\n                    console.log(\"获取到教师当前模板:\", templateInfo);\n                    return templateInfo;\n                }\n            }\n        } catch (error) {\n            console.error(\"获取教师当前模板失败:\", error);\n        }\n        return null;\n    };\n    // 同步教师模板到学生（当教师模板更新时调用）\n    const syncTeacherTemplateToStudents = async ()=>{\n        try {\n            const newTemplate = await fetchTeacherCurrentTemplate();\n            if (newTemplate && students.length > 0) {\n                console.log(\"同步教师模板到学生:\", newTemplate);\n                // 使用 ref 获取最新的 personalTemplateAssignments\n                const currentPersonalAssignments = personalTemplateAssignmentsRef.current;\n                // 更新所有没有个人分配模板的学生\n                setStudents((prevStudents)=>prevStudents.map((student)=>{\n                        // 如果学生有个人分配的模板，保持不变\n                        if (currentPersonalAssignments.has(student.userId)) {\n                            console.log(\"保持学生 \".concat(student.nickName, \" 的个人模板 (syncTeacherTemplateToStudents)\"));\n                            return student;\n                        }\n                        // 否则更新为教师当前模板\n                        console.log(\"更新学生 \".concat(student.nickName, \" 为教师模板 (syncTeacherTemplateToStudents)\"));\n                        return {\n                            ...student,\n                            currentTemplate: newTemplate\n                        };\n                    }));\n            }\n        } catch (error) {\n            console.error(\"同步教师模板失败:\", error);\n        }\n    };\n    // 存储个人分配的模板信息，避免被fetchStudents覆盖\n    const [personalTemplateAssignments, setPersonalTemplateAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    // 添加强制重新渲染的状态\n    const [renderVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // setRenderVersion暂时未使用\n    // 获取用户信息\n    const userId = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector)((state)=>state.user.userState.userId);\n    const roleId = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector)((state)=>state.user.userState.roleId);\n    // 获取班级学生数据\n    const fetchStudents = async ()=>{\n        if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) return;\n        try {\n            setLoading(true);\n            setError(null);\n            // 并行获取班级学生基础数据和教师当前模板\n            const [response] = await Promise.all([\n                _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.getClassStudents(classInfo.id),\n                fetchTeacherCurrentTemplate()\n            ]);\n            if (response.data.code === 200) {\n                const studentsData = response.data.data || [];\n                if (studentsData.length === 0) {\n                    setStudents([]);\n                    setSelectedStudent(null);\n                    // 如果学生数量变为0，也要更新班级信息\n                    if (classInfo.studentCount !== 0) {\n                        const updatedClassInfo = {\n                            ...classInfo,\n                            studentCount: 0\n                        };\n                        setClassInfo(updatedClassInfo);\n                        // 通知父组件更新班级列表中的学生数\n                        onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n                    }\n                    return;\n                }\n                // 提取学生ID列表\n                const userIds = studentsData.map((s)=>s.userId);\n                // 批量获取学生详细信息（包括模板和能量信息）\n                const studentInfoMap = await (userIds.length > 20 ? Promise.all(Array.from({\n                    length: Math.ceil(userIds.length / 20)\n                }, (_, i)=>userIds.slice(i * 20, (i + 1) * 20)).map((batchId)=>_lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.getStudentsBatchInfo(batchId).then((param)=>{\n                        let { data: { data } } = param;\n                        return data;\n                    }).catch((error)=>{\n                        console.error(\"获取学生批量信息失败:\", error);\n                        return {};\n                    }))).then((results)=>Object.assign({}, ...results)) : _lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.getStudentsBatchInfo(userIds).then((param)=>{\n                    let { data: { data } } = param;\n                    return data;\n                }).catch((error)=>{\n                    console.error(\"获取学生批量信息失败:\", error);\n                    return {};\n                }));\n                // 合并学生数据\n                const completeStudents = studentsData.map((s)=>{\n                    const studentInfo = studentInfoMap[s.userId];\n                    // 检查是否有个人分配的模板\n                    const personalTemplate = personalTemplateAssignments.get(s.userId);\n                    return {\n                        ...s,\n                        ...studentInfo,\n                        id: s.userId,\n                        nickName: s.nickName || \"学生\".concat(s.studentNumber || s.userId),\n                        totalPoints: (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.totalPoints) || 0,\n                        availablePoints: (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.availablePoints) || 0,\n                        avatarUrl: s.avatarUrl || \"/default-avatar.png\",\n                        // 优先级：个人分配的模板 > 学生API返回的模板 > 教师当前模板 > null\n                        currentTemplate: (()=>{\n                            var _studentsData_;\n                            const result = personalTemplate || (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.currentTemplate) || globalCurrentTemplate;\n                            // 调试信息\n                            if (s.userId === ((_studentsData_ = studentsData[0]) === null || _studentsData_ === void 0 ? void 0 : _studentsData_.userId)) {\n                                console.log(\"学生模板分配逻辑:\", {\n                                    studentId: s.userId,\n                                    studentName: s.nickName,\n                                    personalTemplate,\n                                    studentApiTemplate: studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.currentTemplate,\n                                    globalCurrentTemplate,\n                                    finalTemplate: result\n                                });\n                            }\n                            return result;\n                        })()\n                    };\n                });\n                setStudents(completeStudents);\n                // 如果学生数量发生变化，更新班级信息并通知父组件\n                if (completeStudents.length !== classInfo.studentCount) {\n                    const updatedClassInfo = {\n                        ...classInfo,\n                        studentCount: completeStudents.length\n                    };\n                    setClassInfo(updatedClassInfo);\n                    // 通知父组件更新班级列表中的学生数\n                    onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n                }\n                // 设置userRoles，与teacher-space保持一致\n                const newUserRoles = completeStudents.map((student)=>({\n                        userId: student.userId,\n                        roleId: 1 // 学生角色ID为1\n                    }));\n                // 添加教师自己的角色\n                newUserRoles.push({\n                    userId: userId,\n                    roleId: 2 // 教师角色ID为2\n                });\n                setUserRoles(newUserRoles);\n                // 默认选择第一个学生\n                if (completeStudents.length > 0) {\n                    setSelectedStudent(completeStudents[0]);\n                }\n            } else {\n                setError(response.data.message || \"获取学生列表失败\");\n            }\n        } catch (err) {\n            console.error(\"获取学生列表失败:\", err);\n            console.error(\"错误详情:\", {\n                message: err instanceof Error ? err.message : \"未知错误\",\n                stack: err instanceof Error ? err.stack : undefined,\n                classId: classInfo.id\n            });\n            setError(\"获取学生列表失败，请稍后重试\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 获取权限模板列表\n    const fetchTemplates = async ()=>{\n        setLoadingTemplates(true);\n        try {\n            const { getRoleTemplateList, getOfficialTemplates } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            // 同时获取教师自定义模板和官方模板\n            const [customResponse, officialResponse] = await Promise.all([\n                getRoleTemplateList(userId),\n                getOfficialTemplates()\n            ]);\n            if (customResponse.data.code === 200 && officialResponse.data.code === 200) {\n                const customTemplates = customResponse.data.data || [];\n                const officialTemplates = officialResponse.data.data || [];\n                // 为官方模板添加标记\n                const markedOfficialTemplates = officialTemplates.map((template)=>({\n                        ...template,\n                        isOfficial: true\n                    }));\n                // 合并所有模板\n                const allTemplates = [\n                    ...customTemplates,\n                    ...markedOfficialTemplates\n                ];\n                setTemplates(allTemplates);\n            }\n        } catch (error) {\n            console.error(\"获取权限模板列表失败:\", error);\n        } finally{\n            setLoadingTemplates(false);\n        }\n    };\n    // 获取模板使用情况\n    const fetchTemplateUsage = async ()=>{\n        try {\n            const { getUserCurrentTemplate, getStudentTemplates } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            // 获取教师使用的模板\n            const teacherResponse = await getUserCurrentTemplate(userId);\n            if (teacherResponse.data.code === 200) {\n                const teacherTemplateData = teacherResponse.data.data;\n                setTeacherTemplate(teacherTemplateData);\n            // 同时更新当前模板状态，确保与模板管理同步\n            // 暂时注释掉以停止循环\n            /*\r\n        if (teacherTemplateData) {\r\n          // 使用refreshCurrentTemplate来更新全局模板状态\r\n          await refreshCurrentTemplate();\r\n        }\r\n        */ }\n            // 获取学生使用的模板\n            const studentResponse = await getStudentTemplates({\n                teacherId: userId,\n                page: 1,\n                size: 200\n            });\n            if (studentResponse.data.code === 200) {\n                // 统计每个模板被使用的次数\n                const usage = {};\n                studentResponse.data.data.list.forEach((item)=>{\n                    if (item.templateId) {\n                        usage[item.templateId] = (usage[item.templateId] || 0) + 1;\n                    }\n                });\n                setStudentTemplateUsage(usage);\n            }\n        } catch (error) {\n            console.error(\"获取模板使用情况失败:\", error);\n        }\n    };\n    // 组件挂载时获取学生数据和模板\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStudents();\n        fetchTemplates();\n        fetchTemplateUsage();\n    }, [\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n        userId\n    ]);\n    // 当组件重新挂载或班级变化时，确保获取最新的当前模板\n    // 暂时注释掉以停止循环\n    /*\r\n  useEffect(() => {\r\n    if (classInfo?.id && userId) {\r\n      refreshCurrentTemplate();\r\n    }\r\n  }, [classInfo?.id, userId]);\r\n  */ // 移除这个useEffect，避免在currentTemplate变化时覆盖个人分配的模板\n    // 使用 useRef 来保存最新的 personalTemplateAssignments\n    const personalTemplateAssignmentsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(personalTemplateAssignments);\n    personalTemplateAssignmentsRef.current = personalTemplateAssignments;\n    // 监听全局模板变化，重新获取教师当前模板并更新学生数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (globalTemplateChangeVersion > 0 && (classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) {\n            console.log(\"检测到模板变化，重新获取教师当前模板\");\n            // 重新获取教师当前模板\n            fetchTeacherCurrentTemplate().then((newTemplate)=>{\n                if (newTemplate && students.length > 0) {\n                    // 不清除个人分配的模板记录，保留学生的个人模板数据\n                    // setPersonalTemplateAssignments(new Map()); // 注释掉这行\n                    // 使用 ref 获取最新的 personalTemplateAssignments\n                    const currentPersonalAssignments = personalTemplateAssignmentsRef.current;\n                    // 只更新没有个人模板的学生为新的教师当前模板\n                    console.log(\"全局模板变化，开始更新学生模板:\", {\n                        newTemplate,\n                        personalTemplateAssignments: Array.from(currentPersonalAssignments.entries()),\n                        studentsCount: students.length\n                    });\n                    setStudents((prevStudents)=>prevStudents.map((student)=>{\n                            const hasPersonalTemplate = currentPersonalAssignments.has(student.userId);\n                            console.log(\"学生 \".concat(student.nickName, \" (\").concat(student.userId, \"):\"), {\n                                hasPersonalTemplate,\n                                currentTemplate: student.currentTemplate,\n                                willUpdate: !hasPersonalTemplate\n                            });\n                            // 如果学生有个人分配的模板，保持不变\n                            if (hasPersonalTemplate) {\n                                console.log(\"保持学生 \".concat(student.nickName, \" 的个人模板\"));\n                                return student;\n                            }\n                            // 否则更新为新的教师当前模板\n                            console.log(\"更新学生 \".concat(student.nickName, \" 为教师模板\"));\n                            return {\n                                ...student,\n                                currentTemplate: newTemplate\n                            };\n                        }));\n                }\n            });\n        }\n    }, [\n        globalTemplateChangeVersion,\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id\n    ]);\n    // 定期检查教师模板更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) return;\n        // 立即检查一次\n        syncTeacherTemplateToStudents();\n        // 设置定时器，每30秒检查一次模板更新\n        const interval = setInterval(()=>{\n            syncTeacherTemplateToStudents();\n        }, 30000); // 30秒\n        return ()=>clearInterval(interval);\n    }, [\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n        students.length\n    ]);\n    // 点击外部关闭下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n            // setIsMoreActionsDropdownOpen(false); // 已移除\n            }\n            if (settingsDropdownRef.current && !settingsDropdownRef.current.contains(event.target)) {\n                setIsSettingsDropdownOpen(false);\n            }\n            if (batchActionsDropdownRef.current && !batchActionsDropdownRef.current.contains(event.target)) {\n                setIsBatchActionsDropdownOpen(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, []);\n    // 处理学生点击选择（用于右侧显示详情）\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n    };\n    // 处理删除学生\n    const handleDeleteStudent = async ()=>{\n        if (!selectedStudent) {\n            alert(\"请先选择要删除的学生\");\n            return;\n        }\n        // 显示确认对话框\n        const confirmed = window.confirm(\"确定要将 \".concat(selectedStudent.nickName, \" 移出班级吗？\\n\\n此操作不可恢复！\"));\n        if (!confirmed) {\n            return;\n        }\n        try {\n            console.log(\"删除学生:\", selectedStudent);\n            // 调用删除学生的API，传入学生的userId数组\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.removeStudentFromClass([\n                selectedStudent.userId\n            ]);\n            console.log(\"删除学生 API 响应:\", response.data);\n            // 检查响应状态\n            if (response.data.code === 200) {\n                console.log(\"删除学生成功\");\n                alert(\"学生已成功移出班级\");\n                // 清除选中的学生\n                setSelectedStudent(null);\n                // 重新获取学生列表\n                await fetchStudents();\n            } else {\n                console.error(\"删除学生失败:\", response.data.message);\n                alert(response.data.message || \"删除学生失败\");\n            }\n        } catch (error) {\n            console.error(\"删除学生失败:\", error);\n            alert(\"删除学生失败，请稍后重试\");\n        }\n    };\n    // 编辑班级\n    const handleEditClass = async (values)=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        try {\n            console.log(\"开始编辑班级:\", {\n                classId: classInfo.id,\n                values: values,\n                originalClassName: classInfo.className\n            });\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.updateClass(classInfo.id, {\n                className: values.className,\n                grade: classInfo.grade || \"\" // 确保传递grade字段，如果没有则使用空字符串\n            });\n            console.log(\"编辑班级API响应:\", response);\n            if (response.data.code === 200) {\n                console.log(\"编辑班级成功\");\n                notification.success(\"编辑班级成功\");\n                setIsEditClassModalVisible(false);\n                // 更新本地班级信息\n                const updatedClassInfo = {\n                    ...classInfo,\n                    className: values.className\n                };\n                setClassInfo(updatedClassInfo);\n                // 通知父组件更新班级列表中的班级信息\n                onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n            } else {\n                console.error(\"编辑班级失败 - API返回错误:\", {\n                    code: response.data.code,\n                    message: response.data.message,\n                    data: response.data\n                });\n                notification.error(response.data.message || \"编辑班级失败\");\n                throw new Error(response.data.message || \"编辑班级失败\"); // 抛出错误让模态框知道失败了\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"编辑班级失败 - 请求异常:\", {\n                error: error,\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // 只有在没有显示过错误消息的情况下才显示通用错误\n            if (!error.message || error.message === \"编辑班级失败\") {\n                var _error_response_data, _error_response2;\n                notification.error(((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"编辑班级失败，请稍后重试\");\n            }\n            throw error; // 重新抛出错误，让模态框保持打开状态\n        }\n    };\n    // 导入学生\n    const handleImportStudents = async (file)=>{\n        try {\n            console.log(\"导入学生文件:\", file);\n            // 这里需要实现文件解析和导入逻辑\n            alert(\"导入学生功能正在开发中\");\n            setIsImportStudentModalVisible(false);\n            return true;\n        } catch (error) {\n            console.error(\"导入学生失败:\", error);\n            alert(\"导入学生失败，请稍后重试\");\n            return false;\n        }\n    };\n    // 导出学生\n    const handleExportStudents = async ()=>{\n        try {\n            const response = await _lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.exportStudents(classInfo.id);\n            console.log(\"导出学生成功:\", response);\n            alert(\"导出学生成功\");\n        } catch (error) {\n            console.error(\"导出学生失败:\", error);\n            alert(\"导出学生失败，请稍后重试\");\n        }\n    };\n    // 搜索教师\n    const handleSearchTeacher = async (phone)=>{\n        try {\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.searchTeacherByPhone(phone);\n            console.log(\"搜索教师响应:\", response);\n            if (response.data.code === 200) {\n                setSearchedTeacher(response.data.data);\n            } else {\n                const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                const notification = GetNotification();\n                notification.error(response.data.message || \"搜索教师失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"搜索教师失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"搜索教师失败\");\n        }\n    };\n    // 转让班级\n    const handleTransferClass = async (values)=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        setTransferLoading(true);\n        try {\n            let newTeacherId;\n            if (values.transferType === \"search\") {\n                if (!searchedTeacher) {\n                    notification.error(\"请先搜索并选择教师\");\n                    return;\n                }\n                newTeacherId = searchedTeacher.id;\n            } else {\n                // 检查是否有协助教师\n                if (!classInfo.assistantTeacherId) {\n                    notification.error(\"该班级没有协助教师\");\n                    return;\n                }\n                newTeacherId = classInfo.assistantTeacherId;\n            }\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.transferClass(classInfo.id, newTeacherId, values.transferType);\n            if (response.data.code === 200) {\n                notification.success(\"转让班级成功\");\n                setIsTransferClassModalVisible(false);\n                setSearchedTeacher(null);\n                // 转让成功后返回班级管理页面\n                onBack();\n            } else {\n                notification.error(response.data.message || \"转让班级失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"转让班级失败:\", error);\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"转让班级失败\");\n        } finally{\n            setTransferLoading(false);\n        }\n    };\n    // 移出协助教师\n    const handleRemoveAssistant = async ()=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        try {\n            // 这里需要调用移出协助教师的API\n            // 暂时使用转让API，将assistantTeacherId设为0\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.updateClass(classInfo.id, {\n                assistantTeacherId: 0\n            });\n            if (response.data.code === 200) {\n                notification.success(\"移出协助教师成功\");\n                setIsTransferClassModalVisible(false);\n                // 更新本地班级信息\n                const updatedClassInfo = {\n                    ...classInfo,\n                    assistantTeacherId: 0\n                };\n                setClassInfo(updatedClassInfo);\n                onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n            } else {\n                notification.error(response.data.message || \"移出协助教师失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"移出协助教师失败:\", error);\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"移出协助教师失败\");\n        }\n    };\n    // 生成邀请码\n    const handleGenerateInviteCode = async ()=>{\n        try {\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.generateInviteCode(classInfo.id);\n            if (response.data.code === 200) {\n                setInviteCode(response.data.data.inviteCode);\n                setIsInviteCodeModalVisible(true);\n            } else {\n                console.error(\"生成邀请码失败:\", response.data.message);\n                alert(response.data.message || \"生成邀请码失败\");\n            }\n        } catch (error) {\n            console.error(\"生成邀请码失败:\", error);\n            alert(\"生成邀请码失败，请稍后重试\");\n        }\n    };\n    // 分配积木（批量或选中学生）\n    const handleAssignBlocks = async ()=>{\n        console.log(\"=== 分配积木开始 ===\");\n        console.log(\"selectedStudentIds:\", selectedStudentIds);\n        console.log(\"selectedStudentIds.length:\", selectedStudentIds.length);\n        // 如果有选中的学生，设置为单个学生分配模式\n        if (selectedStudentIds.length === 1) {\n            console.log(\"单个学生分配模式，studentId:\", selectedStudentIds[0]);\n            setSelectedStudentId(selectedStudentIds[0]);\n        } else {\n            console.log(\"批量分配模式，学生数量:\", selectedStudentIds.length);\n            setSelectedStudentId(null);\n        }\n        await fetchTemplates();\n        await fetchTemplateUsage();\n        setIsAssignBlocksModalVisible(true);\n    };\n    // 单独为学生分配积木\n    const handleIndividualAssignBlocks = async (studentId)=>{\n        console.log(\"=== 单独为学生分配积木 ===\");\n        console.log(\"studentId:\", studentId);\n        // 设置为单个学生分配模式\n        setSelectedStudentId(studentId);\n        setSelectedStudentIds([]); // 清空批量选择\n        await fetchTemplates();\n        await fetchTemplateUsage();\n        setIsAssignBlocksModalVisible(true);\n    };\n    // 处理模板分配（从模板卡片点击）\n    const handleTemplateAssignment = async ()=>{\n        if (!selectedStudent) return;\n        console.log(\"=== 从模板卡片分配模板 ===\");\n        console.log(\"selectedStudent:\", selectedStudent);\n        await handleIndividualAssignBlocks(selectedStudent.userId);\n    };\n    // 删除班级\n    const handleDeleteClass = async ()=>{\n        const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        // 先检查是否有学生\n        if (students.length > 0) {\n            Modal.warning({\n                title: \"无法删除班级\",\n                centered: true,\n                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"班级中还有 \",\n                                students.length,\n                                \" 名学生，请先移除所有学生后再删除班级。\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 805,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-gray-500 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"删除步骤：\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 807,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    className: \"list-decimal ml-4 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"选择要移除的学生\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"使用批量操作移除所有学生\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"再次尝试删除班级\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 808,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 806,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 804,\n                    columnNumber: 11\n                }, undefined),\n                okText: \"知道了\"\n            });\n            return;\n        }\n        // 如果没有学生，显示删除确认对话框\n        Modal.confirm({\n            title: \"确认删除班级\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"您确定要删除 \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: classInfo.className\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 826,\n                                columnNumber: 21\n                            }, undefined),\n                            \" 吗？\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 826,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-3 bg-red-50 rounded-lg border border-red-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 830,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"此操作不可恢复！\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 832,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 828,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-500\",\n                                children: \"删除班级将永久移除班级信息，包括班级设置、模板配置等数据。\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 834,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 827,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 825,\n                columnNumber: 9\n            }, undefined),\n            okText: \"确定删除\",\n            cancelText: \"取消\",\n            okButtonProps: {\n                danger: true\n            },\n            centered: true,\n            onOk: async ()=>{\n                try {\n                    const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.deleteClass(classInfo.id);\n                    if (response.data.code === 200) {\n                        notification.success(\"删除班级成功\");\n                        // 通知父组件班级已被删除\n                        onClassDeleted === null || onClassDeleted === void 0 ? void 0 : onClassDeleted(classInfo.id);\n                        // 返回到班级管理页面\n                        onBack();\n                    } else {\n                        notification.error(response.data.message || \"删除班级失败\");\n                    }\n                } catch (error) {\n                    var _error_response_data, _error_response;\n                    console.error(\"删除班级失败:\", error);\n                    if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                        notification.error(error.response.data.message);\n                    } else {\n                        notification.error(\"删除班级失败，请稍后重试\");\n                    }\n                }\n            }\n        });\n    };\n    // 下拉菜单项处理函数\n    const handleMenuItemClick = (action)=>{\n        // setIsMoreActionsDropdownOpen(false); // 已移除\n        switch(action){\n            case \"edit\":\n                setIsEditClassModalVisible(true);\n                break;\n            case \"addStudent\":\n                setIsAddStudentModalVisible(true);\n                break;\n            case \"importStudent\":\n                setIsImportStudentModalVisible(true);\n                break;\n            case \"exportStudent\":\n                handleExportStudents();\n                break;\n            case \"transfer\":\n                setIsTransferClassModalVisible(true);\n                break;\n            case \"invite\":\n                handleGenerateInviteCode();\n                break;\n            case \"batchRedeem\":\n                console.log(\"批量兑换密令\");\n                setIsBatchUseKeyPackageModalVisible(true);\n                break;\n            case \"assignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"deleteClass\":\n                handleDeleteClass();\n                break;\n            default:\n                break;\n        }\n    };\n    // 设置下拉菜单项处理函数\n    const handleSettingsMenuItemClick = (action)=>{\n        setIsSettingsDropdownOpen(false);\n        switch(action){\n            case \"edit\":\n                setIsEditClassModalVisible(true);\n                break;\n            case \"addStudent\":\n                setIsAddStudentModalVisible(true);\n                break;\n            case \"importStudent\":\n                setIsImportStudentModalVisible(true);\n                break;\n            case \"exportStudent\":\n                handleExportStudents();\n                break;\n            case \"transfer\":\n                setIsTransferClassModalVisible(true);\n                break;\n            case \"invite\":\n                handleGenerateInviteCode();\n                break;\n            case \"batchRedeem\":\n                console.log(\"批量兑换密令\");\n                setIsBatchUseKeyPackageModalVisible(true);\n                break;\n            case \"assignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"deleteClass\":\n                handleDeleteClass();\n                break;\n            default:\n                break;\n        }\n    };\n    // 确保选中状态同步\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const shouldBeSelectAll = selectedStudentIds.length > 0 && selectedStudentIds.length === students.length;\n        if (isSelectAll !== shouldBeSelectAll) {\n            console.log(\"修复选中状态同步:\", {\n                isSelectAll,\n                shouldBeSelectAll,\n                selectedStudentIds: selectedStudentIds.length,\n                totalStudents: students.length\n            });\n            setIsSelectAll(shouldBeSelectAll);\n        }\n    }, [\n        selectedStudentIds,\n        students.length,\n        isSelectAll\n    ]);\n    // 全选/取消全选处理函数\n    const handleSelectAll = ()=>{\n        if (isSelectAll) {\n            // 取消全选\n            setSelectedStudentIds([]);\n            setIsSelectAll(false);\n        } else {\n            // 全选\n            const allStudentIds = students.map((student)=>student.userId);\n            setSelectedStudentIds(allStudentIds);\n            setIsSelectAll(true);\n        }\n    };\n    // 单个学生选择处理函数\n    const handleStudentSelect = (studentId)=>{\n        if (selectedStudentIds.includes(studentId)) {\n            // 取消选择\n            const newSelectedIds = selectedStudentIds.filter((id)=>id !== studentId);\n            setSelectedStudentIds(newSelectedIds);\n            setIsSelectAll(false);\n        } else {\n            // 选择\n            const newSelectedIds = [\n                ...selectedStudentIds,\n                studentId\n            ];\n            setSelectedStudentIds(newSelectedIds);\n            // 检查是否全选\n            if (newSelectedIds.length === students.length) {\n                setIsSelectAll(true);\n            }\n        }\n    };\n    // 批量操作处理函数\n    const handleBatchAction = (action)=>{\n        setIsBatchActionsDropdownOpen(false);\n        if (selectedStudentIds.length === 0) {\n            Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                let { GetNotification } = param;\n                GetNotification().warning(\"请先选择要操作的学生\");\n            });\n            return;\n        }\n        switch(action){\n            case \"batchDelete\":\n                handleBatchRemoveStudents(selectedStudentIds);\n                break;\n            case \"batchAssignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"batchAssignPoints\":\n                // 清除单个学生选择，确保进入批量模式\n                setSelectedStudent(null);\n                setIsAssignPointsModalVisible(true);\n                break;\n            case \"batchUseKeyPackage\":\n                handleBatchUseKeyPackage();\n                break;\n            case \"batchExport\":\n                console.log(\"批量导出学生:\", selectedStudentIds);\n                Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                    let { GetNotification } = param;\n                    GetNotification().info(\"批量导出学生功能正在开发中\");\n                });\n                break;\n            default:\n                break;\n        }\n    };\n    // 批量移出班级的改进版本\n    const handleBatchRemoveStudents = async (studentIds)=>{\n        try {\n            // 获取选中的学生信息\n            const selectedStudentsInfo = students.filter((s)=>studentIds.includes(s.userId));\n            // 计算总可用积分\n            const totalAvailablePoints = selectedStudentsInfo.reduce((sum, student)=>sum + (student.availablePoints || 0), 0);\n            const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n            const { InfoCircleOutlined } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_ant-design_icons_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/index.js\"));\n            Modal.confirm({\n                title: \"确认批量移出班级\",\n                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"确定要将选中的 \",\n                                studentIds.length,\n                                \" 名学生移出班级吗？\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1047,\n                            columnNumber: 13\n                        }, undefined),\n                        totalAvailablePoints > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 p-3 bg-yellow-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-yellow-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoCircleOutlined, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1051,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"选中的学生共有 \",\n                                                totalAvailablePoints,\n                                                \" 点可用能量\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1052,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1050,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-sm text-yellow-500\",\n                                    children: \"移出班级后，可用能量将返还到各自的套餐积分中\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1054,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1049,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1046,\n                    columnNumber: 11\n                }, undefined),\n                okText: \"确定移出\",\n                cancelText: \"取消\",\n                centered: true,\n                okButtonProps: {\n                    danger: true\n                },\n                onOk: async ()=>{\n                    try {\n                        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                        const notification = GetNotification();\n                        const hideLoading = notification.loading(\"正在移出学生...\");\n                        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.removeStudentFromClass(studentIds);\n                        if (hideLoading) {\n                            hideLoading.close();\n                        }\n                        if (response.data.code === 200) {\n                            notification.success(\"成功移出 \".concat(studentIds.length, \" 名学生\"));\n                            // 清除选择状态\n                            setSelectedStudentIds([]);\n                            setIsSelectAll(false);\n                            setSelectedStudent(null);\n                            // 重新获取学生列表\n                            await fetchStudents();\n                        } else {\n                            notification.error(response.data.message || \"批量移出学生失败\");\n                        }\n                    } catch (error) {\n                        var _error_response_data, _error_response;\n                        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                        const notification = GetNotification();\n                        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"批量移出学生失败\");\n                        console.error(\"批量移出学生失败:\", error);\n                    }\n                }\n            });\n        } catch (error) {\n            console.error(\"批量移出学生失败:\", error);\n        }\n    };\n    // 处理添加学生\n    const handleAddStudent = async (values)=>{\n        try {\n            console.log(\"添加学生:\", values);\n            // 添加默认密码\n            const studentData = {\n                ...values,\n                password: \"123456\"\n            };\n            // 调用添加学生的API\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.addStudentToClass(classInfo.id, studentData);\n            console.log(\"添加学生 API 响应:\", response.data);\n            // 检查响应状态\n            if (response.data.code === 200) {\n                console.log(\"添加学生成功\");\n                setIsAddStudentModalVisible(false);\n                // 重新获取学生列表\n                await fetchStudents();\n            } else {\n                console.error(\"添加学生失败:\", response.data.message);\n                alert(response.data.message || \"添加学生失败\");\n            }\n        } catch (error) {\n            console.error(\"添加学生失败:\", error);\n            alert(\"添加学生失败，请稍后重试\");\n        }\n    };\n    // 处理文件上传\n    const handleUpload = async (file)=>{\n        setUploading(true);\n        try {\n            // 这里可以添加文件上传逻辑\n            console.log(\"上传文件:\", file);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error(\"文件上传失败:\", error);\n            return {\n                success: false\n            };\n        } finally{\n            setUploading(false);\n        }\n    };\n    // 处理文件删除\n    const handleRemoveFile = async (file)=>{\n        try {\n            setFileList((prev)=>prev.filter((f)=>f.uid !== file.uid));\n            return true;\n        } catch (error) {\n            console.error(\"删除文件失败:\", error);\n            return false;\n        }\n    };\n    // 处理发布任务\n    const handlePublishTask = async (values)=>{\n        try {\n            console.log(\"发布任务:\", values);\n            // 导入taskApi\n            const { default: taskApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/task */ \"(app-pages-browser)/./lib/api/task.ts\"));\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 构建任务参数\n            const params = {\n                taskName: values.taskName,\n                taskDescription: values.taskDescription || \"\",\n                taskType: 1,\n                startDate: values.startDate ? new Date(values.startDate) : undefined,\n                endDate: values.endDate ? new Date(values.endDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n                taskContent: values.taskContent || \"\",\n                attachments: fileList.map((file)=>{\n                    var _file_response;\n                    return file.url || ((_file_response = file.response) === null || _file_response === void 0 ? void 0 : _file_response.url);\n                }).filter(Boolean),\n                teacherId: userId,\n                classId: classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n                studentIds: selectedStudentIds.length > 0 ? selectedStudentIds : students.map((s)=>s.userId),\n                selfAssessmentItems: values.selfAssessmentItems || [],\n                priority: 1,\n                isPublic: 0,\n                allowLateSubmission: values.allowLateSubmission || false\n            };\n            console.log(\"发布任务参数:\", params);\n            // 调用发布任务API\n            const response = await taskApi.publishTask(params);\n            if (response.data.code === 200) {\n                // 显示详细的成功提示\n                const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n                const studentCount = selectedStudentIds.length > 0 ? selectedStudentIds.length : students.length;\n                const className = (classInfo === null || classInfo === void 0 ? void 0 : classInfo.className) || \"当前班级\";\n                Modal.success({\n                    title: \"任务发布成功\",\n                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"任务 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: params.taskName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1204,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    \" 已成功发布到 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: className\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1204,\n                                        columnNumber: 63\n                                    }, undefined),\n                                    \"。\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1204,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"共有 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: studentCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1205,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    \" 名学生将收到此任务。\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1205,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"学生可以在班级空间查看和提交任务。\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1206,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1203,\n                        columnNumber: 13\n                    }, undefined),\n                    okText: \"确定\",\n                    onOk: ()=>{\n                    // 可以在这里添加导航到任务管理页面的逻辑\n                    }\n                });\n                setIsPublishTaskModalVisible(false);\n                // 清理表单数据\n                setFileList([]);\n                setSelectedStudentIds([]);\n                setSelectedTemplateId(null);\n                // 刷新学生列表（如果需要显示任务相关信息）\n                await fetchStudents();\n            } else {\n                notification.error(response.data.message || \"任务发布失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"发布任务失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"发布任务失败，请稍后重试\");\n        }\n    };\n    // 处理重置密码\n    const handleResetPassword = async ()=>{\n        try {\n            if (!selectedStudent) {\n                alert(\"请先选择要重置密码的学生\");\n                return;\n            }\n            console.log(\"重置密码:\", selectedStudent);\n            // 这里可以添加重置密码的API调用\n            setIsResetPasswordModalVisible(false);\n            alert(\"密码重置成功，新密码为：123456\");\n        } catch (error) {\n            console.error(\"重置密码失败:\", error);\n            alert(\"重置密码失败，请稍后重试\");\n        }\n    };\n    // 处理选择模板\n    const handleSelectTemplate = async (templateId)=>{\n        try {\n            const { addUserJoinRole, batchAddUserJoinRole } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 确定要分配的学生\n            const targetStudents = selectedStudentId !== null ? [\n                selectedStudentId\n            ] : selectedStudentIds;\n            console.log(\"=== 模板分配详情 ===\");\n            console.log(\"selectedStudentId:\", selectedStudentId);\n            console.log(\"selectedStudentIds:\", selectedStudentIds);\n            console.log(\"targetStudents:\", targetStudents);\n            console.log(\"templateId:\", templateId);\n            if (targetStudents.length === 0) {\n                console.log(\"❌ 没有选中任何学生\");\n                notification.warning(\"请先选择学生\");\n                return;\n            }\n            const hideLoading = notification.loading(\"正在分配积木...\");\n            const userRolesMap = userRoles || [];\n            try {\n                // 准备用户数据 - 与teacher-space保持一致的逻辑\n                const usersData = targetStudents.map((userId)=>{\n                    const userInfo = userRolesMap.find((u)=>u.userId === userId);\n                    if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.roleId)) return null;\n                    return {\n                        userId: userId,\n                        roleId: userInfo.roleId,\n                        templateId: templateId,\n                        originalTemplateId: templateId\n                    };\n                }).filter((item)=>item !== null);\n                console.log(\"准备分配模板:\", {\n                    templateId,\n                    targetStudents,\n                    userRolesMap,\n                    usersData\n                });\n                if (usersData.length === 0) {\n                    notification.error(\"无有效用户可分配\");\n                    return;\n                }\n                // 分批并发处理\n                const results = await (usersData.length > 20 ? Promise.all(Array.from({\n                    length: Math.ceil(usersData.length / 20)\n                }, (_, i)=>usersData.slice(i * 20, (i + 1) * 20)).map((batchUsers)=>batchAddUserJoinRole({\n                        users: batchUsers\n                    }).then((param)=>{\n                        let { data } = param;\n                        return data;\n                    }).catch(()=>({\n                            code: 500,\n                            data: {\n                                successCount: 0,\n                                failCount: batchUsers.length\n                            }\n                        })))).then((results)=>({\n                        code: results.some((r)=>r.code !== 200) ? 500 : 200,\n                        data: {\n                            successCount: results.reduce((sum, r)=>{\n                                var _r_data;\n                                return sum + (((_r_data = r.data) === null || _r_data === void 0 ? void 0 : _r_data.successCount) || 0);\n                            }, 0),\n                            failCount: results.reduce((sum, r)=>{\n                                var _r_data;\n                                return sum + (((_r_data = r.data) === null || _r_data === void 0 ? void 0 : _r_data.failCount) || 0);\n                            }, 0)\n                        }\n                    })) : batchAddUserJoinRole({\n                    users: usersData\n                }).then((param)=>{\n                    let { data } = param;\n                    return data;\n                }).catch(()=>({\n                        code: 500,\n                        data: {\n                            successCount: 0,\n                            failCount: usersData.length\n                        }\n                    })));\n                if (hideLoading) {\n                    hideLoading.close();\n                }\n                // 显示结果\n                if (results.code === 200) {\n                    const { successCount = 0, failCount = 0 } = results.data || {};\n                    if (successCount > 0 && failCount === 0) {\n                        notification.success(\"成功为 \".concat(successCount, \" 名学生分配积木\"));\n                    } else if (successCount > 0 && failCount > 0) {\n                        notification.warning(\"成功为 \".concat(successCount, \" 名学生分配积木，\").concat(failCount, \" 名学生分配失败\"));\n                    } else {\n                        notification.error(\"积木分配失败\");\n                    }\n                } else {\n                    notification.error(\"积木分配失败\");\n                }\n                // 立即更新已分配学生的模板信息，无需等待API刷新\n                const selectedTemplate = templates.find((t)=>t.id === templateId);\n                if (selectedTemplate) {\n                    const templateData = {\n                        templateId: templateId,\n                        templateName: selectedTemplate.templateName || selectedTemplate.name,\n                        isOfficial: selectedTemplate.isOfficial || false\n                    };\n                    // 更新personalTemplateAssignments Map，确保数据持久化\n                    setPersonalTemplateAssignments((prev)=>{\n                        const newMap = new Map(prev);\n                        targetStudents.forEach((studentId)=>{\n                            newMap.set(studentId, templateData);\n                        });\n                        return newMap;\n                    });\n                    // 更新学生状态\n                    const updatedStudents = students.map((student)=>{\n                        if (targetStudents.includes(student.userId)) {\n                            // 被选中的学生：设置为新分配的模板\n                            return {\n                                ...student,\n                                currentTemplate: templateData\n                            };\n                        }\n                        // 未选中的学生：保持原有状态\n                        return student;\n                    });\n                    setStudents(updatedStudents);\n                    console.log(\"模板分配成功，已更新personalTemplateAssignments:\", {\n                        targetStudents,\n                        templateData,\n                        personalTemplateAssignmentsBefore: Array.from(personalTemplateAssignments.entries()),\n                        personalTemplateAssignmentsAfter: \"will be updated\"\n                    });\n                    // 延迟打印更新后的状态\n                    setTimeout(()=>{\n                        console.log(\"personalTemplateAssignments更新后:\", Array.from(personalTemplateAssignments.entries()));\n                    }, 100);\n                }\n                // 关闭弹窗并清理状态\n                setIsAssignBlocksModalVisible(false);\n                setSelectedStudentId(null);\n                setSelectedStudentIds([]); // 清空选中的学生\n                setIsSelectAll(false); // 取消全选状态\n                // 刷新相关数据\n                await fetchTemplateUsage(); // 刷新模板使用情况\n            // 当前模板信息由全局状态管理，无需手动刷新\n            } catch (error) {\n                if (hideLoading) {\n                    hideLoading.close();\n                }\n                throw error;\n            }\n        } catch (error) {\n            console.error(\"分配模板失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"分配模板失败，请稍后重试\");\n        }\n    };\n    // 处理模板使用情况点击\n    const handleTemplateUsageClick = (e, template)=>{\n        e.stopPropagation();\n        console.log(\"查看模板使用情况:\", template);\n    };\n    // 处理单个学生分配能量\n    const handleAssignPoints = async (values)=>{\n        var _values_studentExpiries;\n        if (!selectedStudent) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"请先选择学生\");\n            return;\n        }\n        // 从 studentExpiries 中提取单个学生的过期时间\n        const expireTime = (_values_studentExpiries = values.studentExpiries) === null || _values_studentExpiries === void 0 ? void 0 : _values_studentExpiries[selectedStudent.userId];\n        try {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            const hideLoading = notification.loading(\"正在分配能量...\");\n            const { pointsApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\"));\n            await pointsApi.assignPermission({\n                studentUserId: selectedStudent.userId,\n                availablePoints: values.availablePoints,\n                expireTime: expireTime,\n                remark: values.remark\n            });\n            if (hideLoading) {\n                hideLoading.close();\n            }\n            notification.success(\"分配能量成功\");\n            setIsAssignPointsModalVisible(false);\n            // 刷新学生列表\n            await refreshStudentList();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 增加更具体的错误提示\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"分配能量失败\");\n        }\n    };\n    // 刷新学生列表\n    const refreshStudentList = async ()=>{\n        await fetchStudents();\n    };\n    // 更新教师的当前模板（通过UserJoinRole表）\n    const updateClassCurrentTemplate = async (templateId, templateName, isOfficial)=>{\n        try {\n            console.log(\"更新教师当前模板:\", {\n                userId,\n                roleId,\n                templateId,\n                templateName,\n                isOfficial\n            });\n            // 使用addUserJoinRole API来更新教师的模板\n            const { addUserJoinRole } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            const response = await addUserJoinRole({\n                userId: userId,\n                roleId: roleId || 2,\n                templateId: templateId\n            });\n            if (response.data.code === 200) {\n                // 更新本地的全局当前模板状态\n                const newTemplate = {\n                    templateId: templateId,\n                    templateName: templateName,\n                    isOfficial: isOfficial\n                };\n                setGlobalCurrentTemplate(newTemplate);\n                console.log(\"教师当前模板更新成功:\", newTemplate);\n            } else {\n                console.error(\"更新教师当前模板失败:\", response.data);\n            }\n        } catch (error) {\n            console.error(\"更新教师当前模板失败:\", error);\n        }\n    };\n    // 更新学生的当前模板信息\n    const handleUpdateStudentTemplate = (studentIds, templateInfo)=>{\n        const templateData = {\n            templateId: templateInfo.templateId,\n            templateName: templateInfo.templateName,\n            isOfficial: templateInfo.isOfficial || false\n        };\n        // 保存个人分配的模板信息\n        setPersonalTemplateAssignments((prev)=>{\n            const newMap = new Map(prev);\n            studentIds.forEach((studentId)=>{\n                newMap.set(studentId, templateData);\n            });\n            return newMap;\n        });\n        // 更新学生状态\n        setStudents((prevStudents)=>prevStudents.map((student)=>{\n                if (studentIds.includes(student.userId)) {\n                    return {\n                        ...student,\n                        currentTemplate: templateData\n                    };\n                }\n                return student;\n            }));\n    // 不再自动更新教师模板，只分配给学生\n    // updateClassCurrentTemplate(templateData.templateId, templateData.templateName, templateData.isOfficial);\n    };\n    // 处理批量/单个兑换密钥\n    const handleBatchUseKeyPackage = (studentId)=>{\n        if (studentId) {\n            // 单个学生触发\n            setSelectedStudentIds([\n                studentId\n            ]);\n            setIsBatchUseKeyPackageModalVisible(true);\n        } else {\n            // 批量操作触发\n            if (selectedStudentIds.length === 0) {\n                Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                    let { GetNotification } = param;\n                    GetNotification().warning(\"请先选择学生\");\n                });\n                return;\n            }\n            setIsBatchUseKeyPackageModalVisible(true);\n        }\n    };\n    // 处理兑换密令成功\n    const handleBatchUseKeyPackageSuccess = ()=>{\n        refreshStudentList();\n        setIsBatchUseKeyPackageModalVisible(false);\n    };\n    // 处理从兑换密令跳转到分配能量\n    const handleGoToAssignPointsFromRedeem = (studentIds)=>{\n        setIsBatchUseKeyPackageModalVisible(false);\n        // 设置选中的学生\n        setSelectedStudentIds(studentIds);\n        // 打开分配能量弹窗\n        setIsAssignPointsModalVisible(true);\n    };\n    // 批量分配能量处理函数\n    const handleBatchAssignPoints = async (values)=>{\n        if (selectedStudentIds.length === 0) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"请先选择学生\");\n            return;\n        }\n        if (!values.studentExpiries) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"未能获取学生过期时间信息\");\n            return;\n        }\n        try {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            const hideLoading = notification.loading(\"正在批量分配能量...\");\n            // 调用新的批量分配 API\n            const { pointsApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\"));\n            const response = await pointsApi.batchAssignPermission({\n                availablePoints: values.availablePoints,\n                studentExpiries: values.studentExpiries,\n                remark: values.remark\n            });\n            console.log(\"批量分配积分res\", response);\n            if (hideLoading) {\n                hideLoading.close();\n            }\n            if (response.data.code === 200) {\n                // 后端现在返回处理结果数组，可以根据需要处理\n                const results = response.data.data;\n                // 可以根据 results 中的信息给出更详细的成功/失败提示，\n                // 但为了简单起见，我们仍然使用之前的逻辑\n                notification.success(\"成功为 \".concat(results.success, \" 名学生分配能量\"));\n                setIsAssignPointsModalVisible(false);\n                // 刷新学生列表\n                await refreshStudentList(); // 确保使用 await\n                setSelectedStudentIds([]); // 清空选择\n                setIsSelectAll(false);\n            } else {\n                // 处理 API 返回的错误信息\n                notification.error(response.data.message || \"批量分配能量失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"批量分配能量失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 处理请求级别的错误\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"批量分配能量失败，请检查网络连接或稍后重试\");\n        }\n    };\n    if (!selectedSchool) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"class-detail-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-content\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"error-message\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"学校信息不存在\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1634,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onBack,\n                            className: \"back-button\",\n                            children: \"返回班级管理\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1635,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1633,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1632,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n            lineNumber: 1631,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 生成随机头像颜色\n    const getAvatarColor = (index)=>{\n        const colors = [\n            \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n            \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n            \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n            \"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)\",\n            \"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\",\n            \"linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)\"\n        ];\n        // 确保index是有效的正数\n        const safeIndex = Math.max(0, index);\n        return colors[safeIndex % colors.length];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"class-detail-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"back-button\",\n                    onClick: onBack,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1667,\n                            columnNumber: 11\n                        }, undefined),\n                        \"返回班级管理\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1663,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1662,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"left-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"left-section-header\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"settings-container\",\n                                    ref: settingsDropdownRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"settings-btn \".concat(isSettingsDropdownOpen ? \"active\" : \"\"),\n                                            onClick: ()=>setIsSettingsDropdownOpen(!isSettingsDropdownOpen),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1682,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1678,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isSettingsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"dropdown-menu\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-menu-items\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"edit\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#8b5cf6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1693,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1692,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"编辑班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1695,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1688,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"addStudent\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#10b981\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1703,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1702,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"添加学生\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1705,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1698,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"importStudent\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#3b82f6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1713,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1712,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"导入学生\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1715,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1708,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"exportStudent\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#10b981\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1723,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1722,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"导出学生\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1725,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1718,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"transfer\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#f59e0b\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1733,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1732,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"转让管理\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1735,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1728,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"invite\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#3b82f6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1743,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1742,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"生成邀请码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1745,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1738,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"batchRedeem\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#f59e0b\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1753,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1752,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"批量兑换密令\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1755,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1748,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"assignBlocks\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#3b82f6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1763,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1762,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"分配积木\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1765,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1758,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-divider\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1768,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item danger\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"deleteClass\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1775,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1774,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"删除班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1777,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1770,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1687,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1686,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1677,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1676,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"class-info-card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"class-card-header\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"class-card-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"class-card-icon\",\n                                                    children: \"\\uD83D\\uDCCB\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1789,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"class-card-info\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"class-card-title\",\n                                                            children: classInfo.className\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1793,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"class-card-subtitle\",\n                                                            children: selectedSchool.schoolName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1794,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1792,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1788,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"class-card-badge\",\n                                            children: \"当前模板\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1797,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1787,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1786,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"search-section\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"search-box\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        size: 16,\n                                        className: \"search-icon\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1806,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1805,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1804,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"students-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"students-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"学生\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1813,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"students-actions\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"add-student-btn\",\n                                                        onClick: ()=>setIsAddStudentModalVisible(true),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1819,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1815,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"select-all-btn \".concat(isSelectAll ? \"active\" : \"\"),\n                                                        onClick: ()=>{\n                                                            console.log(\"点击全选按钮，当前状态:\", {\n                                                                isSelectAll,\n                                                                selectedStudentIds: selectedStudentIds.length,\n                                                                totalStudents: students.length\n                                                            });\n                                                            handleSelectAll();\n                                                        },\n                                                        title: isSelectAll ? \"取消全选\" : \"全选\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: isSelectAll ? \"取消全选\" : \"全选\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1833,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1821,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    selectedStudentIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"batch-actions-container\",\n                                                        ref: batchActionsDropdownRef,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"batch-actions-btn\",\n                                                                onClick: ()=>setIsBatchActionsDropdownOpen(!isBatchActionsDropdownOpen),\n                                                                title: \"批量操作\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"批量操作(\",\n                                                                            selectedStudentIds.length,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1844,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1845,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1839,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            isBatchActionsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"batch-actions-dropdown\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchAssignBlocks\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1855,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量分配积木\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1856,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1851,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchAssignPoints\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1862,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量分配能量\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1863,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1858,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchUseKeyPackage\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1869,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量兑换密令\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1870,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1865,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchDelete\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1876,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量移出班级\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1877,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1872,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchExport\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1883,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量导出\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1884,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1879,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1850,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1838,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1814,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1812,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"students-list\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            students: students,\n                                            loading: loading,\n                                            error: error,\n                                            selectedStudent: selectedStudent,\n                                            selectedStudentIds: selectedStudentIds,\n                                            currentTemplate: globalCurrentTemplate || currentTemplate,\n                                            renderVersion: renderVersion,\n                                            onStudentClick: handleStudentClick,\n                                            onStudentSelect: handleStudentSelect,\n                                            onRetry: fetchStudents,\n                                            onIndividualAssignBlocks: handleIndividualAssignBlocks,\n                                            onAssignPoints: (studentId)=>{\n                                                const student = students.find((s)=>s.userId === studentId);\n                                                if (student) {\n                                                    setSelectedStudent(student);\n                                                    setIsAssignPointsModalVisible(true);\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1894,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1893,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1811,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1674,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"right-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"student-info-header\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"student-avatar-large\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"student-avatar-circle\",\n                                            style: {\n                                                background: selectedStudent ? getAvatarColor(Math.max(0, students.findIndex((s)=>s.userId === selectedStudent.userId))) : getAvatarColor(0)\n                                            },\n                                            children: selectedStudent ? ((_selectedStudent_nickName = selectedStudent.nickName) === null || _selectedStudent_nickName === void 0 ? void 0 : _selectedStudent_nickName.charAt(0)) || \"S\" : \"S\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1923,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1922,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"student-details\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"student-name-large\",\n                                                children: selectedStudent ? selectedStudent.nickName || \"学生\".concat(selectedStudent.studentNumber || selectedStudent.userId) : \"请选择学生\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1935,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"student-id-large\",\n                                                children: selectedStudent ? selectedStudent.studentNumber || \"无学号\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1938,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1934,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"delete-student-btn\",\n                                        onClick: handleDeleteStudent,\n                                        disabled: !selectedStudent,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1947,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1942,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1921,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"functions-container\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"functions-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"更多功能\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1955,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"function-buttons\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn publish-task\",\n                                                        onClick: ()=>setIsPublishTaskModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83D\\uDCDD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1961,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"发布任务\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1962,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1957,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn distribute-blocks\",\n                                                        onClick: handleAssignBlocks,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83E\\uDDE9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1968,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"分配积木\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1969,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1964,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn distribute-energy\",\n                                                        onClick: ()=>setIsAssignPointsModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"⚡\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1975,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"分配能量\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1976,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1971,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn exchange-tokens\",\n                                                        onClick: ()=>setIsBatchUseKeyPackageModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83C\\uDF81\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1982,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"兑换密令\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1983,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1978,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn reset-password\",\n                                                        onClick: ()=>setIsResetPasswordModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83D\\uDD11\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1989,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"重置密码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1990,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1985,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1956,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1954,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"learning-status\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"课程学习情况\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1997,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"status-placeholder\",\n                                                children: \"该区域功能正在等待开放\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1998,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1996,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1952,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bottom-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"energy-progress-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"energy-progress-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"可用能量/总能量\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2010,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.availablePoints) || 0,\n                                                            \"/\",\n                                                            (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.totalPoints) || 0\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2011,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2009,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"energy-progress-bar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"energy-progress-fill\",\n                                                    style: {\n                                                        width: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.totalPoints) ? \"\".concat(Number(selectedStudent.availablePoints || 0) / Number(selectedStudent.totalPoints || 0) * 100, \"%\") : \"0%\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2016,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2015,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2008,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"template-card-section\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"template-card\",\n                                            onClick: (e)=>{\n                                                // 只有点击卡片主体区域才触发，避免与交换图标冲突\n                                                if (e.target === e.currentTarget || e.target.closest(\".template-card-left\")) {\n                                                    selectedStudent && handleTemplateAssignment();\n                                                }\n                                            },\n                                            style: {\n                                                cursor: selectedStudent ? \"pointer\" : \"default\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"template-card-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"template-card-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"template-card-icon\",\n                                                                children: \"\\uD83D\\uDCCB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 2041,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"template-card-info\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"template-card-title\",\n                                                                        children: (()=>{\n                                                                            if (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.currentTemplate) {\n                                                                                return selectedStudent.currentTemplate.templateName;\n                                                                            } else if (globalCurrentTemplate) {\n                                                                                return globalCurrentTemplate.templateName;\n                                                                            } else if (currentTemplate) {\n                                                                                return currentTemplate.templateName;\n                                                                            } else if (selectedStudent) {\n                                                                                return \"加载中...\";\n                                                                            } else {\n                                                                                return \"请选择学生\";\n                                                                            }\n                                                                        })()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 2045,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"template-card-subtitle\",\n                                                                        children: selectedStudent ? ((_selectedStudent_currentTemplate = selectedStudent.currentTemplate) === null || _selectedStudent_currentTemplate === void 0 ? void 0 : _selectedStudent_currentTemplate.isOfficial) ? \"官方模板\" : \"自定义模板\" : \"点击选择学生查看模板\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 2060,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 2044,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2040,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"template-card-badge\",\n                                                        children: \"当前模板\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2068,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"template-card-swap-icon\",\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            selectedStudent && handleTemplateAssignment();\n                                                        },\n                                                        title: \"更换模板\",\n                                                        children: \"⇌\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2071,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2039,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2028,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2027,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 2007,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1919,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1672,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddStudentModal__WEBPACK_IMPORTED_MODULE_5__.AddStudentModal, {\n                visible: isAddStudentModalVisible,\n                onCancel: ()=>setIsAddStudentModalVisible(false),\n                onOk: handleAddStudent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2089,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.EditClassModal, {\n                visible: isEditClassModalVisible,\n                onCancel: ()=>setIsEditClassModalVisible(false),\n                onOk: handleEditClass,\n                initialValues: {\n                    className: classInfo.className\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2096,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.ImportStudentModal, {\n                visible: isImportStudentModalVisible,\n                onCancel: ()=>setIsImportStudentModalVisible(false),\n                onImport: handleImportStudents,\n                classId: classInfo.id\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2106,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransferClassModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                visible: isTransferClassModalVisible,\n                onCancel: ()=>{\n                    setIsTransferClassModalVisible(false);\n                    setSearchedTeacher(null);\n                },\n                onOk: handleTransferClass,\n                onSearchTeacher: handleSearchTeacher,\n                searchedTeacher: searchedTeacher,\n                hasAssistantTeacher: !!classInfo.assistantTeacherId,\n                onRemoveAssistant: handleRemoveAssistant,\n                loading: transferLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2114,\n                columnNumber: 7\n            }, undefined),\n            isInviteCodeModalVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold\",\n                                    children: \"邀请码生成成功\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2133,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-gray-500 hover:text-gray-700\",\n                                    onClick: ()=>setIsInviteCodeModalVisible(false),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-6 w-6\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2139,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2138,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2134,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2132,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between bg-gray-50 p-3 rounded-lg mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-mono text-lg text-blue-600 mr-4\",\n                                            children: inviteCode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2145,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                            onClick: ()=>{\n                                                navigator.clipboard.writeText(inviteCode).then(()=>alert(\"邀请码已复制\")).catch(()=>alert(\"复制失败，请手动复制\"));\n                                            },\n                                            children: \"复制\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2146,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2144,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-sm space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"您可以:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2158,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-disc list-inside space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"将邀请码分享给学生，让他们加入班级\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2160,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"邀请其他老师作为协助教师加入班级\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2161,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2159,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-500\",\n                                            children: \"⏰ 邀请码有效期为24小时\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2163,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2157,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2143,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                onClick: ()=>setIsInviteCodeModalVisible(false),\n                                children: \"关闭\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 2169,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2168,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 2131,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2130,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.PublishTaskModal, {\n                visible: isPublishTaskModalVisible,\n                onCancel: ()=>setIsPublishTaskModalVisible(false),\n                onOk: handlePublishTask,\n                students: students,\n                selectedStudents: selectedStudentIds,\n                setSelectedStudents: setSelectedStudentIds,\n                fileList: fileList,\n                uploading: uploading,\n                handleUpload: handleUpload,\n                handleRemoveFile: handleRemoveFile,\n                displayTemplates: templates,\n                officialTemplates: officialTemplates,\n                selectedTemplateId: selectedTemplateId,\n                setSelectedTemplateId: setSelectedTemplateId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2183,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.ResetPasswordModal, {\n                visible: isResetPasswordModalVisible,\n                onCancel: ()=>setIsResetPasswordModalVisible(false),\n                onOk: handleResetPassword,\n                isBatch: false,\n                count: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2201,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AssignBlocksModal__WEBPACK_IMPORTED_MODULE_7__.AssignBlocksModal, {\n                visible: isAssignBlocksModalVisible,\n                onCancel: ()=>{\n                    setIsAssignBlocksModalVisible(false);\n                    setSelectedStudentId(null);\n                    setSelectedStudentIds([]);\n                },\n                isClassCardAssign: selectedStudentId !== null,\n                loadingTemplates: loadingTemplates,\n                templates: templates,\n                studentTemplateUsage: studentTemplateUsage,\n                teacherTemplate: teacherTemplate,\n                onSelectTemplate: handleSelectTemplate,\n                onTemplateUsageClick: handleTemplateUsageClick,\n                userId: userId,\n                onRefreshTemplates: fetchTemplates,\n                students: students,\n                selectedStudentIds: selectedStudentIds,\n                userRoles: userRoles,\n                onSuccess: ()=>{\n                    refreshStudentList();\n                    fetchTemplateUsage();\n                },\n                onUpdateStudentTemplate: handleUpdateStudentTemplate\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2210,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AssignPointsModal__WEBPACK_IMPORTED_MODULE_9__.AssignPointsModal, {\n                visible: isAssignPointsModalVisible,\n                onCancel: ()=>{\n                    setIsAssignPointsModalVisible(false);\n                    setSelectedStudent(null);\n                },\n                // 根据 selectedStudent 是否存在来决定调用哪个处理函数\n                onOk: selectedStudent ? handleAssignPoints : handleBatchAssignPoints,\n                studentName: selectedStudent ? \"\".concat(selectedStudent.nickName) : \"已选择 \".concat(selectedStudentIds.length, \" 名学生\"),\n                studentId: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.id) || 0,\n                userId: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.userId) || 0,\n                student: selectedStudent,\n                isBatch: !selectedStudent,\n                selectedStudents: selectedStudentIds,\n                students: students,\n                onSuccess: ()=>{\n                // 移除这里的刷新，因为 onOk 内部已经处理了\n                },\n                refreshStudentList: refreshStudentList,\n                onGoToRedeemKey: (studentIds)=>{\n                    console.log(\"前往兑换密钥:\", studentIds);\n                    setIsBatchUseKeyPackageModalVisible(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2237,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_10__.BatchUseKeyPackageModal, {\n                open: isBatchUseKeyPackageModalVisible,\n                selectedStudentIds: selectedStudentIds,\n                students: students,\n                onClose: ()=>setIsBatchUseKeyPackageModalVisible(false),\n                onSuccess: handleBatchUseKeyPackageSuccess,\n                onGoToAssignPoints: handleGoToAssignPointsFromRedeem\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2263,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n        lineNumber: 1660,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClassDetail, \"rOoOqxdnOob2DP168DTJvyWgCZI=\", false, function() {\n    return [\n        _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__.useTemplate,\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector\n    ];\n});\n_c = ClassDetail;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClassDetail);\nvar _c;\n$RefreshReg$(_c, \"ClassDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/ClassDetail.tsx\n"));

/***/ })

});