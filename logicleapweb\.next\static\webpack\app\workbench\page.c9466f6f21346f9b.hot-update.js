"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/ClassDetail.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/ClassDetail.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/blocks.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _lib_api_student__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/api/student */ \"(app-pages-browser)/./lib/api/student.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/TemplateContext */ \"(app-pages-browser)/./app/workbench/contexts/TemplateContext.tsx\");\n/* harmony import */ var _AddStudentModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AddStudentModal */ \"(app-pages-browser)/./app/workbench/components/AddStudentModal.tsx\");\n/* harmony import */ var _teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../teacher-space/components/modals */ \"(app-pages-browser)/./app/teacher-space/components/modals/index.ts\");\n/* harmony import */ var _AssignBlocksModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AssignBlocksModal */ \"(app-pages-browser)/./app/workbench/components/AssignBlocksModal.tsx\");\n/* harmony import */ var _TransferClassModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TransferClassModal */ \"(app-pages-browser)/./app/workbench/components/TransferClassModal.tsx\");\n/* harmony import */ var _AssignPointsModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./AssignPointsModal */ \"(app-pages-browser)/./app/workbench/components/AssignPointsModal.tsx\");\n/* harmony import */ var _BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./BatchUseKeyPackageModal */ \"(app-pages-browser)/./app/workbench/components/BatchUseKeyPackageModal.tsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./app/workbench/components/StudentList/index.tsx\");\n/* harmony import */ var _ClassDetail_css__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ClassDetail.css */ \"(app-pages-browser)/./app/workbench/components/ClassDetail.css\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 类型定义已移至 ./types/index.ts\nconst ClassDetail = (param)=>{\n    let { classInfo: initialClassInfo, selectedSchool, onBack, onClassInfoUpdate, onClassDeleted } = param;\n    var _selectedStudent_nickName, _selectedStudent_currentTemplate;\n    _s();\n    // 创建内部状态来管理班级信息，这样可以在编辑后更新显示\n    const [classInfo, setClassInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialClassInfo);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 同步外部传入的classInfo变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setClassInfo(initialClassInfo);\n    }, [\n        initialClassInfo\n    ]);\n    const [isAddStudentModalVisible, setIsAddStudentModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSettingsDropdownOpen, setIsSettingsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchActionsDropdownOpen, setIsBatchActionsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const settingsDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const batchActionsDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 模态框状态\n    const [isEditClassModalVisible, setIsEditClassModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isImportStudentModalVisible, setIsImportStudentModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTransferClassModalVisible, setIsTransferClassModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isInviteCodeModalVisible, setIsInviteCodeModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAssignBlocksModalVisible, setIsAssignBlocksModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [inviteCode, setInviteCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 新增功能模态框状态\n    const [isPublishTaskModalVisible, setIsPublishTaskModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResetPasswordModalVisible, setIsResetPasswordModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAssignPointsModalVisible, setIsAssignPointsModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchUseKeyPackageModalVisible, setIsBatchUseKeyPackageModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [isRedeemKeyModalVisible, setIsRedeemKeyModalVisible] = useState(false); // 未使用，已移除\n    // 转让管理相关状态\n    const [searchedTeacher, setSearchedTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [transferLoading, setTransferLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // PublishTaskModal 相关状态\n    const [fileList, setFileList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [officialTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 保留以避免引用错误\n    const [selectedTemplateId, setSelectedTemplateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // AssignBlocksModal 相关状态\n    const [loadingTemplates, setLoadingTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [studentTemplateUsage, setStudentTemplateUsage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [teacherTemplate, setTeacherTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedStudentId, setSelectedStudentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 添加userRoles状态，与teacher-space保持一致\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 多选状态\n    const [selectedStudentIds, setSelectedStudentIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSelectAll, setIsSelectAll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 使用全局模板状态\n    const { currentTemplate, globalTemplateChangeVersion, refreshCurrentTemplate } = (0,_contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__.useTemplate)();\n    // 全局当前模板信息（用于没有个人模板的学生）\n    const [globalCurrentTemplate, setGlobalCurrentTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 获取教师当前模板（从班级接口获取）\n    const fetchTeacherCurrentTemplate = async ()=>{\n        try {\n            if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id) || !(classInfo === null || classInfo === void 0 ? void 0 : classInfo.schoolId)) return null;\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.getTeacherClasses(classInfo.schoolId, userId);\n            if (response.data.code === 200 && response.data.data) {\n                // 找到当前班级的模板信息\n                const currentClass = response.data.data.find((cls)=>cls.id === classInfo.id);\n                if (currentClass && currentClass.templateName) {\n                    const templateInfo = {\n                        templateId: currentClass.templateId || 0,\n                        templateName: currentClass.templateName,\n                        isOfficial: currentClass.isOfficial || false\n                    };\n                    setGlobalCurrentTemplate(templateInfo);\n                    console.log(\"获取到教师当前模板:\", templateInfo);\n                    return templateInfo;\n                }\n            }\n        } catch (error) {\n            console.error(\"获取教师当前模板失败:\", error);\n        }\n        return null;\n    };\n    // 同步教师模板到学生（当教师模板更新时调用）\n    const syncTeacherTemplateToStudents = async ()=>{\n        try {\n            const newTemplate = await fetchTeacherCurrentTemplate();\n            if (newTemplate && students.length > 0) {\n                console.log(\"同步教师模板到学生:\", newTemplate);\n                // 使用 ref 获取最新的 personalTemplateAssignments\n                const currentPersonalAssignments = personalTemplateAssignmentsRef.current;\n                // 更新所有没有个人分配模板的学生\n                setStudents((prevStudents)=>prevStudents.map((student)=>{\n                        // 如果学生有个人分配的模板，保持不变\n                        if (currentPersonalAssignments.has(student.userId)) {\n                            console.log(\"保持学生 \".concat(student.nickName, \" 的个人模板 (syncTeacherTemplateToStudents)\"));\n                            return student;\n                        }\n                        // 否则更新为教师当前模板\n                        console.log(\"更新学生 \".concat(student.nickName, \" 为教师模板 (syncTeacherTemplateToStudents)\"));\n                        return {\n                            ...student,\n                            currentTemplate: newTemplate\n                        };\n                    }));\n            }\n        } catch (error) {\n            console.error(\"同步教师模板失败:\", error);\n        }\n    };\n    // 存储个人分配的模板信息，避免被fetchStudents覆盖\n    const [personalTemplateAssignments, setPersonalTemplateAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    // 添加强制重新渲染的状态\n    const [renderVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // setRenderVersion暂时未使用\n    // 获取用户信息\n    const userId = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector)((state)=>state.user.userState.userId);\n    const roleId = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector)((state)=>state.user.userState.roleId);\n    // 获取班级学生数据\n    const fetchStudents = async ()=>{\n        if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) return;\n        try {\n            setLoading(true);\n            setError(null);\n            // 并行获取班级学生基础数据和教师当前模板\n            const [response] = await Promise.all([\n                _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.getClassStudents(classInfo.id),\n                fetchTeacherCurrentTemplate()\n            ]);\n            if (response.data.code === 200) {\n                const studentsData = response.data.data || [];\n                if (studentsData.length === 0) {\n                    setStudents([]);\n                    setSelectedStudent(null);\n                    // 如果学生数量变为0，也要更新班级信息\n                    if (classInfo.studentCount !== 0) {\n                        const updatedClassInfo = {\n                            ...classInfo,\n                            studentCount: 0\n                        };\n                        setClassInfo(updatedClassInfo);\n                        // 通知父组件更新班级列表中的学生数\n                        onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n                    }\n                    return;\n                }\n                // 提取学生ID列表\n                const userIds = studentsData.map((s)=>s.userId);\n                // 批量获取学生详细信息（包括模板和能量信息）\n                const studentInfoMap = await (userIds.length > 20 ? Promise.all(Array.from({\n                    length: Math.ceil(userIds.length / 20)\n                }, (_, i)=>userIds.slice(i * 20, (i + 1) * 20)).map((batchId)=>_lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.getStudentsBatchInfo(batchId).then((param)=>{\n                        let { data: { data } } = param;\n                        return data;\n                    }).catch((error)=>{\n                        console.error(\"获取学生批量信息失败:\", error);\n                        return {};\n                    }))).then((results)=>Object.assign({}, ...results)) : _lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.getStudentsBatchInfo(userIds).then((param)=>{\n                    let { data: { data } } = param;\n                    return data;\n                }).catch((error)=>{\n                    console.error(\"获取学生批量信息失败:\", error);\n                    return {};\n                }));\n                // 合并学生数据\n                const completeStudents = studentsData.map((s)=>{\n                    const studentInfo = studentInfoMap[s.userId];\n                    // 检查是否有个人分配的模板\n                    const personalTemplate = personalTemplateAssignments.get(s.userId);\n                    return {\n                        ...s,\n                        ...studentInfo,\n                        id: s.userId,\n                        nickName: s.nickName || \"学生\".concat(s.studentNumber || s.userId),\n                        totalPoints: (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.totalPoints) || 0,\n                        availablePoints: (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.availablePoints) || 0,\n                        avatarUrl: s.avatarUrl || \"/default-avatar.png\",\n                        // 优先级：个人分配的模板 > 学生API返回的模板 > 教师当前模板 > null\n                        currentTemplate: (()=>{\n                            var _studentsData_;\n                            const result = personalTemplate || (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.currentTemplate) || globalCurrentTemplate;\n                            // 调试信息\n                            if (s.userId === ((_studentsData_ = studentsData[0]) === null || _studentsData_ === void 0 ? void 0 : _studentsData_.userId)) {\n                                console.log(\"学生模板分配逻辑:\", {\n                                    studentId: s.userId,\n                                    studentName: s.nickName,\n                                    personalTemplate,\n                                    studentApiTemplate: studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.currentTemplate,\n                                    globalCurrentTemplate,\n                                    finalTemplate: result\n                                });\n                            }\n                            return result;\n                        })()\n                    };\n                });\n                setStudents(completeStudents);\n                // 如果学生数量发生变化，更新班级信息并通知父组件\n                if (completeStudents.length !== classInfo.studentCount) {\n                    const updatedClassInfo = {\n                        ...classInfo,\n                        studentCount: completeStudents.length\n                    };\n                    setClassInfo(updatedClassInfo);\n                    // 通知父组件更新班级列表中的学生数\n                    onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n                }\n                // 设置userRoles，与teacher-space保持一致\n                const newUserRoles = completeStudents.map((student)=>({\n                        userId: student.userId,\n                        roleId: 1 // 学生角色ID为1\n                    }));\n                // 添加教师自己的角色\n                newUserRoles.push({\n                    userId: userId,\n                    roleId: 2 // 教师角色ID为2\n                });\n                setUserRoles(newUserRoles);\n                // 默认选择第一个学生\n                if (completeStudents.length > 0) {\n                    setSelectedStudent(completeStudents[0]);\n                }\n            } else {\n                setError(response.data.message || \"获取学生列表失败\");\n            }\n        } catch (err) {\n            console.error(\"获取学生列表失败:\", err);\n            console.error(\"错误详情:\", {\n                message: err instanceof Error ? err.message : \"未知错误\",\n                stack: err instanceof Error ? err.stack : undefined,\n                classId: classInfo.id\n            });\n            setError(\"获取学生列表失败，请稍后重试\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 获取权限模板列表\n    const fetchTemplates = async ()=>{\n        setLoadingTemplates(true);\n        try {\n            const { getRoleTemplateList, getOfficialTemplates } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            // 同时获取教师自定义模板和官方模板\n            const [customResponse, officialResponse] = await Promise.all([\n                getRoleTemplateList(userId),\n                getOfficialTemplates()\n            ]);\n            if (customResponse.data.code === 200 && officialResponse.data.code === 200) {\n                const customTemplates = customResponse.data.data || [];\n                const officialTemplates = officialResponse.data.data || [];\n                // 为官方模板添加标记\n                const markedOfficialTemplates = officialTemplates.map((template)=>({\n                        ...template,\n                        isOfficial: true\n                    }));\n                // 合并所有模板\n                const allTemplates = [\n                    ...customTemplates,\n                    ...markedOfficialTemplates\n                ];\n                setTemplates(allTemplates);\n            }\n        } catch (error) {\n            console.error(\"获取权限模板列表失败:\", error);\n        } finally{\n            setLoadingTemplates(false);\n        }\n    };\n    // 获取模板使用情况\n    const fetchTemplateUsage = async ()=>{\n        try {\n            const { getUserCurrentTemplate, getStudentTemplates } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            // 获取教师使用的模板\n            const teacherResponse = await getUserCurrentTemplate(userId);\n            if (teacherResponse.data.code === 200) {\n                const teacherTemplateData = teacherResponse.data.data;\n                setTeacherTemplate(teacherTemplateData);\n            // 同时更新当前模板状态，确保与模板管理同步\n            // 暂时注释掉以停止循环\n            /*\r\n        if (teacherTemplateData) {\r\n          // 使用refreshCurrentTemplate来更新全局模板状态\r\n          await refreshCurrentTemplate();\r\n        }\r\n        */ }\n            // 获取学生使用的模板\n            const studentResponse = await getStudentTemplates({\n                teacherId: userId,\n                page: 1,\n                size: 200\n            });\n            if (studentResponse.data.code === 200) {\n                // 统计每个模板被使用的次数\n                const usage = {};\n                studentResponse.data.data.list.forEach((item)=>{\n                    if (item.templateId) {\n                        usage[item.templateId] = (usage[item.templateId] || 0) + 1;\n                    }\n                });\n                setStudentTemplateUsage(usage);\n            }\n        } catch (error) {\n            console.error(\"获取模板使用情况失败:\", error);\n        }\n    };\n    // 组件挂载时获取学生数据和模板\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStudents();\n        fetchTemplates();\n        fetchTemplateUsage();\n    }, [\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n        userId\n    ]);\n    // 当组件重新挂载或班级变化时，确保获取最新的当前模板\n    // 暂时注释掉以停止循环\n    /*\r\n  useEffect(() => {\r\n    if (classInfo?.id && userId) {\r\n      refreshCurrentTemplate();\r\n    }\r\n  }, [classInfo?.id, userId]);\r\n  */ // 移除这个useEffect，避免在currentTemplate变化时覆盖个人分配的模板\n    // 使用 useRef 来保存最新的 personalTemplateAssignments\n    const personalTemplateAssignmentsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(personalTemplateAssignments);\n    personalTemplateAssignmentsRef.current = personalTemplateAssignments;\n    // 监听全局模板变化，重新获取教师当前模板并更新学生数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (globalTemplateChangeVersion > 0 && (classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) {\n            console.log(\"检测到模板变化，重新获取教师当前模板\");\n            // 重新获取教师当前模板\n            fetchTeacherCurrentTemplate().then((newTemplate)=>{\n                if (newTemplate && students.length > 0) {\n                    // 不清除个人分配的模板记录，保留学生的个人模板数据\n                    // setPersonalTemplateAssignments(new Map()); // 注释掉这行\n                    // 使用 ref 获取最新的 personalTemplateAssignments\n                    const currentPersonalAssignments = personalTemplateAssignmentsRef.current;\n                    // 只更新没有个人模板的学生为新的教师当前模板\n                    console.log(\"全局模板变化，开始更新学生模板:\", {\n                        newTemplate,\n                        personalTemplateAssignments: Array.from(currentPersonalAssignments.entries()),\n                        studentsCount: students.length\n                    });\n                    setStudents((prevStudents)=>prevStudents.map((student)=>{\n                            const hasPersonalTemplate = currentPersonalAssignments.has(student.userId);\n                            console.log(\"学生 \".concat(student.nickName, \" (\").concat(student.userId, \"):\"), {\n                                hasPersonalTemplate,\n                                currentTemplate: student.currentTemplate,\n                                willUpdate: !hasPersonalTemplate\n                            });\n                            // 如果学生有个人分配的模板，保持不变\n                            if (hasPersonalTemplate) {\n                                console.log(\"保持学生 \".concat(student.nickName, \" 的个人模板\"));\n                                return student;\n                            }\n                            // 否则更新为新的教师当前模板\n                            console.log(\"更新学生 \".concat(student.nickName, \" 为教师模板\"));\n                            return {\n                                ...student,\n                                currentTemplate: newTemplate\n                            };\n                        }));\n                }\n            });\n        }\n    }, [\n        globalTemplateChangeVersion,\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id\n    ]);\n    // 定期检查教师模板更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) return;\n        // 立即检查一次\n        syncTeacherTemplateToStudents();\n        // 设置定时器，每30秒检查一次模板更新\n        const interval = setInterval(()=>{\n            syncTeacherTemplateToStudents();\n        }, 30000); // 30秒\n        return ()=>clearInterval(interval);\n    }, [\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n        students.length\n    ]);\n    // 点击外部关闭下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n            // setIsMoreActionsDropdownOpen(false); // 已移除\n            }\n            if (settingsDropdownRef.current && !settingsDropdownRef.current.contains(event.target)) {\n                setIsSettingsDropdownOpen(false);\n            }\n            if (batchActionsDropdownRef.current && !batchActionsDropdownRef.current.contains(event.target)) {\n                setIsBatchActionsDropdownOpen(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, []);\n    // 处理学生点击选择（用于右侧显示详情）\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n    };\n    // 处理删除学生\n    const handleDeleteStudent = async ()=>{\n        if (!selectedStudent) {\n            alert(\"请先选择要删除的学生\");\n            return;\n        }\n        // 显示确认对话框\n        const confirmed = window.confirm(\"确定要将 \".concat(selectedStudent.nickName, \" 移出班级吗？\\n\\n此操作不可恢复！\"));\n        if (!confirmed) {\n            return;\n        }\n        try {\n            console.log(\"删除学生:\", selectedStudent);\n            // 调用删除学生的API，传入学生的userId数组\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.removeStudentFromClass([\n                selectedStudent.userId\n            ]);\n            console.log(\"删除学生 API 响应:\", response.data);\n            // 检查响应状态\n            if (response.data.code === 200) {\n                console.log(\"删除学生成功\");\n                alert(\"学生已成功移出班级\");\n                // 清除选中的学生\n                setSelectedStudent(null);\n                // 重新获取学生列表\n                await fetchStudents();\n            } else {\n                console.error(\"删除学生失败:\", response.data.message);\n                alert(response.data.message || \"删除学生失败\");\n            }\n        } catch (error) {\n            console.error(\"删除学生失败:\", error);\n            alert(\"删除学生失败，请稍后重试\");\n        }\n    };\n    // 编辑班级\n    const handleEditClass = async (values)=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        try {\n            console.log(\"开始编辑班级:\", {\n                classId: classInfo.id,\n                values: values,\n                originalClassName: classInfo.className\n            });\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.updateClass(classInfo.id, {\n                className: values.className,\n                grade: classInfo.grade || \"\" // 确保传递grade字段，如果没有则使用空字符串\n            });\n            console.log(\"编辑班级API响应:\", response);\n            if (response.data.code === 200) {\n                console.log(\"编辑班级成功\");\n                notification.success(\"编辑班级成功\");\n                setIsEditClassModalVisible(false);\n                // 更新本地班级信息\n                const updatedClassInfo = {\n                    ...classInfo,\n                    className: values.className\n                };\n                setClassInfo(updatedClassInfo);\n                // 通知父组件更新班级列表中的班级信息\n                onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n            } else {\n                console.error(\"编辑班级失败 - API返回错误:\", {\n                    code: response.data.code,\n                    message: response.data.message,\n                    data: response.data\n                });\n                notification.error(response.data.message || \"编辑班级失败\");\n                throw new Error(response.data.message || \"编辑班级失败\"); // 抛出错误让模态框知道失败了\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"编辑班级失败 - 请求异常:\", {\n                error: error,\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // 只有在没有显示过错误消息的情况下才显示通用错误\n            if (!error.message || error.message === \"编辑班级失败\") {\n                var _error_response_data, _error_response2;\n                notification.error(((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"编辑班级失败，请稍后重试\");\n            }\n            throw error; // 重新抛出错误，让模态框保持打开状态\n        }\n    };\n    // 导入学生\n    const handleImportStudents = async (file)=>{\n        try {\n            console.log(\"导入学生文件:\", file);\n            // 这里需要实现文件解析和导入逻辑\n            alert(\"导入学生功能正在开发中\");\n            setIsImportStudentModalVisible(false);\n            return true;\n        } catch (error) {\n            console.error(\"导入学生失败:\", error);\n            alert(\"导入学生失败，请稍后重试\");\n            return false;\n        }\n    };\n    // 导出学生\n    const handleExportStudents = async ()=>{\n        try {\n            const response = await _lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.exportStudents(classInfo.id);\n            console.log(\"导出学生成功:\", response);\n            alert(\"导出学生成功\");\n        } catch (error) {\n            console.error(\"导出学生失败:\", error);\n            alert(\"导出学生失败，请稍后重试\");\n        }\n    };\n    // 搜索教师\n    const handleSearchTeacher = async (phone)=>{\n        try {\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.searchTeacherByPhone(phone);\n            console.log(\"搜索教师响应:\", response);\n            if (response.data.code === 200) {\n                setSearchedTeacher(response.data.data);\n            } else {\n                const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                const notification = GetNotification();\n                notification.error(response.data.message || \"搜索教师失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"搜索教师失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"搜索教师失败\");\n        }\n    };\n    // 转让班级\n    const handleTransferClass = async (values)=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        setTransferLoading(true);\n        try {\n            let newTeacherId;\n            if (values.transferType === \"search\") {\n                if (!searchedTeacher) {\n                    notification.error(\"请先搜索并选择教师\");\n                    return;\n                }\n                newTeacherId = searchedTeacher.id;\n            } else {\n                // 检查是否有协助教师\n                if (!classInfo.assistantTeacherId) {\n                    notification.error(\"该班级没有协助教师\");\n                    return;\n                }\n                newTeacherId = classInfo.assistantTeacherId;\n            }\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.transferClass(classInfo.id, newTeacherId, values.transferType);\n            if (response.data.code === 200) {\n                notification.success(\"转让班级成功\");\n                setIsTransferClassModalVisible(false);\n                setSearchedTeacher(null);\n                // 转让成功后返回班级管理页面\n                onBack();\n            } else {\n                notification.error(response.data.message || \"转让班级失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"转让班级失败:\", error);\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"转让班级失败\");\n        } finally{\n            setTransferLoading(false);\n        }\n    };\n    // 移出协助教师\n    const handleRemoveAssistant = async ()=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        try {\n            // 这里需要调用移出协助教师的API\n            // 暂时使用转让API，将assistantTeacherId设为0\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.updateClass(classInfo.id, {\n                assistantTeacherId: 0\n            });\n            if (response.data.code === 200) {\n                notification.success(\"移出协助教师成功\");\n                setIsTransferClassModalVisible(false);\n                // 更新本地班级信息\n                const updatedClassInfo = {\n                    ...classInfo,\n                    assistantTeacherId: 0\n                };\n                setClassInfo(updatedClassInfo);\n                onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n            } else {\n                notification.error(response.data.message || \"移出协助教师失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"移出协助教师失败:\", error);\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"移出协助教师失败\");\n        }\n    };\n    // 生成邀请码\n    const handleGenerateInviteCode = async ()=>{\n        try {\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.generateInviteCode(classInfo.id);\n            if (response.data.code === 200) {\n                setInviteCode(response.data.data.inviteCode);\n                setIsInviteCodeModalVisible(true);\n            } else {\n                console.error(\"生成邀请码失败:\", response.data.message);\n                alert(response.data.message || \"生成邀请码失败\");\n            }\n        } catch (error) {\n            console.error(\"生成邀请码失败:\", error);\n            alert(\"生成邀请码失败，请稍后重试\");\n        }\n    };\n    // 分配积木（批量或选中学生）\n    const handleAssignBlocks = async ()=>{\n        console.log(\"=== 分配积木开始 ===\");\n        console.log(\"selectedStudentIds:\", selectedStudentIds);\n        console.log(\"selectedStudentIds.length:\", selectedStudentIds.length);\n        // 如果有选中的学生，设置为单个学生分配模式\n        if (selectedStudentIds.length === 1) {\n            console.log(\"单个学生分配模式，studentId:\", selectedStudentIds[0]);\n            setSelectedStudentId(selectedStudentIds[0]);\n        } else {\n            console.log(\"批量分配模式，学生数量:\", selectedStudentIds.length);\n            setSelectedStudentId(null);\n        }\n        await fetchTemplates();\n        await fetchTemplateUsage();\n        setIsAssignBlocksModalVisible(true);\n    };\n    // 单独为学生分配积木\n    const handleIndividualAssignBlocks = async (studentId)=>{\n        console.log(\"=== 单独为学生分配积木 ===\");\n        console.log(\"studentId:\", studentId);\n        // 设置为单个学生分配模式\n        setSelectedStudentId(studentId);\n        setSelectedStudentIds([]); // 清空批量选择\n        await fetchTemplates();\n        await fetchTemplateUsage();\n        setIsAssignBlocksModalVisible(true);\n    };\n    // 处理模板分配（从模板卡片点击）\n    const handleTemplateAssignment = async ()=>{\n        if (!selectedStudent) return;\n        console.log(\"=== 从模板卡片分配模板 ===\");\n        console.log(\"selectedStudent:\", selectedStudent);\n        await handleIndividualAssignBlocks(selectedStudent.userId);\n    };\n    // 删除班级\n    const handleDeleteClass = async ()=>{\n        const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        // 先检查是否有学生\n        if (students.length > 0) {\n            Modal.warning({\n                title: \"无法删除班级\",\n                centered: true,\n                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"班级中还有 \",\n                                students.length,\n                                \" 名学生，请先移除所有学生后再删除班级。\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 805,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-gray-500 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"删除步骤：\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 807,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    className: \"list-decimal ml-4 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"选择要移除的学生\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"使用批量操作移除所有学生\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"再次尝试删除班级\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 808,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 806,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 804,\n                    columnNumber: 11\n                }, undefined),\n                okText: \"知道了\"\n            });\n            return;\n        }\n        // 如果没有学生，显示删除确认对话框\n        Modal.confirm({\n            title: \"确认删除班级\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"您确定要删除 \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: classInfo.className\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 826,\n                                columnNumber: 21\n                            }, undefined),\n                            \" 吗？\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 826,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-3 bg-red-50 rounded-lg border border-red-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 830,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"此操作不可恢复！\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 832,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 828,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-500\",\n                                children: \"删除班级将永久移除班级信息，包括班级设置、模板配置等数据。\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 834,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 827,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 825,\n                columnNumber: 9\n            }, undefined),\n            okText: \"确定删除\",\n            cancelText: \"取消\",\n            okButtonProps: {\n                danger: true\n            },\n            centered: true,\n            onOk: async ()=>{\n                try {\n                    const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.deleteClass(classInfo.id);\n                    if (response.data.code === 200) {\n                        notification.success(\"删除班级成功\");\n                        // 通知父组件班级已被删除\n                        onClassDeleted === null || onClassDeleted === void 0 ? void 0 : onClassDeleted(classInfo.id);\n                        // 返回到班级管理页面\n                        onBack();\n                    } else {\n                        notification.error(response.data.message || \"删除班级失败\");\n                    }\n                } catch (error) {\n                    var _error_response_data, _error_response;\n                    console.error(\"删除班级失败:\", error);\n                    if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                        notification.error(error.response.data.message);\n                    } else {\n                        notification.error(\"删除班级失败，请稍后重试\");\n                    }\n                }\n            }\n        });\n    };\n    // 下拉菜单项处理函数\n    const handleMenuItemClick = (action)=>{\n        // setIsMoreActionsDropdownOpen(false); // 已移除\n        switch(action){\n            case \"edit\":\n                setIsEditClassModalVisible(true);\n                break;\n            case \"addStudent\":\n                setIsAddStudentModalVisible(true);\n                break;\n            case \"importStudent\":\n                setIsImportStudentModalVisible(true);\n                break;\n            case \"exportStudent\":\n                handleExportStudents();\n                break;\n            case \"transfer\":\n                setIsTransferClassModalVisible(true);\n                break;\n            case \"invite\":\n                handleGenerateInviteCode();\n                break;\n            case \"batchRedeem\":\n                console.log(\"批量兑换密令\");\n                setIsBatchUseKeyPackageModalVisible(true);\n                break;\n            case \"assignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"deleteClass\":\n                handleDeleteClass();\n                break;\n            default:\n                break;\n        }\n    };\n    // 设置下拉菜单项处理函数\n    const handleSettingsMenuItemClick = (action)=>{\n        setIsSettingsDropdownOpen(false);\n        switch(action){\n            case \"edit\":\n                setIsEditClassModalVisible(true);\n                break;\n            case \"addStudent\":\n                setIsAddStudentModalVisible(true);\n                break;\n            case \"importStudent\":\n                setIsImportStudentModalVisible(true);\n                break;\n            case \"exportStudent\":\n                handleExportStudents();\n                break;\n            case \"transfer\":\n                setIsTransferClassModalVisible(true);\n                break;\n            case \"invite\":\n                handleGenerateInviteCode();\n                break;\n            case \"batchRedeem\":\n                console.log(\"批量兑换密令\");\n                setIsBatchUseKeyPackageModalVisible(true);\n                break;\n            case \"assignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"deleteClass\":\n                handleDeleteClass();\n                break;\n            default:\n                break;\n        }\n    };\n    // 确保选中状态同步\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const shouldBeSelectAll = selectedStudentIds.length > 0 && selectedStudentIds.length === students.length;\n        if (isSelectAll !== shouldBeSelectAll) {\n            console.log(\"修复选中状态同步:\", {\n                isSelectAll,\n                shouldBeSelectAll,\n                selectedStudentIds: selectedStudentIds.length,\n                totalStudents: students.length\n            });\n            setIsSelectAll(shouldBeSelectAll);\n        }\n    }, [\n        selectedStudentIds,\n        students.length,\n        isSelectAll\n    ]);\n    // 全选/取消全选处理函数\n    const handleSelectAll = ()=>{\n        if (isSelectAll) {\n            // 取消全选\n            setSelectedStudentIds([]);\n            setIsSelectAll(false);\n        } else {\n            // 全选\n            const allStudentIds = students.map((student)=>student.userId);\n            setSelectedStudentIds(allStudentIds);\n            setIsSelectAll(true);\n        }\n    };\n    // 单个学生选择处理函数\n    const handleStudentSelect = (studentId)=>{\n        if (selectedStudentIds.includes(studentId)) {\n            // 取消选择\n            const newSelectedIds = selectedStudentIds.filter((id)=>id !== studentId);\n            setSelectedStudentIds(newSelectedIds);\n            setIsSelectAll(false);\n        } else {\n            // 选择\n            const newSelectedIds = [\n                ...selectedStudentIds,\n                studentId\n            ];\n            setSelectedStudentIds(newSelectedIds);\n            // 检查是否全选\n            if (newSelectedIds.length === students.length) {\n                setIsSelectAll(true);\n            }\n        }\n    };\n    // 批量操作处理函数\n    const handleBatchAction = (action)=>{\n        setIsBatchActionsDropdownOpen(false);\n        if (selectedStudentIds.length === 0) {\n            Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                let { GetNotification } = param;\n                GetNotification().warning(\"请先选择要操作的学生\");\n            });\n            return;\n        }\n        switch(action){\n            case \"batchDelete\":\n                handleBatchRemoveStudents(selectedStudentIds);\n                break;\n            case \"batchAssignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"batchAssignPoints\":\n                // 清除单个学生选择，确保进入批量模式\n                setSelectedStudent(null);\n                setIsAssignPointsModalVisible(true);\n                break;\n            case \"batchUseKeyPackage\":\n                handleBatchUseKeyPackage();\n                break;\n            case \"batchExport\":\n                console.log(\"批量导出学生:\", selectedStudentIds);\n                Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                    let { GetNotification } = param;\n                    GetNotification().info(\"批量导出学生功能正在开发中\");\n                });\n                break;\n            default:\n                break;\n        }\n    };\n    // 批量移出班级的改进版本\n    const handleBatchRemoveStudents = async (studentIds)=>{\n        try {\n            // 获取选中的学生信息\n            const selectedStudentsInfo = students.filter((s)=>studentIds.includes(s.userId));\n            // 计算总可用积分\n            const totalAvailablePoints = selectedStudentsInfo.reduce((sum, student)=>sum + (student.availablePoints || 0), 0);\n            const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n            const { InfoCircleOutlined } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_ant-design_icons_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/index.js\"));\n            Modal.confirm({\n                title: \"确认批量移出班级\",\n                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"确定要将选中的 \",\n                                studentIds.length,\n                                \" 名学生移出班级吗？\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1047,\n                            columnNumber: 13\n                        }, undefined),\n                        totalAvailablePoints > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 p-3 bg-yellow-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-yellow-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoCircleOutlined, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1051,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"选中的学生共有 \",\n                                                totalAvailablePoints,\n                                                \" 点可用能量\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1052,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1050,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-sm text-yellow-500\",\n                                    children: \"移出班级后，可用能量将返还到各自的套餐积分中\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1054,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1049,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1046,\n                    columnNumber: 11\n                }, undefined),\n                okText: \"确定移出\",\n                cancelText: \"取消\",\n                centered: true,\n                okButtonProps: {\n                    danger: true\n                },\n                onOk: async ()=>{\n                    try {\n                        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                        const notification = GetNotification();\n                        const hideLoading = notification.loading(\"正在移出学生...\");\n                        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.removeStudentFromClass(studentIds);\n                        if (hideLoading) {\n                            hideLoading.close();\n                        }\n                        if (response.data.code === 200) {\n                            notification.success(\"成功移出 \".concat(studentIds.length, \" 名学生\"));\n                            // 清除选择状态\n                            setSelectedStudentIds([]);\n                            setIsSelectAll(false);\n                            setSelectedStudent(null);\n                            // 重新获取学生列表\n                            await fetchStudents();\n                        } else {\n                            notification.error(response.data.message || \"批量移出学生失败\");\n                        }\n                    } catch (error) {\n                        var _error_response_data, _error_response;\n                        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                        const notification = GetNotification();\n                        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"批量移出学生失败\");\n                        console.error(\"批量移出学生失败:\", error);\n                    }\n                }\n            });\n        } catch (error) {\n            console.error(\"批量移出学生失败:\", error);\n        }\n    };\n    // 处理添加学生\n    const handleAddStudent = async (values)=>{\n        try {\n            console.log(\"添加学生:\", values);\n            // 添加默认密码\n            const studentData = {\n                ...values,\n                password: \"123456\"\n            };\n            // 调用添加学生的API\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.addStudentToClass(classInfo.id, studentData);\n            console.log(\"添加学生 API 响应:\", response.data);\n            // 检查响应状态\n            if (response.data.code === 200) {\n                console.log(\"添加学生成功\");\n                setIsAddStudentModalVisible(false);\n                // 重新获取学生列表\n                await fetchStudents();\n            } else {\n                console.error(\"添加学生失败:\", response.data.message);\n                alert(response.data.message || \"添加学生失败\");\n            }\n        } catch (error) {\n            console.error(\"添加学生失败:\", error);\n            alert(\"添加学生失败，请稍后重试\");\n        }\n    };\n    // 处理文件上传\n    const handleUpload = async (file)=>{\n        setUploading(true);\n        try {\n            // 这里可以添加文件上传逻辑\n            console.log(\"上传文件:\", file);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error(\"文件上传失败:\", error);\n            return {\n                success: false\n            };\n        } finally{\n            setUploading(false);\n        }\n    };\n    // 处理文件删除\n    const handleRemoveFile = async (file)=>{\n        try {\n            setFileList((prev)=>prev.filter((f)=>f.uid !== file.uid));\n            return true;\n        } catch (error) {\n            console.error(\"删除文件失败:\", error);\n            return false;\n        }\n    };\n    // 处理发布任务\n    const handlePublishTask = async (values)=>{\n        try {\n            console.log(\"发布任务:\", values);\n            // 导入taskApi\n            const { default: taskApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/task */ \"(app-pages-browser)/./lib/api/task.ts\"));\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 构建任务参数\n            const params = {\n                taskName: values.taskName,\n                taskDescription: values.taskDescription || \"\",\n                taskType: 1,\n                startDate: values.startDate ? new Date(values.startDate) : undefined,\n                endDate: values.endDate ? new Date(values.endDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n                taskContent: values.taskContent || \"\",\n                attachments: fileList.map((file)=>{\n                    var _file_response;\n                    return file.url || ((_file_response = file.response) === null || _file_response === void 0 ? void 0 : _file_response.url);\n                }).filter(Boolean),\n                teacherId: userId,\n                classId: classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n                studentIds: selectedStudentIds.length > 0 ? selectedStudentIds : students.map((s)=>s.userId),\n                selfAssessmentItems: values.selfAssessmentItems || [],\n                priority: 1,\n                isPublic: 0,\n                allowLateSubmission: values.allowLateSubmission || false\n            };\n            console.log(\"发布任务参数:\", params);\n            // 调用发布任务API\n            const response = await taskApi.publishTask(params);\n            if (response.data.code === 200) {\n                // 显示详细的成功提示\n                const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n                const studentCount = selectedStudentIds.length > 0 ? selectedStudentIds.length : students.length;\n                const className = (classInfo === null || classInfo === void 0 ? void 0 : classInfo.className) || \"当前班级\";\n                Modal.success({\n                    title: \"任务发布成功\",\n                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"任务 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: params.taskName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1204,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    \" 已成功发布到 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: className\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1204,\n                                        columnNumber: 63\n                                    }, undefined),\n                                    \"。\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1204,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"共有 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: studentCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1205,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    \" 名学生将收到此任务。\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1205,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"学生可以在班级空间查看和提交任务。\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1206,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1203,\n                        columnNumber: 13\n                    }, undefined),\n                    okText: \"确定\",\n                    onOk: ()=>{\n                    // 可以在这里添加导航到任务管理页面的逻辑\n                    }\n                });\n                setIsPublishTaskModalVisible(false);\n                // 清理表单数据\n                setFileList([]);\n                setSelectedStudentIds([]);\n                setSelectedTemplateId(null);\n                // 刷新学生列表（如果需要显示任务相关信息）\n                await fetchStudents();\n            } else {\n                notification.error(response.data.message || \"任务发布失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"发布任务失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"发布任务失败，请稍后重试\");\n        }\n    };\n    // 处理重置密码\n    const handleResetPassword = async ()=>{\n        try {\n            if (!selectedStudent) {\n                alert(\"请先选择要重置密码的学生\");\n                return;\n            }\n            console.log(\"重置密码:\", selectedStudent);\n            // 这里可以添加重置密码的API调用\n            setIsResetPasswordModalVisible(false);\n            alert(\"密码重置成功，新密码为：123456\");\n        } catch (error) {\n            console.error(\"重置密码失败:\", error);\n            alert(\"重置密码失败，请稍后重试\");\n        }\n    };\n    // 处理选择模板\n    const handleSelectTemplate = async (templateId)=>{\n        try {\n            const { addUserJoinRole, batchAddUserJoinRole } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 确定要分配的学生\n            const targetStudents = selectedStudentId !== null ? [\n                selectedStudentId\n            ] : selectedStudentIds;\n            console.log(\"=== 模板分配详情 ===\");\n            console.log(\"selectedStudentId:\", selectedStudentId);\n            console.log(\"selectedStudentIds:\", selectedStudentIds);\n            console.log(\"targetStudents:\", targetStudents);\n            console.log(\"templateId:\", templateId);\n            if (targetStudents.length === 0) {\n                console.log(\"❌ 没有选中任何学生\");\n                notification.warning(\"请先选择学生\");\n                return;\n            }\n            const hideLoading = notification.loading(\"正在分配积木...\");\n            const userRolesMap = userRoles || [];\n            try {\n                // 准备用户数据 - 与teacher-space保持一致的逻辑\n                const usersData = targetStudents.map((userId)=>{\n                    const userInfo = userRolesMap.find((u)=>u.userId === userId);\n                    if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.roleId)) return null;\n                    return {\n                        userId: userId,\n                        roleId: userInfo.roleId,\n                        templateId: templateId,\n                        originalTemplateId: templateId\n                    };\n                }).filter((item)=>item !== null);\n                console.log(\"准备分配模板:\", {\n                    templateId,\n                    targetStudents,\n                    userRolesMap,\n                    usersData\n                });\n                if (usersData.length === 0) {\n                    notification.error(\"无有效用户可分配\");\n                    return;\n                }\n                // 分批并发处理\n                const results = await (usersData.length > 20 ? Promise.all(Array.from({\n                    length: Math.ceil(usersData.length / 20)\n                }, (_, i)=>usersData.slice(i * 20, (i + 1) * 20)).map((batchUsers)=>batchAddUserJoinRole({\n                        users: batchUsers\n                    }).then((param)=>{\n                        let { data } = param;\n                        return data;\n                    }).catch(()=>({\n                            code: 500,\n                            data: {\n                                successCount: 0,\n                                failCount: batchUsers.length\n                            }\n                        })))).then((results)=>({\n                        code: results.some((r)=>r.code !== 200) ? 500 : 200,\n                        data: {\n                            successCount: results.reduce((sum, r)=>{\n                                var _r_data;\n                                return sum + (((_r_data = r.data) === null || _r_data === void 0 ? void 0 : _r_data.successCount) || 0);\n                            }, 0),\n                            failCount: results.reduce((sum, r)=>{\n                                var _r_data;\n                                return sum + (((_r_data = r.data) === null || _r_data === void 0 ? void 0 : _r_data.failCount) || 0);\n                            }, 0)\n                        }\n                    })) : batchAddUserJoinRole({\n                    users: usersData\n                }).then((param)=>{\n                    let { data } = param;\n                    return data;\n                }).catch(()=>({\n                        code: 500,\n                        data: {\n                            successCount: 0,\n                            failCount: usersData.length\n                        }\n                    })));\n                if (hideLoading) {\n                    hideLoading.close();\n                }\n                // 显示结果\n                if (results.code === 200) {\n                    const { successCount = 0, failCount = 0 } = results.data || {};\n                    if (successCount > 0 && failCount === 0) {\n                        notification.success(\"成功为 \".concat(successCount, \" 名学生分配积木\"));\n                    } else if (successCount > 0 && failCount > 0) {\n                        notification.warning(\"成功为 \".concat(successCount, \" 名学生分配积木，\").concat(failCount, \" 名学生分配失败\"));\n                    } else {\n                        notification.error(\"积木分配失败\");\n                    }\n                } else {\n                    notification.error(\"积木分配失败\");\n                }\n                // 立即更新已分配学生的模板信息，无需等待API刷新\n                const selectedTemplate = templates.find((t)=>t.id === templateId);\n                if (selectedTemplate) {\n                    const templateData = {\n                        templateId: templateId,\n                        templateName: selectedTemplate.templateName || selectedTemplate.name,\n                        isOfficial: selectedTemplate.isOfficial || false\n                    };\n                    // 更新personalTemplateAssignments Map，确保数据持久化\n                    setPersonalTemplateAssignments((prev)=>{\n                        const newMap = new Map(prev);\n                        targetStudents.forEach((studentId)=>{\n                            newMap.set(studentId, templateData);\n                        });\n                        return newMap;\n                    });\n                    // 更新学生状态\n                    const updatedStudents = students.map((student)=>{\n                        if (targetStudents.includes(student.userId)) {\n                            // 被选中的学生：设置为新分配的模板\n                            return {\n                                ...student,\n                                currentTemplate: templateData\n                            };\n                        }\n                        // 未选中的学生：保持原有状态\n                        return student;\n                    });\n                    setStudents(updatedStudents);\n                    console.log(\"模板分配成功，已更新personalTemplateAssignments:\", {\n                        targetStudents,\n                        templateData,\n                        personalTemplateAssignmentsBefore: Array.from(personalTemplateAssignments.entries()),\n                        personalTemplateAssignmentsAfter: \"will be updated\"\n                    });\n                    // 延迟打印更新后的状态\n                    setTimeout(()=>{\n                        console.log(\"personalTemplateAssignments更新后:\", Array.from(personalTemplateAssignments.entries()));\n                    }, 100);\n                }\n                // 关闭弹窗并清理状态\n                setIsAssignBlocksModalVisible(false);\n                setSelectedStudentId(null);\n                setSelectedStudentIds([]); // 清空选中的学生\n                setIsSelectAll(false); // 取消全选状态\n                // 刷新相关数据\n                await fetchTemplateUsage(); // 刷新模板使用情况\n            // 当前模板信息由全局状态管理，无需手动刷新\n            } catch (error) {\n                if (hideLoading) {\n                    hideLoading.close();\n                }\n                throw error;\n            }\n        } catch (error) {\n            console.error(\"分配模板失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"分配模板失败，请稍后重试\");\n        }\n    };\n    // 处理模板使用情况点击\n    const handleTemplateUsageClick = (e, template)=>{\n        e.stopPropagation();\n        console.log(\"查看模板使用情况:\", template);\n    };\n    // 处理单个学生分配能量\n    const handleAssignPoints = async (values)=>{\n        var _values_studentExpiries;\n        if (!selectedStudent) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"请先选择学生\");\n            return;\n        }\n        // 从 studentExpiries 中提取单个学生的过期时间\n        const expireTime = (_values_studentExpiries = values.studentExpiries) === null || _values_studentExpiries === void 0 ? void 0 : _values_studentExpiries[selectedStudent.userId];\n        try {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            const hideLoading = notification.loading(\"正在分配能量...\");\n            const { pointsApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\"));\n            await pointsApi.assignPermission({\n                studentUserId: selectedStudent.userId,\n                availablePoints: values.availablePoints,\n                expireTime: expireTime,\n                remark: values.remark\n            });\n            if (hideLoading) {\n                hideLoading.close();\n            }\n            notification.success(\"分配能量成功\");\n            setIsAssignPointsModalVisible(false);\n            // 刷新学生列表\n            await refreshStudentList();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 增加更具体的错误提示\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"分配能量失败\");\n        }\n    };\n    // 刷新学生列表\n    const refreshStudentList = async ()=>{\n        await fetchStudents();\n    };\n    // 更新教师的当前模板（通过UserJoinRole表）\n    const updateClassCurrentTemplate = async (templateId, templateName, isOfficial)=>{\n        try {\n            console.log(\"更新教师当前模板:\", {\n                userId,\n                roleId,\n                templateId,\n                templateName,\n                isOfficial\n            });\n            // 使用addUserJoinRole API来更新教师的模板\n            const { addUserJoinRole } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            const response = await addUserJoinRole({\n                userId: userId,\n                roleId: roleId || 2,\n                templateId: templateId\n            });\n            if (response.data.code === 200) {\n                // 更新本地的全局当前模板状态\n                const newTemplate = {\n                    templateId: templateId,\n                    templateName: templateName,\n                    isOfficial: isOfficial\n                };\n                setGlobalCurrentTemplate(newTemplate);\n                console.log(\"教师当前模板更新成功:\", newTemplate);\n            } else {\n                console.error(\"更新教师当前模板失败:\", response.data);\n            }\n        } catch (error) {\n            console.error(\"更新教师当前模板失败:\", error);\n        }\n    };\n    // 更新学生的当前模板信息\n    const handleUpdateStudentTemplate = (studentIds, templateInfo)=>{\n        const templateData = {\n            templateId: templateInfo.templateId,\n            templateName: templateInfo.templateName,\n            isOfficial: templateInfo.isOfficial || false\n        };\n        // 保存个人分配的模板信息\n        setPersonalTemplateAssignments((prev)=>{\n            const newMap = new Map(prev);\n            studentIds.forEach((studentId)=>{\n                newMap.set(studentId, templateData);\n            });\n            return newMap;\n        });\n        // 更新学生状态\n        setStudents((prevStudents)=>prevStudents.map((student)=>{\n                if (studentIds.includes(student.userId)) {\n                    return {\n                        ...student,\n                        currentTemplate: templateData\n                    };\n                }\n                return student;\n            }));\n    // 不再自动更新教师模板，只分配给学生\n    // updateClassCurrentTemplate(templateData.templateId, templateData.templateName, templateData.isOfficial);\n    };\n    // 处理批量/单个兑换密钥\n    const handleBatchUseKeyPackage = (studentId)=>{\n        if (studentId) {\n            // 单个学生触发\n            setSelectedStudentIds([\n                studentId\n            ]);\n            setIsBatchUseKeyPackageModalVisible(true);\n        } else {\n            // 批量操作触发\n            if (selectedStudentIds.length === 0) {\n                Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                    let { GetNotification } = param;\n                    GetNotification().warning(\"请先选择学生\");\n                });\n                return;\n            }\n            setIsBatchUseKeyPackageModalVisible(true);\n        }\n    };\n    // 处理兑换密令成功\n    const handleBatchUseKeyPackageSuccess = ()=>{\n        refreshStudentList();\n        setIsBatchUseKeyPackageModalVisible(false);\n    };\n    // 处理从兑换密令跳转到分配能量\n    const handleGoToAssignPointsFromRedeem = (studentIds)=>{\n        setIsBatchUseKeyPackageModalVisible(false);\n        // 设置选中的学生\n        setSelectedStudentIds(studentIds);\n        // 打开分配能量弹窗\n        setIsAssignPointsModalVisible(true);\n    };\n    // 批量分配能量处理函数\n    const handleBatchAssignPoints = async (values)=>{\n        if (selectedStudentIds.length === 0) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"请先选择学生\");\n            return;\n        }\n        if (!values.studentExpiries) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"未能获取学生过期时间信息\");\n            return;\n        }\n        try {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            const hideLoading = notification.loading(\"正在批量分配能量...\");\n            // 调用新的批量分配 API\n            const { pointsApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\"));\n            const response = await pointsApi.batchAssignPermission({\n                availablePoints: values.availablePoints,\n                studentExpiries: values.studentExpiries,\n                remark: values.remark\n            });\n            console.log(\"批量分配积分res\", response);\n            if (hideLoading) {\n                hideLoading.close();\n            }\n            if (response.data.code === 200) {\n                // 后端现在返回处理结果数组，可以根据需要处理\n                const results = response.data.data;\n                // 可以根据 results 中的信息给出更详细的成功/失败提示，\n                // 但为了简单起见，我们仍然使用之前的逻辑\n                notification.success(\"成功为 \".concat(results.success, \" 名学生分配能量\"));\n                setIsAssignPointsModalVisible(false);\n                // 刷新学生列表\n                await refreshStudentList(); // 确保使用 await\n                setSelectedStudentIds([]); // 清空选择\n                setIsSelectAll(false);\n            } else {\n                // 处理 API 返回的错误信息\n                notification.error(response.data.message || \"批量分配能量失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"批量分配能量失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 处理请求级别的错误\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"批量分配能量失败，请检查网络连接或稍后重试\");\n        }\n    };\n    if (!selectedSchool) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"class-detail-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-content\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"error-message\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"学校信息不存在\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1634,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onBack,\n                            className: \"back-button\",\n                            children: \"返回班级管理\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1635,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1633,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1632,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n            lineNumber: 1631,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 生成随机头像颜色\n    const getAvatarColor = (index)=>{\n        const colors = [\n            \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n            \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n            \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n            \"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)\",\n            \"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\",\n            \"linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)\"\n        ];\n        // 确保index是有效的正数\n        const safeIndex = Math.max(0, index);\n        return colors[safeIndex % colors.length];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"class-detail-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"back-button\",\n                    onClick: onBack,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1667,\n                            columnNumber: 11\n                        }, undefined),\n                        \"返回班级管理\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1663,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1662,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"left-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"left-section-header\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"settings-container\",\n                                    ref: settingsDropdownRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"settings-btn \".concat(isSettingsDropdownOpen ? \"active\" : \"\"),\n                                            onClick: ()=>setIsSettingsDropdownOpen(!isSettingsDropdownOpen),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1682,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1678,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isSettingsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"dropdown-menu\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-menu-items\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"edit\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#8b5cf6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1693,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1692,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"编辑班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1695,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1688,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"addStudent\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#10b981\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1703,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1702,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"添加学生\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1705,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1698,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"importStudent\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#3b82f6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1713,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1712,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"导入学生\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1715,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1708,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"exportStudent\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#10b981\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1723,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1722,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"导出学生\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1725,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1718,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"transfer\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#f59e0b\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1733,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1732,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"转让管理\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1735,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1728,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"invite\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#3b82f6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1743,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1742,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"生成邀请码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1745,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1738,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"batchRedeem\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#f59e0b\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1753,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1752,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"批量兑换密令\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1755,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1748,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"assignBlocks\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#3b82f6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1763,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1762,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"分配积木\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1765,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1758,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-divider\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1768,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item danger\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"deleteClass\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1775,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1774,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"删除班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1777,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1770,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1687,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1686,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1677,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1676,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"class-info-card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"class-card-header\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"class-card-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"class-card-icon\",\n                                                    children: \"\\uD83D\\uDCCB\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1789,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"class-card-info\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"class-card-title\",\n                                                            children: classInfo.className\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1793,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"class-card-subtitle\",\n                                                            children: selectedSchool.schoolName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1794,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1792,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1788,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"class-card-badge\",\n                                            children: \"当前模板\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1797,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1787,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1786,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"search-section\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"search-box\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        size: 16,\n                                        className: \"search-icon\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1806,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1805,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1804,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"students-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"students-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"学生\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1813,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"students-actions\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"add-student-btn\",\n                                                        onClick: ()=>setIsAddStudentModalVisible(true),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1819,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1815,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"select-all-btn \".concat(isSelectAll ? \"active\" : \"\"),\n                                                        onClick: ()=>{\n                                                            console.log(\"点击全选按钮，当前状态:\", {\n                                                                isSelectAll,\n                                                                selectedStudentIds: selectedStudentIds.length,\n                                                                totalStudents: students.length\n                                                            });\n                                                            handleSelectAll();\n                                                        },\n                                                        title: isSelectAll ? \"取消全选\" : \"全选\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: isSelectAll ? \"取消全选\" : \"全选\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1833,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1821,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    selectedStudentIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"batch-actions-container\",\n                                                        ref: batchActionsDropdownRef,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"batch-actions-btn\",\n                                                                onClick: ()=>setIsBatchActionsDropdownOpen(!isBatchActionsDropdownOpen),\n                                                                title: \"批量操作\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"批量操作(\",\n                                                                            selectedStudentIds.length,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1844,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1845,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1839,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            isBatchActionsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"batch-actions-dropdown\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchAssignBlocks\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1855,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量分配积木\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1856,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1851,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchAssignPoints\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1862,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量分配能量\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1863,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1858,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchUseKeyPackage\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1869,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量兑换密令\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1870,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1865,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchDelete\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1876,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量移出班级\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1877,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1872,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchExport\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1883,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量导出\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1884,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1879,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1850,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1838,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1814,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1812,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"students-list\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            students: students,\n                                            loading: loading,\n                                            error: error,\n                                            selectedStudent: selectedStudent,\n                                            selectedStudentIds: selectedStudentIds,\n                                            currentTemplate: globalCurrentTemplate || currentTemplate,\n                                            renderVersion: renderVersion,\n                                            onStudentClick: handleStudentClick,\n                                            onStudentSelect: handleStudentSelect,\n                                            onRetry: fetchStudents,\n                                            onIndividualAssignBlocks: handleIndividualAssignBlocks,\n                                            onAssignPoints: (studentId)=>{\n                                                const student = students.find((s)=>s.userId === studentId);\n                                                if (student) {\n                                                    setSelectedStudent(student);\n                                                    setIsAssignPointsModalVisible(true);\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1894,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1893,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1811,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1674,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"right-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"student-info-header\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"student-avatar-large\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"student-avatar-circle\",\n                                            style: {\n                                                background: selectedStudent ? getAvatarColor(Math.max(0, students.findIndex((s)=>s.userId === selectedStudent.userId))) : getAvatarColor(0)\n                                            },\n                                            children: selectedStudent ? ((_selectedStudent_nickName = selectedStudent.nickName) === null || _selectedStudent_nickName === void 0 ? void 0 : _selectedStudent_nickName.charAt(0)) || \"S\" : \"S\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1923,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1922,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"student-details\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"student-name-large\",\n                                                children: selectedStudent ? selectedStudent.nickName || \"学生\".concat(selectedStudent.studentNumber || selectedStudent.userId) : \"请选择学生\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1935,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"student-id-large\",\n                                                children: selectedStudent ? selectedStudent.studentNumber || \"无学号\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1938,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1934,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"delete-student-btn\",\n                                        onClick: handleDeleteStudent,\n                                        disabled: !selectedStudent,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1947,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1942,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1921,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"functions-container\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"functions-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"更多功能\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1955,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"function-buttons\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn publish-task\",\n                                                        onClick: ()=>setIsPublishTaskModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83D\\uDCDD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1961,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"发布任务\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1962,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1957,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn distribute-blocks\",\n                                                        onClick: handleAssignBlocks,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83E\\uDDE9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1968,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"分配积木\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1969,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1964,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn distribute-energy\",\n                                                        onClick: ()=>setIsAssignPointsModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"⚡\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1975,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"分配能量\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1976,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1971,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn exchange-tokens\",\n                                                        onClick: ()=>setIsBatchUseKeyPackageModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83C\\uDF81\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1982,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"兑换密令\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1983,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1978,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn reset-password\",\n                                                        onClick: ()=>setIsResetPasswordModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83D\\uDD11\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1989,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"重置密码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1990,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1985,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1956,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1954,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"learning-status\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"课程学习情况\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1997,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"status-placeholder\",\n                                                children: \"该区域功能正在等待开放\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1998,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1996,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1952,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bottom-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"energy-progress-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"energy-progress-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"可用能量/总能量\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2010,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.availablePoints) || 0,\n                                                            \"/\",\n                                                            (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.totalPoints) || 0\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2011,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2009,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"energy-progress-bar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"energy-progress-fill\",\n                                                    style: {\n                                                        width: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.totalPoints) ? \"\".concat(Number(selectedStudent.availablePoints || 0) / Number(selectedStudent.totalPoints || 0) * 100, \"%\") : \"0%\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2016,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2015,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2008,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"template-card-section\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"template-card\",\n                                            onClick: ()=>selectedStudent && handleTemplateAssignment(),\n                                            style: {\n                                                cursor: selectedStudent ? \"pointer\" : \"default\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"template-card-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"template-card-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"template-card-icon\",\n                                                                children: \"\\uD83D\\uDCCB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 2035,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"template-card-info\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"template-card-title\",\n                                                                        children: (()=>{\n                                                                            if (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.currentTemplate) {\n                                                                                return selectedStudent.currentTemplate.templateName;\n                                                                            } else if (globalCurrentTemplate) {\n                                                                                return globalCurrentTemplate.templateName;\n                                                                            } else if (currentTemplate) {\n                                                                                return currentTemplate.templateName;\n                                                                            } else if (selectedStudent) {\n                                                                                return \"加载中...\";\n                                                                            } else {\n                                                                                return \"请选择学生\";\n                                                                            }\n                                                                        })()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 2039,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"template-card-subtitle\",\n                                                                        children: selectedStudent ? ((_selectedStudent_currentTemplate = selectedStudent.currentTemplate) === null || _selectedStudent_currentTemplate === void 0 ? void 0 : _selectedStudent_currentTemplate.isOfficial) ? \"官方模板\" : \"自定义模板\" : \"点击选择学生查看模板\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 2054,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 2038,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2034,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"template-card-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"template-card-badge\",\n                                                                children: \"当前模板\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 2063,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"template-card-swap-icon\",\n                                                                children: \"⇄\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 2066,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2062,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2033,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2028,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2027,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 2007,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1919,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1672,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddStudentModal__WEBPACK_IMPORTED_MODULE_5__.AddStudentModal, {\n                visible: isAddStudentModalVisible,\n                onCancel: ()=>setIsAddStudentModalVisible(false),\n                onOk: handleAddStudent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2078,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.EditClassModal, {\n                visible: isEditClassModalVisible,\n                onCancel: ()=>setIsEditClassModalVisible(false),\n                onOk: handleEditClass,\n                initialValues: {\n                    className: classInfo.className\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2085,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.ImportStudentModal, {\n                visible: isImportStudentModalVisible,\n                onCancel: ()=>setIsImportStudentModalVisible(false),\n                onImport: handleImportStudents,\n                classId: classInfo.id\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2095,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransferClassModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                visible: isTransferClassModalVisible,\n                onCancel: ()=>{\n                    setIsTransferClassModalVisible(false);\n                    setSearchedTeacher(null);\n                },\n                onOk: handleTransferClass,\n                onSearchTeacher: handleSearchTeacher,\n                searchedTeacher: searchedTeacher,\n                hasAssistantTeacher: !!classInfo.assistantTeacherId,\n                onRemoveAssistant: handleRemoveAssistant,\n                loading: transferLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2103,\n                columnNumber: 7\n            }, undefined),\n            isInviteCodeModalVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold\",\n                                    children: \"邀请码生成成功\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2122,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-gray-500 hover:text-gray-700\",\n                                    onClick: ()=>setIsInviteCodeModalVisible(false),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-6 w-6\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2128,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2127,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2123,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2121,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between bg-gray-50 p-3 rounded-lg mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-mono text-lg text-blue-600 mr-4\",\n                                            children: inviteCode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2134,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                            onClick: ()=>{\n                                                navigator.clipboard.writeText(inviteCode).then(()=>alert(\"邀请码已复制\")).catch(()=>alert(\"复制失败，请手动复制\"));\n                                            },\n                                            children: \"复制\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2135,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2133,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-sm space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"您可以:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2147,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-disc list-inside space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"将邀请码分享给学生，让他们加入班级\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2149,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"邀请其他老师作为协助教师加入班级\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2150,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2148,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-500\",\n                                            children: \"⏰ 邀请码有效期为24小时\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2152,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2146,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2132,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                onClick: ()=>setIsInviteCodeModalVisible(false),\n                                children: \"关闭\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 2158,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2157,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 2120,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2119,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.PublishTaskModal, {\n                visible: isPublishTaskModalVisible,\n                onCancel: ()=>setIsPublishTaskModalVisible(false),\n                onOk: handlePublishTask,\n                students: students,\n                selectedStudents: selectedStudentIds,\n                setSelectedStudents: setSelectedStudentIds,\n                fileList: fileList,\n                uploading: uploading,\n                handleUpload: handleUpload,\n                handleRemoveFile: handleRemoveFile,\n                displayTemplates: templates,\n                officialTemplates: officialTemplates,\n                selectedTemplateId: selectedTemplateId,\n                setSelectedTemplateId: setSelectedTemplateId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2172,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.ResetPasswordModal, {\n                visible: isResetPasswordModalVisible,\n                onCancel: ()=>setIsResetPasswordModalVisible(false),\n                onOk: handleResetPassword,\n                isBatch: false,\n                count: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2190,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AssignBlocksModal__WEBPACK_IMPORTED_MODULE_7__.AssignBlocksModal, {\n                visible: isAssignBlocksModalVisible,\n                onCancel: ()=>{\n                    setIsAssignBlocksModalVisible(false);\n                    setSelectedStudentId(null);\n                    setSelectedStudentIds([]);\n                },\n                isClassCardAssign: selectedStudentId !== null,\n                loadingTemplates: loadingTemplates,\n                templates: templates,\n                studentTemplateUsage: studentTemplateUsage,\n                teacherTemplate: teacherTemplate,\n                onSelectTemplate: handleSelectTemplate,\n                onTemplateUsageClick: handleTemplateUsageClick,\n                userId: userId,\n                onRefreshTemplates: fetchTemplates,\n                students: students,\n                selectedStudentIds: selectedStudentIds,\n                userRoles: userRoles,\n                onSuccess: ()=>{\n                    refreshStudentList();\n                    fetchTemplateUsage();\n                },\n                onUpdateStudentTemplate: handleUpdateStudentTemplate\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2199,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AssignPointsModal__WEBPACK_IMPORTED_MODULE_9__.AssignPointsModal, {\n                visible: isAssignPointsModalVisible,\n                onCancel: ()=>{\n                    setIsAssignPointsModalVisible(false);\n                    setSelectedStudent(null);\n                },\n                // 根据 selectedStudent 是否存在来决定调用哪个处理函数\n                onOk: selectedStudent ? handleAssignPoints : handleBatchAssignPoints,\n                studentName: selectedStudent ? \"\".concat(selectedStudent.nickName) : \"已选择 \".concat(selectedStudentIds.length, \" 名学生\"),\n                studentId: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.id) || 0,\n                userId: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.userId) || 0,\n                student: selectedStudent,\n                isBatch: !selectedStudent,\n                selectedStudents: selectedStudentIds,\n                students: students,\n                onSuccess: ()=>{\n                // 移除这里的刷新，因为 onOk 内部已经处理了\n                },\n                refreshStudentList: refreshStudentList,\n                onGoToRedeemKey: (studentIds)=>{\n                    console.log(\"前往兑换密钥:\", studentIds);\n                    setIsBatchUseKeyPackageModalVisible(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2226,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_10__.BatchUseKeyPackageModal, {\n                open: isBatchUseKeyPackageModalVisible,\n                selectedStudentIds: selectedStudentIds,\n                students: students,\n                onClose: ()=>setIsBatchUseKeyPackageModalVisible(false),\n                onSuccess: handleBatchUseKeyPackageSuccess,\n                onGoToAssignPoints: handleGoToAssignPointsFromRedeem\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2252,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n        lineNumber: 1660,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClassDetail, \"rOoOqxdnOob2DP168DTJvyWgCZI=\", false, function() {\n    return [\n        _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__.useTemplate,\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector\n    ];\n});\n_c = ClassDetail;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClassDetail);\nvar _c;\n$RefreshReg$(_c, \"ClassDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/ClassDetail.tsx\n"));

/***/ })

});