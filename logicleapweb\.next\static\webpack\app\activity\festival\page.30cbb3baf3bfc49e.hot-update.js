"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/activity/festival/page",{

/***/ "(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx":
/*!*************************************************************!*\
  !*** ./app/activity/festival/components/ActivityHeader.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./lib/store.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ActivityMediaGallery */ \"(app-pages-browser)/./app/activity/festival/components/ActivityMediaGallery.tsx\");\n/* harmony import */ var _barrel_optimize_names_Modal_Upload_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Modal,Upload!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Modal_Upload_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Modal,Upload!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _components_login_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/login-dialog */ \"(app-pages-browser)/./components/login-dialog.tsx\");\n/* harmony import */ var _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/activity */ \"(app-pages-browser)/./lib/api/activity/index.ts\");\n/* harmony import */ var _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/activity/events-task */ \"(app-pages-browser)/./lib/api/activity/events-task.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../../lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var _lib_api_user__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/user */ \"(app-pages-browser)/./lib/api/user.ts\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 临时实现formatDate函数，以解决模块导入问题\nconst formatDate = (dateString)=>{\n    // 在服务端渲染时，直接返回原始字符串，避免水合错误\n    if (false) {}\n    const date = new Date(dateString);\n    // 检查日期是否有效\n    if (isNaN(date.getTime())) {\n        return dateString; // 如果无效，返回原始字符串\n    }\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, \"0\");\n    const day = String(date.getDate()).padStart(2, \"0\");\n    return \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n};\nconst ActivityHeader = (param)=>{\n    let { title: initialTitle, startTime: initialStartTime, endTime: initialEndTime, bannerImage: initialBannerImage, expanded, setExpanded, activityId, tags = [], organizer: initialOrganizer = \"\", activityType = _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.ActivityType.WORK, onRefreshWorks, // 媒体字段\n    promotionImage, backgroundImage, galleryImages, attachmentFiles } = param;\n    _s();\n    console.log(\"\\uD83D\\uDD0D [DEBUG] ActivityHeader 组件渲染\", {\n        activityId,\n        initialTitle\n    });\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_10__.GetNotification)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.usePathname)();\n    // 显示用的标题和时间\n    const title = initialTitle;\n    const startTime = initialStartTime;\n    const endTime = initialEndTime;\n    const bannerImage = initialBannerImage;\n    const organizer = initialOrganizer;\n    // 获取当前用户信息和dispatch\n    const userState = (0,react_redux__WEBPACK_IMPORTED_MODULE_12__.useSelector)(_lib_store__WEBPACK_IMPORTED_MODULE_3__.selectUserState);\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_12__.useDispatch)();\n    const isAdmin = (userState === null || userState === void 0 ? void 0 : userState.roleId) === 4;\n    // 同步用户状态的函数 - 增强版，从API获取最新用户信息\n    const syncUserState = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 开始同步用户状态\");\n            const localUser = localStorage.getItem(\"user\");\n            if (localUser) {\n                const userData = JSON.parse(localUser);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中的用户数据\", userData);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] Redux 中的用户数据\", userState);\n                // 如果用户已登录但phone为空，尝试从API获取最新用户信息\n                if (userData.isLoggedIn && userData.userId && (!userData.phone || userData.phone.trim() === \"\")) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户phone为空，从API获取最新用户信息\");\n                    try {\n                        const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_9__.userApi.getUserInfo(userData.userId);\n                        if (response.code === 200 && response.data) {\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取到最新用户信息\", response.data);\n                            // 构建更新后的用户数据\n                            const updatedUserData = {\n                                ...userData,\n                                phone: response.data.phone || \"\",\n                                nickName: response.data.nickName || userData.nickName,\n                                avatarUrl: response.data.avatarUrl || userData.avatarUrl,\n                                gender: response.data.gender || userData.gender,\n                                roleId: response.data.roleId || userData.roleId\n                            };\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 更新用户数据\", {\n                                oldPhone: userData.phone,\n                                newPhone: updatedUserData.phone,\n                                updated: updatedUserData\n                            });\n                            // 更新localStorage和Redux\n                            localStorage.setItem(\"user\", JSON.stringify(updatedUserData));\n                            dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(updatedUserData));\n                            return;\n                        }\n                    } catch (error) {\n                        console.error(\"\\uD83D\\uDD0D [DEBUG] 从API获取用户信息失败:\", error);\n                    }\n                }\n                // 检查关键字段是否有变化\n                const hasPhoneChanged = userData.phone !== (userState === null || userState === void 0 ? void 0 : userState.phone);\n                const hasRoleChanged = userData.roleId !== (userState === null || userState === void 0 ? void 0 : userState.roleId);\n                const hasLoginStatusChanged = userData.isLoggedIn !== (userState === null || userState === void 0 ? void 0 : userState.isLoggedIn);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态变化检查\", {\n                    phoneChanged: hasPhoneChanged,\n                    roleChanged: hasRoleChanged,\n                    loginStatusChanged: hasLoginStatusChanged,\n                    oldPhone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                    newPhone: userData.phone,\n                    oldRole: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                    newRole: userData.roleId\n                });\n                // 如果localStorage中的用户状态与Redux中的不一致，说明需要更新\n                if (hasPhoneChanged || hasRoleChanged || hasLoginStatusChanged) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户状态变化，同步Redux状态\");\n                    dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(userData));\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态无变化，无需同步\");\n                }\n            } else {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中无用户数据\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD0D [DEBUG] 检查用户状态失败:\", error);\n        }\n    };\n    // 监听页面焦点变化和路由变化，重新检查用户状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 页面加载时立即检查一次\n        syncUserState().catch(console.error);\n        const handleVisibilityChange = ()=>{\n            if (document.visibilityState === \"visible\") {\n                syncUserState().catch(console.error);\n            }\n        };\n        const handleFocus = ()=>{\n            syncUserState().catch(console.error);\n        };\n        // 添加多种事件监听确保状态同步\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n        window.addEventListener(\"focus\", handleFocus);\n        return ()=>{\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n            window.removeEventListener(\"focus\", handleFocus);\n        };\n    }, [\n        userState === null || userState === void 0 ? void 0 : userState.phone,\n        dispatch\n    ]);\n    // 工具函数：检查用户是否为学生身份\n    const checkIsStudent = ()=>{\n        try {\n            const userStr = localStorage.getItem(\"user\");\n            const localUser = userStr ? JSON.parse(userStr) : null;\n            const userRoleId = (localUser === null || localUser === void 0 ? void 0 : localUser.roleId) || (userState === null || userState === void 0 ? void 0 : userState.roleId);\n            return userRoleId === 1; // 学生身份的roleId为1\n        } catch (error) {\n            console.error(\"解析localStorage中的用户信息失败:\", error);\n            return false;\n        }\n    };\n    // 工具函数：显示身份验证失败提示\n    const showStudentOnlyModal = ()=>{\n        _barrel_optimize_names_Modal_Upload_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"].confirm({\n            title: \"身份验证\",\n            content: \"抱歉，只有学生身份的用户才能参加此活动报名。如果您是学生，请联系管理员更新您的身份信息。\",\n            okText: \"我知道了\",\n            cancelText: \"联系管理员\",\n            onCancel: ()=>{\n                notification.info(\"如需帮助，请联系客服：400-123-4567\");\n            }\n        });\n    };\n    // 获取报名状态文本\n    const getRegistrationStatusText = ()=>{\n        switch(registrationStatus){\n            case 0:\n                return \"已取消\";\n            case 1:\n                return \"已报名\";\n            case 2:\n                return \"已审核通过\";\n            case 3:\n                return \"已拒绝\";\n            case 4:\n                return \"评审中\";\n            case 5:\n                return \"已获奖\";\n            case 6:\n                return \"审核中\";\n            default:\n                return \"已报名\";\n        }\n    };\n    // 可编辑的导航标签文本\n    const [navTags, setNavTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"tag1\",\n            text: \"官方活动\"\n        },\n        {\n            id: \"tag2\",\n            text: \"AIGC作品征集\"\n        }\n    ]);\n    // 活动标签ID列表\n    const [tagIds, setTagIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 标签编辑状态\n    const [editingTagId, setEditingTagId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingTagText, setEditingTagText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 登录和报名相关状态\n    const [showLoginDialog, setShowLoginDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSubmitted, setHasSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [registrationStatus, setRegistrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 报名状态：0-已取消 1-已报名 2-已审核通过 3-已拒绝 4-评审中 5-已获奖 6-审核中\n    const [uploadLoading, setUploadLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fileList, setFileList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUI, setShowUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 添加客户端检测来解决水合错误\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    // 页面加载时检查报名状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkRegistrationStatus = async ()=>{\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态 - useEffect 触发\", {\n                isLoggedIn: userState.isLoggedIn,\n                roleId: userState.roleId,\n                phone: userState.phone,\n                isClient,\n                activityId\n            });\n            // 只有在用户已登录且是学生身份时才检查报名状态\n            const isStudent = checkIsStudent();\n            if (!userState.isLoggedIn || !isStudent) {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录或不是学生身份，重置报名状态\", {\n                    isLoggedIn: userState.isLoggedIn,\n                    isStudent\n                });\n                // 如果用户未登录或不是学生，重置状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n                return;\n            }\n            try {\n                var _response_data, _response_data1;\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 开始检查报名状态 API 调用\");\n                setIsChecking(true);\n                const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.checkRegistration(Number(activityId));\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 页面加载检查报名状态响应:\", response);\n                if ((response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200 && ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data)) {\n                    const { submitted, submit } = response.data.data;\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态检查结果\", {\n                        submitted,\n                        submit\n                    });\n                    setHasSubmitted(submitted);\n                    if (submit && submit.status !== undefined) {\n                        setRegistrationStatus(submit.status);\n                    } else {\n                        setRegistrationStatus(null);\n                    }\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态响应不符合预期，设置为未报名状态\");\n                    // 如果响应不符合预期，设置为未报名状态\n                    setHasSubmitted(false);\n                    setRegistrationStatus(null);\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态失败:\", error);\n                // 出错时设置为未报名状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n            } finally{\n                setIsChecking(false);\n            }\n        };\n        // 只在客户端执行，避免服务端渲染问题\n        if (isClient) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 客户端环境，执行报名状态检查\");\n            checkRegistrationStatus();\n        } else {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 非客户端环境，跳过报名状态检查\");\n        }\n    }, [\n        activityId,\n        userState.isLoggedIn,\n        userState.roleId,\n        userState.phone,\n        isClient\n    ]);\n    // 检查用户是否已提交作品\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkUserSubmitted = async ()=>{\n            try {\n                // 仅针对已登录用户进行检查\n                if (userState && userState.isLoggedIn) {\n                    var _response_data;\n                    setIsChecking(true);\n                    const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityApi.checkUserSubmitted(Number(activityId));\n                    console.log(\"页面加载时检查用户提交状态响应:\", response);\n                    if ((response === null || response === void 0 ? void 0 : response.status) === 200 && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data)) {\n                        setHasSubmitted(response.data.data.submitted);\n                    } else {\n                        // 如果响应不符合预期，默认为未提交\n                        setHasSubmitted(false);\n                    }\n                    setIsChecking(false);\n                } else {\n                    // 未登录用户，直接设置为未提交\n                    setHasSubmitted(false);\n                    setIsChecking(false);\n                }\n            } catch (error) {\n                console.error(\"检查用户提交状态失败:\", error);\n                // 出错时默认设置为未提交\n                setHasSubmitted(false);\n                setIsChecking(false);\n            }\n        };\n        // 页面加载时执行检查\n        if (isClient) {\n            checkUserSubmitted();\n        }\n    }, [\n        activityId,\n        userState,\n        isClient\n    ]);\n    // 加载活动标签\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchActivityTags = async ()=>{\n            if (activityId) {\n                try {\n                    const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityTagApi.getActivityTags(Number(activityId));\n                    if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n                        setTagIds(response.data.data || []);\n                    }\n                } catch (error) {\n                    console.error(\"获取活动标签失败:\", error);\n                }\n            }\n        };\n        fetchActivityTags();\n    }, [\n        activityId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 初始化文件列表\n        if (bannerImage) {\n            setFileList([\n                {\n                    uid: \"-1\",\n                    name: \"当前封面\",\n                    status: \"done\",\n                    url: bannerImage\n                }\n            ]);\n        } else {\n            setFileList([]);\n        }\n    }, [\n        bannerImage\n    ]);\n    // 处理标签点击编辑\n    const handleTagClick = (tag)=>{\n        if (isEditing) {\n            setTagIds(tagIds.includes(Number(tag.id)) ? tagIds.filter((id)=>id !== Number(tag.id)) : [\n                ...tagIds,\n                Number(tag.id)\n            ]);\n        }\n    };\n    // 处理标签编辑取消\n    const handleTagEditCancel = ()=>{\n        setEditingTagId(null);\n    };\n    // 处理添加新标签\n    const handleAddTag = ()=>{\n        if (navTags.length < 5) {\n            const newTag = {\n                id: \"tag-\".concat(Date.now()),\n                text: \"新标签\"\n            };\n            setNavTags([\n                ...navTags,\n                newTag\n            ]);\n            setTagIds([\n                ...tagIds,\n                Number(newTag.id)\n            ]);\n        }\n    };\n    // 处理删除标签\n    const handleDeleteTag = (id)=>{\n        if (navTags.length > 1) {\n            setNavTags(navTags.filter((tag)=>tag.id !== id));\n            setTagIds(tagIds.filter((id)=>id !== Number(id)));\n        }\n    };\n    // 前往任务函数\n    const handleGoToTask = async ()=>{\n        try {\n            const loadingMessage = notification.loading(\"正在跳转到任务...\");\n            // 获取当前用户的赛事任务列表\n            const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n            if (eventsTaskResponse.data.code === 200) {\n                const eventsTasks = eventsTaskResponse.data.data;\n                // 查找与当前活动相关的赛事任务\n                const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                if (relatedTask) {\n                    // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                    window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                    notification.success(\"正在跳转到任务页面...\");\n                } else {\n                    // 如果没有找到对应任务，在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                    notification.info(\"未找到对应任务，已打开班级空间\");\n                }\n            } else {\n                // 如果获取任务失败，在新标签页中打开班级空间\n                window.open(\"/class-space\", \"_blank\");\n                notification.info(\"获取任务失败，已打开班级空间\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"跳转任务失败:\", error);\n            notification.error(\"跳转任务失败，请稍后重试\");\n        }\n    };\n    // 处理报名按钮点击\n    const handleSubmitClick = async ()=>{\n        var _userState_phone, _localUser_phone;\n        // 获取localStorage中的用户信息进行对比\n        const localUserStr = localStorage.getItem(\"user\");\n        const localUser = localUserStr ? JSON.parse(localUserStr) : null;\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 报名按钮点击 - 详细用户状态检查\", {\n            hasSubmitted,\n            \"Redux userState\": {\n                isLoggedIn: userState === null || userState === void 0 ? void 0 : userState.isLoggedIn,\n                phone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                phoneLength: userState === null || userState === void 0 ? void 0 : (_userState_phone = userState.phone) === null || _userState_phone === void 0 ? void 0 : _userState_phone.length,\n                phoneType: typeof (userState === null || userState === void 0 ? void 0 : userState.phone),\n                roleId: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                userId: userState === null || userState === void 0 ? void 0 : userState.userId,\n                nickName: userState === null || userState === void 0 ? void 0 : userState.nickName\n            },\n            \"localStorage user\": {\n                phone: localUser === null || localUser === void 0 ? void 0 : localUser.phone,\n                phoneLength: localUser === null || localUser === void 0 ? void 0 : (_localUser_phone = localUser.phone) === null || _localUser_phone === void 0 ? void 0 : _localUser_phone.length,\n                phoneType: typeof (localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                userId: localUser === null || localUser === void 0 ? void 0 : localUser.userId,\n                nickName: localUser === null || localUser === void 0 ? void 0 : localUser.nickName\n            },\n            \"phone validation\": {\n                \"userState.phone exists\": !!(userState === null || userState === void 0 ? void 0 : userState.phone),\n                \"userState.phone not empty\": (userState === null || userState === void 0 ? void 0 : userState.phone) && userState.phone.trim() !== \"\",\n                \"localUser.phone exists\": !!(localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                \"localUser.phone not empty\": (localUser === null || localUser === void 0 ? void 0 : localUser.phone) && (localUser === null || localUser === void 0 ? void 0 : localUser.phone.trim()) !== \"\"\n            }\n        });\n        // 临时调用API获取最新用户信息进行对比\n        if (userState === null || userState === void 0 ? void 0 : userState.userId) {\n            try {\n                var _response_data, _response_data_phone, _response_data1, _response_data2, _response_data3, _response_data4;\n                const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_9__.userApi.getUserInfo(userState.userId);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取的最新用户信息:\", {\n                    code: response.code,\n                    phone: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.phone,\n                    phoneLength: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_phone = _response_data1.phone) === null || _response_data_phone === void 0 ? void 0 : _response_data_phone.length,\n                    phoneType: typeof ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.phone),\n                    nickName: (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.nickName,\n                    userId: (_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : _response_data4.id\n                });\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 获取用户信息失败:\", error);\n            }\n        }\n        // 如果用户已报名，跳转到任务页面\n        if (hasSubmitted) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已报名，跳转到任务页面\");\n            handleGoToTask();\n            return;\n        }\n        // 检查用户是否登录\n        if (!userState || !userState.isLoggedIn) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录，跳转到登录页面\");\n            // 未登录，显示登录对话框\n            const redirectUrl = pathname || \"/home\";\n            router.push(\"/login?redirect=\".concat(encodeURIComponent(redirectUrl)));\n            return;\n        }\n        // 检查用户身份：只有学生（roleId为1）才能报名\n        const isStudent = checkIsStudent();\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 检查用户身份\", {\n            isStudent,\n            roleId: userState === null || userState === void 0 ? void 0 : userState.roleId\n        });\n        if (!isStudent) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户不是学生身份，显示身份验证弹窗\");\n            showStudentOnlyModal();\n            return;\n        }\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 身份验证通过，开始报名流程\");\n        // 身份验证通过，直接进行报名\n        handleDirectRegistration();\n    };\n    // 处理登录成功\n    const handleLoginSuccess = ()=>{\n        notification.success(\"登录成功\");\n    // 登录成功后，页面的useEffect会自动检查报名状态\n    // 不需要在这里重复检查，让useEffect处理状态更新\n    };\n    // 处理报名成功\n    const handleSubmitSuccess = ()=>{\n        // 更新提交状态\n        setHasSubmitted(true);\n        setRegistrationStatus(1); // 设置为已报名状态\n        notification.success(\"报名成功\");\n        // 通知页面刷新参赛作品列表\n        if (onRefreshWorks) {\n            onRefreshWorks();\n        }\n    };\n    // 直接报名处理函数\n    const handleDirectRegistration = async ()=>{\n        try {\n            var _response_data;\n            // 检查用户是否绑定了手机号\n            if (!userState.phone || userState.phone.trim() === \"\") {\n                // 显示确认对话框\n                _barrel_optimize_names_Modal_Upload_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"].confirm({\n                    title: \"需要绑定手机号\",\n                    content: \"报名活动需要绑定手机号，是否前往绑定？\",\n                    okText: \"前往绑定\",\n                    cancelText: \"取消\",\n                    onOk: ()=>{\n                        // 跳转到登录页面，并在URL中添加参数表示需要绑定手机号\n                        const currentUrl = window.location.href;\n                        router.push(\"/login?needBindPhone=true&redirect=\".concat(encodeURIComponent(currentUrl)));\n                    },\n                    onCancel: ()=>{\n                        notification.info(\"未绑定手机号，无法报名活动\");\n                    }\n                });\n                return;\n            }\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已绑定手机号，继续报名流程\");\n            // 显示加载提示\n            const loadingMessage = notification.loading(\"正在提交报名...\");\n            // 提交报名数据（使用默认值，跳过协议确认步骤）\n            const submitData = {\n                activityId: Number(activityId),\n                agreementAccepted: true,\n                parentConsentAccepted: true,\n                parentSignaturePath: \"\",\n                signatureTime: new Date().toISOString(),\n                remark: \"快速报名时间：\".concat(new Date().toLocaleString(\"zh-CN\"))\n            };\n            const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.submitRegistration(submitData);\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                // 更新UI状态\n                setHasSubmitted(true);\n                setRegistrationStatus(1);\n                notification.success(\"报名成功！正在跳转到班级空间...\");\n                // 通知页面刷新参赛作品列表\n                if (onRefreshWorks) {\n                    onRefreshWorks();\n                }\n                // 报名成功后，获取对应的赛事任务并跳转到班级空间\n                try {\n                    // 等待一小段时间确保后端创建赛事任务完成\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    // 获取当前用户的赛事任务列表\n                    const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n                    if (eventsTaskResponse.data.code === 200) {\n                        const eventsTasks = eventsTaskResponse.data.data;\n                        // 查找与当前活动相关的赛事任务\n                        const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                        if (relatedTask) {\n                            // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                            window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                        } else {\n                            // 如果没有找到对应任务，在新标签页中打开班级空间\n                            window.open(\"/class-space\", \"_blank\");\n                        }\n                    } else {\n                        // 如果获取任务失败，在新标签页中打开班级空间\n                        window.open(\"/class-space\", \"_blank\");\n                    }\n                } catch (taskError) {\n                    console.error(\"获取赛事任务失败:\", taskError);\n                    // 即使获取任务失败，也要在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                }\n            } else {\n                var _response_data1;\n                throw new Error(((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message) || \"报名失败\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"报名失败:\", error);\n            notification.error(error instanceof Error ? error.message : \"报名失败，请稍后重试\");\n        }\n    };\n    // 处理自定义上传请求\n    const handleCustomRequest = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        setUploadLoading(true);\n        try {\n            // 上传到OSS\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_8__.uploadApi.uploadToOss(file);\n            console.log(\"图片上传成功，URL:\", url);\n            // 更新文件列表\n            setFileList([\n                {\n                    ...file,\n                    uid: file.uid,\n                    name: file.name,\n                    status: \"done\",\n                    url: url\n                }\n            ]);\n            // 更新封面图片URL\n            setBannerImage(url);\n            if (onSuccess) {\n                onSuccess(null, file);\n            }\n            notification.success(\"封面图片上传成功\");\n        } catch (err) {\n            console.error(\"上传失败:\", err);\n            notification.error(\"上传失败: \".concat(err.message || \"请稍后重试\"));\n            setFileList([\n                {\n                    ...file,\n                    uid: file.uid,\n                    name: file.name,\n                    status: \"error\",\n                    error: err\n                }\n            ]);\n            if (onError) {\n                onError(err);\n            }\n        } finally{\n            setUploadLoading(false);\n        }\n    };\n    // 处理删除文件\n    const handleRemove = async (file)=>{\n        if (file.url && file.url !== initialBannerImage) {\n            try {\n                await _lib_api_upload__WEBPACK_IMPORTED_MODULE_8__.uploadApi.deleteFromOss(file.url);\n                notification.success(\"已从服务器删除图片\");\n            } catch (error) {\n                console.error(\"从OSS删除文件失败:\", error);\n                notification.error(\"服务器删除图片失败\");\n            }\n        }\n        setFileList([]);\n        setBannerImage(\"\");\n        return true;\n    };\n    // 处理分享按钮点击事件\n    const handleShare = ()=>{\n        if (true) {\n            // 获取当前页面URL\n            const currentUrl = window.location.href;\n            // 复制到剪贴板\n            navigator.clipboard.writeText(currentUrl).then(()=>{\n                notification.success(\"活动链接已复制，快去分享吧！\");\n            }).catch((err)=>{\n                console.error(\"复制失败:\", err);\n                notification.error(\"复制链接失败，请手动复制\");\n            });\n        }\n    };\n    // 如果还没有在客户端挂载，返回一个简单的占位符\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-64 bg-gray-200 animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 786,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-300 rounded mb-2 w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 789,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded mb-1 w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 790,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 791,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 788,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 787,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 785,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n            lineNumber: 784,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n                children: [\n                    isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 left-2 z-50 flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowUI(!showUI),\n                            className: \"p-2 bg-black/20 hover:bg-black/40 text-white rounded-full transition-colors\",\n                            title: showUI ? \"隐藏界面元素\" : \"显示界面元素\",\n                            children: showUI ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"w-4 h-4\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 812,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                        x1: \"1\",\n                                        y1: \"1\",\n                                        x2: \"23\",\n                                        y2: \"23\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 813,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 811,\n                                columnNumber: 19\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"w-4 h-4\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 817,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                        cx: \"12\",\n                                        cy: \"12\",\n                                        r: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 818,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 805,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 804,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 z-50 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSaveEdit,\n                                    className: \"p-2 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors\",\n                                    title: \"保存编辑\",\n                                    disabled: isSaving,\n                                    children: isSaving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 837,\n                                        columnNumber: 31\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 837,\n                                        columnNumber: 77\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 831,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCancelEdit,\n                                    className: \"p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors\",\n                                    title: \"取消编辑\",\n                                    disabled: isSaving,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 839,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 830,\n                            columnNumber: 15\n                        }, undefined) : isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsEditing(true),\n                            className: \"p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors\",\n                            title: \"进入编辑模式\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 855,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 850,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 828,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-[300px] overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-transparent to-black/30 z-10 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 864,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-blue-500/10 animate-pulse z-0 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 867,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 z-10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-full h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: bannerImage,\n                                                        alt: title,\n                                                        fill: true,\n                                                        priority: true,\n                                                        style: {\n                                                            objectFit: \"cover\"\n                                                        },\n                                                        className: \"z-10\",\n                                                        onError: (e)=>{\n                                                            const target = e.target;\n                                                            target.style.background = \"linear-gradient(120deg, #f3f4f6, #e5e7eb)\";\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                        lineNumber: 872,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                    lineNumber: 871,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 flex items-center justify-center bg-black/30 z-20 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-4 w-full max-w-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                className: \"w-full p-2 mb-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm bg-white/90\",\n                                                                value: bannerImage,\n                                                                onChange: (e)=>setBannerImage(e.target.value),\n                                                                placeholder: \"输入横幅图片URL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                                lineNumber: 889,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Modal_Upload_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                listType: \"picture-card\",\n                                                                fileList: fileList,\n                                                                customRequest: handleCustomRequest,\n                                                                onRemove: handleRemove,\n                                                                maxCount: 1,\n                                                                showUploadList: {\n                                                                    showPreviewIcon: true,\n                                                                    showRemoveIcon: true\n                                                                },\n                                                                onPreview: (file)=>{\n                                                                    if (file.url) {\n                                                                        window.open(file.url, \"_blank\");\n                                                                    }\n                                                                },\n                                                                children: fileList.length >= 1 ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                                            lineNumber: 911,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                marginTop: 8\n                                                                            },\n                                                                            children: \"上传\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                                            lineNumber: 912,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                                    lineNumber: 910,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                                lineNumber: 896,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                    lineNumber: 887,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 870,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 869,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: bannerImage,\n                                            alt: title,\n                                            fill: true,\n                                            priority: true,\n                                            style: {\n                                                objectFit: \"cover\"\n                                            },\n                                            className: \"z-10 hover:scale-105 transition-transform duration-700\",\n                                            onError: (e)=>{\n                                                const target = e.target;\n                                                target.style.background = \"linear-gradient(120deg, #f3f4f6, #e5e7eb)\";\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 921,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 866,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-4 left-4 right-4 z-20 flex justify-between items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 943,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 862,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                className: \"w-full md:w-3/4 p-2 text-2xl font-bold border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                value: title,\n                                                onChange: (e)=>setTitle(e.target.value),\n                                                placeholder: \"输入活动标题\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1015,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                className: \"w-full md:w-3/4 p-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                value: organizer,\n                                                onChange: (e)=>setOrganizer(e.target.value),\n                                                placeholder: \"输入主办方\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1023,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 1014,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-800 flex items-center\",\n                                                children: [\n                                                    title,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"ml-2 w-5 h-5 text-yellow-400 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            organizer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm\",\n                                                children: [\n                                                    \"主办方: \",\n                                                    organizer\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1038,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                className: \"p-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                value: startTime,\n                                                onChange: (e)=>setStartTime(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1045,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"self-center\",\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1051,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                className: \"p-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                value: endTime,\n                                                onChange: (e)=>setEndTime(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1052,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 1044,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm mt-2 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-500 to-indigo-500 h-1.5 w-1.5 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1061,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"活动时间：\",\n                                            formatDate(startTime),\n                                            \" - \",\n                                            formatDate(endTime)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 1060,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 1012,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3 mt-4 md:mt-0\",\n                                children: [\n                                    !isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSubmitClick,\n                                        disabled: isChecking,\n                                        className: \"flex items-center px-5 py-2.5 text-sm font-medium rounded-full \".concat(hasSubmitted ? \"bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\" : \"bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\", \" transition-all duration-200\"),\n                                        children: isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"loading-spinner mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                    lineNumber: 1096,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                \"检查状态中\"\n                                            ]\n                                        }, void 0, true) : hasSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"已报名 前往任务\"\n                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"立即报名\"\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 1086,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex items-center p-2.5 text-gray-500 hover:text-blue-500 hover:bg-gray-50 rounded-full hover:scale-110 active:scale-95 transition-all duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 1111,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 1107,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 1067,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 1011,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 801,\n                columnNumber: 7\n            }, undefined),\n            (promotionImage || backgroundImage || galleryImages || attachmentFiles) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    promotionImage: promotionImage,\n                    backgroundImage: backgroundImage,\n                    galleryImages: galleryImages,\n                    attachmentFiles: attachmentFiles,\n                    activityTitle: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                    lineNumber: 1120,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 1119,\n                columnNumber: 9\n            }, undefined),\n            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_login_dialog__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showLoginDialog,\n                onClose: ()=>setShowLoginDialog(false),\n                onSuccess: handleLoginSuccess\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 1132,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ActivityHeader, \"9puqDtaYsDo7+fZ2UWLRshIk+yw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.usePathname,\n        react_redux__WEBPACK_IMPORTED_MODULE_12__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_12__.useDispatch\n    ];\n});\n_c = ActivityHeader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ActivityHeader);\nvar _c;\n$RefreshReg$(_c, \"ActivityHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx\n"));

/***/ })

});