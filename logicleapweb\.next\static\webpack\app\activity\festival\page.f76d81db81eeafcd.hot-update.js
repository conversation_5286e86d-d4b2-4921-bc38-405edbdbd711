"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/activity/festival/page",{

/***/ "(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx":
/*!*************************************************************!*\
  !*** ./app/activity/festival/components/ActivityHeader.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader,Save,Share2,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./lib/store.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ActivityMediaGallery */ \"(app-pages-browser)/./app/activity/festival/components/ActivityMediaGallery.tsx\");\n/* harmony import */ var _barrel_optimize_names_Modal_Upload_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Modal,Upload!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Modal_Upload_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Modal,Upload!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=PlusOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _components_login_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/login-dialog */ \"(app-pages-browser)/./components/login-dialog.tsx\");\n/* harmony import */ var _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/activity */ \"(app-pages-browser)/./lib/api/activity/index.ts\");\n/* harmony import */ var _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/activity/events-task */ \"(app-pages-browser)/./lib/api/activity/events-task.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../../lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var _lib_api_user__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/user */ \"(app-pages-browser)/./lib/api/user.ts\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 临时实现formatDate函数，以解决模块导入问题\nconst formatDate = (dateString)=>{\n    // 在服务端渲染时，直接返回原始字符串，避免水合错误\n    if (false) {}\n    const date = new Date(dateString);\n    // 检查日期是否有效\n    if (isNaN(date.getTime())) {\n        return dateString; // 如果无效，返回原始字符串\n    }\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, \"0\");\n    const day = String(date.getDate()).padStart(2, \"0\");\n    return \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n};\nconst ActivityHeader = (param)=>{\n    let { title: initialTitle, startTime: initialStartTime, endTime: initialEndTime, bannerImage: initialBannerImage, expanded, setExpanded, activityId, tags = [], organizer: initialOrganizer = \"\", activityType = _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.ActivityType.WORK, onRefreshWorks, // 媒体字段\n    promotionImage, backgroundImage, galleryImages, attachmentFiles } = param;\n    _s();\n    console.log(\"\\uD83D\\uDD0D [DEBUG] ActivityHeader 组件渲染\", {\n        activityId,\n        initialTitle\n    });\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_10__.GetNotification)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.usePathname)();\n    // 可编辑的标题和时间\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTitle);\n    const [startTime, setStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialStartTime);\n    const [endTime, setEndTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialEndTime);\n    const [bannerImage, setBannerImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialBannerImage);\n    const [organizer, setOrganizer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialOrganizer);\n    // 获取当前用户信息和dispatch\n    const userState = (0,react_redux__WEBPACK_IMPORTED_MODULE_12__.useSelector)(_lib_store__WEBPACK_IMPORTED_MODULE_3__.selectUserState);\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_12__.useDispatch)();\n    const isAdmin = (userState === null || userState === void 0 ? void 0 : userState.roleId) === 4;\n    // 同步用户状态的函数 - 增强版，从API获取最新用户信息\n    const syncUserState = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 开始同步用户状态\");\n            const localUser = localStorage.getItem(\"user\");\n            if (localUser) {\n                const userData = JSON.parse(localUser);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中的用户数据\", userData);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] Redux 中的用户数据\", userState);\n                // 如果用户已登录但phone为空，尝试从API获取最新用户信息\n                if (userData.isLoggedIn && userData.userId && (!userData.phone || userData.phone.trim() === \"\")) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户phone为空，从API获取最新用户信息\");\n                    try {\n                        const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_9__.userApi.getUserInfo(userData.userId);\n                        if (response.code === 200 && response.data) {\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取到最新用户信息\", response.data);\n                            // 构建更新后的用户数据\n                            const updatedUserData = {\n                                ...userData,\n                                phone: response.data.phone || \"\",\n                                nickName: response.data.nickName || userData.nickName,\n                                avatarUrl: response.data.avatarUrl || userData.avatarUrl,\n                                gender: response.data.gender || userData.gender,\n                                roleId: response.data.roleId || userData.roleId\n                            };\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 更新用户数据\", {\n                                oldPhone: userData.phone,\n                                newPhone: updatedUserData.phone,\n                                updated: updatedUserData\n                            });\n                            // 更新localStorage和Redux\n                            localStorage.setItem(\"user\", JSON.stringify(updatedUserData));\n                            dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(updatedUserData));\n                            return;\n                        }\n                    } catch (error) {\n                        console.error(\"\\uD83D\\uDD0D [DEBUG] 从API获取用户信息失败:\", error);\n                    }\n                }\n                // 检查关键字段是否有变化\n                const hasPhoneChanged = userData.phone !== (userState === null || userState === void 0 ? void 0 : userState.phone);\n                const hasRoleChanged = userData.roleId !== (userState === null || userState === void 0 ? void 0 : userState.roleId);\n                const hasLoginStatusChanged = userData.isLoggedIn !== (userState === null || userState === void 0 ? void 0 : userState.isLoggedIn);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态变化检查\", {\n                    phoneChanged: hasPhoneChanged,\n                    roleChanged: hasRoleChanged,\n                    loginStatusChanged: hasLoginStatusChanged,\n                    oldPhone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                    newPhone: userData.phone,\n                    oldRole: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                    newRole: userData.roleId\n                });\n                // 如果localStorage中的用户状态与Redux中的不一致，说明需要更新\n                if (hasPhoneChanged || hasRoleChanged || hasLoginStatusChanged) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户状态变化，同步Redux状态\");\n                    dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(userData));\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态无变化，无需同步\");\n                }\n            } else {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中无用户数据\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD0D [DEBUG] 检查用户状态失败:\", error);\n        }\n    };\n    // 监听页面焦点变化和路由变化，重新检查用户状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 页面加载时立即检查一次\n        syncUserState().catch(console.error);\n        const handleVisibilityChange = ()=>{\n            if (document.visibilityState === \"visible\") {\n                syncUserState().catch(console.error);\n            }\n        };\n        const handleFocus = ()=>{\n            syncUserState().catch(console.error);\n        };\n        // 添加多种事件监听确保状态同步\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n        window.addEventListener(\"focus\", handleFocus);\n        return ()=>{\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n            window.removeEventListener(\"focus\", handleFocus);\n        };\n    }, [\n        userState === null || userState === void 0 ? void 0 : userState.phone,\n        dispatch\n    ]);\n    // 工具函数：检查用户是否为学生身份\n    const checkIsStudent = ()=>{\n        try {\n            const userStr = localStorage.getItem(\"user\");\n            const localUser = userStr ? JSON.parse(userStr) : null;\n            const userRoleId = (localUser === null || localUser === void 0 ? void 0 : localUser.roleId) || (userState === null || userState === void 0 ? void 0 : userState.roleId);\n            return userRoleId === 1; // 学生身份的roleId为1\n        } catch (error) {\n            console.error(\"解析localStorage中的用户信息失败:\", error);\n            return false;\n        }\n    };\n    // 工具函数：显示身份验证失败提示\n    const showStudentOnlyModal = ()=>{\n        _barrel_optimize_names_Modal_Upload_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"].confirm({\n            title: \"身份验证\",\n            content: \"抱歉，只有学生身份的用户才能参加此活动报名。如果您是学生，请联系管理员更新您的身份信息。\",\n            okText: \"我知道了\",\n            cancelText: \"联系管理员\",\n            onCancel: ()=>{\n                notification.info(\"如需帮助，请联系客服：400-123-4567\");\n            }\n        });\n    };\n    // 获取报名状态文本\n    const getRegistrationStatusText = ()=>{\n        switch(registrationStatus){\n            case 0:\n                return \"已取消\";\n            case 1:\n                return \"已报名\";\n            case 2:\n                return \"已审核通过\";\n            case 3:\n                return \"已拒绝\";\n            case 4:\n                return \"评审中\";\n            case 5:\n                return \"已获奖\";\n            case 6:\n                return \"审核中\";\n            default:\n                return \"已报名\";\n        }\n    };\n    // 可编辑的导航标签文本\n    const [navTags, setNavTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"tag1\",\n            text: \"官方活动\"\n        },\n        {\n            id: \"tag2\",\n            text: \"AIGC作品征集\"\n        }\n    ]);\n    // 活动标签ID列表\n    const [tagIds, setTagIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 标签编辑状态\n    const [editingTagId, setEditingTagId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingTagText, setEditingTagText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 登录和报名相关状态\n    const [showLoginDialog, setShowLoginDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSubmitted, setHasSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [registrationStatus, setRegistrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 报名状态：0-已取消 1-已报名 2-已审核通过 3-已拒绝 4-评审中 5-已获奖 6-审核中\n    const [uploadLoading, setUploadLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fileList, setFileList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUI, setShowUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 添加客户端检测来解决水合错误\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    // 页面加载时检查报名状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkRegistrationStatus = async ()=>{\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态 - useEffect 触发\", {\n                isLoggedIn: userState.isLoggedIn,\n                roleId: userState.roleId,\n                phone: userState.phone,\n                isClient,\n                activityId\n            });\n            // 只有在用户已登录且是学生身份时才检查报名状态\n            const isStudent = checkIsStudent();\n            if (!userState.isLoggedIn || !isStudent) {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录或不是学生身份，重置报名状态\", {\n                    isLoggedIn: userState.isLoggedIn,\n                    isStudent\n                });\n                // 如果用户未登录或不是学生，重置状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n                return;\n            }\n            try {\n                var _response_data, _response_data1;\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 开始检查报名状态 API 调用\");\n                setIsChecking(true);\n                const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.checkRegistration(Number(activityId));\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 页面加载检查报名状态响应:\", response);\n                if ((response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200 && ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data)) {\n                    const { submitted, submit } = response.data.data;\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态检查结果\", {\n                        submitted,\n                        submit\n                    });\n                    setHasSubmitted(submitted);\n                    if (submit && submit.status !== undefined) {\n                        setRegistrationStatus(submit.status);\n                    } else {\n                        setRegistrationStatus(null);\n                    }\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态响应不符合预期，设置为未报名状态\");\n                    // 如果响应不符合预期，设置为未报名状态\n                    setHasSubmitted(false);\n                    setRegistrationStatus(null);\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态失败:\", error);\n                // 出错时设置为未报名状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n            } finally{\n                setIsChecking(false);\n            }\n        };\n        // 只在客户端执行，避免服务端渲染问题\n        if (isClient) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 客户端环境，执行报名状态检查\");\n            checkRegistrationStatus();\n        } else {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 非客户端环境，跳过报名状态检查\");\n        }\n    }, [\n        activityId,\n        userState.isLoggedIn,\n        userState.roleId,\n        userState.phone,\n        isClient\n    ]);\n    // 检查用户是否已提交作品\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkUserSubmitted = async ()=>{\n            try {\n                // 仅针对已登录用户进行检查\n                if (userState && userState.isLoggedIn) {\n                    var _response_data;\n                    setIsChecking(true);\n                    const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityApi.checkUserSubmitted(Number(activityId));\n                    console.log(\"页面加载时检查用户提交状态响应:\", response);\n                    if ((response === null || response === void 0 ? void 0 : response.status) === 200 && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data)) {\n                        setHasSubmitted(response.data.data.submitted);\n                    } else {\n                        // 如果响应不符合预期，默认为未提交\n                        setHasSubmitted(false);\n                    }\n                    setIsChecking(false);\n                } else {\n                    // 未登录用户，直接设置为未提交\n                    setHasSubmitted(false);\n                    setIsChecking(false);\n                }\n            } catch (error) {\n                console.error(\"检查用户提交状态失败:\", error);\n                // 出错时默认设置为未提交\n                setHasSubmitted(false);\n                setIsChecking(false);\n            }\n        };\n        // 页面加载时执行检查\n        if (isClient) {\n            checkUserSubmitted();\n        }\n    }, [\n        activityId,\n        userState,\n        isClient\n    ]);\n    // 加载活动标签\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchActivityTags = async ()=>{\n            if (activityId) {\n                try {\n                    const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityTagApi.getActivityTags(Number(activityId));\n                    if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n                        setTagIds(response.data.data || []);\n                    }\n                } catch (error) {\n                    console.error(\"获取活动标签失败:\", error);\n                }\n            }\n        };\n        fetchActivityTags();\n    }, [\n        activityId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 初始化文件列表\n        if (bannerImage) {\n            setFileList([\n                {\n                    uid: \"-1\",\n                    name: \"当前封面\",\n                    status: \"done\",\n                    url: bannerImage\n                }\n            ]);\n        } else {\n            setFileList([]);\n        }\n    }, [\n        bannerImage\n    ]);\n    // 处理取消编辑\n    const handleCancelEdit = ()=>{\n        setTitle(initialTitle);\n        setStartTime(initialStartTime);\n        setEndTime(initialEndTime);\n        setBannerImage(initialBannerImage);\n        setOrganizer(initialOrganizer);\n        setIsEditing(false);\n    };\n    // 处理保存编辑\n    const handleSaveEdit = async ()=>{\n        if (!activityId) {\n            notification.error(\"活动ID不存在，无法保存\");\n            return;\n        }\n        try {\n            setIsSaving(true);\n            // 准备更新数据\n            const updateData = {\n                id: Number(activityId),\n                name: title,\n                startTime: new Date(startTime),\n                endTime: new Date(endTime),\n                coverImage: bannerImage,\n                organizer: organizer,\n                // 这些字段为必填，但我们不修改它们\n                detailContent: \"\",\n                rulesContent: \"\",\n                awardsContent: \"\"\n            };\n            console.log(\"保存活动数据:\", updateData);\n            // 更新活动基本信息\n            const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityApi.update(updateData);\n            console.log(\"保存活动响应:\", response);\n            if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n                // 如果有标签，则更新标签\n                const tagResponse = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityTagApi.update(Number(activityId), tagIds);\n                console.log(\"标签更新响应:\", tagResponse);\n                notification.success(\"活动信息已成功保存\");\n                setIsEditing(false);\n            } else {\n                var _response_data;\n                notification.error((response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.message) || \"保存失败\");\n            }\n        } catch (error) {\n            console.error(\"保存活动信息失败:\", error);\n            notification.error(\"保存活动信息失败，请稍后重试\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    // 处理标签点击编辑\n    const handleTagClick = (tag)=>{\n        if (isEditing) {\n            setTagIds(tagIds.includes(Number(tag.id)) ? tagIds.filter((id)=>id !== Number(tag.id)) : [\n                ...tagIds,\n                Number(tag.id)\n            ]);\n        }\n    };\n    // 处理标签编辑取消\n    const handleTagEditCancel = ()=>{\n        setEditingTagId(null);\n    };\n    // 处理添加新标签\n    const handleAddTag = ()=>{\n        if (navTags.length < 5) {\n            const newTag = {\n                id: \"tag-\".concat(Date.now()),\n                text: \"新标签\"\n            };\n            setNavTags([\n                ...navTags,\n                newTag\n            ]);\n            setTagIds([\n                ...tagIds,\n                Number(newTag.id)\n            ]);\n        }\n    };\n    // 处理删除标签\n    const handleDeleteTag = (id)=>{\n        if (navTags.length > 1) {\n            setNavTags(navTags.filter((tag)=>tag.id !== id));\n            setTagIds(tagIds.filter((id)=>id !== Number(id)));\n        }\n    };\n    // 前往任务函数\n    const handleGoToTask = async ()=>{\n        try {\n            const loadingMessage = notification.loading(\"正在跳转到任务...\");\n            // 获取当前用户的赛事任务列表\n            const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n            if (eventsTaskResponse.data.code === 200) {\n                const eventsTasks = eventsTaskResponse.data.data;\n                // 查找与当前活动相关的赛事任务\n                const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                if (relatedTask) {\n                    // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                    window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                    notification.success(\"正在跳转到任务页面...\");\n                } else {\n                    // 如果没有找到对应任务，在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                    notification.info(\"未找到对应任务，已打开班级空间\");\n                }\n            } else {\n                // 如果获取任务失败，在新标签页中打开班级空间\n                window.open(\"/class-space\", \"_blank\");\n                notification.info(\"获取任务失败，已打开班级空间\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"跳转任务失败:\", error);\n            notification.error(\"跳转任务失败，请稍后重试\");\n        }\n    };\n    // 处理报名按钮点击\n    const handleSubmitClick = async ()=>{\n        var _userState_phone, _localUser_phone;\n        // 获取localStorage中的用户信息进行对比\n        const localUserStr = localStorage.getItem(\"user\");\n        const localUser = localUserStr ? JSON.parse(localUserStr) : null;\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 报名按钮点击 - 详细用户状态检查\", {\n            hasSubmitted,\n            \"Redux userState\": {\n                isLoggedIn: userState === null || userState === void 0 ? void 0 : userState.isLoggedIn,\n                phone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                phoneLength: userState === null || userState === void 0 ? void 0 : (_userState_phone = userState.phone) === null || _userState_phone === void 0 ? void 0 : _userState_phone.length,\n                phoneType: typeof (userState === null || userState === void 0 ? void 0 : userState.phone),\n                roleId: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                userId: userState === null || userState === void 0 ? void 0 : userState.userId,\n                nickName: userState === null || userState === void 0 ? void 0 : userState.nickName\n            },\n            \"localStorage user\": {\n                phone: localUser === null || localUser === void 0 ? void 0 : localUser.phone,\n                phoneLength: localUser === null || localUser === void 0 ? void 0 : (_localUser_phone = localUser.phone) === null || _localUser_phone === void 0 ? void 0 : _localUser_phone.length,\n                phoneType: typeof (localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                userId: localUser === null || localUser === void 0 ? void 0 : localUser.userId,\n                nickName: localUser === null || localUser === void 0 ? void 0 : localUser.nickName\n            },\n            \"phone validation\": {\n                \"userState.phone exists\": !!(userState === null || userState === void 0 ? void 0 : userState.phone),\n                \"userState.phone not empty\": (userState === null || userState === void 0 ? void 0 : userState.phone) && userState.phone.trim() !== \"\",\n                \"localUser.phone exists\": !!(localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                \"localUser.phone not empty\": (localUser === null || localUser === void 0 ? void 0 : localUser.phone) && (localUser === null || localUser === void 0 ? void 0 : localUser.phone.trim()) !== \"\"\n            }\n        });\n        // 临时调用API获取最新用户信息进行对比\n        if (userState === null || userState === void 0 ? void 0 : userState.userId) {\n            try {\n                var _response_data, _response_data_phone, _response_data1, _response_data2, _response_data3, _response_data4;\n                const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_9__.userApi.getUserInfo(userState.userId);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取的最新用户信息:\", {\n                    code: response.code,\n                    phone: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.phone,\n                    phoneLength: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_phone = _response_data1.phone) === null || _response_data_phone === void 0 ? void 0 : _response_data_phone.length,\n                    phoneType: typeof ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.phone),\n                    nickName: (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.nickName,\n                    userId: (_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : _response_data4.id\n                });\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 获取用户信息失败:\", error);\n            }\n        }\n        // 如果用户已报名，跳转到任务页面\n        if (hasSubmitted) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已报名，跳转到任务页面\");\n            handleGoToTask();\n            return;\n        }\n        // 检查用户是否登录\n        if (!userState || !userState.isLoggedIn) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录，跳转到登录页面\");\n            // 未登录，显示登录对话框\n            const redirectUrl = pathname || \"/home\";\n            router.push(\"/login?redirect=\".concat(encodeURIComponent(redirectUrl)));\n            return;\n        }\n        // 检查用户身份：只有学生（roleId为1）才能报名\n        const isStudent = checkIsStudent();\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 检查用户身份\", {\n            isStudent,\n            roleId: userState === null || userState === void 0 ? void 0 : userState.roleId\n        });\n        if (!isStudent) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户不是学生身份，显示身份验证弹窗\");\n            showStudentOnlyModal();\n            return;\n        }\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 身份验证通过，开始报名流程\");\n        // 身份验证通过，直接进行报名\n        handleDirectRegistration();\n    };\n    // 处理登录成功\n    const handleLoginSuccess = ()=>{\n        notification.success(\"登录成功\");\n    // 登录成功后，页面的useEffect会自动检查报名状态\n    // 不需要在这里重复检查，让useEffect处理状态更新\n    };\n    // 处理报名成功\n    const handleSubmitSuccess = ()=>{\n        // 更新提交状态\n        setHasSubmitted(true);\n        setRegistrationStatus(1); // 设置为已报名状态\n        notification.success(\"报名成功\");\n        // 通知页面刷新参赛作品列表\n        if (onRefreshWorks) {\n            onRefreshWorks();\n        }\n    };\n    // 直接报名处理函数\n    const handleDirectRegistration = async ()=>{\n        try {\n            var _response_data;\n            // 检查用户是否绑定了手机号\n            if (!userState.phone || userState.phone.trim() === \"\") {\n                // 显示确认对话框\n                _barrel_optimize_names_Modal_Upload_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"].confirm({\n                    title: \"需要绑定手机号\",\n                    content: \"报名活动需要绑定手机号，是否前往绑定？\",\n                    okText: \"前往绑定\",\n                    cancelText: \"取消\",\n                    onOk: ()=>{\n                        // 跳转到登录页面，并在URL中添加参数表示需要绑定手机号\n                        const currentUrl = window.location.href;\n                        router.push(\"/login?needBindPhone=true&redirect=\".concat(encodeURIComponent(currentUrl)));\n                    },\n                    onCancel: ()=>{\n                        notification.info(\"未绑定手机号，无法报名活动\");\n                    }\n                });\n                return;\n            }\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已绑定手机号，继续报名流程\");\n            // 显示加载提示\n            const loadingMessage = notification.loading(\"正在提交报名...\");\n            // 提交报名数据（使用默认值，跳过协议确认步骤）\n            const submitData = {\n                activityId: Number(activityId),\n                agreementAccepted: true,\n                parentConsentAccepted: true,\n                parentSignaturePath: \"\",\n                signatureTime: new Date().toISOString(),\n                remark: \"快速报名时间：\".concat(new Date().toLocaleString(\"zh-CN\"))\n            };\n            const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.submitRegistration(submitData);\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                // 更新UI状态\n                setHasSubmitted(true);\n                setRegistrationStatus(1);\n                notification.success(\"报名成功！正在跳转到班级空间...\");\n                // 通知页面刷新参赛作品列表\n                if (onRefreshWorks) {\n                    onRefreshWorks();\n                }\n                // 报名成功后，获取对应的赛事任务并跳转到班级空间\n                try {\n                    // 等待一小段时间确保后端创建赛事任务完成\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    // 获取当前用户的赛事任务列表\n                    const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n                    if (eventsTaskResponse.data.code === 200) {\n                        const eventsTasks = eventsTaskResponse.data.data;\n                        // 查找与当前活动相关的赛事任务\n                        const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                        if (relatedTask) {\n                            // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                            window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                        } else {\n                            // 如果没有找到对应任务，在新标签页中打开班级空间\n                            window.open(\"/class-space\", \"_blank\");\n                        }\n                    } else {\n                        // 如果获取任务失败，在新标签页中打开班级空间\n                        window.open(\"/class-space\", \"_blank\");\n                    }\n                } catch (taskError) {\n                    console.error(\"获取赛事任务失败:\", taskError);\n                    // 即使获取任务失败，也要在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                }\n            } else {\n                var _response_data1;\n                throw new Error(((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message) || \"报名失败\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"报名失败:\", error);\n            notification.error(error instanceof Error ? error.message : \"报名失败，请稍后重试\");\n        }\n    };\n    // 处理自定义上传请求\n    const handleCustomRequest = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        setUploadLoading(true);\n        try {\n            // 上传到OSS\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_8__.uploadApi.uploadToOss(file);\n            console.log(\"图片上传成功，URL:\", url);\n            // 更新文件列表\n            setFileList([\n                {\n                    ...file,\n                    uid: file.uid,\n                    name: file.name,\n                    status: \"done\",\n                    url: url\n                }\n            ]);\n            // 更新封面图片URL\n            setBannerImage(url);\n            if (onSuccess) {\n                onSuccess(null, file);\n            }\n            notification.success(\"封面图片上传成功\");\n        } catch (err) {\n            console.error(\"上传失败:\", err);\n            notification.error(\"上传失败: \".concat(err.message || \"请稍后重试\"));\n            setFileList([\n                {\n                    ...file,\n                    uid: file.uid,\n                    name: file.name,\n                    status: \"error\",\n                    error: err\n                }\n            ]);\n            if (onError) {\n                onError(err);\n            }\n        } finally{\n            setUploadLoading(false);\n        }\n    };\n    // 处理删除文件\n    const handleRemove = async (file)=>{\n        if (file.url && file.url !== initialBannerImage) {\n            try {\n                await _lib_api_upload__WEBPACK_IMPORTED_MODULE_8__.uploadApi.deleteFromOss(file.url);\n                notification.success(\"已从服务器删除图片\");\n            } catch (error) {\n                console.error(\"从OSS删除文件失败:\", error);\n                notification.error(\"服务器删除图片失败\");\n            }\n        }\n        setFileList([]);\n        setBannerImage(\"\");\n        return true;\n    };\n    // 处理分享按钮点击事件\n    const handleShare = ()=>{\n        if (true) {\n            // 获取当前页面URL\n            const currentUrl = window.location.href;\n            // 复制到剪贴板\n            navigator.clipboard.writeText(currentUrl).then(()=>{\n                notification.success(\"活动链接已复制，快去分享吧！\");\n            }).catch((err)=>{\n                console.error(\"复制失败:\", err);\n                notification.error(\"复制链接失败，请手动复制\");\n            });\n        }\n    };\n    // 如果还没有在客户端挂载，返回一个简单的占位符\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-64 bg-gray-200 animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 849,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-300 rounded mb-2 w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 852,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded mb-1 w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 853,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 854,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 851,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 850,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 848,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n            lineNumber: 847,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n                children: [\n                    isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 left-2 z-50 flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowUI(!showUI),\n                            className: \"p-2 bg-black/20 hover:bg-black/40 text-white rounded-full transition-colors\",\n                            title: showUI ? \"隐藏界面元素\" : \"显示界面元素\",\n                            children: showUI ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"w-4 h-4\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 875,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                        x1: \"1\",\n                                        y1: \"1\",\n                                        x2: \"23\",\n                                        y2: \"23\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 874,\n                                columnNumber: 19\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"w-4 h-4\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 880,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                        cx: \"12\",\n                                        cy: \"12\",\n                                        r: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 881,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 879,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 868,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 867,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 z-50 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSaveEdit,\n                                    className: \"p-2 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors\",\n                                    title: \"保存编辑\",\n                                    disabled: isSaving,\n                                    children: isSaving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 900,\n                                        columnNumber: 31\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 900,\n                                        columnNumber: 77\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 894,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCancelEdit,\n                                    className: \"p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors\",\n                                    title: \"取消编辑\",\n                                    disabled: isSaving,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 908,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 902,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 893,\n                            columnNumber: 15\n                        }, undefined) : isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsEditing(true),\n                            className: \"p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors\",\n                            title: \"进入编辑模式\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 913,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 891,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-[300px] overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-transparent to-black/30 z-10 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 927,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-blue-500/10 animate-pulse z-0 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 z-10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-full h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: bannerImage,\n                                                        alt: title,\n                                                        fill: true,\n                                                        priority: true,\n                                                        style: {\n                                                            objectFit: \"cover\"\n                                                        },\n                                                        className: \"z-10\",\n                                                        onError: (e)=>{\n                                                            const target = e.target;\n                                                            target.style.background = \"linear-gradient(120deg, #f3f4f6, #e5e7eb)\";\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                        lineNumber: 935,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                    lineNumber: 934,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 flex items-center justify-center bg-black/30 z-20 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-4 w-full max-w-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                className: \"w-full p-2 mb-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm bg-white/90\",\n                                                                value: bannerImage,\n                                                                onChange: (e)=>setBannerImage(e.target.value),\n                                                                placeholder: \"输入横幅图片URL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                                lineNumber: 952,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Modal_Upload_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                listType: \"picture-card\",\n                                                                fileList: fileList,\n                                                                customRequest: handleCustomRequest,\n                                                                onRemove: handleRemove,\n                                                                maxCount: 1,\n                                                                showUploadList: {\n                                                                    showPreviewIcon: true,\n                                                                    showRemoveIcon: true\n                                                                },\n                                                                onPreview: (file)=>{\n                                                                    if (file.url) {\n                                                                        window.open(file.url, \"_blank\");\n                                                                    }\n                                                                },\n                                                                children: fileList.length >= 1 ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                                            lineNumber: 974,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                marginTop: 8\n                                                                            },\n                                                                            children: \"上传\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                                            lineNumber: 975,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                                    lineNumber: 973,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                                lineNumber: 959,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                        lineNumber: 951,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                    lineNumber: 950,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 933,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 932,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-[-200px]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: bannerImage,\n                                            alt: title,\n                                            fill: true,\n                                            priority: true,\n                                            style: {\n                                                objectFit: \"cover\"\n                                            },\n                                            className: \"z-10 hover:scale-105 transition-transform duration-700\",\n                                            onError: (e)=>{\n                                                const target = e.target;\n                                                target.style.background = \"linear-gradient(120deg, #f3f4f6, #e5e7eb)\";\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 987,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 929,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-4 left-4 right-4 z-20 flex justify-between items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 1006,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 925,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                className: \"w-full md:w-3/4 p-2 text-2xl font-bold border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                value: title,\n                                                onChange: (e)=>setTitle(e.target.value),\n                                                placeholder: \"输入活动标题\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1078,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                className: \"w-full md:w-3/4 p-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                value: organizer,\n                                                onChange: (e)=>setOrganizer(e.target.value),\n                                                placeholder: \"输入主办方\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1086,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 1077,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-800 flex items-center\",\n                                                children: [\n                                                    title,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"ml-2 w-5 h-5 text-yellow-400 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                        lineNumber: 1098,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1096,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            organizer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm\",\n                                                children: [\n                                                    \"主办方: \",\n                                                    organizer\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1101,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                className: \"p-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                value: startTime,\n                                                onChange: (e)=>setStartTime(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1108,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"self-center\",\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1114,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                className: \"p-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                value: endTime,\n                                                onChange: (e)=>setEndTime(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1115,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 1107,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm mt-2 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-500 to-indigo-500 h-1.5 w-1.5 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 1124,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"活动时间：\",\n                                            formatDate(startTime),\n                                            \" - \",\n                                            formatDate(endTime)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 1123,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 1075,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3 mt-4 md:mt-0\",\n                                children: [\n                                    !isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSubmitClick,\n                                        disabled: isChecking,\n                                        className: \"flex items-center px-5 py-2.5 text-sm font-medium rounded-full \".concat(hasSubmitted ? \"bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\" : \"bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\", \" transition-all duration-200\"),\n                                        children: isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"loading-spinner mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                    lineNumber: 1159,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                \"检查状态中\"\n                                            ]\n                                        }, void 0, true) : hasSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"已报名 前往任务\"\n                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"立即报名\"\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 1149,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex items-center p-2.5 text-gray-500 hover:text-blue-500 hover:bg-gray-50 rounded-full hover:scale-110 active:scale-95 transition-all duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader_Save_Share2_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 1174,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 1170,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 1130,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 1074,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 864,\n                columnNumber: 7\n            }, undefined),\n            (promotionImage || backgroundImage || galleryImages || attachmentFiles) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    promotionImage: promotionImage,\n                    backgroundImage: backgroundImage,\n                    galleryImages: galleryImages,\n                    attachmentFiles: attachmentFiles,\n                    activityTitle: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                    lineNumber: 1183,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 1182,\n                columnNumber: 9\n            }, undefined),\n            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_login_dialog__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showLoginDialog,\n                onClose: ()=>setShowLoginDialog(false),\n                onSuccess: handleLoginSuccess\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 1195,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ActivityHeader, \"Quh7kbZm+/e8RORk6NuDYkdM4Hw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.usePathname,\n        react_redux__WEBPACK_IMPORTED_MODULE_12__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_12__.useDispatch\n    ];\n});\n_c = ActivityHeader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ActivityHeader);\nvar _c;\n$RefreshReg$(_c, \"ActivityHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx\n"));

/***/ })

});