'use client'

import { useState, useEffect } from 'react';
import { Card, Empty, Spin, Button, Modal, Avatar, Carousel } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { Eye } from 'lucide-react';
import Image from 'next/image';
import { worksApi } from '@/lib/api/works';
import { viewWork } from '../../../lib/utils/view-work-modal';
import { GetNotification } from 'logic-common/dist/components/Notification';

// 添加自定义箭头组件
export const CustomArrow = ({ type, onClick }: { type: 'prev' | 'next', onClick?: () => void }) => (
  <div
    onClick={onClick}
    className={`absolute top-1/2 -translate-y-1/2 cursor-pointer carousel-arrow
      ${type === 'prev' ? 'left-2' : 'right-2'}`}
  >
    {type === 'prev' ? <LeftOutlined /> : <RightOutlined />}
  </div>
);

// 添加图片标签组件
export const ImageLabel = ({ text }: { text: string }) => (
  <div className="absolute bottom-2 left-2 px-2 py-1 bg-black/50 text-white text-sm rounded">
    {text}
  </div>
);

interface ClassProjectsProps {
  classId: number;
}

export default function ClassProjects({ classId }: ClassProjectsProps) {
  const [projects, setProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const notification = GetNotification();

  // 获取班级项目列表
  const fetchClassProjects = async () => {
    try {
      setLoading(true);
      const response = await worksApi.searchProjects(classId);

      if (response?.data?.code === 200) {
        setProjects(response.data.data?.list || []);
      }
    } catch (error) {
      console.error('获取班级项目失败:', error);
      notification.error('获取班级项目失败');
    } finally {
      setLoading(false);
    }
  };

  // 查看作品
  const handleViewWork = async (workId: number) => {
    try {
      const loadingNotification = notification.loading('正在加载作品...');
      const response = (await worksApi.getDetail(workId)) as any;

      if (response?.data?.code === 200) {
        const work = response.data.data;
        loadingNotification?.close();
        viewWork({
          content: work.content,
          workId: workId,
          userId: work.userId
        });
        notification.success('加载成功');
      } else {
        loadingNotification?.close();
        notification.error('获取作品详情失败');
      }
    } catch (error) {
      console.error('获取作品详情失败:', error);
      notification.error('加载作品失败');
    }
  };

  // 预览项目
  const handlePreviewProject = (project: any) => {
    setSelectedProject(project);
    setPreviewModalVisible(true);
  };

  useEffect(() => {
    if (classId) {
      fetchClassProjects();
    }
  }, [classId]);

  return (
    <div className="flex flex-col h-full">
      {loading ? (
        <div className="flex justify-center py-8">
          <Spin />
        </div>
      ) : projects.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 auto-rows-fr mt-4">
          {projects.map((project) => (
            <Card
              key={project.id}
              hoverable
              className="project-card w-full"
              onClick={() => handlePreviewProject(project)}
              bodyStyle={{ padding: 0 }}
            >
              {/* 顶部作者信息区域 */}
              <div className="p-3 lg:p-4 flex items-center gap-2">
                <div className="flex items-center gap-2 min-w-0 flex-1">
                  <Avatar size="small" src={project.author?.avatarUrl} className="shrink-0">
                    {project.author?.nickName?.[0] || 'U'}
                  </Avatar>
                  <span className="text-xs lg:text-sm text-gray-600 truncate">
                    {project.author?.nickName || '未知用户'}
                  </span>
                </div>
                <span className="text-[11px] lg:text-xs text-gray-400 shrink-0 hidden sm:block">
                  {new Date(project.publishToClassTime).toLocaleString()}
                </span>
              </div>

              {/* 中间封面图区域 */}
              <div className="relative aspect-video">
                {project.coverImage ? (
                  <Image
                    src={project.coverImage}
                    alt={project.title}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <Image
                    src={project.screenShotImage}
                    alt={project.title}
                    fill
                    className="object-cover"
                  />
                )}
              </div>

              {/* 底部作品信息区域 */}
              <div className="p-3 lg:p-4 space-y-2">
                <div className="space-y-1">
                  <h4 className="text-sm lg:text-base font-medium text-gray-800 truncate">
                    {project.title}
                  </h4>
                  <p className="text-xs lg:text-sm text-gray-500 line-clamp-2 min-h-[2.5em]">
                    {project.description || '暂无描述'}
                  </p>
                </div>

                <div className="flex flex-wrap items-center gap-2">
                  <div className="flex items-center text-[11px] lg:text-xs text-gray-400 shrink-0">
                    <Eye size={12} className="mr-1" />
                    <span>浏览：{project.viewCount || 0}</span>
                  </div>
                  <div className="flex-1 min-w-[100px] flex justify-end">
                    <Button
                      type="primary"
                      size="small"
                      className="view-btn text-xs lg:text-sm px-2 lg:px-3 h-6 lg:h-7"
                    >
                      查看作品
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <Empty description="暂无班级项目" />
      )}

      {/* 项目预览模态框 */}
      <Modal
        title="作品预览"
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setPreviewModalVisible(false)}>
            关闭
          </Button>,
          <Button
            key="view"
            type="primary"
            onClick={() => {
              if (selectedProject) {
                handleViewWork(selectedProject.id);
              }
            }}
          >
            打开作品
          </Button>
        ]}
        width={800}
      >
        {selectedProject && (
          <div className="space-y-4">
            <div className="relative h-[400px]">
              <Carousel
                dots={{ className: 'custom-dots' }}
                arrows
                prevArrow={<CustomArrow type="prev" />}
                nextArrow={<CustomArrow type="next" />}
              >
                {selectedProject.coverImage && (
                  <div className="h-[400px] relative">
                    <img
                      src={selectedProject.coverImage}
                      alt={`${selectedProject.title} 封面`}
                      className="w-full h-full object-contain"
                    />
                    <ImageLabel text="作品封面" />
                  </div>
                )}
                {selectedProject.screenShotImage && (
                  <div className="h-[400px] relative">
                    <img
                      src={selectedProject.screenShotImage}
                      alt={`${selectedProject.title} 截图`}
                      className="w-full h-full object-contain"
                    />
                    <ImageLabel text="作品截图" />
                  </div>
                )}
              </Carousel>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2">{selectedProject.title}</h3>
                <p className="text-gray-600">{selectedProject.description || '暂无描述'}</p>
              </div>

              <div className="flex items-center justify-between text-sm text-gray-500">
                <div className="flex items-center space-x-2">
                  <Avatar size="small" src={selectedProject.author?.avatarUrl}>
                    {selectedProject.author?.nickName?.[0] || 'U'}
                  </Avatar>
                  <span>{selectedProject.author?.nickName || '未知用户'}</span>
                </div>
                <div>
                  发布时间：{new Date(selectedProject.publishToClassTime).toLocaleString()}
                </div>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
} 