"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/activity/festival/page",{

/***/ "(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx":
/*!*************************************************************!*\
  !*** ./app/activity/festival/components/ActivityHeader.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Share2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Share2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./lib/store.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ActivityMediaGallery */ \"(app-pages-browser)/./app/activity/festival/components/ActivityMediaGallery.tsx\");\n/* harmony import */ var _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Modal!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _components_login_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/login-dialog */ \"(app-pages-browser)/./components/login-dialog.tsx\");\n/* harmony import */ var _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/activity */ \"(app-pages-browser)/./lib/api/activity/index.ts\");\n/* harmony import */ var _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/activity/events-task */ \"(app-pages-browser)/./lib/api/activity/events-task.ts\");\n/* harmony import */ var _lib_api_user__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/user */ \"(app-pages-browser)/./lib/api/user.ts\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 临时实现formatDate函数，以解决模块导入问题\nconst formatDate = (dateString)=>{\n    // 在服务端渲染时，直接返回原始字符串，避免水合错误\n    if (false) {}\n    const date = new Date(dateString);\n    // 检查日期是否有效\n    if (isNaN(date.getTime())) {\n        return dateString; // 如果无效，返回原始字符串\n    }\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, \"0\");\n    const day = String(date.getDate()).padStart(2, \"0\");\n    return \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n};\nconst ActivityHeader = (param)=>{\n    let { title: initialTitle, startTime: initialStartTime, endTime: initialEndTime, bannerImage: initialBannerImage, expanded, setExpanded, activityId, tags = [], organizer: initialOrganizer = \"\", activityType = _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.ActivityType.WORK, onRefreshWorks, // 媒体字段\n    promotionImage, backgroundImage, galleryImages, attachmentFiles } = param;\n    _s();\n    console.log(\"\\uD83D\\uDD0D [DEBUG] ActivityHeader 组件渲染\", {\n        activityId,\n        initialTitle\n    });\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_9__.GetNotification)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.usePathname)();\n    // 显示用的标题和时间\n    const title = initialTitle;\n    const startTime = initialStartTime;\n    const endTime = initialEndTime;\n    const bannerImage = initialBannerImage;\n    const organizer = initialOrganizer;\n    // 获取当前用户信息和dispatch\n    const userState = (0,react_redux__WEBPACK_IMPORTED_MODULE_11__.useSelector)(_lib_store__WEBPACK_IMPORTED_MODULE_3__.selectUserState);\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_11__.useDispatch)();\n    const isAdmin = (userState === null || userState === void 0 ? void 0 : userState.roleId) === 4;\n    // 同步用户状态的函数 - 增强版，从API获取最新用户信息\n    const syncUserState = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 开始同步用户状态\");\n            const localUser = localStorage.getItem(\"user\");\n            if (localUser) {\n                const userData = JSON.parse(localUser);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中的用户数据\", userData);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] Redux 中的用户数据\", userState);\n                // 如果用户已登录但phone为空，尝试从API获取最新用户信息\n                if (userData.isLoggedIn && userData.userId && (!userData.phone || userData.phone.trim() === \"\")) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户phone为空，从API获取最新用户信息\");\n                    try {\n                        const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_8__.userApi.getUserInfo(userData.userId);\n                        if (response.code === 200 && response.data) {\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取到最新用户信息\", response.data);\n                            // 构建更新后的用户数据\n                            const updatedUserData = {\n                                ...userData,\n                                phone: response.data.phone || \"\",\n                                nickName: response.data.nickName || userData.nickName,\n                                avatarUrl: response.data.avatarUrl || userData.avatarUrl,\n                                gender: response.data.gender || userData.gender,\n                                roleId: response.data.roleId || userData.roleId\n                            };\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 更新用户数据\", {\n                                oldPhone: userData.phone,\n                                newPhone: updatedUserData.phone,\n                                updated: updatedUserData\n                            });\n                            // 更新localStorage和Redux\n                            localStorage.setItem(\"user\", JSON.stringify(updatedUserData));\n                            dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(updatedUserData));\n                            return;\n                        }\n                    } catch (error) {\n                        console.error(\"\\uD83D\\uDD0D [DEBUG] 从API获取用户信息失败:\", error);\n                    }\n                }\n                // 检查关键字段是否有变化\n                const hasPhoneChanged = userData.phone !== (userState === null || userState === void 0 ? void 0 : userState.phone);\n                const hasRoleChanged = userData.roleId !== (userState === null || userState === void 0 ? void 0 : userState.roleId);\n                const hasLoginStatusChanged = userData.isLoggedIn !== (userState === null || userState === void 0 ? void 0 : userState.isLoggedIn);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态变化检查\", {\n                    phoneChanged: hasPhoneChanged,\n                    roleChanged: hasRoleChanged,\n                    loginStatusChanged: hasLoginStatusChanged,\n                    oldPhone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                    newPhone: userData.phone,\n                    oldRole: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                    newRole: userData.roleId\n                });\n                // 如果localStorage中的用户状态与Redux中的不一致，说明需要更新\n                if (hasPhoneChanged || hasRoleChanged || hasLoginStatusChanged) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户状态变化，同步Redux状态\");\n                    dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(userData));\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态无变化，无需同步\");\n                }\n            } else {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中无用户数据\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD0D [DEBUG] 检查用户状态失败:\", error);\n        }\n    };\n    // 监听页面焦点变化和路由变化，重新检查用户状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 页面加载时立即检查一次\n        syncUserState().catch(console.error);\n        const handleVisibilityChange = ()=>{\n            if (document.visibilityState === \"visible\") {\n                syncUserState().catch(console.error);\n            }\n        };\n        const handleFocus = ()=>{\n            syncUserState().catch(console.error);\n        };\n        // 添加多种事件监听确保状态同步\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n        window.addEventListener(\"focus\", handleFocus);\n        return ()=>{\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n            window.removeEventListener(\"focus\", handleFocus);\n        };\n    }, [\n        userState === null || userState === void 0 ? void 0 : userState.phone,\n        dispatch\n    ]);\n    // 工具函数：检查用户是否为学生身份\n    const checkIsStudent = ()=>{\n        try {\n            const userStr = localStorage.getItem(\"user\");\n            const localUser = userStr ? JSON.parse(userStr) : null;\n            const userRoleId = (localUser === null || localUser === void 0 ? void 0 : localUser.roleId) || (userState === null || userState === void 0 ? void 0 : userState.roleId);\n            return userRoleId === 1; // 学生身份的roleId为1\n        } catch (error) {\n            console.error(\"解析localStorage中的用户信息失败:\", error);\n            return false;\n        }\n    };\n    // 工具函数：显示身份验证失败提示\n    const showStudentOnlyModal = ()=>{\n        _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].confirm({\n            title: \"身份验证\",\n            content: \"抱歉，只有学生身份的用户才能参加此活动报名。如果您是学生，请联系管理员更新您的身份信息。\",\n            okText: \"我知道了\",\n            cancelText: \"联系管理员\",\n            onCancel: ()=>{\n                notification.info(\"如需帮助，请联系客服：400-123-4567\");\n            }\n        });\n    };\n    // 获取报名状态文本\n    const getRegistrationStatusText = ()=>{\n        switch(registrationStatus){\n            case 0:\n                return \"已取消\";\n            case 1:\n                return \"已报名\";\n            case 2:\n                return \"已审核通过\";\n            case 3:\n                return \"已拒绝\";\n            case 4:\n                return \"评审中\";\n            case 5:\n                return \"已获奖\";\n            case 6:\n                return \"审核中\";\n            default:\n                return \"已报名\";\n        }\n    };\n    // 登录和报名相关状态\n    const [showLoginDialog, setShowLoginDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSubmitted, setHasSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [registrationStatus, setRegistrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 报名状态：0-已取消 1-已报名 2-已审核通过 3-已拒绝 4-评审中 5-已获奖 6-审核中\n    const [showUI, setShowUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 添加客户端检测来解决水合错误\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    // 页面加载时检查报名状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkRegistrationStatus = async ()=>{\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态 - useEffect 触发\", {\n                isLoggedIn: userState.isLoggedIn,\n                roleId: userState.roleId,\n                phone: userState.phone,\n                isClient,\n                activityId\n            });\n            // 只有在用户已登录且是学生身份时才检查报名状态\n            const isStudent = checkIsStudent();\n            if (!userState.isLoggedIn || !isStudent) {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录或不是学生身份，重置报名状态\", {\n                    isLoggedIn: userState.isLoggedIn,\n                    isStudent\n                });\n                // 如果用户未登录或不是学生，重置状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n                return;\n            }\n            try {\n                var _response_data, _response_data1;\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 开始检查报名状态 API 调用\");\n                setIsChecking(true);\n                const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.checkRegistration(Number(activityId));\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 页面加载检查报名状态响应:\", response);\n                if ((response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200 && ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data)) {\n                    const { submitted, submit } = response.data.data;\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态检查结果\", {\n                        submitted,\n                        submit\n                    });\n                    setHasSubmitted(submitted);\n                    if (submit && submit.status !== undefined) {\n                        setRegistrationStatus(submit.status);\n                    } else {\n                        setRegistrationStatus(null);\n                    }\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态响应不符合预期，设置为未报名状态\");\n                    // 如果响应不符合预期，设置为未报名状态\n                    setHasSubmitted(false);\n                    setRegistrationStatus(null);\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态失败:\", error);\n                // 出错时设置为未报名状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n            } finally{\n                setIsChecking(false);\n            }\n        };\n        // 只在客户端执行，避免服务端渲染问题\n        if (isClient) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 客户端环境，执行报名状态检查\");\n            checkRegistrationStatus();\n        } else {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 非客户端环境，跳过报名状态检查\");\n        }\n    }, [\n        activityId,\n        userState.isLoggedIn,\n        userState.roleId,\n        userState.phone,\n        isClient\n    ]);\n    // 加载活动标签\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchActivityTags = async ()=>{\n            if (activityId) {\n                try {\n                    const response = await activityTagApi.getActivityTags(Number(activityId));\n                    if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n                        setTagIds(response.data.data || []);\n                    }\n                } catch (error) {\n                    console.error(\"获取活动标签失败:\", error);\n                }\n            }\n        };\n        fetchActivityTags();\n    }, [\n        activityId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 初始化文件列表\n        if (bannerImage) {\n            setFileList([\n                {\n                    uid: \"-1\",\n                    name: \"当前封面\",\n                    status: \"done\",\n                    url: bannerImage\n                }\n            ]);\n        } else {\n            setFileList([]);\n        }\n    }, [\n        bannerImage\n    ]);\n    // 前往任务函数\n    const handleGoToTask = async ()=>{\n        try {\n            const loadingMessage = notification.loading(\"正在跳转到任务...\");\n            // 获取当前用户的赛事任务列表\n            const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n            if (eventsTaskResponse.data.code === 200) {\n                const eventsTasks = eventsTaskResponse.data.data;\n                // 查找与当前活动相关的赛事任务\n                const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                if (relatedTask) {\n                    // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                    window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                    notification.success(\"正在跳转到任务页面...\");\n                } else {\n                    // 如果没有找到对应任务，在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                    notification.info(\"未找到对应任务，已打开班级空间\");\n                }\n            } else {\n                // 如果获取任务失败，在新标签页中打开班级空间\n                window.open(\"/class-space\", \"_blank\");\n                notification.info(\"获取任务失败，已打开班级空间\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"跳转任务失败:\", error);\n            notification.error(\"跳转任务失败，请稍后重试\");\n        }\n    };\n    // 处理报名按钮点击\n    const handleSubmitClick = async ()=>{\n        var _userState_phone, _localUser_phone;\n        // 获取localStorage中的用户信息进行对比\n        const localUserStr = localStorage.getItem(\"user\");\n        const localUser = localUserStr ? JSON.parse(localUserStr) : null;\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 报名按钮点击 - 详细用户状态检查\", {\n            hasSubmitted,\n            \"Redux userState\": {\n                isLoggedIn: userState === null || userState === void 0 ? void 0 : userState.isLoggedIn,\n                phone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                phoneLength: userState === null || userState === void 0 ? void 0 : (_userState_phone = userState.phone) === null || _userState_phone === void 0 ? void 0 : _userState_phone.length,\n                phoneType: typeof (userState === null || userState === void 0 ? void 0 : userState.phone),\n                roleId: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                userId: userState === null || userState === void 0 ? void 0 : userState.userId,\n                nickName: userState === null || userState === void 0 ? void 0 : userState.nickName\n            },\n            \"localStorage user\": {\n                phone: localUser === null || localUser === void 0 ? void 0 : localUser.phone,\n                phoneLength: localUser === null || localUser === void 0 ? void 0 : (_localUser_phone = localUser.phone) === null || _localUser_phone === void 0 ? void 0 : _localUser_phone.length,\n                phoneType: typeof (localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                userId: localUser === null || localUser === void 0 ? void 0 : localUser.userId,\n                nickName: localUser === null || localUser === void 0 ? void 0 : localUser.nickName\n            },\n            \"phone validation\": {\n                \"userState.phone exists\": !!(userState === null || userState === void 0 ? void 0 : userState.phone),\n                \"userState.phone not empty\": (userState === null || userState === void 0 ? void 0 : userState.phone) && userState.phone.trim() !== \"\",\n                \"localUser.phone exists\": !!(localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                \"localUser.phone not empty\": (localUser === null || localUser === void 0 ? void 0 : localUser.phone) && (localUser === null || localUser === void 0 ? void 0 : localUser.phone.trim()) !== \"\"\n            }\n        });\n        // 临时调用API获取最新用户信息进行对比\n        if (userState === null || userState === void 0 ? void 0 : userState.userId) {\n            try {\n                var _response_data, _response_data_phone, _response_data1, _response_data2, _response_data3, _response_data4;\n                const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_8__.userApi.getUserInfo(userState.userId);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取的最新用户信息:\", {\n                    code: response.code,\n                    phone: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.phone,\n                    phoneLength: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_phone = _response_data1.phone) === null || _response_data_phone === void 0 ? void 0 : _response_data_phone.length,\n                    phoneType: typeof ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.phone),\n                    nickName: (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.nickName,\n                    userId: (_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : _response_data4.id\n                });\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 获取用户信息失败:\", error);\n            }\n        }\n        // 如果用户已报名，跳转到任务页面\n        if (hasSubmitted) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已报名，跳转到任务页面\");\n            handleGoToTask();\n            return;\n        }\n        // 检查用户是否登录\n        if (!userState || !userState.isLoggedIn) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录，跳转到登录页面\");\n            // 未登录，显示登录对话框\n            const redirectUrl = pathname || \"/home\";\n            router.push(\"/login?redirect=\".concat(encodeURIComponent(redirectUrl)));\n            return;\n        }\n        // 检查用户身份：只有学生（roleId为1）才能报名\n        const isStudent = checkIsStudent();\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 检查用户身份\", {\n            isStudent,\n            roleId: userState === null || userState === void 0 ? void 0 : userState.roleId\n        });\n        if (!isStudent) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户不是学生身份，显示身份验证弹窗\");\n            showStudentOnlyModal();\n            return;\n        }\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 身份验证通过，开始报名流程\");\n        // 身份验证通过，直接进行报名\n        handleDirectRegistration();\n    };\n    // 处理登录成功\n    const handleLoginSuccess = ()=>{\n        notification.success(\"登录成功\");\n    // 登录成功后，页面的useEffect会自动检查报名状态\n    // 不需要在这里重复检查，让useEffect处理状态更新\n    };\n    // 处理报名成功\n    const handleSubmitSuccess = ()=>{\n        // 更新提交状态\n        setHasSubmitted(true);\n        setRegistrationStatus(1); // 设置为已报名状态\n        notification.success(\"报名成功\");\n        // 通知页面刷新参赛作品列表\n        if (onRefreshWorks) {\n            onRefreshWorks();\n        }\n    };\n    // 直接报名处理函数\n    const handleDirectRegistration = async ()=>{\n        try {\n            var _response_data;\n            // 检查用户是否绑定了手机号\n            if (!userState.phone || userState.phone.trim() === \"\") {\n                // 显示确认对话框\n                _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].confirm({\n                    title: \"需要绑定手机号\",\n                    content: \"报名活动需要绑定手机号，是否前往绑定？\",\n                    okText: \"前往绑定\",\n                    cancelText: \"取消\",\n                    onOk: ()=>{\n                        // 跳转到登录页面，并在URL中添加参数表示需要绑定手机号\n                        const currentUrl = window.location.href;\n                        router.push(\"/login?needBindPhone=true&redirect=\".concat(encodeURIComponent(currentUrl)));\n                    },\n                    onCancel: ()=>{\n                        notification.info(\"未绑定手机号，无法报名活动\");\n                    }\n                });\n                return;\n            }\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已绑定手机号，继续报名流程\");\n            // 显示加载提示\n            const loadingMessage = notification.loading(\"正在提交报名...\");\n            // 提交报名数据（使用默认值，跳过协议确认步骤）\n            const submitData = {\n                activityId: Number(activityId),\n                agreementAccepted: true,\n                parentConsentAccepted: true,\n                parentSignaturePath: \"\",\n                signatureTime: new Date().toISOString(),\n                remark: \"快速报名时间：\".concat(new Date().toLocaleString(\"zh-CN\"))\n            };\n            const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.submitRegistration(submitData);\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                // 更新UI状态\n                setHasSubmitted(true);\n                setRegistrationStatus(1);\n                notification.success(\"报名成功！正在跳转到班级空间...\");\n                // 通知页面刷新参赛作品列表\n                if (onRefreshWorks) {\n                    onRefreshWorks();\n                }\n                // 报名成功后，获取对应的赛事任务并跳转到班级空间\n                try {\n                    // 等待一小段时间确保后端创建赛事任务完成\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    // 获取当前用户的赛事任务列表\n                    const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n                    if (eventsTaskResponse.data.code === 200) {\n                        const eventsTasks = eventsTaskResponse.data.data;\n                        // 查找与当前活动相关的赛事任务\n                        const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                        if (relatedTask) {\n                            // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                            window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                        } else {\n                            // 如果没有找到对应任务，在新标签页中打开班级空间\n                            window.open(\"/class-space\", \"_blank\");\n                        }\n                    } else {\n                        // 如果获取任务失败，在新标签页中打开班级空间\n                        window.open(\"/class-space\", \"_blank\");\n                    }\n                } catch (taskError) {\n                    console.error(\"获取赛事任务失败:\", taskError);\n                    // 即使获取任务失败，也要在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                }\n            } else {\n                var _response_data1;\n                throw new Error(((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message) || \"报名失败\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"报名失败:\", error);\n            notification.error(error instanceof Error ? error.message : \"报名失败，请稍后重试\");\n        }\n    };\n    // 处理分享按钮点击事件\n    const handleShare = ()=>{\n        if (true) {\n            // 获取当前页面URL\n            const currentUrl = window.location.href;\n            // 复制到剪贴板\n            navigator.clipboard.writeText(currentUrl).then(()=>{\n                notification.success(\"活动链接已复制，快去分享吧！\");\n            }).catch((err)=>{\n                console.error(\"复制失败:\", err);\n                notification.error(\"复制链接失败，请手动复制\");\n            });\n        }\n    };\n    // 如果还没有在客户端挂载，返回一个简单的占位符\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-64 bg-gray-200 animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 639,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-300 rounded mb-2 w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded mb-1 w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 638,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n            lineNumber: 637,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-[300px] overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-transparent to-black/30 z-10 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-blue-500/10 animate-pulse z-0 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: bannerImage,\n                                            alt: title,\n                                            fill: true,\n                                            priority: true,\n                                            style: {\n                                                objectFit: \"cover\"\n                                            },\n                                            className: \"z-10 hover:scale-105 transition-transform duration-700\",\n                                            onError: (e)=>{\n                                                const target = e.target;\n                                                target.style.background = \"linear-gradient(120deg, #f3f4f6, #e5e7eb)\";\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-4 left-4 right-4 z-20 flex justify-between items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 683,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 658,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-800 flex items-center\",\n                                        children: [\n                                            title,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"ml-2 w-5 h-5 text-yellow-400 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    organizer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: [\n                                            \"主办方: \",\n                                            organizer\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 758,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm mt-2 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-500 to-indigo-500 h-1.5 w-1.5 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"活动时间：\",\n                                            formatDate(startTime),\n                                            \" - \",\n                                            formatDate(endTime)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 761,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 752,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3 mt-4 md:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSubmitClick,\n                                        disabled: isChecking,\n                                        className: \"flex items-center px-5 py-2.5 text-sm font-medium rounded-full \".concat(hasSubmitted ? \"bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\" : \"bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\", \" transition-all duration-200\"),\n                                        children: isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"loading-spinner mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"检查状态中\"\n                                            ]\n                                        }, void 0, true) : hasSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"已报名 前往任务\"\n                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"立即报名\"\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex items-center p-2.5 text-gray-500 hover:text-blue-500 hover:bg-gray-50 rounded-full hover:scale-110 active:scale-95 transition-all duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 805,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 767,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 751,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 654,\n                columnNumber: 7\n            }, undefined),\n            (promotionImage || backgroundImage || galleryImages || attachmentFiles) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    promotionImage: promotionImage,\n                    backgroundImage: backgroundImage,\n                    galleryImages: galleryImages,\n                    attachmentFiles: attachmentFiles,\n                    activityTitle: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                    lineNumber: 818,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 817,\n                columnNumber: 9\n            }, undefined),\n            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_login_dialog__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showLoginDialog,\n                onClose: ()=>setShowLoginDialog(false),\n                onSuccess: handleLoginSuccess\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 830,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ActivityHeader, \"D0DlXhkmRTEiLXZUiQNwdeSahJ0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.usePathname,\n        react_redux__WEBPACK_IMPORTED_MODULE_11__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_11__.useDispatch\n    ];\n});\n_c = ActivityHeader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ActivityHeader);\nvar _c;\n$RefreshReg$(_c, \"ActivityHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx\n"));

/***/ })

});