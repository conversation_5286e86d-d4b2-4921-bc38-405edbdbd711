/* 按钮基础样式 */
.btn-modern {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out, border-color 0.2s ease-out !important;
}

/* 修改所有antd按钮的默认过渡效果 */
.ant-btn {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out, border-color 0.2s ease-out !important;
}

.ant-btn:hover {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out, border-color 0.2s ease-out !important;
}

/* 返回按钮样式 */
.btn-back {
  position: relative;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  color: #374151;
}

.btn-back:hover {
  border-color: #d1d5db;
}

.btn-back::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255,255,255,0) 0%,
    rgba(255,255,255,0.2) 50%,
    rgba(255,255,255,0) 100%
  );
  transition: left 0.5s;
}

.btn-back:hover::before {
  left: 100%;
}

/* 返回按钮图标样式 */
.btn-back .anticon {
  margin-right: 0.25rem !important;
  transition: transform 0.3s !important;
}

/* 返回按钮悬浮时图标动画 */
.btn-back:hover .anticon {
  transform: translateX(-2px) !important;
}

/* 移除按钮点击波纹效果 */
.btn-back::after {
  display: none !important;
}

/* 暗色模式返回按钮样式 */
.dark .btn-back {
  background: rgba(31, 41, 55, 0.9) !important;
  color: #d1d5db !important;
  border-color: #374151 !important;
}

/* 卡片悬浮效果 */
.ant-card-hoverable {
  transition: all 0.3s ease-out !important;
}

.ant-card-hoverable:hover {
  box-shadow: 0 12px 24px -8px rgba(0, 0, 0, 0.15) !important;
}

/* 暗色模式卡片样式 */
.dark .ant-card {
  background: #1f2937 !important;
  border-color: #374151 !important;
}

.dark .ant-card-hoverable:hover {
  box-shadow: 0 12px 24px -8px rgba(0, 0, 0, 0.3) !important;
}

/* Empty 组件样式 */
.ant-empty {
  color: #9ca3af !important;
}

.dark .ant-empty {
  color: #6b7280 !important;
}

/* 加载动画样式 */
.ant-spin {
  color: #4766C2 !important;
}

.dark .ant-spin {
  color: #60a5fa !important;
}

/* 项目卡片样式 */
.ant-card {
  background: linear-gradient(to bottom right, #ffffff, #fafafa);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(229, 231, 235, 0.8);
}

.ant-card:hover {
  box-shadow: 0 12px 24px -8px rgba(0, 0, 0, 0.12);
  border-color: #93c5fd;
}

.ant-tag {
  font-weight: 500;
  letter-spacing: 0.5px;
}

.ant-btn.rounded-full {
  height: 28px;
  padding: 0 16px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s;
}

.ant-btn.rounded-full:hover {
  transform: translateX(2px);
}

.ant-dropdown-menu {
  padding: 4px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.ant-dropdown-menu-item {
  border-radius: 6px;
  padding: 6px 12px;
  margin: 2px 0;
}

.ant-dropdown-menu-item:hover {
  background-color: #f0f7ff;
}

.ant-dropdown-menu-item-danger:hover {
  background-color: #fef2f2 !important;
  color: #ef4444 !important;
}

/* 添加一些动画效果 */
@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.ant-card {
  animation: slideUp 0.3s ease;
}

/* 添加渐变背景色 */
.bg-gradient-primary {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
}

/* 添加阴影效果 */
.hover-shadow-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* 修改凹陷效果的样式 */
.shadow-inner {
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.08);
  background: linear-gradient(to bottom, #f1f5f9, #f8fafc);
}

/* 内容区域的滚动条样式 */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #CBD5E0 #F1F5F9;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #F1F5F9;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: #CBD5E0;
  border-radius: 3px;
}

/* 卡片在凹陷背景中的样式 */
.ant-card {
  backdrop-filter: blur(8px);
  border: 1px solid rgb(141 242 255 / 10%);
}

/* Tab样式优化 */
.ant-tabs-nav {
  background: white;
  padding: 4px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.ant-tabs-tab {
  margin: 0 4px !important;
  padding: 8px 16px !important;
  transition: all 0.3s ease;
}

.ant-tabs-tab.ant-tabs-tab-active {
  background: #f0f7ff;
  border-radius: 6px;
}

.ant-tabs-tab:hover {
  background: #f9fafb;
  border-radius: 6px;
}

/* 标题渐变效果 */
.gradient-text {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 修改卡片标题样式 */
.ant-card-head-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

/* 分隔线样式 */
.divider {
  height: 24px;
  width: 1px;
  background: #e5e7eb;
  margin: 0 12px;
}

/* 自定义 Tab 样式 */
.custom-tabs .ant-tabs-nav {
  margin: 0 !important;
  background: transparent;
  box-shadow: none;
  padding: 0;
}

.custom-tabs .ant-tabs-nav::before {
  border-bottom: none !important;
}

.custom-tabs .ant-tabs-tab {
  margin: 0 16px 0 0 !important;
  padding: 8px 16px !important;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: white;
  border: 1px solid #e5e7eb;
}

.custom-tabs .ant-tabs-tab:hover {
  background: #fff;
  border-color: #93c5fd;
}

.custom-tabs .ant-tabs-tab.ant-tabs-tab-active {
  background: #3b82f6;
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.custom-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: white !important;
  font-weight: 500;
}

.custom-tabs .ant-tabs-ink-bar {
  display: none;
}

/* Tab 内容区域样式 */
.ant-tabs-content {
  background: transparent;
  padding-top: 16px;
}

/* 按钮样式优化 */
.ant-btn {
  background: white;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.ant-btn-primary {
  border: none;
  background: linear-gradient(to right, #3b82f6, #60a5fa);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.ant-btn-primary:hover {
  background: linear-gradient(to right, #2563eb, #3b82f6);
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
}

/* 卡片悬浮效果增强 */
.ant-card-hoverable:hover {
  box-shadow: 0 12px 24px -8px rgba(0, 0, 0, 0.15);
  border-color: #93c5fd;
}

/* 激活状态的卡片样式 */
.ant-card.active {
  background: linear-gradient(to right, #eff6ff, #f0f9ff);
  border-color: #3b82f6;
}

/* 学生卡片样式 */
.student-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.student-card:hover {
  background: white;
  border-color: #93c5fd;
  transform: translateY(-2px);
  box-shadow: 0 8px 16px -4px rgba(0, 0, 0, 0.1);
}

/* 进度条样式 */
.zero-points .ant-progress-bg {
  background-color: #E5E7EB !important;
}

/* 内容区域的圆角和阴影 */
.content-area {
  border-radius: 1rem;
  background: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 移除按钮悬浮效果 */
.remove-btn:hover {
  background: #fee2e2 !important;
  color: #ef4444 !important;
}

/* 三点按钮样式 */
.ant-btn.ant-btn-text {
  width: 24px;
  height: 24px;
  min-width: 24px;
  padding: 0;
  border: none;
  background: transparent;
}

.ant-btn.ant-btn-text:hover {
  background: transparent;
}

/* 下拉菜单样式优化 */
.ant-dropdown-menu {
  min-width: 120px;
  padding: 4px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.ant-dropdown-menu-item {
  border-radius: 4px;
  padding: 6px 12px;
  margin: 2px 0;
}

.ant-dropdown-menu-item:hover {
  background-color: #f3f4f6;
}

.ant-dropdown-menu-item-danger:hover {
  background-color: #fef2f2 !important;
  color: #ef4444 !important;
}

/* 学校卡片样式 */
.school-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  cursor: pointer;
}

.school-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px -8px rgba(0, 0, 0, 0.12);
  border-color: #93c5fd;
}

/* 学校图标容器样式 */
.school-card .school-icon-container {
  width: 96px;
  height: 96px;
  border-radius: 12px;
  background: linear-gradient(135deg, #EFF6FF 0%, #F1F5F9 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.school-card:hover .school-icon-container {
  background: linear-gradient(135deg, #DBEAFE 0%, #EFF6FF 100%);
}

.school-card .school-icon-container i {
  font-size: 36px;
  color: #60A5FA;
  transition: all 0.3s ease;
}

.school-card:hover .school-icon-container i {
  color: #3B82F6;
  transform: scale(1.1);
}

/* 统计卡片样式 */
.stat-card {
  background: linear-gradient(to bottom right, #f8fafc, #f1f5f9);
  padding: 16px;
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: linear-gradient(to bottom right, #f0f7ff, #eff6ff);
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 学校卡片按钮样式 */
.school-card-btn {
  background: linear-gradient(to right, #3b82f6, #60a5fa);
  border: none;
  padding: 6px 16px;
  border-radius: 20px;
  display: flex;
  align-items: center;
}

.school-card-btn:hover {
  background: linear-gradient(to right, #2563eb, #3b82f6);
  transform: translateX(4px);
}

/* 分隔线样式 */
.school-card-divider {
  width: 1px;
  background: linear-gradient(to bottom, transparent, #e5e7eb, transparent);
}

/* 多选模式下的卡片样式 */
.student-card-multiselect {
  cursor: pointer;
  user-select: none;
}

.student-card-multiselect:hover {
  border-color: #93c5fd;
  background-color: #f0f7ff;
}

.student-card-selected {
  border-color: #3b82f6 !important;
  background-color: #eff6ff !important;
}

/* Checkbox 样式优化 */
.ant-checkbox-wrapper {
  margin-right: 8px;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

/* 添加拖动选择相关样式 */
.select-none {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 拖动选择时的视觉反馈 */
.dragging {
  cursor: pointer;
}

/* 优化 Checkbox 的点击区域 */
.ant-checkbox-wrapper {
  padding: 4px;
  border-radius: 4px;
}

.ant-checkbox-wrapper:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

/* 分配积分模态框样式 */
.points-modal {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
  padding: 0 16px !important;
}

.points-modal .ant-modal-content {
  max-height: calc(100vh - 48px);
  margin: 0;
  border-radius: clamp(12px, 2vw, 16px);
  overflow: hidden;
  position: relative;
  font-size: clamp(14px, 1.2vw, 16px);
}

.points-modal .ant-modal-header {
  padding: clamp(16px, 2vw, 24px) !important;
}

.points-modal .ant-modal-body {
  max-height: calc(100vh - 180px);
  overflow-y: auto;
  padding: 0;
  scrollbar-width: thin;
}

.points-modal .ant-modal-body::-webkit-scrollbar {
  width: 6px;
}

.points-modal .ant-modal-body::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.points-modal .ant-modal-body::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 3px;
}

.points-modal .ant-modal-body::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}

/* 调整头像大小 */
.points-modal .avatar-container {
  width: clamp(32px, 4vw, 48px);
  height: clamp(32px, 4vw, 48px);
}

/* 调整标题和文本大小 */
.points-modal .title-text {
  font-size: clamp(16px, 1.8vw, 20px);
}

.points-modal .subtitle-text {
  font-size: clamp(14px, 1.4vw, 16px);
}

.points-modal .content-text {
  font-size: clamp(12px, 1.2vw, 14px);
}

/* 调整输入框大小 */
.points-modal .ant-input-number {
  height: clamp(40px, 5vw, 48px);
  padding: clamp(6px, 1vw, 8px) clamp(12px, 1.5vw, 16px);
}

.points-modal .ant-input-number-input {
  height: clamp(28px, 4vw, 32px);
  font-size: clamp(18px, 2.2vw, 24px) !important;
}

/* 数值输入动画 */
@keyframes numberPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.points-modal .ant-input-number-input:focus {
  animation: numberPulse 0.3s ease-out;
}

/* 确认按钮动画 */
@keyframes confirmButtonGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.points-modal .ant-btn-primary:not(:disabled):not(.ant-btn-loading):hover {
  animation: confirmButtonGlow 1.5s infinite;
}

/* 权限模板容器样式 */
.permission-templates-container {
  max-height: calc(100vh - 240px);
  overflow-y: auto;
  padding: 8px 8px;
  position: relative;
  mask-image: linear-gradient(
    to bottom,
    transparent 0,
    black 8px,
    black calc(100% - 40px),
    transparent 100%
  );
  -webkit-mask-image: linear-gradient(
    to bottom,
    transparent 0,
    black 8px,
    black calc(100% - 40px),
    transparent 100%
  );
}

/* 调整模板列表间距 */
.permission-templates-container .space-y-3 {
  margin-top: -4px;  /* 抵消第一个卡片的上边距 */
}

.permission-templates-container .space-y-3 > div {
  margin-top: 8px !important;  /* 减小卡片之间的间距 */
}

.permission-templates-container::-webkit-scrollbar {
  width: 6px;
}

.permission-templates-container::-webkit-scrollbar-track {
  background: #F1F5F9;
  border-radius: 3px;
}

.permission-templates-container::-webkit-scrollbar-thumb {
  background-color: #CBD5E0;
  border-radius: 3px;
  &:hover {
    background-color: #94A3B8;
  }
}

/* 轮播图样式优化 */
.ant-carousel {
  border-radius: 8px;
  overflow: hidden;
}

.carousel-arrow {
  width: 28px;
  height: 28px;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 2;
  transition: all 0.3s ease;
  opacity: 0;
}

.ant-carousel:hover .carousel-arrow {
  opacity: 1;
}

.carousel-arrow:hover {
  background: rgba(0, 0, 0, 0.6);
  width: 32px;
  height: 32px;
}

/* 自定义轮播图指示点样式 */
.custom-dots {
  bottom: 8px !important;
}

.custom-dots li {
  margin: 0 4px !important;
}

.custom-dots li button {
  width: 6px !important;
  height: 6px !important;
  border-radius: 50% !important;
  background: rgba(255, 255, 255, 0.6) !important;
  transition: all 0.3s !important;
}

.custom-dots li.slick-active button {
  width: 16px !important;
  border-radius: 4px !important;
  background: white !important;
}

/* 图片标签样式 */
.image-label {
  position: absolute;
  bottom: 8px;
  left: 8px;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  padding: 4px 8px;
  border-radius: 4px;
  color: white;
  font-size: 12px;
  opacity: 0;
  transform: translateY(4px);
  transition: all 0.3s;
}

.ant-carousel:hover .image-label {
  opacity: 1;
  transform: translateY(0);
}

/* 查看作品按钮样式 */
.ant-btn.rounded-full {
  height: 28px;
  padding: 0 16px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s;
}

.ant-btn.rounded-full:hover {
  transform: translateX(2px);
}

/* 作者头像样式 */
.ant-avatar {
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 项目网格布局样式 */
.grid-cols-3 {
  gap: 24px;
}

@media (max-width: 1280px) {
  .grid-cols-3 {
    gap: 16px;
  }
}

/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 分配积木模态框样式 */
.blocks-modal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
}

.blocks-modal .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
  margin-bottom: 0;
}

.blocks-modal .ant-modal-body {
  padding: 16px 24px;
}

.blocks-modal .ant-modal-close {
  top: 16px;
  right: 16px;
}

.custom-tabs .ant-tabs-tab {
  padding: 8px 16px !important;
  margin: 0 8px 0 0 !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  background: white !important;
  border: 1px solid #e5e7eb !important;
}

.custom-tabs .ant-tabs-tab:hover {
  background: #fff !important;
  border-color: #93c5fd !important;
}

.custom-tabs .ant-tabs-tab.ant-tabs-tab-active {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2) !important;
}

.custom-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: white !important;
  font-weight: 500 !important;
}

.custom-tabs .ant-tabs-ink-bar {
  display: none !important;
}

/* 模板卡片样式 */
.blocks-modal .group {
  border: 1px solid #e5e7eb;
  background: white;
  transition: all 0.3s ease;
  position: relative;
}

.blocks-modal .group:hover {
  border-color: #93c5fd;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: scale(1.01);
}

/* 图标容器样式 */
.blocks-modal .w-7 {
  transition: all 0.3s ease;
}

.blocks-modal .group:hover .w-7 {
  background-color: rgba(59, 130, 246, 0.1);
}

/* 查看详情提示样式 */
.blocks-modal .view-details-hint {
  opacity: 0;
  transform: translateX(-4px);
  transition: all 0.3s ease;
}

.blocks-modal .group:hover .view-details-hint {
  opacity: 1;
  transform: translateX(0);
}

/* 展开式搜索框样式 */
.search-box {
  position: relative;
  height: 32px;
  display: flex;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.search-box .search-input {
  width: 0;
  padding: 0;
  border: none;
  outline: none;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  opacity: 0;
}

.search-box.expanded .search-input {
  width: 200px;
  padding: 4px 11px;
  opacity: 1;
  border: 1px solid #d9d9d9;
  border-radius: 16px;
  margin-right: 8px;
}

.search-box .search-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border: 1px solid #d9d9d9;
  cursor: pointer;
  transition: all 0.3s;
}

.search-box .search-button:hover {
  background: #f0f0f0;
  border-color: #d9d9d9;
}

.search-box.expanded .search-button {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.search-box.expanded .search-button:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

.expandable-search-container {
  position: relative;
  box-sizing: border-box;
  width: fit-content;
}

.expandable-search-box {
  box-sizing: border-box;
  position: relative;
  width: 230px;
  height: 40px;
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  background-color: #f5f5f5;
  transition: all 0.3s ease;
  border: 1px solid #e8e8e8;
}

.expandable-search-box:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.expandable-search-input {
  box-sizing: border-box;
  height: 100%;
  width: 170px;
  background-color: transparent;
  border: none;
  outline: none;
  padding: 0 10px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  transition: all 0.3s ease;
}

.expandable-search-input::placeholder {
  color: rgba(0, 0, 0, 0.45);
}

.expandable-search-icon-container {
  box-sizing: border-box;
  padding: 0 12px;
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.45);
}

.expandable-search-icon-container:hover {
  color: rgba(0, 0, 0, 0.85);
}

.expandable-search-box.collapsed {
  width: 40px;
  cursor: pointer;
}

.expandable-search-box.collapsed .expandable-search-input {
  width: 0;
  padding: 0;
}

/* 确保内容不会过小 */
@media screen and (max-width: 640px) {
  .points-modal {
    padding: 0 8px !important;
    width: calc(100vw - 16px) !important;
  }
  
  .points-modal .ant-modal-content {
    max-height: calc(100vh - 32px);
    margin: 0;
    font-size: 14px;
  }
  
  .points-modal .ant-modal-body {
    max-height: calc(100vh - 160px);
  }

  .points-modal .title-text {
    font-size: 16px;
  }

  .points-modal .subtitle-text {
    font-size: 14px;
  }

  .points-modal .content-text {
    font-size: 12px;
  }
}

/* 确保内容不会过大 */
@media screen and (min-width: 1920px) {
  .points-modal .ant-modal-content {
    font-size: 16px;
  }

  .points-modal .title-text {
    font-size: 20px;
  }

  .points-modal .subtitle-text {
    font-size: 16px;
  }

  .points-modal .content-text {
    font-size: 14px;
  }
}

/* 模态框按钮统一样式 */
.points-modal .modal-btn {
  min-width: clamp(88px, 10vw, 96px);
  height: 32px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  transition: all 0.3s ease;
}

.points-modal .modal-btn.ant-btn-default {
  background: white;
  border: 1px solid #e5e7eb;
}

.points-modal .modal-btn.ant-btn-default:hover {
  color: #3b82f6;
  border-color: #3b82f6;
  background: #f0f7ff;
}

.points-modal .modal-btn.ant-btn-primary {
  background: linear-gradient(to right, #3b82f6, #60a5fa);
  border: none;
  color: white;
}

.points-modal .modal-btn.ant-btn-primary:hover {
  background: linear-gradient(to right, #2563eb, #3b82f6);
}