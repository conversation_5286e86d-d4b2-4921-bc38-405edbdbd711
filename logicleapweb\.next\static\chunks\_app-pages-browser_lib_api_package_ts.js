"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_lib_api_package_ts"],{

/***/ "(app-pages-browser)/./lib/api/package.ts":
/*!****************************!*\
  !*** ./lib/api/package.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   packageApi: function() { return /* binding */ packageApi; }\n/* harmony export */ });\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../request */ \"(app-pages-browser)/./lib/request.ts\");\n\nconst packageApi = {\n    baseUrl: \"api/web/package\",\n    // 获取可用套餐列表\n    getAvailablePackages: ()=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(packageApi.baseUrl, \"/available\"));\n    },\n    // 分配套餐给用户\n    assignPackage: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(packageApi.baseUrl, \"/assign\"), params);\n    },\n    // 获取用户的套餐记录\n    getUserPackageRecords: (userId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(packageApi.baseUrl, \"/user/records\"), {\n            params: {\n                userId\n            }\n        });\n    },\n    // 获取用户消息中心的套餐记录\n    getUserMessageCenterPackages: (userId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(packageApi.baseUrl, \"/user/message-center\"), {\n            params: {\n                userId\n            }\n        });\n    },\n    // 获取用户当前有效套餐\n    getUserCurrentPackage: (userId)=>{\n        console.log(\"getUserCurrentPackage\", userId);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(packageApi.baseUrl, \"/user/current?userId=\").concat(userId));\n    },\n    // 检查套餐状态\n    checkPackageStatus: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(packageApi.baseUrl, \"/user/check\"), params);\n    },\n    // 获取套餐列表(分页)\n    getList: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(packageApi.baseUrl, \"/list\"), {\n            params\n        });\n    },\n    // 添加套餐\n    add: (data)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(packageApi.baseUrl, \"/add\"), data);\n    },\n    // 更新套餐\n    update: (data)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(packageApi.baseUrl, \"/update\"), data);\n    },\n    // 删除套餐\n    delete: (id)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(packageApi.baseUrl, \"/delete\"), {\n            id\n        });\n    },\n    // 获取套餐详情\n    getInfo: (id)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(packageApi.baseUrl, \"/info\"), {\n            params: {\n                id\n            }\n        });\n    },\n    // 获取用户套餐详情\n    getUserPackageDetails: (userId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/user-point/packages\", {\n            params: {\n                userId\n            }\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/package.ts\n"));

/***/ })

}]);