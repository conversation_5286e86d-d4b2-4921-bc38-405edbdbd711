'use client'

import { useEffect, useState } from 'react';
import { <PERSON>, Statistic, Button, Form, } from 'antd';
import { UserOutlined, TeamOutlined, AppstoreOutlined, ArrowLeftOutlined, PictureOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store';
import { userApi } from '@/lib/api/user';
import { activityApi } from '@/lib/api/activity';
import { GetNotification } from 'logic-common/dist/components/Notification';

import pcaData from '@/public/pca.json';

import { carouselApi } from '@/lib/api/carousel';


import PackageManagement from '@/app/admin-space/components/package-management';
import TemplateManagement from './components/template-management';
import SchoolManagement from './components/school-management';
import CarouselManagement from './components/carousel-management';
import AnnouncementManagement from './components/announcement-management';
import UserManagement from './components/user-management';
import ReviewManagement from './components/review-management';
import KeyPackageManagement from './components/key-package-management';
import ActivityManagement from './components/activity-management';


interface UserInfo {
  id: number;
  nickName: string | null;
  phone: string | null;
  roleId: number;
  points: number;
  avatarUrl: string | null;
}


interface CarouselItem {
  id: number;
  type: number;
  displayType: number;
  largeTitle: string;
  mediumTitle: string;
  smallTitle: string;
  imageUrl: string;
  videoUrl: string;
  redirectUrl: string;
  sort: number;
  buttonStatus: number;
  buttonText: string;
  status: number;
}

type Region = { code: string; name: string; children?: Region[] };

export default function AdminSpace() {
  const router = useRouter();
  const roleId = useSelector((state: RootState) => state.user.userState.roleId);
  const userId = useSelector((state: RootState) => state.user.userState.userId);
  const [loading, setLoading] = useState(true);
  const [userList, setUserList] = useState<UserInfo[]>([]);
  const [roleMap] = useState<Record<number, string>>({});
  const [modalUserType] = useState<number | undefined>();
  const [modalKeyword] = useState('');
  const [totalUsers, setTotalUsers] = useState(0);
  const [teacherCount, setTeacherCount] = useState(0);
  const notification = GetNotification();




  const [addSchoolForm] = Form.useForm();

  const [editSchoolForm] = Form.useForm();

  const [, setCities] = useState<Region[]>([]);
  const [, setDistricts] = useState<Region[]>([]);

  const [, setCarouselList] = useState<CarouselItem[]>([]);
  const [carouselTotal, setCarouselTotal] = useState(0);


  // 添加一个状态来保存所有已选择的用户


  // 添加用户列表分页相关状态
  const [userListPage] = useState(1);
  const [, setUserListTotal] = useState(0);
  const [userListPageSize] = useState(10);



  // 获取用户列表
  const fetchUserList = async (params?: { roleId?: number; keyword?: string; page?: number }) => {
    try {
      const { data: res } = await userApi.getUserList({
        page: params?.page || userListPage,
        size: userListPageSize,
        roleId: params?.roleId,
        keyword: params?.keyword
      });
      if (res.code === 200) {

        setUserList(res.data);
        // setUserListTotal(res.data.pagination.total);
      }
    } catch (error) {
      notification.error('获取用户列表失败');
    }
  };


  // 获取用户统计数据
  const fetchUserStats = async () => {
    try {
      // 获取总用户数和教师数量
      const { data: res } = await userApi.getUserList({
        page: 1,
        size: 999
      });

      if (res.code === 200) {
        setTotalUsers(res.data.total);
        // 过滤出教师数量 (roleId === 2)
        const teachers = res.data.list.filter((user: UserInfo) => user.roleId === 2);
        setTeacherCount(teachers.length);
      }
    } catch (error) {
      console.error('获取用户统计失败:', error);
    }
  };




  // 获取轮播图列表
  const fetchCarouselList = async () => {
    console.log('获取轮播图列表');
    try {
      const { data: res } = await carouselApi.getAllList();
      if (res.code === 200) {
        setCarouselList(res.data);
        setCarouselTotal(res.data.length);
      }
    } catch (error) {
      console.error('获取轮播图列表失败:', error);
      notification.error('获取轮播图列表失败');
    }
  };







  useEffect(() => {
    if (roleId !== 4) {
      notification.error('您没有权限访问此页面');
      router.push('/home');
    } else {
      fetchUserStats(); // 获取用户统计数据
      fetchCarouselList();
      setLoading(false);
    }
  }, [roleId, router]);



  // 判断是否是直辖市
  const isMunicipality = (provinceName: string) => {
    return ['北京市', '上海市', '天津市', '重庆市'].includes(provinceName);
  };

  // 处理省份变化
  useEffect(() => {
    const province = addSchoolForm.getFieldValue('province');
    if (province) {
      const selectedProvince = pcaData.find(p => p.name === province);
      if (selectedProvince && selectedProvince.children) {
        if (isMunicipality(province)) {
          setCities([]);
          setDistricts(selectedProvince.children[0].children || []);
          addSchoolForm.setFieldsValue({ city: province, district: undefined });
        } else {
          setCities(selectedProvince.children);
          setDistricts([]);
          addSchoolForm.setFieldsValue({ city: undefined, district: undefined });
        }
      }
    } else {
      setCities([]);
      setDistricts([]);
      addSchoolForm.setFieldsValue({ city: undefined, district: undefined });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [addSchoolForm, addSchoolForm.getFieldValue('province')]);

  // 处理城市变化
  useEffect(() => {
    const province = addSchoolForm.getFieldValue('province');
    const city = addSchoolForm.getFieldValue('city');
    if (city && !isMunicipality(province)) {
      const selectedProvince = pcaData.find(p => p.name === province);
      const selectedCity = selectedProvince?.children?.find(c => c.name === city);
      if (selectedCity && selectedCity.children) {
        setDistricts(selectedCity.children);
        addSchoolForm.setFieldValue('district', undefined);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [addSchoolForm, addSchoolForm.getFieldValue('city')]);

  // 添加编辑表单的省份变化处理
  useEffect(() => {
    const province = editSchoolForm.getFieldValue('province');
    if (province) {
      const selectedProvince = pcaData.find(p => p.name === province);
      if (selectedProvince && selectedProvince.children) {
        if (isMunicipality(province)) {
          setCities([]);
          setDistricts(selectedProvince.children[0].children || []);
          editSchoolForm.setFieldsValue({ city: province, district: undefined });
        } else {
          setCities(selectedProvince.children);
          setDistricts([]);
          editSchoolForm.setFieldsValue({ city: undefined, district: undefined });
        }
      }
    } else {
      setCities([]);
      setDistricts([]);
      editSchoolForm.setFieldsValue({ city: undefined, district: undefined });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editSchoolForm, editSchoolForm.getFieldValue('province')]);

  // 添加编辑表单的城市变化处理
  useEffect(() => {
    const province = editSchoolForm.getFieldValue('province');
    const city = editSchoolForm.getFieldValue('city');
    if (city && !isMunicipality(province)) {
      const selectedProvince = pcaData.find(p => p.name === province);
      const selectedCity = selectedProvince?.children?.find(c => c.name === city);
      if (selectedCity && selectedCity.children) {
        setDistricts(selectedCity.children);
        editSchoolForm.setFieldValue('district', undefined);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editSchoolForm, editSchoolForm.getFieldValue('city')]);


  // 在组件内部添加获取审核列表的方法
  const fetchReviewList = async () => {
    try {
      const { data: res } = await activityApi.getReviewList();
      if (res.code === 200) {
        return res;
      }
    } catch (error) {
      console.error('获取审核列表失败:', error);
      notification.error('获取审核列表失败');
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center gap-4">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => router.push('/home')}
              className="flex items-center"
            >
              返回
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-800">管理员空间</h1>
              <p className="text-gray-500 mt-2">管理系统的用户、模板和其他设置</p>
            </div>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card hoverable>
            <Statistic
              title="总用户数"
              value={totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
          <Card hoverable>
            <Statistic
              title="教师数量"
              value={teacherCount}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
          <Card hoverable>
            <Statistic
              title="轮播图数量"
              value={carouselTotal}
              prefix={<PictureOutlined />}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
          <Card hoverable>
            <Statistic
              title="系统积木数"
              value={108}
              prefix={<AppstoreOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </div>

        {/* 快捷操作区 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <PackageManagement userId={userId} roleMap={roleMap} />

          <TemplateManagement
            userId={userId}
            roleId={roleId}
            roleMap={roleMap}
            userList={userList}
            onUserListRefresh={fetchUserList}
            modalUserType={modalUserType}
            modalKeyword={modalKeyword}
          />

          <SchoolManagement roleMap={roleMap} />

          <CarouselManagement
            onCarouselCountChange={(count) => setCarouselTotal(count)}
          />

          <UserManagement fetchUserStats={fetchUserStats} />

          <AnnouncementManagement />

          <KeyPackageManagement userId={userId} />

          <ReviewManagement />

          <ActivityManagement userId={userId} />


        </div>
      </div>
    </div>
  );
}

