"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unist-util-filter";
exports.ids = ["vendor-chunks/unist-util-filter"];
exports.modules = {

/***/ "(ssr)/./node_modules/unist-util-filter/lib/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/unist-util-filter/lib/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   filter: () => (/* binding */ filter)\n/* harmony export */ });\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(ssr)/./node_modules/unist-util-is/lib/index.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n *\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */\n\n/**\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [cascade=true]\n *   Whether to drop parent nodes if they had children, but all their children\n *   were filtered out (default: `true`).\n */\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * Create a new `tree` of copies of all nodes that pass `test`.\n *\n * The tree is walked in *preorder* (NLR), visiting the node itself, then its\n * head, etc.\n *\n * @template {Node} Tree\n * @template {Test} Check\n *\n * @overload\n * @param {Tree} tree\n * @param {Options | null | undefined} options\n * @param {Check} test\n * @returns {import('./complex-types.js').Matches<Tree, Check>}\n *\n * @overload\n * @param {Tree} tree\n * @param {Check} test\n * @returns {import('./complex-types.js').Matches<Tree, Check>}\n *\n * @overload\n * @param {Tree} tree\n * @param {null | undefined} [options]\n * @returns {Tree}\n *\n * @param {Node} tree\n *   Tree to filter.\n * @param {Options | Test} [options]\n *   Configuration (optional).\n * @param {Test} [test]\n *   `unist-util-is` compatible test.\n * @returns {Node | undefined}\n *   New filtered tree.\n *\n *   `undefined` is returned if `tree` itself didn’t pass the test, or is\n *   cascaded away.\n */\nfunction filter(tree, options, test) {\n  const is = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(test || options)\n  const cascadeRaw =\n    options && typeof options === 'object' && 'cascade' in options\n      ? /** @type {boolean | null | undefined} */ (options.cascade)\n      : undefined\n  const cascade =\n    cascadeRaw === undefined || cascadeRaw === null ? true : cascadeRaw\n\n  return preorder(tree)\n\n  /**\n   * @param {Node} node\n   *   Current node.\n   * @param {number | undefined} [index]\n   *   Index of `node` in `parent`.\n   * @param {Parent | undefined} [parentNode]\n   *   Parent node.\n   * @returns {Node | undefined}\n   *   Shallow copy of `node`.\n   */\n  function preorder(node, index, parentNode) {\n    /** @type {Array<Node>} */\n    const children = []\n\n    if (!is(node, index, parentNode)) return undefined\n\n    if (parent(node)) {\n      let childIndex = -1\n\n      while (++childIndex < node.children.length) {\n        const result = preorder(node.children[childIndex], childIndex, node)\n\n        if (result) {\n          children.push(result)\n        }\n      }\n\n      if (cascade && node.children.length > 0 && children.length === 0) {\n        return undefined\n      }\n    }\n\n    // Create a shallow clone, using the new children.\n    /** @type {typeof node} */\n    // @ts-expect-error all the fields will be copied over.\n    const next = {}\n    /** @type {string} */\n    let key\n\n    for (key in node) {\n      if (own.call(node, key)) {\n        // @ts-expect-error: Looks like a record.\n        next[key] = key === 'children' ? children : node[key]\n      }\n    }\n\n    return next\n  }\n}\n\n/**\n * @param {Node} node\n * @returns {node is Parent}\n */\nfunction parent(node) {\n  return 'children' in node && node.children !== undefined\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unist-util-filter/lib/index.js\n");

/***/ })

};
;