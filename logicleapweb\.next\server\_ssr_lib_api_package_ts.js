"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_lib_api_package_ts";
exports.ids = ["_ssr_lib_api_package_ts"];
exports.modules = {

/***/ "(ssr)/./lib/api/package.ts":
/*!****************************!*\
  !*** ./lib/api/package.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   packageApi: () => (/* binding */ packageApi)\n/* harmony export */ });\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../request */ \"(ssr)/./lib/request.ts\");\n\nconst packageApi = {\n    baseUrl: \"api/web/package\",\n    // 获取可用套餐列表\n    getAvailablePackages: ()=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${packageApi.baseUrl}/available`);\n    },\n    // 分配套餐给用户\n    assignPackage: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${packageApi.baseUrl}/assign`, params);\n    },\n    // 获取用户的套餐记录\n    getUserPackageRecords: (userId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${packageApi.baseUrl}/user/records`, {\n            params: {\n                userId\n            }\n        });\n    },\n    // 获取用户消息中心的套餐记录\n    getUserMessageCenterPackages: (userId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${packageApi.baseUrl}/user/message-center`, {\n            params: {\n                userId\n            }\n        });\n    },\n    // 获取用户当前有效套餐\n    getUserCurrentPackage: (userId)=>{\n        console.log(\"getUserCurrentPackage\", userId);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${packageApi.baseUrl}/user/current?userId=${userId}`);\n    },\n    // 检查套餐状态\n    checkPackageStatus: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${packageApi.baseUrl}/user/check`, params);\n    },\n    // 获取套餐列表(分页)\n    getList: (params)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${packageApi.baseUrl}/list`, {\n            params\n        });\n    },\n    // 添加套餐\n    add: (data)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${packageApi.baseUrl}/add`, data);\n    },\n    // 更新套餐\n    update: (data)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${packageApi.baseUrl}/update`, data);\n    },\n    // 删除套餐\n    delete: (id)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${packageApi.baseUrl}/delete`, {\n            id\n        });\n    },\n    // 获取套餐详情\n    getInfo: (id)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${packageApi.baseUrl}/info`, {\n            params: {\n                id\n            }\n        });\n    },\n    // 获取用户套餐详情\n    getUserPackageDetails: (userId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/api/user-point/packages`, {\n            params: {\n                userId\n            }\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api/package.ts\n");

/***/ })

};
;