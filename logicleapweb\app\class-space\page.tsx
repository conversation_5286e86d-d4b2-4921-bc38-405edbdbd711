'use client'

import { useEffect, useState, useRef } from 'react';
import { Card, Empty, Spin, Button, Form, Input, Tabs, Tag, Modal, Carousel, Avatar, Pagination, Select, Cascader, Radio, Upload } from 'antd';
import request from '../../lib/request';
import { UserOutlined, LeftOutlined, RightOutlined, ArrowLeftOutlined, InboxOutlined, FileTextOutlined, DeleteOutlined, CheckOutlined, InfoCircleOutlined, CloseOutlined } from '@ant-design/icons';
import { Eye } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useSelector } from 'react-redux';
import { RootState } from '../../lib/store';
import task, { TaskType, Priority, TaskStatus } from '../../lib/api/task';
import { worksApi } from '@/lib/api/works';
import taskApi from '../../lib/api/task';
import './class-space.css';
import { uploadApi } from '@/lib/api/upload';
import type { UploadFile } from 'antd/es/upload/interface';
import ClassProjects from './components/class-projects';
import { CustomArrow, ImageLabel } from './components/class-projects';
import TaskList, { TaskListRef } from './components/task-list';
import TaskDetailModal from './components/task-detail-modal';
import TaskSubmitModal from './components/task-submit-modal';
import { ExtendedTask } from './types';
import { studentApi } from '@/lib/api/student';
import { classApi } from '@/lib/api/class';
import { GetNotification } from 'logic-common/dist/components/Notification';

interface ClassInfo {
  id: number;
  grade: string;
  className: string;
  teacherName: string;
  schoolName: string;
}

interface StudentInfo {
  id: number;
  classId: number | null;
  schoolId: number | null;
  studentNumber: string;
}

interface TaskAssignment {
  id: number;
  taskId: number;
  studentId: number;
  taskStatus: number;
  submitTime?: Date;
  taskScore?: number;
  feedback?: string;
  workId?: number;
}

interface Task {
  id: number;
  taskName: string;
  taskDescription: string;
  taskType: number;
  priority: number;
  startDate?: string;
  endDate: Date;
  taskContent?: string;
  attachments?: string;
  assignments: TaskAssignment[];
}

interface WorkItem {
  id: number;
  title: string;
  description: string;
  type: number;
  status: number;
  createTime: string;
  coverImage?: string;
  screenShotImage?: string;
}

export default function ClassSpace() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [classInfo, setClassInfo] = useState<ClassInfo | null>(null);
  const [studentInfo, setStudentInfo] = useState<StudentInfo | null>(null);
  const [showJoinForm, setShowJoinForm] = useState(false);
  const userId = useSelector((state: RootState) => state.user.userState.userId);
  const avatarUrl = useSelector((state: RootState) => state.user.userState.avatarUrl);
  const [form] = Form.useForm();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [taskLoading, setTaskLoading] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [taskDetailVisible, setTaskDetailVisible] = useState(false);
  const [taskDetailLoading, setTaskDetailLoading] = useState(false);
  const [submitModalVisible, setSubmitModalVisible] = useState(false);
  const [works, setWorks] = useState<WorkItem[]>([]);
  const [selectedWorkId, setSelectedWorkId] = useState<number | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [activeTaskFilter, setActiveTaskFilter] = useState('all');
  const [taskSortBy, setTaskSortBy] = useState('deadline'); // 'deadline' | 'newest' | 'priority'
  const [submissionType, setSubmissionType] = useState<'work' | 'files'>('work');
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const taskListRef = useRef<TaskListRef>(null);
  const notification = GetNotification();

  // 获取学生信息和班级信息
  const fetchStudentInfo = async () => {
    try {
      setLoading(true);
      const response = await studentApi.getStudentInfo(userId);
      if (response.data.code === 200) {
        setStudentInfo(response.data.data);
        setShowJoinForm(false); // 找到学生记录，隐藏加入班级表单

        // 如果有班级ID，获取班级信息
        if (response.data.data.classId) {
          const classResponse = await classApi.getClassInfo(response.data.data.classId);
          if (classResponse.data.code === 200) {
            setClassInfo(classResponse.data.data);
          }
        }
      }
    } catch (error: any) {
      console.error('获取信息失败:', error);

      // 检查是否是404错误（学生记录未找到）
      if (error.response?.status === 404 ||
          error.response?.data?.message?.includes('学生记录未找到') ||
          error.response?.data?.message?.includes('的学生记录未找到')) {
        console.log('学生记录未找到，显示加入班级表单');
        setStudentInfo(null);
        setShowJoinForm(true); // 显示加入班级表单
      } else {
        // 其他错误，可能是网络问题等
        notification.error('获取学生信息失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  // 添加通过邀请码加入的处理函数
  const handleJoinByInviteCode = async (values: { inviteCode: string }) => {
    try {
      const response = await classApi.joinClassByInvite(userId, values.inviteCode);

      if (response.data.code === 200) {
        notification.success('成功加入班级');
        setShowJoinForm(false);
        fetchStudentInfo(); // 刷新信息
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        notification.error(error.response.data.message);
      } else {
        notification.error('加入班级失败');
      }
    }
  };

  // 修改获取任务列表的函数
  const fetchTasks = async () => {
    try {
      setTaskLoading(true);
      const userData = localStorage.getItem('user');
      const user = userData ? JSON.parse(userData) : null;
      const roleId = user?.roleId || 1;

      const response = await taskApi.getTaskList({
        studentId: userId,
        roleId,
        page: 1,
        size: 50
      });
      if (response.data.code === 200) {
        const taskList = response.data.data.list;
        console.log('taskList', taskList);
        // 检查并更新每个任务的状态
        for (const task of taskList) {
          if (task.assignments && task.assignments.length > 0) {
            const assignment = task.assignments[0];
            const now = new Date();
            const startDate = task.startDate ? new Date(task.startDate) : null;
            const endDate = new Date(task.endDate);
            let newStatus = assignment.taskStatus;

            // 如果任务已过期且未完成
            if (now > endDate && assignment.taskStatus !== TaskStatus.COMPLETED) {
              newStatus = TaskStatus.EXPIRED;
            }
            // 如果任务已开始但未完成
            else if (!startDate || now >= startDate) {
              if (assignment.taskStatus === TaskStatus.NOT_STARTED) {
                newStatus = TaskStatus.IN_PROGRESS;
              }
            }

            console.log('newStatus', newStatus);

            // 如果状态需要更新
            if (newStatus !== assignment.taskStatus) {
              try {
                await taskApi.updateTaskStatus({
                  assignmentId: assignment.id,
                  taskStatus: newStatus
                });
              } catch (error) {
                console.error('更新任务状态失败:', error);
              }
            }
          }
        }
        // 重新获取最新的任务列表
        const updatedResponse = await taskApi.getTaskList({
          studentId: userId,
          roleId,
          page: 1,
          size: 50
        });
        if (updatedResponse.data.code === 200) {
          setTasks(updatedResponse.data.data.list);
        }
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
    } finally {
      setTaskLoading(false);
    }
  };

  // 获取任务详情
  const fetchTaskDetail = async (taskId: number) => {
    try {
      setTaskDetailLoading(true);
      const response = await task.getTaskDetail(taskId);
      if (response.data.code === 200) {
        // 获取当前用户的任务详情
        const taskDetail = response.data.data;
        console.log("ta", taskDetail);

        // 过滤assignments，只保留当前用户的分配
        if (taskDetail && taskDetail.assignments && taskDetail.assignments.length > 0) {

          taskDetail.assignments = taskDetail.assignments.filter(
            (assignment: TaskAssignment) => assignment.studentId === userId
          );
        }

        setSelectedTask(taskDetail);
        setTaskDetailVisible(true);
      }
    } catch (error) {
      console.error('获取任务详情失败:', error);
      notification.error('获取任务详情失败');
    } finally {
      setTaskDetailLoading(false);
    }
  };

  // 修改打开提交弹窗的处理函数
  const handleOpenSubmitModal = async () => {
    if (!selectedTask?.assignments?.[0]?.id) return;

    try {
      // 1. 获取当前任务的提交信息
      const submissionResponse = await taskApi.getStudentWork(selectedTask.assignments[0].id);

      if (submissionResponse.data.code === 200) {
        const submission = submissionResponse.data.data;

        // 设置默认提交类型
        if (submission.hasWork) {
          setSubmissionType('work');
          // 如果有提交的作品，自动选中该作品
          setSelectedWorkId(submission.workId);
        } else if (submission.hasFiles) {
          setSubmissionType('files');
        }

        // 如果有提交的文件，设置文件列表
        if (submission.hasFiles && submission.submissionFiles?.length) {
          const files = submission.submissionFiles.map((url: string) => ({
            uid: url,
            name: url.split('/').pop() || 'unknown',
            status: 'done' as const,
            url: url
          }));
          setUploadFiles(files);
        }
      }

      // 2. 获取所有可用作品列表
      const { data: response } = await worksApi.getList({
        type: 1, // 图形化作品
        page: 1,
        size: 100
      });
      console.log("666", response.data);

      if (response.code === 200) {
        setWorks(response.data || []);
      }
    } catch (error) {
      console.error('获取提交信息或作品列表失败:', error);
      notification.error('获取作品列表失败');
    }

    setSubmitModalVisible(true);
  };

  useEffect(() => {
    if (userId) {
      fetchStudentInfo();
      fetchTasks();

      // 检查URL参数中是否包含taskId
      const urlParams = new URLSearchParams(window.location.search);
      const taskId = urlParams.get('taskId');

      if (taskId) {
        // 延迟一段时间确保任务列表加载完成，然后打开对应任务
        const timer = setTimeout(() => {
          if (taskListRef.current?.openTaskById) {
            taskListRef.current.openTaskById(parseInt(taskId, 10));
          }
          // 清除URL参数，避免重复触发
          window.history.replaceState({}, '', '/class-space');
        }, 1500); // 给任务列表足够的时间加载

        return () => clearTimeout(timer);
      }
    }
  }, [userId]);

  if (loading) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      {/* Header Section */}
      <div className="header-bg h-[120px] relative">
        <div className="absolute inset-0">
          <Image
            src="/images/mywork_background.svg"
            alt="背景图片"
            fill
            className="object-cover opacity-30"
            priority
          />
        </div>

        <div className="container mx-auto h-full relative">
          <div className="header-layout">
            <div className="header-avatar-section">
              <div className="header-avatar">
                {avatarUrl ? (
                  <img
                    src={avatarUrl}
                    alt="用户头像"
                    className="w-full h-full object-cover rounded-xl"
                  />
                ) : (
                  <UserOutlined className="text-2xl text-blue-400" />
                )}
              </div>
              <Button
                icon={<ArrowLeftOutlined />}
                className="header-back-btn"
                onClick={() => router.back()}
              >
                返回
              </Button>
            </div>

            <div className="header-title-section">
              <h1 className="header-title">班级空间</h1>
              <p className="header-subtitle">查看您的班级信息</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto custom-scrollbar">
        <main className="container mx-auto px-4 py-4">
          {loading ? (
            <div className="min-h-[300px] flex flex-col items-center justify-center">
              <div className="text-center">
                <Empty description="正在加载学生信息..." />
              </div>
            </div>
          ) : showJoinForm ? (
            <div className="main-content">
              {/* 加入班级区域 */}
              <div className="flex-1 min-w-0">
                <div className="content-area">
                  <div className="content-area-header">
                    <h2 className="content-area-title">加入班级</h2>
                  </div>
                  <div className="content-area-body">
                    <div className="max-w-md mx-auto">
                      <Card className="content-area">
                        <h3 className="text-lg font-semibold mb-4">加入班级</h3>
                        <Tabs
                          defaultActiveKey="invite"
                          items={[
                            {
                              key: 'invite',
                              label: '通过邀请码',
                              children: (
                                <Form
                                  form={form}
                                  layout="vertical"
                                  onFinish={handleJoinByInviteCode}
                                >
                                  <Form.Item
                                    label="邀请码"
                                    name="inviteCode"
                                    rules={[{ required: true, message: '请输入邀请码' }]}
                                  >
                                    <Input placeholder="请输入班级邀请码" />
                                  </Form.Item>

                                  <Form.Item className="mb-0">
                                    <div className="flex justify-end gap-2">
                                      <Button onClick={() => setShowJoinForm(false)}>
                                        取消
                                      </Button>
                                      <Button type="primary" htmlType="submit">
                                        确定
                                      </Button>
                                    </div>
                                  </Form.Item>
                                </Form>
                              ),
                            },
                          ]}
                        />
                      </Card>
                    </div>
                  </div>
                </div>
              </div>

              {/* 赛事任务区域 - 当学生记录不存在时显示 */}
              <div className="w-[320px] shrink-0 ml-4 h-[calc(100vh-180px)]">
                <TaskList
                  userId={userId}
                  onTaskClick={(task) => {
                    fetchTaskDetail(task.id);
                  }}
                  onlyEventsTask={true} // 只显示赛事任务
                  ref={taskListRef}
                />
              </div>
            </div>
          ) : !studentInfo ? (
            // 当学生记录不存在时，显示类似有记录但没有班级的样式
            <div className="main-content">
              {/* 项目区域 */}
              <div className="flex-1 min-w-0">
                <div className="content-area">
                  <div className="content-area-header">
                    <h2 className="content-area-title">我的项目</h2>
                  </div>
                  <div className="content-area-body">
                    <div className="text-center text-gray-500 py-8">
                      <p>当前没有输入学生信息</p>
                      <p className="text-sm mt-2">加入班级后可以查看班级项目和班级任务</p>
                      <Button
                        type="primary"
                        className="mt-4"
                        onClick={() => setShowJoinForm(true)}
                      >
                        加入班级
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* 任务列表区域 - 显示赛事任务 */}
              <div className="w-[320px] shrink-0 ml-4 h-[calc(100vh-180px)]">
                <TaskList
                  userId={userId}
                  onTaskClick={(task) => {
                    fetchTaskDetail(task.id);
                  }}
                  onlyEventsTask={true} // 只显示赛事任务
                  ref={taskListRef}
                />
              </div>
            </div>
          ) : studentInfo ? (
            <div className="main-content">
              {/* 班级项目区域 */}
              <div className="flex-1 min-w-0">
                <div className="content-area">
                  <div className="content-area-header">
                    <h2 className="content-area-title">
                      {studentInfo.classId ? '班级项目' : '我的项目'}
                    </h2>
                  </div>
                  <div className="content-area-body">
                    {studentInfo.classId ? (
                      <ClassProjects classId={studentInfo.classId} />
                    ) : (
                      <div className="text-center text-gray-500 py-8">
                        <p>您还没有加入任何班级</p>
                        <p className="text-sm mt-2">加入班级后可以查看班级项目和班级任务</p>
                        <Button
                          type="primary"
                          className="mt-4"
                          onClick={() => setShowJoinForm(true)}
                        >
                          加入班级
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 任务列表区域 - 无论是否有班级都显示 */}
              <div className="w-[320px] shrink-0 ml-4 h-[calc(100vh-180px)]">
                <TaskList
                  userId={userId}
                  onTaskClick={(task) => {
                    fetchTaskDetail(task.id);
                  }}
                  onlyEventsTask={!studentInfo.classId} // 没有班级时只显示赛事任务
                  ref={taskListRef}
                />
              </div>
            </div>
          ) : null}
        </main>
      </div>

      {/* 任务详情模态框 */}
      <TaskDetailModal
        visible={taskDetailVisible}
        task={selectedTask as ExtendedTask | null}
        loading={taskDetailLoading}
        onClose={() => setTaskDetailVisible(false)}
        onStartTask={() => {
          router.push('/logicleap');
          setTaskDetailVisible(false);
        }}
        onTaskSubmitted={() => {
          fetchTasks();
          fetchTaskDetail(selectedTask!.id);
        }}
        onOpenSubmitModal={handleOpenSubmitModal}
      />

      {/* 提交任务模态框 */}
      <TaskSubmitModal
        visible={submitModalVisible}
        task={selectedTask as ExtendedTask | null}
        onClose={() => {
          setSubmitModalVisible(false);
          setSelectedWorkId(null);
          setUploadFiles([]);
          setSubmissionType('work');
        }}
        onSubmitted={() => {
          // 调用任务列表的刷新方法
          taskListRef.current?.fetchTasks();
          fetchTasks();
          fetchTaskDetail(selectedTask!.id);
        }}
      />
    </div>
  );
} 