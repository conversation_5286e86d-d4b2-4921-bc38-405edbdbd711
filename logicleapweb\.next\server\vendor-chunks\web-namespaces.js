"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/web-namespaces";
exports.ids = ["vendor-chunks/web-namespaces"];
exports.modules = {

/***/ "(ssr)/./node_modules/web-namespaces/index.js":
/*!**********************************************!*\
  !*** ./node_modules/web-namespaces/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   webNamespaces: () => (/* binding */ webNamespaces)\n/* harmony export */ });\n/**\n * Map of web namespaces.\n *\n * @type {Record<string, string>}\n */\nconst webNamespaces = {\n  html: 'http://www.w3.org/1999/xhtml',\n  mathml: 'http://www.w3.org/1998/Math/MathML',\n  svg: 'http://www.w3.org/2000/svg',\n  xlink: 'http://www.w3.org/1999/xlink',\n  xml: 'http://www.w3.org/XML/1998/namespace',\n  xmlns: 'http://www.w3.org/2000/xmlns/'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2ViLW5hbWVzcGFjZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvd2ViLW5hbWVzcGFjZXMvaW5kZXguanM/NWQzYiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIE1hcCBvZiB3ZWIgbmFtZXNwYWNlcy5cbiAqXG4gKiBAdHlwZSB7UmVjb3JkPHN0cmluZywgc3RyaW5nPn1cbiAqL1xuZXhwb3J0IGNvbnN0IHdlYk5hbWVzcGFjZXMgPSB7XG4gIGh0bWw6ICdodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hodG1sJyxcbiAgbWF0aG1sOiAnaHR0cDovL3d3dy53My5vcmcvMTk5OC9NYXRoL01hdGhNTCcsXG4gIHN2ZzogJ2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyxcbiAgeGxpbms6ICdodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rJyxcbiAgeG1sOiAnaHR0cDovL3d3dy53My5vcmcvWE1MLzE5OTgvbmFtZXNwYWNlJyxcbiAgeG1sbnM6ICdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3htbG5zLydcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/web-namespaces/index.js\n");

/***/ })

};
;