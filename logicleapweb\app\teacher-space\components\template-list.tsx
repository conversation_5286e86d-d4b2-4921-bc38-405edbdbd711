'use client'

import { useState, useEffect } from 'react';
import { Button, Tag, Spin, Empty, Dropdown, Modal } from 'antd';
import {
  PlusOutlined,
  BlockOutlined,
  EditOutlined,
  DeleteOutlined,
  EllipsisOutlined,
  EyeOutlined,
  ArrowLeftOutlined,
  FolderOutlined,
  A<PERSON>toreOutlined,
  StarOutlined,
  ExclamationCircleFilled
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { getRoleTemplateList, deleteTemplate, getTemplateInfo, getOfficialTemplates, addUserJoinRole, getUserCurrentTemplate, getStudentTemplates } from '../../../lib/api/role';
import { roleTemplateFolderApi } from '../../../lib/api/role-template-folder';
import PermissionTemplateModal from '@/components/permission-template-modal';
import PermissionModal from '@/components/permission-modal';
import { GetNotification } from 'logic-common/dist/components/Notification';
import './template-list.css';

interface TemplateDisplay {
  id: number;
  templateName: string;
  templateDescription: string;
  createTime: string;
  isDefault: boolean;
  lastModified?: string;
  isOfficial?: boolean;
  userId?: number;
}

interface TemplateListProps {
  userId: number;
  roleId: number;
  teacherTemplate?: any;
  studentTemplateUsage: { [key: number]: number };
  onTemplateUsageClick: (e: React.MouseEvent, template: TemplateDisplay) => void;
  onTemplateUseSuccess?: () => void;
}

interface MyTemplateListProps {
  templates: TemplateDisplay[];
  teacherTemplate: any;
  studentTemplateUsage: { [key: number]: number };
  onTemplateClick: (template: TemplateDisplay) => void;
  onEditTemplate: (template: TemplateDisplay) => void;
  onDeleteTemplate: (template: TemplateDisplay) => void;
  onTemplateUsageClick: (e: React.MouseEvent, template: TemplateDisplay) => void;
  onUseTemplate: (templateId: number) => Promise<void>;
}

interface OfficialTemplateListProps {
  templates: TemplateDisplay[];
  teacherTemplate: any;
  studentTemplateUsage: { [key: number]: number };
  onTemplateClick: (template: TemplateDisplay) => void;
  onTemplateUsageClick: (e: React.MouseEvent, template: TemplateDisplay) => void;
  setIsTemplateModalVisible: (visible: boolean) => void;
  setSelectedTemplateId: (id: number | null) => void;
  setIsPermissionModalVisible?: (visible: boolean) => void;
  onUseTemplate: (templateId: number) => Promise<void>;
}

interface TemplateCardProps {
  template: TemplateDisplay;
  teacherTemplate: any;
  studentTemplateUsage: { [key: number]: number };
  onTemplateClick: (template: TemplateDisplay) => void;
  onEditTemplate?: (template: TemplateDisplay) => void;
  onDeleteTemplate?: (template: TemplateDisplay) => void;
  onTemplateUsageClick: (e: React.MouseEvent, template: TemplateDisplay) => void;
  isOfficial: boolean;
  setIsTemplateModalVisible?: (visible: boolean) => void;
  setSelectedTemplateId?: (id: number | null) => void;
  setIsPermissionModalVisible?: (visible: boolean) => void;
  onUseTemplate: (templateId: number) => Promise<void>;
}

export default function TemplateList({
  userId,
  roleId,
  teacherTemplate,
  studentTemplateUsage,
  onTemplateUsageClick,
  onTemplateUseSuccess
}: TemplateListProps) {
  const notification = GetNotification();
  
  const [activeTab, setActiveTab] = useState('official');
  const [displayTemplates, setDisplayTemplates] = useState<TemplateDisplay[]>([]);
  const [officialTemplates, setOfficialTemplates] = useState<TemplateDisplay[]>([]);
  const [templateLoading, setTemplateLoading] = useState(false);
  const [loadingOfficialTemplates, setLoadingOfficialTemplates] = useState(false);
  const [isTemplateModalVisible, setIsTemplateModalVisible] = useState(false);
  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(null);
  const [isEditTemplateModalVisible, setIsEditTemplateModalVisible] = useState(false);
  const [editingTemplateId, setEditingTemplateId] = useState<number | null>(null);
  const [localTeacherTemplate, setLocalTeacherTemplate] = useState<any>(null);
  const [localStudentTemplateUsage, setLocalStudentTemplateUsage] = useState<{ [key: number]: number }>({});
  const [currentView, setCurrentView] = useState<'folders' | 'templates'>('folders');
  const [currentFolder, setCurrentFolder] = useState<any>(null);
  const [folders, setFolders] = useState<any[]>([]);
  const [loadingFolderTemplates, setLoadingFolderTemplates] = useState(false);
  const [specialTemplate, setSpecialTemplate] = useState<TemplateDisplay | null>(null);
  const [loadingSpecialTemplate, setLoadingSpecialTemplate] = useState(false);

  // 获取展示用的权限模板列表
  const fetchDisplayTemplates = async () => {
    setTemplateLoading(true);
    try {
      const response = await getRoleTemplateList(userId);
      if (response.data.code === 200) {
        setDisplayTemplates(response.data.data);
      }
    } catch (error) {
      console.error('获取权限模板列表失败:', error);
    } finally {
      setTemplateLoading(false);
    }
  };

  // 获取官方模板列表
  const fetchOfficialTemplates = async () => {
    setLoadingOfficialTemplates(true);
    try {
      const response = await getOfficialTemplates();
      if (response.data.code === 200) {
        setOfficialTemplates(response.data.data);
      }
    } catch (error) {
      console.error('获取官方模板列表失败:', error);
      // notification.error('请检查网络以及登录状态');
    } finally {
      setLoadingOfficialTemplates(false);
    }
  };

  // 获取特殊模板（userId为-1的模板）
  const fetchSpecialTemplate = async () => {
    if (activeTab !== 'official') return;

    setLoadingSpecialTemplate(true);
    try {
      const response = await getOfficialTemplates();
      if (response.data.code === 200) {
        // 查找userId为-1的模板
        const specialTemplateData = response.data.data.find((template: any) => template.userId === -1);

        if (specialTemplateData) {
          // 获取模板的详细信息
          const templateInfoResponse = await getTemplateInfo(specialTemplateData.id);
          console.log('获取特殊模板响应1:', templateInfoResponse);
          if (templateInfoResponse.data.code === 200) {
            const templateData = templateInfoResponse.data.data;
            setSpecialTemplate({
              id: templateData.id,
              templateName: templateData.templateName,
              templateDescription: templateData.templateDescription || '',
              createTime: templateData.createTime,
              isDefault: false,
              isOfficial: true,
              lastModified: templateData.updateTime || templateData.createTime,
              userId: -1 // 明确设置userId为-1
            });
          }
        }
      }
    } catch (error) {
      console.error('获取特殊模板失败:', error);
    } finally {
      setLoadingSpecialTemplate(false);
    }
  };

  // 添加获取模板使用情况的函数
  const fetchTemplateUsage = async () => {
    try {
      // 获取教师使用的模板
      const teacherResponse = await getUserCurrentTemplate(userId);
      if (teacherResponse.data.code === 200) {
        setLocalTeacherTemplate(teacherResponse.data.data);
      }

      // 获取学生使用的模板
      const studentResponse = await getStudentTemplates({
        teacherId: userId,
        page: 1,
        size: 200
      });

      console.log('学生使用的模板:', studentResponse);
      if (studentResponse.data.code === 200) {
        // 统计每个模板被使用的次数
        const usage: { [key: number]: number } = {};
        studentResponse.data.data.list.forEach((item: any) => {
          if (item.templateId) {
            usage[item.templateId] = (usage[item.templateId] || 0) + 1;
          }
        });
        setLocalStudentTemplateUsage(usage);
        // console.log('模板使用情况:', usage); // 添加日志以便调试
      }
    } catch (error) {
      console.error('获取模板使用情况失败:', error);
    }
  };

  // 获取文件夹列表
  const fetchFolders = async () => {
    try {
      const res = await roleTemplateFolderApi.getFolderList();
      console.log('获取文件夹列表响应:', res);
      if (res.data.data.code === 200) {
        // 获取每个文件夹的模板数量
        const foldersWithCount = await Promise.all(
          res.data.data.data.map(async (folder: any) => {
            const templatesRes = await roleTemplateFolderApi.getFolderTemplates(folder.id);
            return {
              ...folder,
              templateCount: templatesRes.data.data.code === 200 ? templatesRes.data.data.data.length : 0
            };
          })
        );
        setFolders(foldersWithCount);
      }
    } catch (error) {
      console.error('获取文件夹列表失败:', error);
    }
  };

  // 添加 useEffect 来获取模板使用情况和特殊模板
  useEffect(() => {
    fetchDisplayTemplates();
    fetchOfficialTemplates();
    fetchTemplateUsage();
  }, [roleId, userId]);

  useEffect(() => {
    if (activeTab === 'official') {
      fetchFolders();
      fetchSpecialTemplate();
    }
  }, [activeTab]);

  // 处理编辑模板
  const handleEditTemplate = async (template: TemplateDisplay) => {
    try {
      const response = await getTemplateInfo(template.id);
      console.log('获取模板详情响应2:', response);
      if (response.data.code === 200) {
        setEditingTemplateId(template.id);
        setIsEditTemplateModalVisible(true);
      } else {
        notification.error(response.data.message || '获取模板详情失败');
      }
    } catch (error) {
      console.error('获取模板详情失败:', error);
      notification.error('获取模板详情失败');
    }
  };

  // 处理删除模板
  const handleDeleteTemplate = async (template: TemplateDisplay) => {
    try {
      await deleteTemplate(template.id);
      notification.success('删除模板成功');
      fetchDisplayTemplates();
    } catch (error) {
      console.error('删除模板失败:', error);
      notification.error('删除模板失败');
    }
  };

  // 处理模板点击
  const handleTemplateClick = async (template: TemplateDisplay) => {
    if (template.isOfficial) {
      // 官方模板点击时，打开查看权限模态框
      setSelectedTemplateId(template.id);
      setIsPermissionModalVisible(true);
    } else {
      // 我的模板点击时，直接进入编辑状态
      try {
        const response = await getTemplateInfo(template.id);
        console.log('获取模板详情响应3:', response);
        if (response.data.code === 200) {
          setEditingTemplateId(template.id);
          setIsEditTemplateModalVisible(true);
        } else {
          notification.error(response.data.message || '获取模板详情失败');
        }
      } catch (error) {
        console.error('获取模板详情失败:', error);
        notification.error('获取模板详情失败');
      }
    }
  };

  // 修改 handleUseTemplate 函数，在成功后刷新使用情况
  const handleUseTemplate = async (templateId: number) => {
    try {
      const response = await addUserJoinRole({
        userId: userId,
        roleId: roleId || 2,
        templateId: templateId
      });

      if (response.data.code === 200) {
        notification.success('模板应用成功');
        setIsPermissionModalVisible(false);
        setSelectedTemplateId(null);

        // 添加延时，确保后端数据更新完成，然后再刷新使用情况
        setTimeout(() => {
          // 刷新模板使用情况
          fetchTemplateUsage();
          // 设置本地状态以立即显示"当前使用"标签
          setLocalTeacherTemplate({ templateId: templateId });
          onTemplateUseSuccess?.();
        }, 500);
      } else {
        notification.error(response.data.message || '模板应用失败');
      }
    } catch (error) {
      console.error('应用模板失败:', error);
      notification.error('应用模板失败');
    }
  };

  // 返回文件夹列表
  const handleBackToFolders = () => {
    setCurrentView('folders');
    setCurrentFolder(null);
    setOfficialTemplates([]); // 清空模板列表数据
  };

  // 处理文件夹点击
  const handleFolderClick = async (folder: any) => {
    setCurrentFolder(folder);
    setCurrentView('templates');
    setOfficialTemplates([]); // 先清空之前的数据
    setLoadingFolderTemplates(true); // 开始加载时显示加载动画

    try {
      const folderRes = await roleTemplateFolderApi.getFolderTemplates(folder.id);
      // console.log('获取文件夹模板响应:', folderRes);
      if (folderRes.data.data.code === 200) {
        const templatePromises = folderRes.data.data.data.map((item: { id: number }) =>
          roleTemplateFolderApi.getTemplateInfo(item.id)
        );
        console.log('获取模板详情响应4:', templatePromises);
        const templateResults = await Promise.all(templatePromises);
        const templates = templateResults
          .filter(res => res.data.code === 200)
          .map(res => {
            const templateData = res.data.data;
            return {
              id: templateData.id,
              templateName: templateData.templateName,
              templateDescription: templateData.templateDescription || '',
              createTime: templateData.createTime,
              isDefault: false,
              isOfficial: true,
              lastModified: templateData.updateTime || templateData.createTime,
              roleId: templateData.roleId,
              userId: templateData.userId,
              status: templateData.status
            };
          });

        setOfficialTemplates(templates);
        // 获取文件夹模板后重新获取一次使用情况，确保官方模板也能获取到使用数据
        fetchTemplateUsage();
      }
    } catch (error) {
      console.error('获取文件夹模板失败2:', error);
    } finally {
      setLoadingFolderTemplates(false); // 无论成功失败都关闭加载动画
    }
  };

  // 文件夹列表组件
  const FolderList = () => {
    return (
      <div className="grid grid-cols-1 gap-3">
        {/* 特殊模板（userId为-1的模板）置顶显示 */}
        {specialTemplate && (
          <div
            key={`special-${specialTemplate.id}`}
            className="bg-white p-4 rounded-xl cursor-pointer hover:shadow-md transition-all group"
            onClick={() => handleUseTemplate(specialTemplate.id)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-xl bg-amber-50 flex items-center justify-center group-hover:bg-amber-100 transition-colors">
                  <StarOutlined className="text-amber-500 text-xl" />
                </div>
                <div>
                  <h3 className="text-[14px] font-medium text-gray-800 group-hover:text-blue-600 transition-colors">
                    {specialTemplate.templateName}
                    <Tag color="gold" className="ml-2 rounded-full">推荐</Tag>
                    {(localTeacherTemplate?.templateId === specialTemplate.id || teacherTemplate?.templateId === specialTemplate.id) && (
                      <Tag color="purple" className="ml-2 rounded-full">当前使用</Tag>
                    )}
                  </h3>
                  <p className="text-xs text-gray-400 mt-0.5">
                    推荐使用的模板
                  </p>
                </div>
              </div>
            </div>

            {/* 添加装饰线、学生使用数量和使用模板按钮 */}
            <div className="mt-4 pt-4 border-t border-gray-100 flex justify-between items-center">
              {/* 添加使用人数显示 */}
              <div className="text-xs text-gray-500">
                {localStudentTemplateUsage[specialTemplate.id] ? (
                  <span
                    className="cursor-pointer hover:text-blue-500 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      onTemplateUsageClick(e, specialTemplate);
                    }}
                  >
                    {localStudentTemplateUsage[specialTemplate.id]} 名学生正在使用
                  </span>
                ) : (
                  <span>暂无学生使用</span>
                )}
              </div>

              <Button
                type="primary"
                size="small"
                className="bg-blue-500 rounded-full px-4 hover:bg-blue-600"
                onClick={(e) => {
                  e.stopPropagation();
                  handleUseTemplate(specialTemplate.id);
                }}
              >
                使用此模板
              </Button>
            </div>
          </div>
        )}

        {/* 文件夹列表 */}
        {folders.map(folder => (
          <div
            key={folder.id}
            className="bg-white p-4 rounded-xl cursor-pointer hover:shadow-md transition-all group h-[100px]"
            onClick={() => handleFolderClick(folder)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-xl bg-blue-50 flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                  <FolderOutlined className="text-blue-500 text-xl" />
                </div>
                <div>
                  <h3 className="text-[14px] font-medium text-gray-800 group-hover:text-blue-600 transition-colors">
                    {folder.folderName}
                    <Tag color="gold" className="ml-2 rounded-full">官方</Tag>
                  </h3>
                  <p className="text-xs text-gray-400 mt-0.5">
                    {folder.templateCount || 0} 个模板
                  </p>
                </div>
              </div>

            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <>
      <div className="bg-[#F6F7F9] rounded-xl border border-[rgb(229,231,235)] h-[calc(100vh-160px)] flex flex-col sticky top-4 right-0">
        <div className="flex flex-wrap items-center gap-3 p-4 border-b border-gray-200 flex-none">
          <div className="flex flex-wrap items-center gap-3 flex-1 min-w-[200px]">
            <Button
              type={activeTab === 'my' ? 'primary' : 'default'}
              onClick={() => setActiveTab('my')}
              className="rounded-full min-w-[80px] whitespace-nowrap"
            >
              我的模板
            </Button>
            <Button
              type={activeTab === 'official' ? 'primary' : 'default'}
              onClick={() => {
                setActiveTab('official');
                fetchTemplateUsage();
              }}
              className="rounded-full min-w-[80px] whitespace-nowrap"
            >
              官方模板
            </Button>
          </div>
          {activeTab === 'my' && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsTemplateModalVisible(true)}
              className="rounded-full bg-blue-500 hover:bg-blue-600 border-none min-w-[120px] whitespace-nowrap"
            >
              创建模板
            </Button>
          )}
        </div>

        {/* 列表内容区域 */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto px-4 py-4">
            {templateLoading || loadingOfficialTemplates ? (
              <div className="flex justify-center items-center h-64">
                <Spin />
              </div>
            ) : (
              <div className="space-y-3">
                {activeTab === 'my' ? (
                  <MyTemplateList
                    templates={displayTemplates}
                    teacherTemplate={localTeacherTemplate || teacherTemplate}
                    studentTemplateUsage={localStudentTemplateUsage}
                    onTemplateClick={handleTemplateClick}
                    onEditTemplate={handleEditTemplate}
                    onDeleteTemplate={handleDeleteTemplate}
                    onTemplateUsageClick={onTemplateUsageClick}
                    onUseTemplate={handleUseTemplate}
                  />
                ) : (
                  <>
                    {currentView === 'folders' ? (
                      <FolderList />
                    ) : (
                      <div>
                        <div className="flex items-center mb-4">
                          <Button
                            icon={<ArrowLeftOutlined />}
                            onClick={handleBackToFolders}
                            className="mr-2"
                          >
                            返回
                          </Button>
                          <span className="text-lg font-medium">
                            {currentFolder?.folderName}
                          </span>
                        </div>
                        {loadingFolderTemplates ? (
                          <div className="flex justify-center items-center h-64">
                            <Spin />
                          </div>
                        ) : (
                          <OfficialTemplateList
                            templates={officialTemplates}
                            teacherTemplate={localTeacherTemplate || teacherTemplate}
                            studentTemplateUsage={localStudentTemplateUsage}
                            onTemplateClick={handleTemplateClick}
                            onTemplateUsageClick={onTemplateUsageClick}
                            setIsTemplateModalVisible={setIsTemplateModalVisible}
                            setSelectedTemplateId={setSelectedTemplateId}
                            setIsPermissionModalVisible={setIsPermissionModalVisible}
                            onUseTemplate={handleUseTemplate}
                          />
                        )}
                      </div>
                    )}
                  </>
                )}
              </div>
            )}
          </div>
        </div>

        {/* 模态框组件 */}
        {isTemplateModalVisible && (
          <PermissionTemplateModal
            userId={userId}
            roleId={roleId}
            visible={isTemplateModalVisible}
            onClose={() => {
              setIsTemplateModalVisible(false);
              setSelectedTemplateId(null);
            }}
            onSuccess={() => {
              fetchDisplayTemplates();
              setIsTemplateModalVisible(false);
            }}
            sourceTemplateId={selectedTemplateId}
          />
        )}

        {isPermissionModalVisible && selectedTemplateId && (
          <PermissionModal
            userId={0}
            visible={isPermissionModalVisible}
            onClose={() => {
              setIsPermissionModalVisible(false);
              setSelectedTemplateId(null);
            }}
            templateId={selectedTemplateId}
            readOnly={true}
            onUseTemplate={handleUseTemplate}
            onEditTemplate={(templateId) => {
              setIsPermissionModalVisible(false);
              setEditingTemplateId(templateId);
              setIsEditTemplateModalVisible(true);
            }}
          />
        )}

        {isEditTemplateModalVisible && editingTemplateId && (
          <PermissionTemplateModal
            userId={userId}
            roleId={roleId}
            visible={isEditTemplateModalVisible}
            onClose={() => {
              setIsEditTemplateModalVisible(false);
              setEditingTemplateId(null);
            }}
            onSuccess={() => {
              fetchDisplayTemplates();
              setIsEditTemplateModalVisible(false);
              setEditingTemplateId(null);
            }}
            templateId={editingTemplateId}
            onDeleteTemplate={(templateId) => {
              setIsEditTemplateModalVisible(false);
              setEditingTemplateId(null);
              // 查找要删除的模板信息
              const templateToDelete = displayTemplates.find(t => t.id === templateId);
              if (templateToDelete) {
                handleDeleteTemplate(templateToDelete);
              }
            }}
            onUseTemplate={handleUseTemplate}
          />
        )}
      </div>
    </>

  );
}

// 自定义模板列表组件
function MyTemplateList({ templates, teacherTemplate, studentTemplateUsage, onTemplateClick, onEditTemplate, onDeleteTemplate, onTemplateUsageClick, onUseTemplate }: MyTemplateListProps) {
  if (templates.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 bg-white rounded-xl">
        <div className="w-16 h-16 rounded-full bg-gray-50 flex items-center justify-center mb-4">
          <AppstoreOutlined className="text-gray-300 text-2xl" />
        </div>
        <p className="text-gray-500 mb-2">当前还没有自定义模板，点击上方开始创建</p>
      </div>
    );
  }

  return templates.map(template => (
    <TemplateCard
      key={template.id}
      template={template}
      teacherTemplate={teacherTemplate}
      studentTemplateUsage={studentTemplateUsage}
      onTemplateClick={onTemplateClick}
      onEditTemplate={onEditTemplate}
      onDeleteTemplate={onDeleteTemplate}
      onTemplateUsageClick={onTemplateUsageClick}
      isOfficial={false}
      onUseTemplate={onUseTemplate}
    />
  ));
}

// 官方模板列表组件
function OfficialTemplateList({ templates, teacherTemplate, studentTemplateUsage, onTemplateClick, onTemplateUsageClick, setIsTemplateModalVisible, setSelectedTemplateId, setIsPermissionModalVisible, onUseTemplate }: OfficialTemplateListProps) {
  if (templates.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 bg-white rounded-xl">
        <div className="w-16 h-16 rounded-full bg-gray-50 flex items-center justify-center mb-4">
          <BlockOutlined className="text-gray-300 text-2xl" />
        </div>
        <p className="text-gray-500 mb-2">当前文件夹暂无模板</p>
      </div>
    );
  }

  return (
    <>
      {templates.map(template => (
        <TemplateCard
          key={template.id}
          template={template}
          teacherTemplate={teacherTemplate}
          studentTemplateUsage={studentTemplateUsage}
          onTemplateClick={onTemplateClick}
          onTemplateUsageClick={onTemplateUsageClick}
          isOfficial={true}
          setIsTemplateModalVisible={setIsTemplateModalVisible}
          setSelectedTemplateId={setSelectedTemplateId}
          setIsPermissionModalVisible={setIsPermissionModalVisible}
          onUseTemplate={onUseTemplate}
        />
      ))}
    </>
  );
}

// 模板卡片组件
function TemplateCard({ template, teacherTemplate, studentTemplateUsage, onTemplateClick, onEditTemplate, onDeleteTemplate, onTemplateUsageClick, isOfficial, setIsTemplateModalVisible, setSelectedTemplateId, setIsPermissionModalVisible, onUseTemplate }: TemplateCardProps) {
  if (isOfficial) {
    // 官方模板的新样式
    return (
      <div
        className="bg-white p-6 rounded-xl cursor-pointer hover:shadow-md transition-all group mb-3"
        onClick={() => {
          if (template.userId === -1) {
            // 特殊模板，直接使用
            onUseTemplate(template.id);
          } else {
            // 普通官方模板，点击卡片显示弹窗
            onTemplateClick(template);
          }
        }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 flex-1 min-w-0">
            <div className="w-12 h-12 rounded-xl bg-blue-50 flex-shrink-0 flex items-center justify-center group-hover:bg-blue-100 transition-colors">
              <BlockOutlined className="text-amber-500 text-xl" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <span className="text-[14px] font-medium text-gray-800 group-hover:text-blue-600 transition-colors truncate max-w-[calc(100%-70px)]">
                  {template.templateName}
                </span>
                <Tag color="gold" className="flex-shrink-0 rounded-full">官方</Tag>
                {teacherTemplate?.templateId === template.id && (
                  <Tag color="purple" className="rounded-full">当前使用</Tag>
                )}
              </div>
              {/* <p className="text-sm text-gray-400 line-clamp-2 mt-1 hidden sm:block">
                {template.templateDescription || '暂无描述'}
              </p> */}
              <p className="text-xs text-gray-500 mt-1">
                创建于 {dayjs(template.createTime).format('YYYY-MM-DD')}
              </p>
            </div>
          </div>
        </div>

        {/* 装饰线和使用人数显示 - 适用于所有官方模板 */}
        <div className="mt-4 pt-4 border-t border-gray-100 flex justify-between items-center">
          {/* 添加使用人数显示 */}
          <div className="text-xs text-gray-500">
            {studentTemplateUsage[template.id] ? (
              <span
                className="cursor-pointer hover:text-blue-500 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  onTemplateUsageClick(e, template);
                }}
              >
                {studentTemplateUsage[template.id]} 名学生正在使用
              </span>
            ) : (
              <span>暂无学生使用</span>
            )}
          </div>

          {/* 使用此模板按钮 - 适用于所有官方模板 */}
          <Button
            type="primary"
            size="small"
            className="bg-blue-500 rounded-full px-4 hover:bg-blue-600"
            onClick={(e) => {
              e.stopPropagation();
              onUseTemplate(template.id);
            }}
          >
            使用此模板
          </Button>
        </div>
      </div>
    );
  }

  // 我的模板的样式 - 移除三点菜单
  return (
    <div
      className="bg-white rounded-xl p-6 cursor-pointer hover:shadow-md transition-all group mb-3"
      onClick={() => onTemplateClick(template)}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-4 flex-1 min-w-0">
          <div className="w-12 h-12 rounded-xl bg-blue-50 flex-shrink-0 flex items-center justify-center group-hover:bg-blue-100 transition-colors">
            <AppstoreOutlined className="text-blue-500 text-xl" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center flex-wrap gap-2">
              <span className="text-[14px] font-medium text-gray-800 group-hover:text-blue-600 transition-colors truncate">
                {template.templateName}
              </span>
              <div className="flex-shrink-0 flex gap-2 flex-wrap">
                <Tag color="blue" className="rounded-full">自定义</Tag>
                {template.isDefault && (
                  <Tag color="blue" className="rounded-full">默认</Tag>
                )}
                {teacherTemplate?.templateId === template.id && (
                  <Tag color="purple" className="rounded-full">当前使用</Tag>
                )}
              </div>
            </div>
            <div className="text-xs text-gray-400 mt-0.5">
              创建于 {dayjs(template.createTime).format('YYYY-MM-DD')}
            </div>
          </div>
        </div>
      </div>

      {/* 添加装饰线和使用人数显示 */}
      <div className="mt-4 pt-4 border-t border-gray-100 flex justify-between items-center">
        {/* 添加使用人数显示 */}
        <div className="text-xs text-gray-500">
          {studentTemplateUsage[template.id] ? (
            <div
              className="cursor-pointer hover:text-blue-500 transition-colors"
              onClick={(e) => onTemplateUsageClick(e, template)}
            >
              <span>{studentTemplateUsage[template.id]} 名学生正在使用</span>
            </div>
          ) : (
            <div className="text-gray-500">
              <span>暂无学生使用</span>
            </div>
          )}
        </div>

        {/* 使用模板按钮 */}
        <Button
          type="primary"
          size="small"
          className="bg-blue-500 rounded-full px-4 hover:bg-blue-600"
          onClick={(e) => {
            e.stopPropagation();
            onUseTemplate(template.id);
          }}
        >
          使用此模板
        </Button>
      </div>
    </div>
  );
} 