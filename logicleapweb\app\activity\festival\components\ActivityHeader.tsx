'use client'

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import { Share2, Spark<PERSON> } from 'lucide-react'
import { selectUserState, setUser } from '@/lib/store'
import { useSelector, useDispatch } from 'react-redux'

import ActivityMediaGallery from './ActivityMediaGallery'
import { Modal } from 'antd'
import LoginDialog from '../../../../components/login-dialog'
import { activityRegistrationApi, ActivityType } from '@/lib/api/activity'
import { eventsTaskApi } from '@/lib/api/activity/events-task'
import { userApi } from '@/lib/api/user'
import { GetNotification } from 'logic-common/dist/components/Notification'
import { useRouter, usePathname } from 'next/navigation'

// 临时实现formatDate函数，以解决模块导入问题
const formatDate = (dateString: string): string => {
  // 在服务端渲染时，直接返回原始字符串，避免水合错误
  if (typeof window === 'undefined') {
    return dateString;
  }

  const date = new Date(dateString);

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return dateString; // 如果无效，返回原始字符串
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

interface ActivityHeaderProps {
  title: string
  startTime: string
  endTime: string
  bannerImage: string
  activityId: number | string
  organizer?: string
  activityType?: number // 添加活动类型属性
  onRefreshWorks?: () => void
  // 新增媒体字段
  promotionImage?: string
  backgroundImage?: string
  galleryImages?: string
  attachmentFiles?: string
}



const ActivityHeader: React.FC<ActivityHeaderProps> = ({
  title: initialTitle,
  startTime: initialStartTime,
  endTime: initialEndTime,
  bannerImage: initialBannerImage,
  activityId,
  organizer: initialOrganizer = '',
  activityType = ActivityType.WORK, // 默认为作品活动
  onRefreshWorks,
  // 媒体字段
  promotionImage,
  backgroundImage,
  galleryImages,
  attachmentFiles
}) => {
  console.log('🔍 [DEBUG] ActivityHeader 组件渲染', { activityId, initialTitle });


  const notification = GetNotification();
  const router = useRouter();
  const pathname = usePathname();

  // 显示用的标题和时间
  const title = initialTitle;
  const startTime = initialStartTime;
  const endTime = initialEndTime;
  const bannerImage = initialBannerImage;
  const organizer = initialOrganizer;

  // 获取当前用户信息和dispatch
  const userState = useSelector(selectUserState)
  const dispatch = useDispatch()

  // 同步用户状态的函数 - 增强版，从API获取最新用户信息
  const syncUserState = async () => {
    try {
      console.log('🔍 [DEBUG] 开始同步用户状态');
      const localUser = localStorage.getItem('user');
      if (localUser) {
        const userData = JSON.parse(localUser);
        console.log('🔍 [DEBUG] localStorage 中的用户数据', userData);
        console.log('🔍 [DEBUG] Redux 中的用户数据', userState);

        // 如果用户已登录但phone为空，尝试从API获取最新用户信息
        if (userData.isLoggedIn && userData.userId && (!userData.phone || userData.phone.trim() === '')) {
          console.log('🔍 [DEBUG] 检测到用户phone为空，从API获取最新用户信息');
          try {
            const response = await userApi.getUserInfo(userData.userId);
            if (response.code === 200 && response.data) {
              console.log('🔍 [DEBUG] 从API获取到最新用户信息', response.data);

              // 构建更新后的用户数据
              const updatedUserData = {
                ...userData,
                phone: response.data.phone || '',
                nickName: response.data.nickName || userData.nickName,
                avatarUrl: response.data.avatarUrl || userData.avatarUrl,
                gender: response.data.gender || userData.gender,
                roleId: response.data.roleId || userData.roleId
              };

              console.log('🔍 [DEBUG] 更新用户数据', {
                oldPhone: userData.phone,
                newPhone: updatedUserData.phone,
                updated: updatedUserData
              });

              // 更新localStorage和Redux
              localStorage.setItem('user', JSON.stringify(updatedUserData));
              dispatch(setUser(updatedUserData));
              return;
            }
          } catch (error) {
            console.error('🔍 [DEBUG] 从API获取用户信息失败:', error);
          }
        }

        // 检查关键字段是否有变化
        const hasPhoneChanged = userData.phone !== userState?.phone;
        const hasRoleChanged = userData.roleId !== userState?.roleId;
        const hasLoginStatusChanged = userData.isLoggedIn !== userState?.isLoggedIn;

        console.log('🔍 [DEBUG] 用户状态变化检查', {
          phoneChanged: hasPhoneChanged,
          roleChanged: hasRoleChanged,
          loginStatusChanged: hasLoginStatusChanged,
          oldPhone: userState?.phone,
          newPhone: userData.phone,
          oldRole: userState?.roleId,
          newRole: userData.roleId
        });

        // 如果localStorage中的用户状态与Redux中的不一致，说明需要更新
        if (hasPhoneChanged || hasRoleChanged || hasLoginStatusChanged) {
          console.log('🔍 [DEBUG] 检测到用户状态变化，同步Redux状态');
          dispatch(setUser(userData));
        } else {
          console.log('🔍 [DEBUG] 用户状态无变化，无需同步');
        }
      } else {
        console.log('🔍 [DEBUG] localStorage 中无用户数据');
      }
    } catch (error) {
      console.error('🔍 [DEBUG] 检查用户状态失败:', error);
    }
  };

  // 监听页面焦点变化和路由变化，重新检查用户状态
  useEffect(() => {
    // 页面加载时立即检查一次
    syncUserState().catch(console.error);

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        syncUserState().catch(console.error);
      }
    };

    const handleFocus = () => {
      syncUserState().catch(console.error);
    };

    // 添加多种事件监听确保状态同步
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [userState?.phone, dispatch]);

  // 工具函数：检查用户是否为学生身份
  const checkIsStudent = () => {
    try {
      const userStr = localStorage.getItem('user');
      const localUser = userStr ? JSON.parse(userStr) : null;
      const userRoleId = localUser?.roleId || userState?.roleId;
      return userRoleId === 1; // 学生身份的roleId为1
    } catch (error) {
      console.error('解析localStorage中的用户信息失败:', error);
      return false;
    }
  };



  // 工具函数：显示身份验证失败提示
  const showStudentOnlyModal = () => {
    Modal.confirm({
      title: '身份验证',
      content: '抱歉，只有学生身份的用户才能参加此活动报名。如果您是学生，请联系管理员更新您的身份信息。',
      okText: '我知道了',
      cancelText: '联系管理员',
      onCancel: () => {
        notification.info('如需帮助，请联系客服：************');
      }
    });
  };





  // 登录和报名相关状态
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [registrationStatus, setRegistrationStatus] = useState<number | null>(null); // 报名状态：0-已取消 1-已报名 2-已审核通过 3-已拒绝 4-评审中 5-已获奖 6-审核中







  // 添加客户端检测来解决水合错误
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // 页面加载时检查报名状态
  useEffect(() => {
    const checkRegistrationStatus = async () => {
      console.log('🔍 [DEBUG] 检查报名状态 - useEffect 触发', {
        isLoggedIn: userState.isLoggedIn,
        roleId: userState.roleId,
        phone: userState.phone,
        isClient,
        activityId
      });

      // 只有在用户已登录且是学生身份时才检查报名状态
      const isStudent = checkIsStudent();
      if (!userState.isLoggedIn || !isStudent) {
        console.log('🔍 [DEBUG] 用户未登录或不是学生身份，重置报名状态', {
          isLoggedIn: userState.isLoggedIn,
          isStudent
        });
        // 如果用户未登录或不是学生，重置状态
        setHasSubmitted(false);
        setRegistrationStatus(null);
        return;
      }

      try {
        console.log('🔍 [DEBUG] 开始检查报名状态 API 调用');
        setIsChecking(true);
        const response = await activityRegistrationApi.checkRegistration(Number(activityId));
        console.log('🔍 [DEBUG] 页面加载检查报名状态响应:', response);

        if (response?.data?.code === 200 && response.data?.data) {
          const { submitted, submit } = response.data.data;
          console.log('🔍 [DEBUG] 报名状态检查结果', { submitted, submit });
          setHasSubmitted(submitted);

          if (submit && submit.status !== undefined) {
            setRegistrationStatus(submit.status);
          } else {
            setRegistrationStatus(null);
          }
        } else {
          console.log('🔍 [DEBUG] 报名状态响应不符合预期，设置为未报名状态');
          // 如果响应不符合预期，设置为未报名状态
          setHasSubmitted(false);
          setRegistrationStatus(null);
        }
      } catch (error) {
        console.error('🔍 [DEBUG] 检查报名状态失败:', error);
        // 出错时设置为未报名状态
        setHasSubmitted(false);
        setRegistrationStatus(null);
      } finally {
        setIsChecking(false);
      }
    };

    // 只在客户端执行，避免服务端渲染问题
    if (isClient) {
      console.log('🔍 [DEBUG] 客户端环境，执行报名状态检查');
      checkRegistrationStatus();
    } else {
      console.log('🔍 [DEBUG] 非客户端环境，跳过报名状态检查');
    }
  }, [activityId, userState.isLoggedIn, userState.roleId, userState.phone, isClient]);













  // 前往任务函数
  const handleGoToTask = async () => {
    try {
      const loadingMessage = notification.loading('正在跳转到任务...');

      // 获取当前用户的赛事任务列表
      const eventsTaskResponse = await eventsTaskApi.getMyTasks();

      if (eventsTaskResponse.data.code === 200) {
        const eventsTasks = eventsTaskResponse.data.data;
        // 查找与当前活动相关的赛事任务
        const relatedTask = eventsTasks.find((task: any) => task.activityId === Number(activityId));

        if (relatedTask) {
          // 在新标签页中打开班级空间并自动打开对应的赛事任务
          window.open(`/class-space?taskId=${relatedTask.id}`, '_blank');
          notification.success('正在跳转到任务页面...');
        } else {
          // 如果没有找到对应任务，在新标签页中打开班级空间
          window.open('/class-space', '_blank');
          notification.info('未找到对应任务，已打开班级空间');
        }
      } else {
        // 如果获取任务失败，在新标签页中打开班级空间
        window.open('/class-space', '_blank');
        notification.info('获取任务失败，已打开班级空间');
      }

      if (loadingMessage) {
        loadingMessage.close();
      }
    } catch (error) {
      console.error('跳转任务失败:', error);
      notification.error('跳转任务失败，请稍后重试');
    }
  };

  // 处理报名按钮点击
  const handleSubmitClick = async () => {
    // 获取localStorage中的用户信息进行对比
    const localUserStr = localStorage.getItem('user');
    const localUser = localUserStr ? JSON.parse(localUserStr) : null;

    console.log('🔍 [DEBUG] 报名按钮点击 - 详细用户状态检查', {
      hasSubmitted,
      'Redux userState': {
        isLoggedIn: userState?.isLoggedIn,
        phone: userState?.phone,
        phoneLength: userState?.phone?.length,
        phoneType: typeof userState?.phone,
        roleId: userState?.roleId,
        userId: userState?.userId,
        nickName: userState?.nickName
      },
      'localStorage user': {
        phone: localUser?.phone,
        phoneLength: localUser?.phone?.length,
        phoneType: typeof localUser?.phone,
        userId: localUser?.userId,
        nickName: localUser?.nickName
      },
      'phone validation': {
        'userState.phone exists': !!userState?.phone,
        'userState.phone not empty': userState?.phone && userState.phone.trim() !== '',
        'localUser.phone exists': !!localUser?.phone,
        'localUser.phone not empty': localUser?.phone && localUser?.phone.trim() !== ''
      }
    });

    // 临时调用API获取最新用户信息进行对比
    if (userState?.userId) {
      try {
        const response = await userApi.getUserInfo(userState.userId);
        console.log('🔍 [DEBUG] 从API获取的最新用户信息:', {
          code: response.code,
          phone: response.data?.phone,
          phoneLength: response.data?.phone?.length,
          phoneType: typeof response.data?.phone,
          nickName: response.data?.nickName,
          userId: response.data?.id
        });
      } catch (error) {
        console.error('🔍 [DEBUG] 获取用户信息失败:', error);
      }
    }

    // 如果用户已报名，跳转到任务页面
    if (hasSubmitted) {
      console.log('🔍 [DEBUG] 用户已报名，跳转到任务页面');
      handleGoToTask();
      return;
    }

    // 检查用户是否登录
    if (!userState || !userState.isLoggedIn) {
      console.log('🔍 [DEBUG] 用户未登录，跳转到登录页面');
      // 未登录，显示登录对话框
      const redirectUrl = pathname || '/home';
      router.push(`/login?redirect=${encodeURIComponent(redirectUrl)}`);
      return;
    }

    // 检查用户身份：只有学生（roleId为1）才能报名
    const isStudent = checkIsStudent();
    console.log('🔍 [DEBUG] 检查用户身份', { isStudent, roleId: userState?.roleId });
    if (!isStudent) {
      console.log('🔍 [DEBUG] 用户不是学生身份，显示身份验证弹窗');
      showStudentOnlyModal();
      return;
    }

    console.log('🔍 [DEBUG] 身份验证通过，开始报名流程');
    // 身份验证通过，直接进行报名
    handleDirectRegistration();
  };

  // 处理登录成功
  const handleLoginSuccess = () => {
    notification.success('登录成功');

    // 登录成功后，页面的useEffect会自动检查报名状态
    // 不需要在这里重复检查，让useEffect处理状态更新
  };



  // 直接报名处理函数
  const handleDirectRegistration = async () => {
    try {
      // 检查用户是否绑定了手机号
      if (!userState.phone || userState.phone.trim() === '') {
        // 显示确认对话框
        Modal.confirm({
          title: '需要绑定手机号',
          content: '报名活动需要绑定手机号，是否前往绑定？',
          okText: '前往绑定',
          cancelText: '取消',
          onOk: () => {
            // 跳转到登录页面，并在URL中添加参数表示需要绑定手机号
            const currentUrl = window.location.href;
            router.push(`/login?needBindPhone=true&redirect=${encodeURIComponent(currentUrl)}`);
          },
          onCancel: () => {
            notification.info('未绑定手机号，无法报名活动');
          }
        });
        return;
      }

      console.log('🔍 [DEBUG] 用户已绑定手机号，继续报名流程');

      // 显示加载提示
      const loadingMessage = notification.loading('正在提交报名...');

      // 提交报名数据（使用默认值，跳过协议确认步骤）
      const submitData = {
        activityId: Number(activityId),
        agreementAccepted: true, // 默认同意协议
        parentConsentAccepted: true, // 默认同意家长知情同意书
        parentSignaturePath: '', // 空签名路径
        signatureTime: new Date().toISOString(),
        remark: `快速报名时间：${new Date().toLocaleString('zh-CN')}`
      };

      const response = await activityRegistrationApi.submitRegistration(submitData);

      if (response.data?.code === 200) {
        // 更新UI状态
        setHasSubmitted(true);
        setRegistrationStatus(1);
        notification.success('报名成功！正在跳转到班级空间...');

        // 通知页面刷新参赛作品列表
        if (onRefreshWorks) {
          onRefreshWorks();
        }

        // 报名成功后，获取对应的赛事任务并跳转到班级空间
        try {
          // 等待一小段时间确保后端创建赛事任务完成
          await new Promise(resolve => setTimeout(resolve, 1000));

          // 获取当前用户的赛事任务列表
          const eventsTaskResponse = await eventsTaskApi.getMyTasks();

          if (eventsTaskResponse.data.code === 200) {
            const eventsTasks = eventsTaskResponse.data.data;
            // 查找与当前活动相关的赛事任务
            const relatedTask = eventsTasks.find((task: any) => task.activityId === Number(activityId));

            if (relatedTask) {
              // 在新标签页中打开班级空间并自动打开对应的赛事任务
              window.open(`/class-space?taskId=${relatedTask.id}`, '_blank');
            } else {
              // 如果没有找到对应任务，在新标签页中打开班级空间
              window.open('/class-space', '_blank');
            }
          } else {
            // 如果获取任务失败，在新标签页中打开班级空间
            window.open('/class-space', '_blank');
          }
        } catch (taskError) {
          console.error('获取赛事任务失败:', taskError);
          // 即使获取任务失败，也要在新标签页中打开班级空间
          window.open('/class-space', '_blank');
        }
      } else {
        throw new Error(response.data?.message || '报名失败');
      }

      if (loadingMessage) {
        loadingMessage.close();
      }
    } catch (error) {
      console.error('报名失败:', error);
      notification.error(error instanceof Error ? error.message : '报名失败，请稍后重试');
    }
  };









  // 处理分享按钮点击事件
  const handleShare = () => {
    if (typeof window !== 'undefined') {
      // 获取当前页面URL
      const currentUrl = window.location.href;

      // 复制到剪贴板
      navigator.clipboard.writeText(currentUrl)
        .then(() => {
          notification.success('活动链接已复制，快去分享吧！');
        })
        .catch(err => {
          console.error('复制失败:', err);
          notification.error('复制链接失败，请手动复制');
        });
    }
  };

  // 如果还没有在客户端挂载，返回一个简单的占位符
  if (!isClient) {
    return (
      <div className="activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300">
        <div className="relative h-64 bg-gray-200 animate-pulse">
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
          <div className="relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center h-full">
            <div className="flex-1">
              <div className="h-8 bg-gray-300 rounded mb-2 w-3/4"></div>
              <div className="h-4 bg-gray-300 rounded mb-1 w-1/2"></div>
              <div className="h-4 bg-gray-300 rounded w-1/3"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300">


          {/* 横幅图片 - 始终显示 */}
          <div className="relative w-full h-[300px] overflow-hidden">
            {/* Banner Image */}
            <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/30 z-10" />

            <div className="relative w-full h-full">
              <div className="absolute inset-0">
                <Image
                  src={bannerImage}
                  alt={title}
                  fill
                  priority
                  style={{
                    objectFit: 'cover'
                  }}
                  className="z-10 hover:scale-105 transition-transform duration-700"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.background = 'linear-gradient(120deg, #f3f4f6, #e5e7eb)';
                  }}
                />
              </div>
            </div>
              {/* 标签功能暂时注释，保留以便将来使用 
              <div className="inline-flex items-center px-4 py-1.5 bg-white/90 backdrop-blur-sm rounded-full text-sm font-medium text-blue-600 shadow-sm hover:shadow-md transition-shadow duration-300 hover:bg-white transform hover:-translate-y-1 hover:scale-105 relative">
                
                {isEditing && navTags.length < 5 && (
                  <button 
                    onClick={handleAddTag}
                    className="absolute -top-8 left-0 flex items-center px-2 py-1 bg-blue-500 text-white rounded-lg text-xs hover:bg-blue-600 transition-colors"
                  >
                    <Plus className="w-3 h-3 mr-1" />
                    添加标签
                  </button>
                )}

                {tags && tags.length > 0 ? (
                  tags.map((tag, index) => (
                    <React.Fragment key={tag.id}>
                      {index > 0 && <span className="mx-2 text-gray-300">|</span>}
                      <span className="inline-flex items-center">
                        <span 
                          className="px-2 py-0.5 rounded-full text-xs" 
                          style={{ 
                            backgroundColor: tag.color ? `${tag.color}30` : '#e5e7eb',
                            color: tag.color || '#4b5563'
                          }}
                        >
                          {tag.name}
                        </span>
                        {isEditing && (
                          <input 
                            type="checkbox"
                            className="ml-1 w-3 h-3"
                            checked={tagIds.includes(tag.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setTagIds([...tagIds, tag.id]);
                              } else {
                                setTagIds(tagIds.filter(id => id !== tag.id));
                              }
                            }}
                          />
                        )}
                      </span>
                    </React.Fragment>
                  ))
                ) : (
                  navTags.map((tag, index) => (
                    <React.Fragment key={tag.id}>
                      {index > 0 && <span className="mx-2 text-gray-300">|</span>}
                      
                      {isEditing && navTags.length > 1 && (
                        <button
                          onClick={() => handleDeleteTag(tag.id)}
                          className="ml-1 text-red-500 hover:text-red-700"
                          title="删除标签"
                        >
                          <Trash className="w-3 h-3" />
                        </button>
                      )}
                    </React.Fragment>
                  ))
                )}
              </div>
              */}
            </div>
          </div>

          {/* 简洁版头部 - 始终显示 */}
          <div className={`relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center transition-opacity duration-300 ${showUI ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}>
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-800 flex items-center">
                {title}
                <Sparkles className="ml-2 w-5 h-5 text-yellow-400 animate-pulse" />
              </h1>
              {organizer && (
                <p className="text-gray-600 text-sm">主办方: {organizer}</p>
              )}

              <p className="text-gray-500 text-sm mt-2 flex items-center">
                <span className="bg-gradient-to-r from-blue-500 to-indigo-500 h-1.5 w-1.5 rounded-full mr-2 animate-pulse"></span>
                活动时间：{formatDate(startTime)} - {formatDate(endTime)}
              </p>
            </div>

            <div className="flex space-x-3 mt-4 md:mt-0">
              {/* <button
                onClick={() => setExpanded(!expanded)}
                className="flex items-center px-5 py-2.5 text-sm font-medium rounded-full border border-gray-200 text-gray-600 hover:bg-gray-50 hover:scale-105 active:scale-95 transition-all duration-200"
              >
                {expanded ? (
                  <>
                    收起详情
                    <ChevronUp className="ml-2 w-4 h-4" />
                  </>
                ) : (
                  <>
                    展开详情
                    <ChevronDown className="ml-2 w-4 h-4" />
                  </>
                )}
              </button> */}

              <button
                onClick={handleSubmitClick}
                disabled={isChecking}
                className={`flex items-center px-5 py-2.5 text-sm font-medium rounded-full ${hasSubmitted
                  ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg'
                  : 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg'
                  } transition-all duration-200`}
              >
                {isChecking ? (
                  <>
                    <span className="loading-spinner mr-2"></span>
                    检查状态中
                  </>
                ) : hasSubmitted ? (
                  <>已报名 前往任务</>
                ) : (
                  <>立即报名</>
                )}
              </button>

              <button
                onClick={handleShare}
                className="flex items-center p-2.5 text-gray-500 hover:text-blue-500 hover:bg-gray-50 rounded-full hover:scale-110 active:scale-95 transition-all duration-200"
              >
                <Share2 className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

      {/* 媒体资源展示区域 */}
      {(promotionImage || backgroundImage || galleryImages || attachmentFiles) && (
        <div className="mt-6">
          <ActivityMediaGallery
            promotionImage={promotionImage}
            backgroundImage={backgroundImage}
            galleryImages={galleryImages}
            attachmentFiles={attachmentFiles}
            activityTitle={title}
          />
        </div>
      )}

      {/* 登录对话框 - 只在客户端渲染 */}
      {isClient && (
        <LoginDialog
          isOpen={showLoginDialog}
          onClose={() => setShowLoginDialog(false)}
          onSuccess={handleLoginSuccess}
        />
      )}
    </>
  )
}

export default ActivityHeader 