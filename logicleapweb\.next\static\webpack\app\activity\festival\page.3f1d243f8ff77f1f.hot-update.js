"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/activity/festival/page",{

/***/ "(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx":
/*!*************************************************************!*\
  !*** ./app/activity/festival/components/ActivityHeader.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Share2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Share2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./lib/store.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ActivityMediaGallery */ \"(app-pages-browser)/./app/activity/festival/components/ActivityMediaGallery.tsx\");\n/* harmony import */ var _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Modal!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _components_login_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/login-dialog */ \"(app-pages-browser)/./components/login-dialog.tsx\");\n/* harmony import */ var _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/activity */ \"(app-pages-browser)/./lib/api/activity/index.ts\");\n/* harmony import */ var _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/activity/events-task */ \"(app-pages-browser)/./lib/api/activity/events-task.ts\");\n/* harmony import */ var _lib_api_user__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/user */ \"(app-pages-browser)/./lib/api/user.ts\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 临时实现formatDate函数，以解决模块导入问题\nconst formatDate = (dateString)=>{\n    // 在服务端渲染时，直接返回原始字符串，避免水合错误\n    if (false) {}\n    const date = new Date(dateString);\n    // 检查日期是否有效\n    if (isNaN(date.getTime())) {\n        return dateString; // 如果无效，返回原始字符串\n    }\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, \"0\");\n    const day = String(date.getDate()).padStart(2, \"0\");\n    return \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n};\nconst ActivityHeader = (param)=>{\n    let { title: initialTitle, startTime: initialStartTime, endTime: initialEndTime, bannerImage: initialBannerImage, expanded, setExpanded, activityId, tags = [], organizer: initialOrganizer = \"\", activityType = _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.ActivityType.WORK, onRefreshWorks, // 媒体字段\n    promotionImage, backgroundImage, galleryImages, attachmentFiles } = param;\n    _s();\n    console.log(\"\\uD83D\\uDD0D [DEBUG] ActivityHeader 组件渲染\", {\n        activityId,\n        initialTitle\n    });\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_9__.GetNotification)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.usePathname)();\n    // 显示用的标题和时间\n    const title = initialTitle;\n    const startTime = initialStartTime;\n    const endTime = initialEndTime;\n    const bannerImage = initialBannerImage;\n    const organizer = initialOrganizer;\n    // 获取当前用户信息和dispatch\n    const userState = (0,react_redux__WEBPACK_IMPORTED_MODULE_11__.useSelector)(_lib_store__WEBPACK_IMPORTED_MODULE_3__.selectUserState);\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_11__.useDispatch)();\n    const isAdmin = (userState === null || userState === void 0 ? void 0 : userState.roleId) === 4;\n    // 同步用户状态的函数 - 增强版，从API获取最新用户信息\n    const syncUserState = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 开始同步用户状态\");\n            const localUser = localStorage.getItem(\"user\");\n            if (localUser) {\n                const userData = JSON.parse(localUser);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中的用户数据\", userData);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] Redux 中的用户数据\", userState);\n                // 如果用户已登录但phone为空，尝试从API获取最新用户信息\n                if (userData.isLoggedIn && userData.userId && (!userData.phone || userData.phone.trim() === \"\")) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户phone为空，从API获取最新用户信息\");\n                    try {\n                        const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_8__.userApi.getUserInfo(userData.userId);\n                        if (response.code === 200 && response.data) {\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取到最新用户信息\", response.data);\n                            // 构建更新后的用户数据\n                            const updatedUserData = {\n                                ...userData,\n                                phone: response.data.phone || \"\",\n                                nickName: response.data.nickName || userData.nickName,\n                                avatarUrl: response.data.avatarUrl || userData.avatarUrl,\n                                gender: response.data.gender || userData.gender,\n                                roleId: response.data.roleId || userData.roleId\n                            };\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 更新用户数据\", {\n                                oldPhone: userData.phone,\n                                newPhone: updatedUserData.phone,\n                                updated: updatedUserData\n                            });\n                            // 更新localStorage和Redux\n                            localStorage.setItem(\"user\", JSON.stringify(updatedUserData));\n                            dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(updatedUserData));\n                            return;\n                        }\n                    } catch (error) {\n                        console.error(\"\\uD83D\\uDD0D [DEBUG] 从API获取用户信息失败:\", error);\n                    }\n                }\n                // 检查关键字段是否有变化\n                const hasPhoneChanged = userData.phone !== (userState === null || userState === void 0 ? void 0 : userState.phone);\n                const hasRoleChanged = userData.roleId !== (userState === null || userState === void 0 ? void 0 : userState.roleId);\n                const hasLoginStatusChanged = userData.isLoggedIn !== (userState === null || userState === void 0 ? void 0 : userState.isLoggedIn);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态变化检查\", {\n                    phoneChanged: hasPhoneChanged,\n                    roleChanged: hasRoleChanged,\n                    loginStatusChanged: hasLoginStatusChanged,\n                    oldPhone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                    newPhone: userData.phone,\n                    oldRole: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                    newRole: userData.roleId\n                });\n                // 如果localStorage中的用户状态与Redux中的不一致，说明需要更新\n                if (hasPhoneChanged || hasRoleChanged || hasLoginStatusChanged) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户状态变化，同步Redux状态\");\n                    dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(userData));\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态无变化，无需同步\");\n                }\n            } else {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中无用户数据\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD0D [DEBUG] 检查用户状态失败:\", error);\n        }\n    };\n    // 监听页面焦点变化和路由变化，重新检查用户状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 页面加载时立即检查一次\n        syncUserState().catch(console.error);\n        const handleVisibilityChange = ()=>{\n            if (document.visibilityState === \"visible\") {\n                syncUserState().catch(console.error);\n            }\n        };\n        const handleFocus = ()=>{\n            syncUserState().catch(console.error);\n        };\n        // 添加多种事件监听确保状态同步\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n        window.addEventListener(\"focus\", handleFocus);\n        return ()=>{\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n            window.removeEventListener(\"focus\", handleFocus);\n        };\n    }, [\n        userState === null || userState === void 0 ? void 0 : userState.phone,\n        dispatch\n    ]);\n    // 工具函数：检查用户是否为学生身份\n    const checkIsStudent = ()=>{\n        try {\n            const userStr = localStorage.getItem(\"user\");\n            const localUser = userStr ? JSON.parse(userStr) : null;\n            const userRoleId = (localUser === null || localUser === void 0 ? void 0 : localUser.roleId) || (userState === null || userState === void 0 ? void 0 : userState.roleId);\n            return userRoleId === 1; // 学生身份的roleId为1\n        } catch (error) {\n            console.error(\"解析localStorage中的用户信息失败:\", error);\n            return false;\n        }\n    };\n    // 工具函数：显示身份验证失败提示\n    const showStudentOnlyModal = ()=>{\n        _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].confirm({\n            title: \"身份验证\",\n            content: \"抱歉，只有学生身份的用户才能参加此活动报名。如果您是学生，请联系管理员更新您的身份信息。\",\n            okText: \"我知道了\",\n            cancelText: \"联系管理员\",\n            onCancel: ()=>{\n                notification.info(\"如需帮助，请联系客服：400-123-4567\");\n            }\n        });\n    };\n    // 获取报名状态文本\n    const getRegistrationStatusText = ()=>{\n        switch(registrationStatus){\n            case 0:\n                return \"已取消\";\n            case 1:\n                return \"已报名\";\n            case 2:\n                return \"已审核通过\";\n            case 3:\n                return \"已拒绝\";\n            case 4:\n                return \"评审中\";\n            case 5:\n                return \"已获奖\";\n            case 6:\n                return \"审核中\";\n            default:\n                return \"已报名\";\n        }\n    };\n    // 可编辑的导航标签文本\n    const [navTags, setNavTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"tag1\",\n            text: \"官方活动\"\n        },\n        {\n            id: \"tag2\",\n            text: \"AIGC作品征集\"\n        }\n    ]);\n    // 活动标签ID列表\n    const [tagIds, setTagIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 标签编辑状态\n    const [editingTagId, setEditingTagId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingTagText, setEditingTagText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 登录和报名相关状态\n    const [showLoginDialog, setShowLoginDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSubmitted, setHasSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [registrationStatus, setRegistrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 报名状态：0-已取消 1-已报名 2-已审核通过 3-已拒绝 4-评审中 5-已获奖 6-审核中\n    const [uploadLoading, setUploadLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fileList, setFileList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showUI, setShowUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 添加客户端检测来解决水合错误\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    // 页面加载时检查报名状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkRegistrationStatus = async ()=>{\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态 - useEffect 触发\", {\n                isLoggedIn: userState.isLoggedIn,\n                roleId: userState.roleId,\n                phone: userState.phone,\n                isClient,\n                activityId\n            });\n            // 只有在用户已登录且是学生身份时才检查报名状态\n            const isStudent = checkIsStudent();\n            if (!userState.isLoggedIn || !isStudent) {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录或不是学生身份，重置报名状态\", {\n                    isLoggedIn: userState.isLoggedIn,\n                    isStudent\n                });\n                // 如果用户未登录或不是学生，重置状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n                return;\n            }\n            try {\n                var _response_data, _response_data1;\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 开始检查报名状态 API 调用\");\n                setIsChecking(true);\n                const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.checkRegistration(Number(activityId));\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 页面加载检查报名状态响应:\", response);\n                if ((response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200 && ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data)) {\n                    const { submitted, submit } = response.data.data;\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态检查结果\", {\n                        submitted,\n                        submit\n                    });\n                    setHasSubmitted(submitted);\n                    if (submit && submit.status !== undefined) {\n                        setRegistrationStatus(submit.status);\n                    } else {\n                        setRegistrationStatus(null);\n                    }\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态响应不符合预期，设置为未报名状态\");\n                    // 如果响应不符合预期，设置为未报名状态\n                    setHasSubmitted(false);\n                    setRegistrationStatus(null);\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态失败:\", error);\n                // 出错时设置为未报名状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n            } finally{\n                setIsChecking(false);\n            }\n        };\n        // 只在客户端执行，避免服务端渲染问题\n        if (isClient) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 客户端环境，执行报名状态检查\");\n            checkRegistrationStatus();\n        } else {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 非客户端环境，跳过报名状态检查\");\n        }\n    }, [\n        activityId,\n        userState.isLoggedIn,\n        userState.roleId,\n        userState.phone,\n        isClient\n    ]);\n    // 检查用户是否已提交作品\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkUserSubmitted = async ()=>{\n            try {\n                // 仅针对已登录用户进行检查\n                if (userState && userState.isLoggedIn) {\n                    var _response_data;\n                    setIsChecking(true);\n                    const response = await activityApi.checkUserSubmitted(Number(activityId));\n                    console.log(\"页面加载时检查用户提交状态响应:\", response);\n                    if ((response === null || response === void 0 ? void 0 : response.status) === 200 && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data)) {\n                        setHasSubmitted(response.data.data.submitted);\n                    } else {\n                        // 如果响应不符合预期，默认为未提交\n                        setHasSubmitted(false);\n                    }\n                    setIsChecking(false);\n                } else {\n                    // 未登录用户，直接设置为未提交\n                    setHasSubmitted(false);\n                    setIsChecking(false);\n                }\n            } catch (error) {\n                console.error(\"检查用户提交状态失败:\", error);\n                // 出错时默认设置为未提交\n                setHasSubmitted(false);\n                setIsChecking(false);\n            }\n        };\n        // 页面加载时执行检查\n        if (isClient) {\n            checkUserSubmitted();\n        }\n    }, [\n        activityId,\n        userState,\n        isClient\n    ]);\n    // 加载活动标签\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchActivityTags = async ()=>{\n            if (activityId) {\n                try {\n                    const response = await activityTagApi.getActivityTags(Number(activityId));\n                    if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n                        setTagIds(response.data.data || []);\n                    }\n                } catch (error) {\n                    console.error(\"获取活动标签失败:\", error);\n                }\n            }\n        };\n        fetchActivityTags();\n    }, [\n        activityId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 初始化文件列表\n        if (bannerImage) {\n            setFileList([\n                {\n                    uid: \"-1\",\n                    name: \"当前封面\",\n                    status: \"done\",\n                    url: bannerImage\n                }\n            ]);\n        } else {\n            setFileList([]);\n        }\n    }, [\n        bannerImage\n    ]);\n    // 前往任务函数\n    const handleGoToTask = async ()=>{\n        try {\n            const loadingMessage = notification.loading(\"正在跳转到任务...\");\n            // 获取当前用户的赛事任务列表\n            const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n            if (eventsTaskResponse.data.code === 200) {\n                const eventsTasks = eventsTaskResponse.data.data;\n                // 查找与当前活动相关的赛事任务\n                const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                if (relatedTask) {\n                    // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                    window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                    notification.success(\"正在跳转到任务页面...\");\n                } else {\n                    // 如果没有找到对应任务，在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                    notification.info(\"未找到对应任务，已打开班级空间\");\n                }\n            } else {\n                // 如果获取任务失败，在新标签页中打开班级空间\n                window.open(\"/class-space\", \"_blank\");\n                notification.info(\"获取任务失败，已打开班级空间\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"跳转任务失败:\", error);\n            notification.error(\"跳转任务失败，请稍后重试\");\n        }\n    };\n    // 处理报名按钮点击\n    const handleSubmitClick = async ()=>{\n        var _userState_phone, _localUser_phone;\n        // 获取localStorage中的用户信息进行对比\n        const localUserStr = localStorage.getItem(\"user\");\n        const localUser = localUserStr ? JSON.parse(localUserStr) : null;\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 报名按钮点击 - 详细用户状态检查\", {\n            hasSubmitted,\n            \"Redux userState\": {\n                isLoggedIn: userState === null || userState === void 0 ? void 0 : userState.isLoggedIn,\n                phone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                phoneLength: userState === null || userState === void 0 ? void 0 : (_userState_phone = userState.phone) === null || _userState_phone === void 0 ? void 0 : _userState_phone.length,\n                phoneType: typeof (userState === null || userState === void 0 ? void 0 : userState.phone),\n                roleId: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                userId: userState === null || userState === void 0 ? void 0 : userState.userId,\n                nickName: userState === null || userState === void 0 ? void 0 : userState.nickName\n            },\n            \"localStorage user\": {\n                phone: localUser === null || localUser === void 0 ? void 0 : localUser.phone,\n                phoneLength: localUser === null || localUser === void 0 ? void 0 : (_localUser_phone = localUser.phone) === null || _localUser_phone === void 0 ? void 0 : _localUser_phone.length,\n                phoneType: typeof (localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                userId: localUser === null || localUser === void 0 ? void 0 : localUser.userId,\n                nickName: localUser === null || localUser === void 0 ? void 0 : localUser.nickName\n            },\n            \"phone validation\": {\n                \"userState.phone exists\": !!(userState === null || userState === void 0 ? void 0 : userState.phone),\n                \"userState.phone not empty\": (userState === null || userState === void 0 ? void 0 : userState.phone) && userState.phone.trim() !== \"\",\n                \"localUser.phone exists\": !!(localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                \"localUser.phone not empty\": (localUser === null || localUser === void 0 ? void 0 : localUser.phone) && (localUser === null || localUser === void 0 ? void 0 : localUser.phone.trim()) !== \"\"\n            }\n        });\n        // 临时调用API获取最新用户信息进行对比\n        if (userState === null || userState === void 0 ? void 0 : userState.userId) {\n            try {\n                var _response_data, _response_data_phone, _response_data1, _response_data2, _response_data3, _response_data4;\n                const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_8__.userApi.getUserInfo(userState.userId);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取的最新用户信息:\", {\n                    code: response.code,\n                    phone: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.phone,\n                    phoneLength: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_phone = _response_data1.phone) === null || _response_data_phone === void 0 ? void 0 : _response_data_phone.length,\n                    phoneType: typeof ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.phone),\n                    nickName: (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.nickName,\n                    userId: (_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : _response_data4.id\n                });\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 获取用户信息失败:\", error);\n            }\n        }\n        // 如果用户已报名，跳转到任务页面\n        if (hasSubmitted) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已报名，跳转到任务页面\");\n            handleGoToTask();\n            return;\n        }\n        // 检查用户是否登录\n        if (!userState || !userState.isLoggedIn) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录，跳转到登录页面\");\n            // 未登录，显示登录对话框\n            const redirectUrl = pathname || \"/home\";\n            router.push(\"/login?redirect=\".concat(encodeURIComponent(redirectUrl)));\n            return;\n        }\n        // 检查用户身份：只有学生（roleId为1）才能报名\n        const isStudent = checkIsStudent();\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 检查用户身份\", {\n            isStudent,\n            roleId: userState === null || userState === void 0 ? void 0 : userState.roleId\n        });\n        if (!isStudent) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户不是学生身份，显示身份验证弹窗\");\n            showStudentOnlyModal();\n            return;\n        }\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 身份验证通过，开始报名流程\");\n        // 身份验证通过，直接进行报名\n        handleDirectRegistration();\n    };\n    // 处理登录成功\n    const handleLoginSuccess = ()=>{\n        notification.success(\"登录成功\");\n    // 登录成功后，页面的useEffect会自动检查报名状态\n    // 不需要在这里重复检查，让useEffect处理状态更新\n    };\n    // 处理报名成功\n    const handleSubmitSuccess = ()=>{\n        // 更新提交状态\n        setHasSubmitted(true);\n        setRegistrationStatus(1); // 设置为已报名状态\n        notification.success(\"报名成功\");\n        // 通知页面刷新参赛作品列表\n        if (onRefreshWorks) {\n            onRefreshWorks();\n        }\n    };\n    // 直接报名处理函数\n    const handleDirectRegistration = async ()=>{\n        try {\n            var _response_data;\n            // 检查用户是否绑定了手机号\n            if (!userState.phone || userState.phone.trim() === \"\") {\n                // 显示确认对话框\n                _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].confirm({\n                    title: \"需要绑定手机号\",\n                    content: \"报名活动需要绑定手机号，是否前往绑定？\",\n                    okText: \"前往绑定\",\n                    cancelText: \"取消\",\n                    onOk: ()=>{\n                        // 跳转到登录页面，并在URL中添加参数表示需要绑定手机号\n                        const currentUrl = window.location.href;\n                        router.push(\"/login?needBindPhone=true&redirect=\".concat(encodeURIComponent(currentUrl)));\n                    },\n                    onCancel: ()=>{\n                        notification.info(\"未绑定手机号，无法报名活动\");\n                    }\n                });\n                return;\n            }\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已绑定手机号，继续报名流程\");\n            // 显示加载提示\n            const loadingMessage = notification.loading(\"正在提交报名...\");\n            // 提交报名数据（使用默认值，跳过协议确认步骤）\n            const submitData = {\n                activityId: Number(activityId),\n                agreementAccepted: true,\n                parentConsentAccepted: true,\n                parentSignaturePath: \"\",\n                signatureTime: new Date().toISOString(),\n                remark: \"快速报名时间：\".concat(new Date().toLocaleString(\"zh-CN\"))\n            };\n            const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.submitRegistration(submitData);\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                // 更新UI状态\n                setHasSubmitted(true);\n                setRegistrationStatus(1);\n                notification.success(\"报名成功！正在跳转到班级空间...\");\n                // 通知页面刷新参赛作品列表\n                if (onRefreshWorks) {\n                    onRefreshWorks();\n                }\n                // 报名成功后，获取对应的赛事任务并跳转到班级空间\n                try {\n                    // 等待一小段时间确保后端创建赛事任务完成\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    // 获取当前用户的赛事任务列表\n                    const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n                    if (eventsTaskResponse.data.code === 200) {\n                        const eventsTasks = eventsTaskResponse.data.data;\n                        // 查找与当前活动相关的赛事任务\n                        const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                        if (relatedTask) {\n                            // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                            window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                        } else {\n                            // 如果没有找到对应任务，在新标签页中打开班级空间\n                            window.open(\"/class-space\", \"_blank\");\n                        }\n                    } else {\n                        // 如果获取任务失败，在新标签页中打开班级空间\n                        window.open(\"/class-space\", \"_blank\");\n                    }\n                } catch (taskError) {\n                    console.error(\"获取赛事任务失败:\", taskError);\n                    // 即使获取任务失败，也要在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                }\n            } else {\n                var _response_data1;\n                throw new Error(((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message) || \"报名失败\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"报名失败:\", error);\n            notification.error(error instanceof Error ? error.message : \"报名失败，请稍后重试\");\n        }\n    };\n    // 处理分享按钮点击事件\n    const handleShare = ()=>{\n        if (true) {\n            // 获取当前页面URL\n            const currentUrl = window.location.href;\n            // 复制到剪贴板\n            navigator.clipboard.writeText(currentUrl).then(()=>{\n                notification.success(\"活动链接已复制，快去分享吧！\");\n            }).catch((err)=>{\n                console.error(\"复制失败:\", err);\n                notification.error(\"复制链接失败，请手动复制\");\n            });\n        }\n    };\n    // 如果还没有在客户端挂载，返回一个简单的占位符\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-64 bg-gray-200 animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 692,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-300 rounded mb-2 w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded mb-1 w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 696,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 693,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 691,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n            lineNumber: 690,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-[300px] overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-transparent to-black/30 z-10 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 713,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-blue-500/10 animate-pulse z-0 transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: bannerImage,\n                                            alt: title,\n                                            fill: true,\n                                            priority: true,\n                                            style: {\n                                                objectFit: \"cover\"\n                                            },\n                                            className: \"z-10 hover:scale-105 transition-transform duration-700\",\n                                            onError: (e)=>{\n                                                const target = e.target;\n                                                target.style.background = \"linear-gradient(120deg, #f3f4f6, #e5e7eb)\";\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-4 left-4 right-4 z-20 flex justify-between items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 711,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center transition-opacity duration-300 \".concat(showUI ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-800 flex items-center\",\n                                        children: [\n                                            title,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"ml-2 w-5 h-5 text-yellow-400 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 808,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 806,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    organizer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: [\n                                            \"主办方: \",\n                                            organizer\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm mt-2 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-500 to-indigo-500 h-1.5 w-1.5 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 815,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"活动时间：\",\n                                            formatDate(startTime),\n                                            \" - \",\n                                            formatDate(endTime)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 814,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 805,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3 mt-4 md:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSubmitClick,\n                                        disabled: isChecking,\n                                        className: \"flex items-center px-5 py-2.5 text-sm font-medium rounded-full \".concat(hasSubmitted ? \"bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\" : \"bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\", \" transition-all duration-200\"),\n                                        children: isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"loading-spinner mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                    lineNumber: 848,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"检查状态中\"\n                                            ]\n                                        }, void 0, true) : hasSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"已报名 前往任务\"\n                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"立即报名\"\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 838,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex items-center p-2.5 text-gray-500 hover:text-blue-500 hover:bg-gray-50 rounded-full hover:scale-110 active:scale-95 transition-all duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 862,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 858,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 820,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 804,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 707,\n                columnNumber: 7\n            }, undefined),\n            (promotionImage || backgroundImage || galleryImages || attachmentFiles) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    promotionImage: promotionImage,\n                    backgroundImage: backgroundImage,\n                    galleryImages: galleryImages,\n                    attachmentFiles: attachmentFiles,\n                    activityTitle: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                    lineNumber: 871,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 870,\n                columnNumber: 9\n            }, undefined),\n            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_login_dialog__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showLoginDialog,\n                onClose: ()=>setShowLoginDialog(false),\n                onSuccess: handleLoginSuccess\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 883,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ActivityHeader, \"9puqDtaYsDo7+fZ2UWLRshIk+yw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.usePathname,\n        react_redux__WEBPACK_IMPORTED_MODULE_11__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_11__.useDispatch\n    ];\n});\n_c = ActivityHeader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ActivityHeader);\nvar _c;\n$RefreshReg$(_c, \"ActivityHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9hY3Rpdml0eS9mZXN0aXZhbC9jb21wb25lbnRzL0FjdGl2aXR5SGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWtEO0FBQ3BCO0FBQ2lCO0FBQ087QUFDQTtBQUVHO0FBQzdCO0FBQ2lDO0FBQ2E7QUFDWjtBQUN0QjtBQUNtQztBQUNuQjtBQUV4RCw2QkFBNkI7QUFDN0IsTUFBTW9CLGFBQWEsQ0FBQ0M7SUFDbEIsMkJBQTJCO0lBQzNCLElBQUksS0FBa0IsRUFBYSxFQUVsQztJQUVELE1BQU1DLE9BQU8sSUFBSUMsS0FBS0Y7SUFFdEIsV0FBVztJQUNYLElBQUlHLE1BQU1GLEtBQUtHLE9BQU8sS0FBSztRQUN6QixPQUFPSixZQUFZLGVBQWU7SUFDcEM7SUFFQSxNQUFNSyxPQUFPSixLQUFLSyxXQUFXO0lBQzdCLE1BQU1DLFFBQVFDLE9BQU9QLEtBQUtRLFFBQVEsS0FBSyxHQUFHQyxRQUFRLENBQUMsR0FBRztJQUN0RCxNQUFNQyxNQUFNSCxPQUFPUCxLQUFLVyxPQUFPLElBQUlGLFFBQVEsQ0FBQyxHQUFHO0lBRS9DLE9BQU8sR0FBV0gsT0FBUkYsTUFBSyxLQUFZTSxPQUFUSixPQUFNLEtBQU8sT0FBSkk7QUFDN0I7QUFnQ0EsTUFBTUUsaUJBQWdEO1FBQUMsRUFDckRDLE9BQU9DLFlBQVksRUFDbkJDLFdBQVdDLGdCQUFnQixFQUMzQkMsU0FBU0MsY0FBYyxFQUN2QkMsYUFBYUMsa0JBQWtCLEVBQy9CQyxRQUFRLEVBQ1JDLFdBQVcsRUFDWEMsVUFBVSxFQUNWQyxPQUFPLEVBQUUsRUFDVEMsV0FBV0MsbUJBQW1CLEVBQUUsRUFFaENDLGVBQWVuQywyREFBWUEsQ0FBQ29DLElBQUksRUFDaENDLGNBQWMsRUFDZCxPQUFPO0lBQ1BDLGNBQWMsRUFDZEMsZUFBZSxFQUNmQyxhQUFhLEVBQ2JDLGVBQWUsRUFDaEI7O0lBQ0NDLFFBQVFDLEdBQUcsQ0FBQyw0Q0FBa0M7UUFBRVo7UUFBWVQ7SUFBYTtJQUd6RSxNQUFNc0IsZUFBZXpDLDBGQUFlQTtJQUNwQyxNQUFNMEMsU0FBU3pDLDJEQUFTQTtJQUN4QixNQUFNMEMsV0FBV3pDLDZEQUFXQTtJQUU1QixZQUFZO0lBQ1osTUFBTWdCLFFBQVFDO0lBQ2QsTUFBTUMsWUFBWUM7SUFDbEIsTUFBTUMsVUFBVUM7SUFDaEIsTUFBTUMsY0FBY0M7SUFDcEIsTUFBTUssWUFBWUM7SUFFbEIsb0JBQW9CO0lBQ3BCLE1BQU1hLFlBQVlyRCx5REFBV0EsQ0FBQ0YsdURBQWVBO0lBQzdDLE1BQU13RCxXQUFXckQseURBQVdBO0lBQzVCLE1BQU1zRCxVQUFVRixDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVdHLE1BQU0sTUFBSztJQUV0QywrQkFBK0I7SUFDL0IsTUFBTUMsZ0JBQWdCO1FBQ3BCLElBQUk7WUFDRlQsUUFBUUMsR0FBRyxDQUFDO1lBQ1osTUFBTVMsWUFBWUMsYUFBYUMsT0FBTyxDQUFDO1lBQ3ZDLElBQUlGLFdBQVc7Z0JBQ2IsTUFBTUcsV0FBV0MsS0FBS0MsS0FBSyxDQUFDTDtnQkFDNUJWLFFBQVFDLEdBQUcsQ0FBQyw0Q0FBa0NZO2dCQUM5Q2IsUUFBUUMsR0FBRyxDQUFDLHFDQUEyQkk7Z0JBRXZDLGlDQUFpQztnQkFDakMsSUFBSVEsU0FBU0csVUFBVSxJQUFJSCxTQUFTSSxNQUFNLElBQUssRUFBQ0osU0FBU0ssS0FBSyxJQUFJTCxTQUFTSyxLQUFLLENBQUNDLElBQUksT0FBTyxFQUFDLEdBQUk7b0JBQy9GbkIsUUFBUUMsR0FBRyxDQUFDO29CQUNaLElBQUk7d0JBQ0YsTUFBTW1CLFdBQVcsTUFBTTVELGtEQUFPQSxDQUFDNkQsV0FBVyxDQUFDUixTQUFTSSxNQUFNO3dCQUMxRCxJQUFJRyxTQUFTRSxJQUFJLEtBQUssT0FBT0YsU0FBU0csSUFBSSxFQUFFOzRCQUMxQ3ZCLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBNEJtQixTQUFTRyxJQUFJOzRCQUVyRCxhQUFhOzRCQUNiLE1BQU1DLGtCQUFrQjtnQ0FDdEIsR0FBR1gsUUFBUTtnQ0FDWEssT0FBT0UsU0FBU0csSUFBSSxDQUFDTCxLQUFLLElBQUk7Z0NBQzlCTyxVQUFVTCxTQUFTRyxJQUFJLENBQUNFLFFBQVEsSUFBSVosU0FBU1ksUUFBUTtnQ0FDckRDLFdBQVdOLFNBQVNHLElBQUksQ0FBQ0csU0FBUyxJQUFJYixTQUFTYSxTQUFTO2dDQUN4REMsUUFBUVAsU0FBU0csSUFBSSxDQUFDSSxNQUFNLElBQUlkLFNBQVNjLE1BQU07Z0NBQy9DbkIsUUFBUVksU0FBU0csSUFBSSxDQUFDZixNQUFNLElBQUlLLFNBQVNMLE1BQU07NEJBQ2pEOzRCQUVBUixRQUFRQyxHQUFHLENBQUMsK0JBQXFCO2dDQUMvQjJCLFVBQVVmLFNBQVNLLEtBQUs7Z0NBQ3hCVyxVQUFVTCxnQkFBZ0JOLEtBQUs7Z0NBQy9CWSxTQUFTTjs0QkFDWDs0QkFFQSx1QkFBdUI7NEJBQ3ZCYixhQUFhb0IsT0FBTyxDQUFDLFFBQVFqQixLQUFLa0IsU0FBUyxDQUFDUjs0QkFDNUNsQixTQUFTdkQsbURBQU9BLENBQUN5RTs0QkFDakI7d0JBQ0Y7b0JBQ0YsRUFBRSxPQUFPUyxPQUFPO3dCQUNkakMsUUFBUWlDLEtBQUssQ0FBQyxzQ0FBNEJBO29CQUM1QztnQkFDRjtnQkFFQSxjQUFjO2dCQUNkLE1BQU1DLGtCQUFrQnJCLFNBQVNLLEtBQUssTUFBS2Isc0JBQUFBLGdDQUFBQSxVQUFXYSxLQUFLO2dCQUMzRCxNQUFNaUIsaUJBQWlCdEIsU0FBU0wsTUFBTSxNQUFLSCxzQkFBQUEsZ0NBQUFBLFVBQVdHLE1BQU07Z0JBQzVELE1BQU00Qix3QkFBd0J2QixTQUFTRyxVQUFVLE1BQUtYLHNCQUFBQSxnQ0FBQUEsVUFBV1csVUFBVTtnQkFFM0VoQixRQUFRQyxHQUFHLENBQUMsaUNBQXVCO29CQUNqQ29DLGNBQWNIO29CQUNkSSxhQUFhSDtvQkFDYkksb0JBQW9CSDtvQkFDcEJSLFFBQVEsRUFBRXZCLHNCQUFBQSxnQ0FBQUEsVUFBV2EsS0FBSztvQkFDMUJXLFVBQVVoQixTQUFTSyxLQUFLO29CQUN4QnNCLE9BQU8sRUFBRW5DLHNCQUFBQSxnQ0FBQUEsVUFBV0csTUFBTTtvQkFDMUJpQyxTQUFTNUIsU0FBU0wsTUFBTTtnQkFDMUI7Z0JBRUEseUNBQXlDO2dCQUN6QyxJQUFJMEIsbUJBQW1CQyxrQkFBa0JDLHVCQUF1QjtvQkFDOURwQyxRQUFRQyxHQUFHLENBQUM7b0JBQ1pLLFNBQVN2RCxtREFBT0EsQ0FBQzhEO2dCQUNuQixPQUFPO29CQUNMYixRQUFRQyxHQUFHLENBQUM7Z0JBQ2Q7WUFDRixPQUFPO2dCQUNMRCxRQUFRQyxHQUFHLENBQUM7WUFDZDtRQUNGLEVBQUUsT0FBT2dDLE9BQU87WUFDZGpDLFFBQVFpQyxLQUFLLENBQUMsa0NBQXdCQTtRQUN4QztJQUNGO0lBRUEseUJBQXlCO0lBQ3pCdkYsZ0RBQVNBLENBQUM7UUFDUixjQUFjO1FBQ2QrRCxnQkFBZ0JpQyxLQUFLLENBQUMxQyxRQUFRaUMsS0FBSztRQUVuQyxNQUFNVSx5QkFBeUI7WUFDN0IsSUFBSUMsU0FBU0MsZUFBZSxLQUFLLFdBQVc7Z0JBQzFDcEMsZ0JBQWdCaUMsS0FBSyxDQUFDMUMsUUFBUWlDLEtBQUs7WUFDckM7UUFDRjtRQUVBLE1BQU1hLGNBQWM7WUFDbEJyQyxnQkFBZ0JpQyxLQUFLLENBQUMxQyxRQUFRaUMsS0FBSztRQUNyQztRQUVBLGlCQUFpQjtRQUNqQlcsU0FBU0csZ0JBQWdCLENBQUMsb0JBQW9CSjtRQUM5Q0ssT0FBT0QsZ0JBQWdCLENBQUMsU0FBU0Q7UUFFakMsT0FBTztZQUNMRixTQUFTSyxtQkFBbUIsQ0FBQyxvQkFBb0JOO1lBQ2pESyxPQUFPQyxtQkFBbUIsQ0FBQyxTQUFTSDtRQUN0QztJQUNGLEdBQUc7UUFBQ3pDLHNCQUFBQSxnQ0FBQUEsVUFBV2EsS0FBSztRQUFFWjtLQUFTO0lBRS9CLG1CQUFtQjtJQUNuQixNQUFNNEMsaUJBQWlCO1FBQ3JCLElBQUk7WUFDRixNQUFNQyxVQUFVeEMsYUFBYUMsT0FBTyxDQUFDO1lBQ3JDLE1BQU1GLFlBQVl5QyxVQUFVckMsS0FBS0MsS0FBSyxDQUFDb0MsV0FBVztZQUNsRCxNQUFNQyxhQUFhMUMsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXRixNQUFNLE1BQUlILHNCQUFBQSxnQ0FBQUEsVUFBV0csTUFBTTtZQUN6RCxPQUFPNEMsZUFBZSxHQUFHLGdCQUFnQjtRQUMzQyxFQUFFLE9BQU9uQixPQUFPO1lBQ2RqQyxRQUFRaUMsS0FBSyxDQUFDLDJCQUEyQkE7WUFDekMsT0FBTztRQUNUO0lBQ0Y7SUFJQSxrQkFBa0I7SUFDbEIsTUFBTW9CLHVCQUF1QjtRQUMzQmxHLDBFQUFLQSxDQUFDbUcsT0FBTyxDQUFDO1lBQ1ozRSxPQUFPO1lBQ1A0RSxTQUFTO1lBQ1RDLFFBQVE7WUFDUkMsWUFBWTtZQUNaQyxVQUFVO2dCQUNSeEQsYUFBYXlELElBQUksQ0FBQztZQUNwQjtRQUNGO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTUMsNEJBQTRCO1FBQ2hDLE9BQVFDO1lBQ04sS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1Q7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxhQUFhO0lBQ2IsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUd0SCwrQ0FBUUEsQ0FBVztRQUMvQztZQUFFdUgsSUFBSTtZQUFRQyxNQUFNO1FBQU87UUFDM0I7WUFBRUQsSUFBSTtZQUFRQyxNQUFNO1FBQVc7S0FDaEM7SUFFRCxXQUFXO0lBQ1gsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUcxSCwrQ0FBUUEsQ0FBVyxFQUFFO0lBRWpELFNBQVM7SUFDVCxNQUFNLENBQUMySCxjQUFjQyxnQkFBZ0IsR0FBRzVILCtDQUFRQSxDQUFnQjtJQUNoRSxNQUFNLENBQUM2SCxnQkFBZ0JDLGtCQUFrQixHQUFHOUgsK0NBQVFBLENBQUM7SUFFckQsWUFBWTtJQUNaLE1BQU0sQ0FBQytILGlCQUFpQkMsbUJBQW1CLEdBQUdoSSwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUNpSSxjQUFjQyxnQkFBZ0IsR0FBR2xJLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ21JLFlBQVlDLGNBQWMsR0FBR3BJLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ29ILG9CQUFvQmlCLHNCQUFzQixHQUFHckksK0NBQVFBLENBQWdCLE9BQU8sbURBQW1EO0lBRXRJLE1BQU0sQ0FBQ3NJLGVBQWVDLGlCQUFpQixHQUFHdkksK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDd0ksVUFBVUMsWUFBWSxHQUFHekksK0NBQVFBLENBQXFCLEVBQUU7SUFJL0QsTUFBTSxDQUFDMEksUUFBUUMsVUFBVSxHQUFHM0ksK0NBQVFBLENBQUM7SUFFckMsaUJBQWlCO0lBQ2pCLE1BQU0sQ0FBQzRJLFVBQVVDLFlBQVksR0FBRzdJLCtDQUFRQSxDQUFDO0lBRXpDQyxnREFBU0EsQ0FBQztRQUNSNEksWUFBWTtJQUNkLEdBQUcsRUFBRTtJQUVMLGNBQWM7SUFDZDVJLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTTZJLDBCQUEwQjtZQUM5QnZGLFFBQVFDLEdBQUcsQ0FBQyw4Q0FBb0M7Z0JBQzlDZSxZQUFZWCxVQUFVVyxVQUFVO2dCQUNoQ1IsUUFBUUgsVUFBVUcsTUFBTTtnQkFDeEJVLE9BQU9iLFVBQVVhLEtBQUs7Z0JBQ3RCbUU7Z0JBQ0FoRztZQUNGO1lBRUEseUJBQXlCO1lBQ3pCLE1BQU1tRyxZQUFZdEM7WUFDbEIsSUFBSSxDQUFDN0MsVUFBVVcsVUFBVSxJQUFJLENBQUN3RSxXQUFXO2dCQUN2Q3hGLFFBQVFDLEdBQUcsQ0FBQyw0Q0FBa0M7b0JBQzVDZSxZQUFZWCxVQUFVVyxVQUFVO29CQUNoQ3dFO2dCQUNGO2dCQUNBLG9CQUFvQjtnQkFDcEJiLGdCQUFnQjtnQkFDaEJHLHNCQUFzQjtnQkFDdEI7WUFDRjtZQUVBLElBQUk7b0JBTUUxRCxnQkFBZ0NBO2dCQUxwQ3BCLFFBQVFDLEdBQUcsQ0FBQztnQkFDWjRFLGNBQWM7Z0JBQ2QsTUFBTXpELFdBQVcsTUFBTS9ELHNFQUF1QkEsQ0FBQ29JLGlCQUFpQixDQUFDQyxPQUFPckc7Z0JBQ3hFVyxRQUFRQyxHQUFHLENBQUMsc0NBQTRCbUI7Z0JBRXhDLElBQUlBLENBQUFBLHFCQUFBQSxnQ0FBQUEsaUJBQUFBLFNBQVVHLElBQUksY0FBZEgscUNBQUFBLGVBQWdCRSxJQUFJLE1BQUssU0FBT0Ysa0JBQUFBLFNBQVNHLElBQUksY0FBYkgsc0NBQUFBLGdCQUFlRyxJQUFJLEdBQUU7b0JBQ3ZELE1BQU0sRUFBRW9FLFNBQVMsRUFBRUMsTUFBTSxFQUFFLEdBQUd4RSxTQUFTRyxJQUFJLENBQUNBLElBQUk7b0JBQ2hEdkIsUUFBUUMsR0FBRyxDQUFDLGlDQUF1Qjt3QkFBRTBGO3dCQUFXQztvQkFBTztvQkFDdkRqQixnQkFBZ0JnQjtvQkFFaEIsSUFBSUMsVUFBVUEsT0FBT0MsTUFBTSxLQUFLQyxXQUFXO3dCQUN6Q2hCLHNCQUFzQmMsT0FBT0MsTUFBTTtvQkFDckMsT0FBTzt3QkFDTGYsc0JBQXNCO29CQUN4QjtnQkFDRixPQUFPO29CQUNMOUUsUUFBUUMsR0FBRyxDQUFDO29CQUNaLHFCQUFxQjtvQkFDckIwRSxnQkFBZ0I7b0JBQ2hCRyxzQkFBc0I7Z0JBQ3hCO1lBQ0YsRUFBRSxPQUFPN0MsT0FBTztnQkFDZGpDLFFBQVFpQyxLQUFLLENBQUMsa0NBQXdCQTtnQkFDdEMsY0FBYztnQkFDZDBDLGdCQUFnQjtnQkFDaEJHLHNCQUFzQjtZQUN4QixTQUFVO2dCQUNSRCxjQUFjO1lBQ2hCO1FBQ0Y7UUFFQSxvQkFBb0I7UUFDcEIsSUFBSVEsVUFBVTtZQUNackYsUUFBUUMsR0FBRyxDQUFDO1lBQ1pzRjtRQUNGLE9BQU87WUFDTHZGLFFBQVFDLEdBQUcsQ0FBQztRQUNkO0lBQ0YsR0FBRztRQUFDWjtRQUFZZ0IsVUFBVVcsVUFBVTtRQUFFWCxVQUFVRyxNQUFNO1FBQUVILFVBQVVhLEtBQUs7UUFBRW1FO0tBQVM7SUFNbEYsY0FBYztJQUNkM0ksZ0RBQVNBLENBQUM7UUFDUixNQUFNcUoscUJBQXFCO1lBQ3pCLElBQUk7Z0JBQ0YsZUFBZTtnQkFDZixJQUFJMUYsYUFBYUEsVUFBVVcsVUFBVSxFQUFFO3dCQUlMSTtvQkFIaEN5RCxjQUFjO29CQUNkLE1BQU16RCxXQUFXLE1BQU00RSxZQUFZRCxrQkFBa0IsQ0FBQ0wsT0FBT3JHO29CQUM3RFcsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQm1CO29CQUNoQyxJQUFJQSxDQUFBQSxxQkFBQUEsK0JBQUFBLFNBQVV5RSxNQUFNLE1BQUssU0FBT3pFLGlCQUFBQSxTQUFTRyxJQUFJLGNBQWJILHFDQUFBQSxlQUFlRyxJQUFJLEdBQUU7d0JBQ25Eb0QsZ0JBQWdCdkQsU0FBU0csSUFBSSxDQUFDQSxJQUFJLENBQUNvRSxTQUFTO29CQUM5QyxPQUFPO3dCQUNMLG1CQUFtQjt3QkFDbkJoQixnQkFBZ0I7b0JBQ2xCO29CQUNBRSxjQUFjO2dCQUNoQixPQUFPO29CQUNMLGlCQUFpQjtvQkFDakJGLGdCQUFnQjtvQkFDaEJFLGNBQWM7Z0JBQ2hCO1lBQ0YsRUFBRSxPQUFPNUMsT0FBTztnQkFDZGpDLFFBQVFpQyxLQUFLLENBQUMsZUFBZUE7Z0JBQzdCLGNBQWM7Z0JBQ2QwQyxnQkFBZ0I7Z0JBQ2hCRSxjQUFjO1lBQ2hCO1FBQ0Y7UUFFQSxZQUFZO1FBQ1osSUFBSVEsVUFBVTtZQUNaVTtRQUNGO0lBQ0YsR0FBRztRQUFDMUc7UUFBWWdCO1FBQVdnRjtLQUFTO0lBRXBDLFNBQVM7SUFDVDNJLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXVKLG9CQUFvQjtZQUN4QixJQUFJNUcsWUFBWTtnQkFDZCxJQUFJO29CQUNGLE1BQU0rQixXQUFXLE1BQU04RSxlQUFlQyxlQUFlLENBQUNULE9BQU9yRztvQkFDN0QsSUFBSStCLENBQUFBLHFCQUFBQSwrQkFBQUEsU0FBVXlFLE1BQU0sTUFBSyxLQUFLO3dCQUM1QjFCLFVBQVUvQyxTQUFTRyxJQUFJLENBQUNBLElBQUksSUFBSSxFQUFFO29CQUNwQztnQkFDRixFQUFFLE9BQU9VLE9BQU87b0JBQ2RqQyxRQUFRaUMsS0FBSyxDQUFDLGFBQWFBO2dCQUM3QjtZQUNGO1FBQ0Y7UUFFQWdFO0lBQ0YsR0FBRztRQUFDNUc7S0FBVztJQUVmM0MsZ0RBQVNBLENBQUM7UUFDUixVQUFVO1FBQ1YsSUFBSXVDLGFBQWE7WUFDZmlHLFlBQVk7Z0JBQ1Y7b0JBQ0VrQixLQUFLO29CQUNMQyxNQUFNO29CQUNOUixRQUFRO29CQUNSUyxLQUFLckg7Z0JBQ1A7YUFDRDtRQUNILE9BQU87WUFDTGlHLFlBQVksRUFBRTtRQUNoQjtJQUNGLEdBQUc7UUFBQ2pHO0tBQVk7SUFNaEIsU0FBUztJQUNULE1BQU1zSCxpQkFBaUI7UUFDckIsSUFBSTtZQUNGLE1BQU1DLGlCQUFpQnRHLGFBQWF1RyxPQUFPLENBQUM7WUFFNUMsZ0JBQWdCO1lBQ2hCLE1BQU1DLHFCQUFxQixNQUFNbkosd0VBQWFBLENBQUNvSixVQUFVO1lBRXpELElBQUlELG1CQUFtQm5GLElBQUksQ0FBQ0QsSUFBSSxLQUFLLEtBQUs7Z0JBQ3hDLE1BQU1zRixjQUFjRixtQkFBbUJuRixJQUFJLENBQUNBLElBQUk7Z0JBQ2hELGlCQUFpQjtnQkFDakIsTUFBTXNGLGNBQWNELFlBQVlFLElBQUksQ0FBQyxDQUFDQyxPQUFjQSxLQUFLMUgsVUFBVSxLQUFLcUcsT0FBT3JHO2dCQUUvRSxJQUFJd0gsYUFBYTtvQkFDZiwyQkFBMkI7b0JBQzNCN0QsT0FBT2dFLElBQUksQ0FBQyx1QkFBc0MsT0FBZkgsWUFBWTdDLEVBQUUsR0FBSTtvQkFDckQ5RCxhQUFhK0csT0FBTyxDQUFDO2dCQUN2QixPQUFPO29CQUNMLDBCQUEwQjtvQkFDMUJqRSxPQUFPZ0UsSUFBSSxDQUFDLGdCQUFnQjtvQkFDNUI5RyxhQUFheUQsSUFBSSxDQUFDO2dCQUNwQjtZQUNGLE9BQU87Z0JBQ0wsd0JBQXdCO2dCQUN4QlgsT0FBT2dFLElBQUksQ0FBQyxnQkFBZ0I7Z0JBQzVCOUcsYUFBYXlELElBQUksQ0FBQztZQUNwQjtZQUVBLElBQUk2QyxnQkFBZ0I7Z0JBQ2xCQSxlQUFlVSxLQUFLO1lBQ3RCO1FBQ0YsRUFBRSxPQUFPakYsT0FBTztZQUNkakMsUUFBUWlDLEtBQUssQ0FBQyxXQUFXQTtZQUN6Qi9CLGFBQWErQixLQUFLLENBQUM7UUFDckI7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNa0Ysb0JBQW9CO1lBVVA5RyxrQkFRQUs7UUFqQmpCLDJCQUEyQjtRQUMzQixNQUFNMEcsZUFBZXpHLGFBQWFDLE9BQU8sQ0FBQztRQUMxQyxNQUFNRixZQUFZMEcsZUFBZXRHLEtBQUtDLEtBQUssQ0FBQ3FHLGdCQUFnQjtRQUU1RHBILFFBQVFDLEdBQUcsQ0FBQywwQ0FBZ0M7WUFDMUN5RTtZQUNBLG1CQUFtQjtnQkFDakIxRCxVQUFVLEVBQUVYLHNCQUFBQSxnQ0FBQUEsVUFBV1csVUFBVTtnQkFDakNFLEtBQUssRUFBRWIsc0JBQUFBLGdDQUFBQSxVQUFXYSxLQUFLO2dCQUN2Qm1HLFdBQVcsRUFBRWhILHNCQUFBQSxpQ0FBQUEsbUJBQUFBLFVBQVdhLEtBQUssY0FBaEJiLHVDQUFBQSxpQkFBa0JpSCxNQUFNO2dCQUNyQ0MsV0FBVyxRQUFPbEgsc0JBQUFBLGdDQUFBQSxVQUFXYSxLQUFLO2dCQUNsQ1YsTUFBTSxFQUFFSCxzQkFBQUEsZ0NBQUFBLFVBQVdHLE1BQU07Z0JBQ3pCUyxNQUFNLEVBQUVaLHNCQUFBQSxnQ0FBQUEsVUFBV1ksTUFBTTtnQkFDekJRLFFBQVEsRUFBRXBCLHNCQUFBQSxnQ0FBQUEsVUFBV29CLFFBQVE7WUFDL0I7WUFDQSxxQkFBcUI7Z0JBQ25CUCxLQUFLLEVBQUVSLHNCQUFBQSxnQ0FBQUEsVUFBV1EsS0FBSztnQkFDdkJtRyxXQUFXLEVBQUUzRyxzQkFBQUEsaUNBQUFBLG1CQUFBQSxVQUFXUSxLQUFLLGNBQWhCUix1Q0FBQUEsaUJBQWtCNEcsTUFBTTtnQkFDckNDLFdBQVcsUUFBTzdHLHNCQUFBQSxnQ0FBQUEsVUFBV1EsS0FBSztnQkFDbENELE1BQU0sRUFBRVAsc0JBQUFBLGdDQUFBQSxVQUFXTyxNQUFNO2dCQUN6QlEsUUFBUSxFQUFFZixzQkFBQUEsZ0NBQUFBLFVBQVdlLFFBQVE7WUFDL0I7WUFDQSxvQkFBb0I7Z0JBQ2xCLDBCQUEwQixDQUFDLEVBQUNwQixzQkFBQUEsZ0NBQUFBLFVBQVdhLEtBQUs7Z0JBQzVDLDZCQUE2QmIsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXYSxLQUFLLEtBQUliLFVBQVVhLEtBQUssQ0FBQ0MsSUFBSSxPQUFPO2dCQUM1RSwwQkFBMEIsQ0FBQyxFQUFDVCxzQkFBQUEsZ0NBQUFBLFVBQVdRLEtBQUs7Z0JBQzVDLDZCQUE2QlIsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXUSxLQUFLLEtBQUlSLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBV1EsS0FBSyxDQUFDQyxJQUFJLFFBQU87WUFDL0U7UUFDRjtRQUVBLHNCQUFzQjtRQUN0QixJQUFJZCxzQkFBQUEsZ0NBQUFBLFVBQVdZLE1BQU0sRUFBRTtZQUNyQixJQUFJO29CQUlPRyxnQkFDTUEsc0JBQUFBLGlCQUNLQSxpQkFDUkEsaUJBQ0ZBO2dCQVBWLE1BQU1BLFdBQVcsTUFBTTVELGtEQUFPQSxDQUFDNkQsV0FBVyxDQUFDaEIsVUFBVVksTUFBTTtnQkFDM0RqQixRQUFRQyxHQUFHLENBQUMsdUNBQTZCO29CQUN2Q3FCLE1BQU1GLFNBQVNFLElBQUk7b0JBQ25CSixLQUFLLEdBQUVFLGlCQUFBQSxTQUFTRyxJQUFJLGNBQWJILHFDQUFBQSxlQUFlRixLQUFLO29CQUMzQm1HLFdBQVcsR0FBRWpHLGtCQUFBQSxTQUFTRyxJQUFJLGNBQWJILHVDQUFBQSx1QkFBQUEsZ0JBQWVGLEtBQUssY0FBcEJFLDJDQUFBQSxxQkFBc0JrRyxNQUFNO29CQUN6Q0MsV0FBVyxTQUFPbkcsa0JBQUFBLFNBQVNHLElBQUksY0FBYkgsc0NBQUFBLGdCQUFlRixLQUFLO29CQUN0Q08sUUFBUSxHQUFFTCxrQkFBQUEsU0FBU0csSUFBSSxjQUFiSCxzQ0FBQUEsZ0JBQWVLLFFBQVE7b0JBQ2pDUixNQUFNLEdBQUVHLGtCQUFBQSxTQUFTRyxJQUFJLGNBQWJILHNDQUFBQSxnQkFBZTRDLEVBQUU7Z0JBQzNCO1lBQ0YsRUFBRSxPQUFPL0IsT0FBTztnQkFDZGpDLFFBQVFpQyxLQUFLLENBQUMsa0NBQXdCQTtZQUN4QztRQUNGO1FBRUEsa0JBQWtCO1FBQ2xCLElBQUl5QyxjQUFjO1lBQ2hCMUUsUUFBUUMsR0FBRyxDQUFDO1lBQ1pzRztZQUNBO1FBQ0Y7UUFFQSxXQUFXO1FBQ1gsSUFBSSxDQUFDbEcsYUFBYSxDQUFDQSxVQUFVVyxVQUFVLEVBQUU7WUFDdkNoQixRQUFRQyxHQUFHLENBQUM7WUFDWixjQUFjO1lBQ2QsTUFBTXVILGNBQWNwSCxZQUFZO1lBQ2hDRCxPQUFPc0gsSUFBSSxDQUFDLG1CQUFtRCxPQUFoQ0MsbUJBQW1CRjtZQUNsRDtRQUNGO1FBRUEsNEJBQTRCO1FBQzVCLE1BQU1oQyxZQUFZdEM7UUFDbEJsRCxRQUFRQyxHQUFHLENBQUMsK0JBQXFCO1lBQUV1RjtZQUFXaEYsTUFBTSxFQUFFSCxzQkFBQUEsZ0NBQUFBLFVBQVdHLE1BQU07UUFBQztRQUN4RSxJQUFJLENBQUNnRixXQUFXO1lBQ2R4RixRQUFRQyxHQUFHLENBQUM7WUFDWm9EO1lBQ0E7UUFDRjtRQUVBckQsUUFBUUMsR0FBRyxDQUFDO1FBQ1osZ0JBQWdCO1FBQ2hCMEg7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNQyxxQkFBcUI7UUFDekIxSCxhQUFhK0csT0FBTyxDQUFDO0lBRXJCLDhCQUE4QjtJQUM5Qiw4QkFBOEI7SUFDaEM7SUFFQSxTQUFTO0lBQ1QsTUFBTVksc0JBQXNCO1FBQzFCLFNBQVM7UUFDVGxELGdCQUFnQjtRQUNoQkcsc0JBQXNCLElBQUksV0FBVztRQUNyQzVFLGFBQWErRyxPQUFPLENBQUM7UUFFckIsZUFBZTtRQUNmLElBQUl0SCxnQkFBZ0I7WUFDbEJBO1FBQ0Y7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNZ0ksMkJBQTJCO1FBQy9CLElBQUk7Z0JBc0NFdkc7WUFyQ0osZUFBZTtZQUNmLElBQUksQ0FBQ2YsVUFBVWEsS0FBSyxJQUFJYixVQUFVYSxLQUFLLENBQUNDLElBQUksT0FBTyxJQUFJO2dCQUNyRCxVQUFVO2dCQUNWaEUsMEVBQUtBLENBQUNtRyxPQUFPLENBQUM7b0JBQ1ozRSxPQUFPO29CQUNQNEUsU0FBUztvQkFDVEMsUUFBUTtvQkFDUkMsWUFBWTtvQkFDWnFFLE1BQU07d0JBQ0osOEJBQThCO3dCQUM5QixNQUFNQyxhQUFhL0UsT0FBT2dGLFFBQVEsQ0FBQ0MsSUFBSTt3QkFDdkM5SCxPQUFPc0gsSUFBSSxDQUFDLHNDQUFxRSxPQUEvQkMsbUJBQW1CSztvQkFDdkU7b0JBQ0FyRSxVQUFVO3dCQUNSeEQsYUFBYXlELElBQUksQ0FBQztvQkFDcEI7Z0JBQ0Y7Z0JBQ0E7WUFDRjtZQUVBM0QsUUFBUUMsR0FBRyxDQUFDO1lBRVosU0FBUztZQUNULE1BQU11RyxpQkFBaUJ0RyxhQUFhdUcsT0FBTyxDQUFDO1lBRTVDLHlCQUF5QjtZQUN6QixNQUFNeUIsYUFBYTtnQkFDakI3SSxZQUFZcUcsT0FBT3JHO2dCQUNuQjhJLG1CQUFtQjtnQkFDbkJDLHVCQUF1QjtnQkFDdkJDLHFCQUFxQjtnQkFDckJDLGVBQWUsSUFBSXZLLE9BQU93SyxXQUFXO2dCQUNyQ0MsUUFBUSxVQUE2QyxPQUFuQyxJQUFJekssT0FBTzBLLGNBQWMsQ0FBQztZQUM5QztZQUVBLE1BQU1ySCxXQUFXLE1BQU0vRCxzRUFBdUJBLENBQUNxTCxrQkFBa0IsQ0FBQ1I7WUFFbEUsSUFBSTlHLEVBQUFBLGlCQUFBQSxTQUFTRyxJQUFJLGNBQWJILHFDQUFBQSxlQUFlRSxJQUFJLE1BQUssS0FBSztnQkFDL0IsU0FBUztnQkFDVHFELGdCQUFnQjtnQkFDaEJHLHNCQUFzQjtnQkFDdEI1RSxhQUFhK0csT0FBTyxDQUFDO2dCQUVyQixlQUFlO2dCQUNmLElBQUl0SCxnQkFBZ0I7b0JBQ2xCQTtnQkFDRjtnQkFFQSwwQkFBMEI7Z0JBQzFCLElBQUk7b0JBQ0Ysc0JBQXNCO29CQUN0QixNQUFNLElBQUlnSixRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO29CQUVqRCxnQkFBZ0I7b0JBQ2hCLE1BQU1sQyxxQkFBcUIsTUFBTW5KLHdFQUFhQSxDQUFDb0osVUFBVTtvQkFFekQsSUFBSUQsbUJBQW1CbkYsSUFBSSxDQUFDRCxJQUFJLEtBQUssS0FBSzt3QkFDeEMsTUFBTXNGLGNBQWNGLG1CQUFtQm5GLElBQUksQ0FBQ0EsSUFBSTt3QkFDaEQsaUJBQWlCO3dCQUNqQixNQUFNc0YsY0FBY0QsWUFBWUUsSUFBSSxDQUFDLENBQUNDLE9BQWNBLEtBQUsxSCxVQUFVLEtBQUtxRyxPQUFPckc7d0JBRS9FLElBQUl3SCxhQUFhOzRCQUNmLDJCQUEyQjs0QkFDM0I3RCxPQUFPZ0UsSUFBSSxDQUFDLHVCQUFzQyxPQUFmSCxZQUFZN0MsRUFBRSxHQUFJO3dCQUN2RCxPQUFPOzRCQUNMLDBCQUEwQjs0QkFDMUJoQixPQUFPZ0UsSUFBSSxDQUFDLGdCQUFnQjt3QkFDOUI7b0JBQ0YsT0FBTzt3QkFDTCx3QkFBd0I7d0JBQ3hCaEUsT0FBT2dFLElBQUksQ0FBQyxnQkFBZ0I7b0JBQzlCO2dCQUNGLEVBQUUsT0FBTzhCLFdBQVc7b0JBQ2xCOUksUUFBUWlDLEtBQUssQ0FBQyxhQUFhNkc7b0JBQzNCLDBCQUEwQjtvQkFDMUI5RixPQUFPZ0UsSUFBSSxDQUFDLGdCQUFnQjtnQkFDOUI7WUFDRixPQUFPO29CQUNXNUY7Z0JBQWhCLE1BQU0sSUFBSTJILE1BQU0zSCxFQUFBQSxrQkFBQUEsU0FBU0csSUFBSSxjQUFiSCxzQ0FBQUEsZ0JBQWU0SCxPQUFPLEtBQUk7WUFDNUM7WUFFQSxJQUFJeEMsZ0JBQWdCO2dCQUNsQkEsZUFBZVUsS0FBSztZQUN0QjtRQUNGLEVBQUUsT0FBT2pGLE9BQU87WUFDZGpDLFFBQVFpQyxLQUFLLENBQUMsU0FBU0E7WUFDdkIvQixhQUFhK0IsS0FBSyxDQUFDQSxpQkFBaUI4RyxRQUFROUcsTUFBTStHLE9BQU8sR0FBRztRQUM5RDtJQUNGO0lBVUEsYUFBYTtJQUNiLE1BQU1DLGNBQWM7UUFDbEIsSUFBSSxJQUFrQixFQUFhO1lBQ2pDLFlBQVk7WUFDWixNQUFNbEIsYUFBYS9FLE9BQU9nRixRQUFRLENBQUNDLElBQUk7WUFFdkMsU0FBUztZQUNUaUIsVUFBVUMsU0FBUyxDQUFDQyxTQUFTLENBQUNyQixZQUMzQnNCLElBQUksQ0FBQztnQkFDSm5KLGFBQWErRyxPQUFPLENBQUM7WUFDdkIsR0FDQ3ZFLEtBQUssQ0FBQzRHLENBQUFBO2dCQUNMdEosUUFBUWlDLEtBQUssQ0FBQyxTQUFTcUg7Z0JBQ3ZCcEosYUFBYStCLEtBQUssQ0FBQztZQUNyQjtRQUNKO0lBQ0Y7SUFFQSx5QkFBeUI7SUFDekIsSUFBSSxDQUFDb0QsVUFBVTtRQUNiLHFCQUNFLDhEQUFDa0U7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7Ozs7OzhDQUNmLDhEQUFDRDtvQ0FBSUMsV0FBVTs7Ozs7OzhDQUNmLDhEQUFDRDtvQ0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU0zQjtJQUVBLHFCQUNFOzswQkFDRSw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUlYLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBRWIsOERBQUNEO2dDQUFJQyxXQUFXLHVHQUE0SSxPQUFyQ3JFLFNBQVMsZ0JBQWdCOzs7Ozs7MENBRWhKLDhEQUFDb0U7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVyxxRkFBMEgsT0FBckNyRSxTQUFTLGdCQUFnQjs7Ozs7O2tEQUM5SCw4REFBQ29FO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDN00sa0RBQUtBOzRDQUNKOE0sS0FBS3hLOzRDQUNMeUssS0FBSy9LOzRDQUNMZ0wsSUFBSTs0Q0FDSkMsUUFBUTs0Q0FDUkMsT0FBTztnREFDTEMsV0FBVzs0Q0FDYjs0Q0FDQU4sV0FBVTs0Q0FDVk8sU0FBUyxDQUFDQztnREFDUixNQUFNQyxTQUFTRCxFQUFFQyxNQUFNO2dEQUN2QkEsT0FBT0osS0FBSyxDQUFDSyxVQUFVLEdBQUc7NENBQzVCOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FNTiw4REFBQ1g7Z0NBQUlDLFdBQVcsMkdBQW9LLE9BQXpEckUsU0FBUyxnQkFBZ0I7Ozs7Ozs7Ozs7OztrQ0FvRXRKLDhEQUFDb0U7d0JBQUlDLFdBQVcsc0hBQStLLE9BQXpEckUsU0FBUyxnQkFBZ0I7OzBDQUM3Siw4REFBQ29FO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ1c7d0NBQUdYLFdBQVU7OzRDQUNYN0s7MERBQ0QsOERBQUM5Qiw0RkFBUUE7Z0RBQUMyTSxXQUFVOzs7Ozs7Ozs7Ozs7b0NBRXJCakssMkJBQ0MsOERBQUM2Szt3Q0FBRVosV0FBVTs7NENBQXdCOzRDQUFNaks7Ozs7Ozs7a0RBRzdDLDhEQUFDNks7d0NBQUVaLFdBQVU7OzBEQUNYLDhEQUFDYTtnREFBS2IsV0FBVTs7Ozs7OzRDQUFrRzs0Q0FDNUc1TCxXQUFXaUI7NENBQVc7NENBQUlqQixXQUFXbUI7Ozs7Ozs7Ozs7Ozs7MENBSS9DLDhEQUFDd0s7Z0NBQUlDLFdBQVU7O2tEQWtCYiw4REFBQ2M7d0NBQ0NDLFNBQVNwRDt3Q0FDVHFELFVBQVU1Rjt3Q0FDVjRFLFdBQVcsa0VBR1IsT0FIMEU5RSxlQUN6RSx5SUFDQSxzSUFDRDtrREFFRkUsMkJBQ0M7OzhEQUNFLDhEQUFDeUY7b0RBQUtiLFdBQVU7Ozs7OztnREFBOEI7OzJEQUc5QzlFLDZCQUNGO3NEQUFFOzBFQUVGO3NEQUFFOzs7Ozs7O2tEQUlOLDhEQUFDNEY7d0NBQ0NDLFNBQVN0Qjt3Q0FDVE8sV0FBVTtrREFFViw0RUFBQzVNLDRGQUFNQTs0Q0FBQzRNLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTzFCNUosQ0FBQUEsa0JBQWtCQyxtQkFBbUJDLGlCQUFpQkMsZUFBYyxtQkFDcEUsOERBQUN3SjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ3RNLDZEQUFvQkE7b0JBQ25CMEMsZ0JBQWdCQTtvQkFDaEJDLGlCQUFpQkE7b0JBQ2pCQyxlQUFlQTtvQkFDZkMsaUJBQWlCQTtvQkFDakIwSyxlQUFlOUw7Ozs7Ozs7Ozs7O1lBTXBCMEcsMEJBQ0MsOERBQUNqSSxnRUFBV0E7Z0JBQ1ZzTixRQUFRbEc7Z0JBQ1JtRyxTQUFTLElBQU1sRyxtQkFBbUI7Z0JBQ2xDbUcsV0FBV2hEOzs7Ozs7OztBQUtyQjtHQXR6Qk1sSjs7UUF1QldoQix1REFBU0E7UUFDUEMseURBQVdBO1FBVVZYLHFEQUFXQTtRQUNaQyxxREFBV0E7OztLQW5DeEJ5QjtBQXd6Qk4sK0RBQWVBLGNBQWNBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2FjdGl2aXR5L2Zlc3RpdmFsL2NvbXBvbmVudHMvQWN0aXZpdHlIZWFkZXIudHN4PzgwYzEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xyXG5pbXBvcnQgSW1hZ2UgZnJvbSAnbmV4dC9pbWFnZSdcclxuaW1wb3J0IHsgU2hhcmUyLCBTcGFya2xlcyB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcclxuaW1wb3J0IHsgc2VsZWN0VXNlclN0YXRlLCBzZXRVc2VyIH0gZnJvbSAnQC9saWIvc3RvcmUnXHJcbmltcG9ydCB7IHVzZVNlbGVjdG9yLCB1c2VEaXNwYXRjaCB9IGZyb20gJ3JlYWN0LXJlZHV4J1xyXG5cclxuaW1wb3J0IEFjdGl2aXR5TWVkaWFHYWxsZXJ5IGZyb20gJy4vQWN0aXZpdHlNZWRpYUdhbGxlcnknXHJcbmltcG9ydCB7IE1vZGFsIH0gZnJvbSAnYW50ZCdcclxuaW1wb3J0IExvZ2luRGlhbG9nIGZyb20gJy4uLy4uLy4uLy4uL2NvbXBvbmVudHMvbG9naW4tZGlhbG9nJ1xyXG5pbXBvcnQgeyBhY3Rpdml0eVJlZ2lzdHJhdGlvbkFwaSwgQWN0aXZpdHlUeXBlIH0gZnJvbSAnQC9saWIvYXBpL2FjdGl2aXR5J1xyXG5pbXBvcnQgeyBldmVudHNUYXNrQXBpIH0gZnJvbSAnQC9saWIvYXBpL2FjdGl2aXR5L2V2ZW50cy10YXNrJ1xyXG5pbXBvcnQgeyB1c2VyQXBpIH0gZnJvbSAnQC9saWIvYXBpL3VzZXInXHJcbmltcG9ydCB7IEdldE5vdGlmaWNhdGlvbiB9IGZyb20gJ2xvZ2ljLWNvbW1vbi9kaXN0L2NvbXBvbmVudHMvTm90aWZpY2F0aW9uJ1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIsIHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xyXG5cclxuLy8g5Li05pe25a6e546wZm9ybWF0RGF0ZeWHveaVsO+8jOS7peino+WGs+aooeWdl+WvvOWFpemXrumimFxyXG5jb25zdCBmb3JtYXREYXRlID0gKGRhdGVTdHJpbmc6IHN0cmluZyk6IHN0cmluZyA9PiB7XHJcbiAgLy8g5Zyo5pyN5Yqh56uv5riy5p+T5pe277yM55u05o6l6L+U5Zue5Y6f5aeL5a2X56ym5Liy77yM6YG/5YWN5rC05ZCI6ZSZ6K+vXHJcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICByZXR1cm4gZGF0ZVN0cmluZztcclxuICB9XHJcblxyXG4gIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShkYXRlU3RyaW5nKTtcclxuXHJcbiAgLy8g5qOA5p+l5pel5pyf5piv5ZCm5pyJ5pWIXHJcbiAgaWYgKGlzTmFOKGRhdGUuZ2V0VGltZSgpKSkge1xyXG4gICAgcmV0dXJuIGRhdGVTdHJpbmc7IC8vIOWmguaenOaXoOaViO+8jOi/lOWbnuWOn+Wni+Wtl+espuS4slxyXG4gIH1cclxuXHJcbiAgY29uc3QgeWVhciA9IGRhdGUuZ2V0RnVsbFllYXIoKTtcclxuICBjb25zdCBtb250aCA9IFN0cmluZyhkYXRlLmdldE1vbnRoKCkgKyAxKS5wYWRTdGFydCgyLCAnMCcpO1xyXG4gIGNvbnN0IGRheSA9IFN0cmluZyhkYXRlLmdldERhdGUoKSkucGFkU3RhcnQoMiwgJzAnKTtcclxuXHJcbiAgcmV0dXJuIGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fWA7XHJcbn07XHJcblxyXG5pbnRlcmZhY2UgQWN0aXZpdHlIZWFkZXJQcm9wcyB7XHJcbiAgdGl0bGU6IHN0cmluZ1xyXG4gIHN0YXJ0VGltZTogc3RyaW5nXHJcbiAgZW5kVGltZTogc3RyaW5nXHJcbiAgYmFubmVySW1hZ2U6IHN0cmluZ1xyXG4gIGV4cGFuZGVkOiBib29sZWFuXHJcbiAgc2V0RXhwYW5kZWQ6IFJlYWN0LkRpc3BhdGNoPFJlYWN0LlNldFN0YXRlQWN0aW9uPGJvb2xlYW4+PlxyXG4gIGFjdGl2aXR5SWQ6IG51bWJlciB8IHN0cmluZ1xyXG4gIHRhZ3M/OiBBcnJheTx7IGlkOiBudW1iZXIsIG5hbWU6IHN0cmluZywgY29sb3I/OiBzdHJpbmcgfT5cclxuICBvcmdhbml6ZXI/OiBzdHJpbmdcclxuXHJcbiAgYWN0aXZpdHlUeXBlPzogbnVtYmVyIC8vIOa3u+WKoOa0u+WKqOexu+Wei+WxnuaAp1xyXG4gIG9uUmVmcmVzaFdvcmtzPzogKCkgPT4gdm9pZFxyXG4gIC8vIOaWsOWinuWqkuS9k+Wtl+autVxyXG4gIHByb21vdGlvbkltYWdlPzogc3RyaW5nXHJcbiAgYmFja2dyb3VuZEltYWdlPzogc3RyaW5nXHJcbiAgZ2FsbGVyeUltYWdlcz86IHN0cmluZ1xyXG4gIGF0dGFjaG1lbnRGaWxlcz86IHN0cmluZ1xyXG59XHJcblxyXG4vLyDmoIfnrb7mjqXlj6PlrprkuYlcclxuaW50ZXJmYWNlIE5hdlRhZyB7XHJcbiAgaWQ6IHN0cmluZztcclxuICB0ZXh0OiBzdHJpbmc7XHJcbn1cclxuXHJcbmludGVyZmFjZSBDdXN0b21VcGxvYWRGaWxlIGV4dGVuZHMgVXBsb2FkRmlsZSB7XHJcbiAgZXJyb3I/OiBhbnk7XHJcbn1cclxuXHJcbmNvbnN0IEFjdGl2aXR5SGVhZGVyOiBSZWFjdC5GQzxBY3Rpdml0eUhlYWRlclByb3BzPiA9ICh7XHJcbiAgdGl0bGU6IGluaXRpYWxUaXRsZSxcclxuICBzdGFydFRpbWU6IGluaXRpYWxTdGFydFRpbWUsXHJcbiAgZW5kVGltZTogaW5pdGlhbEVuZFRpbWUsXHJcbiAgYmFubmVySW1hZ2U6IGluaXRpYWxCYW5uZXJJbWFnZSxcclxuICBleHBhbmRlZCxcclxuICBzZXRFeHBhbmRlZCxcclxuICBhY3Rpdml0eUlkLFxyXG4gIHRhZ3MgPSBbXSxcclxuICBvcmdhbml6ZXI6IGluaXRpYWxPcmdhbml6ZXIgPSAnJyxcclxuXHJcbiAgYWN0aXZpdHlUeXBlID0gQWN0aXZpdHlUeXBlLldPUkssIC8vIOm7mOiupOS4uuS9nOWTgea0u+WKqFxyXG4gIG9uUmVmcmVzaFdvcmtzLFxyXG4gIC8vIOWqkuS9k+Wtl+autVxyXG4gIHByb21vdGlvbkltYWdlLFxyXG4gIGJhY2tncm91bmRJbWFnZSxcclxuICBnYWxsZXJ5SW1hZ2VzLFxyXG4gIGF0dGFjaG1lbnRGaWxlc1xyXG59KSA9PiB7XHJcbiAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSBBY3Rpdml0eUhlYWRlciDnu4Tku7bmuLLmn5MnLCB7IGFjdGl2aXR5SWQsIGluaXRpYWxUaXRsZSB9KTtcclxuXHJcblxyXG4gIGNvbnN0IG5vdGlmaWNhdGlvbiA9IEdldE5vdGlmaWNhdGlvbigpO1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKTtcclxuXHJcbiAgLy8g5pi+56S655So55qE5qCH6aKY5ZKM5pe26Ze0XHJcbiAgY29uc3QgdGl0bGUgPSBpbml0aWFsVGl0bGU7XHJcbiAgY29uc3Qgc3RhcnRUaW1lID0gaW5pdGlhbFN0YXJ0VGltZTtcclxuICBjb25zdCBlbmRUaW1lID0gaW5pdGlhbEVuZFRpbWU7XHJcbiAgY29uc3QgYmFubmVySW1hZ2UgPSBpbml0aWFsQmFubmVySW1hZ2U7XHJcbiAgY29uc3Qgb3JnYW5pemVyID0gaW5pdGlhbE9yZ2FuaXplcjtcclxuXHJcbiAgLy8g6I635Y+W5b2T5YmN55So5oi35L+h5oGv5ZKMZGlzcGF0Y2hcclxuICBjb25zdCB1c2VyU3RhdGUgPSB1c2VTZWxlY3RvcihzZWxlY3RVc2VyU3RhdGUpXHJcbiAgY29uc3QgZGlzcGF0Y2ggPSB1c2VEaXNwYXRjaCgpXHJcbiAgY29uc3QgaXNBZG1pbiA9IHVzZXJTdGF0ZT8ucm9sZUlkID09PSA0XHJcblxyXG4gIC8vIOWQjOatpeeUqOaIt+eKtuaAgeeahOWHveaVsCAtIOWinuW8uueJiO+8jOS7jkFQSeiOt+WPluacgOaWsOeUqOaIt+S/oeaBr1xyXG4gIGNvbnN0IHN5bmNVc2VyU3RhdGUgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIOW8gOWni+WQjOatpeeUqOaIt+eKtuaAgScpO1xyXG4gICAgICBjb25zdCBsb2NhbFVzZXIgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndXNlcicpO1xyXG4gICAgICBpZiAobG9jYWxVc2VyKSB7XHJcbiAgICAgICAgY29uc3QgdXNlckRhdGEgPSBKU09OLnBhcnNlKGxvY2FsVXNlcik7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSBsb2NhbFN0b3JhZ2Ug5Lit55qE55So5oi35pWw5o2uJywgdXNlckRhdGEpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10gUmVkdXgg5Lit55qE55So5oi35pWw5o2uJywgdXNlclN0YXRlKTtcclxuXHJcbiAgICAgICAgLy8g5aaC5p6c55So5oi35bey55m75b2V5L2GcGhvbmXkuLrnqbrvvIzlsJ3or5Xku45BUEnojrflj5bmnIDmlrDnlKjmiLfkv6Hmga9cclxuICAgICAgICBpZiAodXNlckRhdGEuaXNMb2dnZWRJbiAmJiB1c2VyRGF0YS51c2VySWQgJiYgKCF1c2VyRGF0YS5waG9uZSB8fCB1c2VyRGF0YS5waG9uZS50cmltKCkgPT09ICcnKSkge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSDmo4DmtYvliLDnlKjmiLdwaG9uZeS4uuepuu+8jOS7jkFQSeiOt+WPluacgOaWsOeUqOaIt+S/oeaBrycpO1xyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB1c2VyQXBpLmdldFVzZXJJbmZvKHVzZXJEYXRhLnVzZXJJZCk7XHJcbiAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAgJiYgcmVzcG9uc2UuZGF0YSkge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10g5LuOQVBJ6I635Y+W5Yiw5pyA5paw55So5oi35L+h5oGvJywgcmVzcG9uc2UuZGF0YSk7XHJcblxyXG4gICAgICAgICAgICAgIC8vIOaehOW7uuabtOaWsOWQjueahOeUqOaIt+aVsOaNrlxyXG4gICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRVc2VyRGF0YSA9IHtcclxuICAgICAgICAgICAgICAgIC4uLnVzZXJEYXRhLFxyXG4gICAgICAgICAgICAgICAgcGhvbmU6IHJlc3BvbnNlLmRhdGEucGhvbmUgfHwgJycsXHJcbiAgICAgICAgICAgICAgICBuaWNrTmFtZTogcmVzcG9uc2UuZGF0YS5uaWNrTmFtZSB8fCB1c2VyRGF0YS5uaWNrTmFtZSxcclxuICAgICAgICAgICAgICAgIGF2YXRhclVybDogcmVzcG9uc2UuZGF0YS5hdmF0YXJVcmwgfHwgdXNlckRhdGEuYXZhdGFyVXJsLFxyXG4gICAgICAgICAgICAgICAgZ2VuZGVyOiByZXNwb25zZS5kYXRhLmdlbmRlciB8fCB1c2VyRGF0YS5nZW5kZXIsXHJcbiAgICAgICAgICAgICAgICByb2xlSWQ6IHJlc3BvbnNlLmRhdGEucm9sZUlkIHx8IHVzZXJEYXRhLnJvbGVJZFxyXG4gICAgICAgICAgICAgIH07XHJcblxyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10g5pu05paw55So5oi35pWw5o2uJywge1xyXG4gICAgICAgICAgICAgICAgb2xkUGhvbmU6IHVzZXJEYXRhLnBob25lLFxyXG4gICAgICAgICAgICAgICAgbmV3UGhvbmU6IHVwZGF0ZWRVc2VyRGF0YS5waG9uZSxcclxuICAgICAgICAgICAgICAgIHVwZGF0ZWQ6IHVwZGF0ZWRVc2VyRGF0YVxyXG4gICAgICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgICAgICAvLyDmm7TmlrBsb2NhbFN0b3JhZ2XlkoxSZWR1eFxyXG4gICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd1c2VyJywgSlNPTi5zdHJpbmdpZnkodXBkYXRlZFVzZXJEYXRhKSk7XHJcbiAgICAgICAgICAgICAgZGlzcGF0Y2goc2V0VXNlcih1cGRhdGVkVXNlckRhdGEpKTtcclxuICAgICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ/CflI0gW0RFQlVHXSDku45BUEnojrflj5bnlKjmiLfkv6Hmga/lpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8g5qOA5p+l5YWz6ZSu5a2X5q615piv5ZCm5pyJ5Y+Y5YyWXHJcbiAgICAgICAgY29uc3QgaGFzUGhvbmVDaGFuZ2VkID0gdXNlckRhdGEucGhvbmUgIT09IHVzZXJTdGF0ZT8ucGhvbmU7XHJcbiAgICAgICAgY29uc3QgaGFzUm9sZUNoYW5nZWQgPSB1c2VyRGF0YS5yb2xlSWQgIT09IHVzZXJTdGF0ZT8ucm9sZUlkO1xyXG4gICAgICAgIGNvbnN0IGhhc0xvZ2luU3RhdHVzQ2hhbmdlZCA9IHVzZXJEYXRhLmlzTG9nZ2VkSW4gIT09IHVzZXJTdGF0ZT8uaXNMb2dnZWRJbjtcclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSDnlKjmiLfnirbmgIHlj5jljJbmo4Dmn6UnLCB7XHJcbiAgICAgICAgICBwaG9uZUNoYW5nZWQ6IGhhc1Bob25lQ2hhbmdlZCxcclxuICAgICAgICAgIHJvbGVDaGFuZ2VkOiBoYXNSb2xlQ2hhbmdlZCxcclxuICAgICAgICAgIGxvZ2luU3RhdHVzQ2hhbmdlZDogaGFzTG9naW5TdGF0dXNDaGFuZ2VkLFxyXG4gICAgICAgICAgb2xkUGhvbmU6IHVzZXJTdGF0ZT8ucGhvbmUsXHJcbiAgICAgICAgICBuZXdQaG9uZTogdXNlckRhdGEucGhvbmUsXHJcbiAgICAgICAgICBvbGRSb2xlOiB1c2VyU3RhdGU/LnJvbGVJZCxcclxuICAgICAgICAgIG5ld1JvbGU6IHVzZXJEYXRhLnJvbGVJZFxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICAvLyDlpoLmnpxsb2NhbFN0b3JhZ2XkuK3nmoTnlKjmiLfnirbmgIHkuI5SZWR1eOS4reeahOS4jeS4gOiHtO+8jOivtOaYjumcgOimgeabtOaWsFxyXG4gICAgICAgIGlmIChoYXNQaG9uZUNoYW5nZWQgfHwgaGFzUm9sZUNoYW5nZWQgfHwgaGFzTG9naW5TdGF0dXNDaGFuZ2VkKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIOajgOa1i+WIsOeUqOaIt+eKtuaAgeWPmOWMlu+8jOWQjOatpVJlZHV454q25oCBJyk7XHJcbiAgICAgICAgICBkaXNwYXRjaChzZXRVc2VyKHVzZXJEYXRhKSk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10g55So5oi354q25oCB5peg5Y+Y5YyW77yM5peg6ZyA5ZCM5q2lJyk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10gbG9jYWxTdG9yYWdlIOS4reaXoOeUqOaIt+aVsOaNricpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfwn5SNIFtERUJVR10g5qOA5p+l55So5oi354q25oCB5aSx6LSlOicsIGVycm9yKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyDnm5HlkKzpobXpnaLnhKbngrnlj5jljJblkozot6/nlLHlj5jljJbvvIzph43mlrDmo4Dmn6XnlKjmiLfnirbmgIFcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8g6aG16Z2i5Yqg6L295pe256uL5Y2z5qOA5p+l5LiA5qyhXHJcbiAgICBzeW5jVXNlclN0YXRlKCkuY2F0Y2goY29uc29sZS5lcnJvcik7XHJcblxyXG4gICAgY29uc3QgaGFuZGxlVmlzaWJpbGl0eUNoYW5nZSA9ICgpID0+IHtcclxuICAgICAgaWYgKGRvY3VtZW50LnZpc2liaWxpdHlTdGF0ZSA9PT0gJ3Zpc2libGUnKSB7XHJcbiAgICAgICAgc3luY1VzZXJTdGF0ZSgpLmNhdGNoKGNvbnNvbGUuZXJyb3IpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIGNvbnN0IGhhbmRsZUZvY3VzID0gKCkgPT4ge1xyXG4gICAgICBzeW5jVXNlclN0YXRlKCkuY2F0Y2goY29uc29sZS5lcnJvcik7XHJcbiAgICB9O1xyXG5cclxuICAgIC8vIOa3u+WKoOWkmuenjeS6i+S7tuebkeWQrOehruS/neeKtuaAgeWQjOatpVxyXG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigndmlzaWJpbGl0eWNoYW5nZScsIGhhbmRsZVZpc2liaWxpdHlDaGFuZ2UpO1xyXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2ZvY3VzJywgaGFuZGxlRm9jdXMpO1xyXG5cclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Zpc2liaWxpdHljaGFuZ2UnLCBoYW5kbGVWaXNpYmlsaXR5Q2hhbmdlKTtcclxuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2ZvY3VzJywgaGFuZGxlRm9jdXMpO1xyXG4gICAgfTtcclxuICB9LCBbdXNlclN0YXRlPy5waG9uZSwgZGlzcGF0Y2hdKTtcclxuXHJcbiAgLy8g5bel5YW35Ye95pWw77ya5qOA5p+l55So5oi35piv5ZCm5Li65a2m55Sf6Lqr5Lu9XHJcbiAgY29uc3QgY2hlY2tJc1N0dWRlbnQgPSAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB1c2VyU3RyID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3VzZXInKTtcclxuICAgICAgY29uc3QgbG9jYWxVc2VyID0gdXNlclN0ciA/IEpTT04ucGFyc2UodXNlclN0cikgOiBudWxsO1xyXG4gICAgICBjb25zdCB1c2VyUm9sZUlkID0gbG9jYWxVc2VyPy5yb2xlSWQgfHwgdXNlclN0YXRlPy5yb2xlSWQ7XHJcbiAgICAgIHJldHVybiB1c2VyUm9sZUlkID09PSAxOyAvLyDlrabnlJ/ouqvku73nmoRyb2xlSWTkuLoxXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfop6PmnpBsb2NhbFN0b3JhZ2XkuK3nmoTnlKjmiLfkv6Hmga/lpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcblxyXG5cclxuICAvLyDlt6Xlhbflh73mlbDvvJrmmL7npLrouqvku73pqozor4HlpLHotKXmj5DnpLpcclxuICBjb25zdCBzaG93U3R1ZGVudE9ubHlNb2RhbCA9ICgpID0+IHtcclxuICAgIE1vZGFsLmNvbmZpcm0oe1xyXG4gICAgICB0aXRsZTogJ+i6q+S7vemqjOivgScsXHJcbiAgICAgIGNvbnRlbnQ6ICfmirHmrYnvvIzlj6rmnInlrabnlJ/ouqvku73nmoTnlKjmiLfmiY3og73lj4LliqDmraTmtLvliqjmiqXlkI3jgILlpoLmnpzmgqjmmK/lrabnlJ/vvIzor7fogZTns7vnrqHnkIblkZjmm7TmlrDmgqjnmoTouqvku73kv6Hmga/jgIInLFxyXG4gICAgICBva1RleHQ6ICfmiJHnn6XpgZPkuoYnLFxyXG4gICAgICBjYW5jZWxUZXh0OiAn6IGU57O7566h55CG5ZGYJyxcclxuICAgICAgb25DYW5jZWw6ICgpID0+IHtcclxuICAgICAgICBub3RpZmljYXRpb24uaW5mbygn5aaC6ZyA5biu5Yqp77yM6K+36IGU57O75a6i5pyN77yaNDAwLTEyMy00NTY3Jyk7XHJcbiAgICAgIH1cclxuICAgIH0pO1xyXG4gIH07XHJcblxyXG4gIC8vIOiOt+WPluaKpeWQjeeKtuaAgeaWh+acrFxyXG4gIGNvbnN0IGdldFJlZ2lzdHJhdGlvblN0YXR1c1RleHQgPSAoKSA9PiB7XHJcbiAgICBzd2l0Y2ggKHJlZ2lzdHJhdGlvblN0YXR1cykge1xyXG4gICAgICBjYXNlIDA6XHJcbiAgICAgICAgcmV0dXJuICflt7Llj5bmtognO1xyXG4gICAgICBjYXNlIDE6XHJcbiAgICAgICAgcmV0dXJuICflt7LmiqXlkI0nO1xyXG4gICAgICBjYXNlIDI6XHJcbiAgICAgICAgcmV0dXJuICflt7LlrqHmoLjpgJrov4cnO1xyXG4gICAgICBjYXNlIDM6XHJcbiAgICAgICAgcmV0dXJuICflt7Lmi5Lnu50nO1xyXG4gICAgICBjYXNlIDQ6XHJcbiAgICAgICAgcmV0dXJuICfor4TlrqHkuK0nO1xyXG4gICAgICBjYXNlIDU6XHJcbiAgICAgICAgcmV0dXJuICflt7LojrflpZYnO1xyXG4gICAgICBjYXNlIDY6XHJcbiAgICAgICAgcmV0dXJuICflrqHmoLjkuK0nO1xyXG4gICAgICBkZWZhdWx0OlxyXG4gICAgICAgIHJldHVybiAn5bey5oql5ZCNJztcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyDlj6/nvJbovpHnmoTlr7zoiKrmoIfnrb7mlofmnKxcclxuICBjb25zdCBbbmF2VGFncywgc2V0TmF2VGFnc10gPSB1c2VTdGF0ZTxOYXZUYWdbXT4oW1xyXG4gICAgeyBpZDogJ3RhZzEnLCB0ZXh0OiBcIuWumOaWuea0u+WKqFwiIH0sXHJcbiAgICB7IGlkOiAndGFnMicsIHRleHQ6IFwiQUlHQ+S9nOWTgeW+gembhlwiIH1cclxuICBdKTtcclxuXHJcbiAgLy8g5rS75Yqo5qCH562+SUTliJfooahcclxuICBjb25zdCBbdGFnSWRzLCBzZXRUYWdJZHNdID0gdXNlU3RhdGU8bnVtYmVyW10+KFtdKTtcclxuXHJcbiAgLy8g5qCH562+57yW6L6R54q25oCBXHJcbiAgY29uc3QgW2VkaXRpbmdUYWdJZCwgc2V0RWRpdGluZ1RhZ0lkXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtlZGl0aW5nVGFnVGV4dCwgc2V0RWRpdGluZ1RhZ1RleHRdID0gdXNlU3RhdGUoJycpO1xyXG5cclxuICAvLyDnmbvlvZXlkozmiqXlkI3nm7jlhbPnirbmgIFcclxuICBjb25zdCBbc2hvd0xvZ2luRGlhbG9nLCBzZXRTaG93TG9naW5EaWFsb2ddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtoYXNTdWJtaXR0ZWQsIHNldEhhc1N1Ym1pdHRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzQ2hlY2tpbmcsIHNldElzQ2hlY2tpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtyZWdpc3RyYXRpb25TdGF0dXMsIHNldFJlZ2lzdHJhdGlvblN0YXR1c10gPSB1c2VTdGF0ZTxudW1iZXIgfCBudWxsPihudWxsKTsgLy8g5oql5ZCN54q25oCB77yaMC3lt7Llj5bmtoggMS3lt7LmiqXlkI0gMi3lt7LlrqHmoLjpgJrov4cgMy3lt7Lmi5Lnu50gNC3or4TlrqHkuK0gNS3lt7LojrflpZYgNi3lrqHmoLjkuK1cclxuXHJcbiAgY29uc3QgW3VwbG9hZExvYWRpbmcsIHNldFVwbG9hZExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtmaWxlTGlzdCwgc2V0RmlsZUxpc3RdID0gdXNlU3RhdGU8Q3VzdG9tVXBsb2FkRmlsZVtdPihbXSk7XHJcblxyXG5cclxuXHJcbiAgY29uc3QgW3Nob3dVSSwgc2V0U2hvd1VJXSA9IHVzZVN0YXRlKHRydWUpO1xyXG5cclxuICAvLyDmt7vliqDlrqLmiLfnq6/mo4DmtYvmnaXop6PlhrPmsLTlkIjplJnor69cclxuICBjb25zdCBbaXNDbGllbnQsIHNldElzQ2xpZW50XSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIHNldElzQ2xpZW50KHRydWUpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8g6aG16Z2i5Yqg6L295pe25qOA5p+l5oql5ZCN54q25oCBXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGNoZWNrUmVnaXN0cmF0aW9uU3RhdHVzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIOajgOafpeaKpeWQjeeKtuaAgSAtIHVzZUVmZmVjdCDop6blj5EnLCB7XHJcbiAgICAgICAgaXNMb2dnZWRJbjogdXNlclN0YXRlLmlzTG9nZ2VkSW4sXHJcbiAgICAgICAgcm9sZUlkOiB1c2VyU3RhdGUucm9sZUlkLFxyXG4gICAgICAgIHBob25lOiB1c2VyU3RhdGUucGhvbmUsXHJcbiAgICAgICAgaXNDbGllbnQsXHJcbiAgICAgICAgYWN0aXZpdHlJZFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIC8vIOWPquacieWcqOeUqOaIt+W3sueZu+W9leS4lOaYr+WtpueUn+i6q+S7veaXtuaJjeajgOafpeaKpeWQjeeKtuaAgVxyXG4gICAgICBjb25zdCBpc1N0dWRlbnQgPSBjaGVja0lzU3R1ZGVudCgpO1xyXG4gICAgICBpZiAoIXVzZXJTdGF0ZS5pc0xvZ2dlZEluIHx8ICFpc1N0dWRlbnQpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIOeUqOaIt+acqueZu+W9leaIluS4jeaYr+WtpueUn+i6q+S7ve+8jOmHjee9ruaKpeWQjeeKtuaAgScsIHtcclxuICAgICAgICAgIGlzTG9nZ2VkSW46IHVzZXJTdGF0ZS5pc0xvZ2dlZEluLFxyXG4gICAgICAgICAgaXNTdHVkZW50XHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAgLy8g5aaC5p6c55So5oi35pyq55m75b2V5oiW5LiN5piv5a2m55Sf77yM6YeN572u54q25oCBXHJcbiAgICAgICAgc2V0SGFzU3VibWl0dGVkKGZhbHNlKTtcclxuICAgICAgICBzZXRSZWdpc3RyYXRpb25TdGF0dXMobnVsbCk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10g5byA5aeL5qOA5p+l5oql5ZCN54q25oCBIEFQSSDosIPnlKgnKTtcclxuICAgICAgICBzZXRJc0NoZWNraW5nKHRydWUpO1xyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYWN0aXZpdHlSZWdpc3RyYXRpb25BcGkuY2hlY2tSZWdpc3RyYXRpb24oTnVtYmVyKGFjdGl2aXR5SWQpKTtcclxuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIOmhtemdouWKoOi9veajgOafpeaKpeWQjeeKtuaAgeWTjeW6lDonLCByZXNwb25zZSk7XHJcblxyXG4gICAgICAgIGlmIChyZXNwb25zZT8uZGF0YT8uY29kZSA9PT0gMjAwICYmIHJlc3BvbnNlLmRhdGE/LmRhdGEpIHtcclxuICAgICAgICAgIGNvbnN0IHsgc3VibWl0dGVkLCBzdWJtaXQgfSA9IHJlc3BvbnNlLmRhdGEuZGF0YTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10g5oql5ZCN54q25oCB5qOA5p+l57uT5p6cJywgeyBzdWJtaXR0ZWQsIHN1Ym1pdCB9KTtcclxuICAgICAgICAgIHNldEhhc1N1Ym1pdHRlZChzdWJtaXR0ZWQpO1xyXG5cclxuICAgICAgICAgIGlmIChzdWJtaXQgJiYgc3VibWl0LnN0YXR1cyAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgICAgIHNldFJlZ2lzdHJhdGlvblN0YXR1cyhzdWJtaXQuc3RhdHVzKTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHNldFJlZ2lzdHJhdGlvblN0YXR1cyhudWxsKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSDmiqXlkI3nirbmgIHlk43lupTkuI3nrKblkIjpooTmnJ/vvIzorr7nva7kuLrmnKrmiqXlkI3nirbmgIEnKTtcclxuICAgICAgICAgIC8vIOWmguaenOWTjeW6lOS4jeespuWQiOmihOacn++8jOiuvue9ruS4uuacquaKpeWQjeeKtuaAgVxyXG4gICAgICAgICAgc2V0SGFzU3VibWl0dGVkKGZhbHNlKTtcclxuICAgICAgICAgIHNldFJlZ2lzdHJhdGlvblN0YXR1cyhudWxsKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcign8J+UjSBbREVCVUddIOajgOafpeaKpeWQjeeKtuaAgeWksei0pTonLCBlcnJvcik7XHJcbiAgICAgICAgLy8g5Ye66ZSZ5pe26K6+572u5Li65pyq5oql5ZCN54q25oCBXHJcbiAgICAgICAgc2V0SGFzU3VibWl0dGVkKGZhbHNlKTtcclxuICAgICAgICBzZXRSZWdpc3RyYXRpb25TdGF0dXMobnVsbCk7XHJcbiAgICAgIH0gZmluYWxseSB7XHJcbiAgICAgICAgc2V0SXNDaGVja2luZyhmYWxzZSk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgLy8g5Y+q5Zyo5a6i5oi356uv5omn6KGM77yM6YG/5YWN5pyN5Yqh56uv5riy5p+T6Zeu6aKYXHJcbiAgICBpZiAoaXNDbGllbnQpIHtcclxuICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSDlrqLmiLfnq6/njq/looPvvIzmiafooYzmiqXlkI3nirbmgIHmo4Dmn6UnKTtcclxuICAgICAgY2hlY2tSZWdpc3RyYXRpb25TdGF0dXMoKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10g6Z2e5a6i5oi356uv546v5aKD77yM6Lez6L+H5oql5ZCN54q25oCB5qOA5p+lJyk7XHJcbiAgICB9XHJcbiAgfSwgW2FjdGl2aXR5SWQsIHVzZXJTdGF0ZS5pc0xvZ2dlZEluLCB1c2VyU3RhdGUucm9sZUlkLCB1c2VyU3RhdGUucGhvbmUsIGlzQ2xpZW50XSk7XHJcblxyXG5cclxuXHJcblxyXG5cclxuICAvLyDmo4Dmn6XnlKjmiLfmmK/lkKblt7Lmj5DkuqTkvZzlk4FcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgY2hlY2tVc2VyU3VibWl0dGVkID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIC8vIOS7hemSiOWvueW3sueZu+W9leeUqOaIt+i/m+ihjOajgOafpVxyXG4gICAgICAgIGlmICh1c2VyU3RhdGUgJiYgdXNlclN0YXRlLmlzTG9nZ2VkSW4pIHtcclxuICAgICAgICAgIHNldElzQ2hlY2tpbmcodHJ1ZSk7XHJcbiAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFjdGl2aXR5QXBpLmNoZWNrVXNlclN1Ym1pdHRlZChOdW1iZXIoYWN0aXZpdHlJZCkpO1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ+mhtemdouWKoOi9veaXtuajgOafpeeUqOaIt+aPkOS6pOeKtuaAgeWTjeW6lDonLCByZXNwb25zZSk7XHJcbiAgICAgICAgICBpZiAocmVzcG9uc2U/LnN0YXR1cyA9PT0gMjAwICYmIHJlc3BvbnNlLmRhdGE/LmRhdGEpIHtcclxuICAgICAgICAgICAgc2V0SGFzU3VibWl0dGVkKHJlc3BvbnNlLmRhdGEuZGF0YS5zdWJtaXR0ZWQpO1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgLy8g5aaC5p6c5ZON5bqU5LiN56ym5ZCI6aKE5pyf77yM6buY6K6k5Li65pyq5o+Q5LqkXHJcbiAgICAgICAgICAgIHNldEhhc1N1Ym1pdHRlZChmYWxzZSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBzZXRJc0NoZWNraW5nKGZhbHNlKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgLy8g5pyq55m75b2V55So5oi377yM55u05o6l6K6+572u5Li65pyq5o+Q5LqkXHJcbiAgICAgICAgICBzZXRIYXNTdWJtaXR0ZWQoZmFsc2UpO1xyXG4gICAgICAgICAgc2V0SXNDaGVja2luZyhmYWxzZSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ajgOafpeeUqOaIt+aPkOS6pOeKtuaAgeWksei0pTonLCBlcnJvcik7XHJcbiAgICAgICAgLy8g5Ye66ZSZ5pe26buY6K6k6K6+572u5Li65pyq5o+Q5LqkXHJcbiAgICAgICAgc2V0SGFzU3VibWl0dGVkKGZhbHNlKTtcclxuICAgICAgICBzZXRJc0NoZWNraW5nKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICAvLyDpobXpnaLliqDovb3ml7bmiafooYzmo4Dmn6VcclxuICAgIGlmIChpc0NsaWVudCkge1xyXG4gICAgICBjaGVja1VzZXJTdWJtaXR0ZWQoKTtcclxuICAgIH1cclxuICB9LCBbYWN0aXZpdHlJZCwgdXNlclN0YXRlLCBpc0NsaWVudF0pO1xyXG5cclxuICAvLyDliqDovb3mtLvliqjmoIfnrb5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgZmV0Y2hBY3Rpdml0eVRhZ3MgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGlmIChhY3Rpdml0eUlkKSB7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYWN0aXZpdHlUYWdBcGkuZ2V0QWN0aXZpdHlUYWdzKE51bWJlcihhY3Rpdml0eUlkKSk7XHJcbiAgICAgICAgICBpZiAocmVzcG9uc2U/LnN0YXR1cyA9PT0gMjAwKSB7XHJcbiAgICAgICAgICAgIHNldFRhZ0lkcyhyZXNwb25zZS5kYXRhLmRhdGEgfHwgW10pO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bmtLvliqjmoIfnrb7lpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBmZXRjaEFjdGl2aXR5VGFncygpO1xyXG4gIH0sIFthY3Rpdml0eUlkXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAvLyDliJ3lp4vljJbmlofku7bliJfooahcclxuICAgIGlmIChiYW5uZXJJbWFnZSkge1xyXG4gICAgICBzZXRGaWxlTGlzdChbXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgdWlkOiAnLTEnLFxyXG4gICAgICAgICAgbmFtZTogJ+W9k+WJjeWwgemdoicsXHJcbiAgICAgICAgICBzdGF0dXM6ICdkb25lJyxcclxuICAgICAgICAgIHVybDogYmFubmVySW1hZ2VcclxuICAgICAgICB9XHJcbiAgICAgIF0pO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0RmlsZUxpc3QoW10pO1xyXG4gICAgfVxyXG4gIH0sIFtiYW5uZXJJbWFnZV0pO1xyXG5cclxuXHJcblxyXG5cclxuXHJcbiAgLy8g5YmN5b6A5Lu75Yqh5Ye95pWwXHJcbiAgY29uc3QgaGFuZGxlR29Ub1Rhc2sgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBsb2FkaW5nTWVzc2FnZSA9IG5vdGlmaWNhdGlvbi5sb2FkaW5nKCfmraPlnKjot7PovazliLDku7vliqEuLi4nKTtcclxuXHJcbiAgICAgIC8vIOiOt+WPluW9k+WJjeeUqOaIt+eahOi1m+S6i+S7u+WKoeWIl+ihqFxyXG4gICAgICBjb25zdCBldmVudHNUYXNrUmVzcG9uc2UgPSBhd2FpdCBldmVudHNUYXNrQXBpLmdldE15VGFza3MoKTtcclxuXHJcbiAgICAgIGlmIChldmVudHNUYXNrUmVzcG9uc2UuZGF0YS5jb2RlID09PSAyMDApIHtcclxuICAgICAgICBjb25zdCBldmVudHNUYXNrcyA9IGV2ZW50c1Rhc2tSZXNwb25zZS5kYXRhLmRhdGE7XHJcbiAgICAgICAgLy8g5p+l5om+5LiO5b2T5YmN5rS75Yqo55u45YWz55qE6LWb5LqL5Lu75YqhXHJcbiAgICAgICAgY29uc3QgcmVsYXRlZFRhc2sgPSBldmVudHNUYXNrcy5maW5kKCh0YXNrOiBhbnkpID0+IHRhc2suYWN0aXZpdHlJZCA9PT0gTnVtYmVyKGFjdGl2aXR5SWQpKTtcclxuXHJcbiAgICAgICAgaWYgKHJlbGF0ZWRUYXNrKSB7XHJcbiAgICAgICAgICAvLyDlnKjmlrDmoIfnrb7pobXkuK3miZPlvIDnj63nuqfnqbrpl7Tlubboh6rliqjmiZPlvIDlr7nlupTnmoTotZvkuovku7vliqFcclxuICAgICAgICAgIHdpbmRvdy5vcGVuKGAvY2xhc3Mtc3BhY2U/dGFza0lkPSR7cmVsYXRlZFRhc2suaWR9YCwgJ19ibGFuaycpO1xyXG4gICAgICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+ato+WcqOi3s+i9rOWIsOS7u+WKoemhtemdoi4uLicpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAvLyDlpoLmnpzmsqHmnInmib7liLDlr7nlupTku7vliqHvvIzlnKjmlrDmoIfnrb7pobXkuK3miZPlvIDnj63nuqfnqbrpl7RcclxuICAgICAgICAgIHdpbmRvdy5vcGVuKCcvY2xhc3Mtc3BhY2UnLCAnX2JsYW5rJyk7XHJcbiAgICAgICAgICBub3RpZmljYXRpb24uaW5mbygn5pyq5om+5Yiw5a+55bqU5Lu75Yqh77yM5bey5omT5byA54+t57qn56m66Ze0Jyk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIC8vIOWmguaenOiOt+WPluS7u+WKoeWksei0pe+8jOWcqOaWsOagh+etvumhteS4reaJk+W8gOePree6p+epuumXtFxyXG4gICAgICAgIHdpbmRvdy5vcGVuKCcvY2xhc3Mtc3BhY2UnLCAnX2JsYW5rJyk7XHJcbiAgICAgICAgbm90aWZpY2F0aW9uLmluZm8oJ+iOt+WPluS7u+WKoeWksei0pe+8jOW3suaJk+W8gOePree6p+epuumXtCcpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAobG9hZGluZ01lc3NhZ2UpIHtcclxuICAgICAgICBsb2FkaW5nTWVzc2FnZS5jbG9zZSgpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfot7Povazku7vliqHlpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+i3s+i9rOS7u+WKoeWksei0pe+8jOivt+eojeWQjumHjeivlScpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIOWkhOeQhuaKpeWQjeaMiemSrueCueWHu1xyXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdENsaWNrID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgLy8g6I635Y+WbG9jYWxTdG9yYWdl5Lit55qE55So5oi35L+h5oGv6L+b6KGM5a+55q+UXHJcbiAgICBjb25zdCBsb2NhbFVzZXJTdHIgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndXNlcicpO1xyXG4gICAgY29uc3QgbG9jYWxVc2VyID0gbG9jYWxVc2VyU3RyID8gSlNPTi5wYXJzZShsb2NhbFVzZXJTdHIpIDogbnVsbDtcclxuXHJcbiAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIOaKpeWQjeaMiemSrueCueWHuyAtIOivpue7hueUqOaIt+eKtuaAgeajgOafpScsIHtcclxuICAgICAgaGFzU3VibWl0dGVkLFxyXG4gICAgICAnUmVkdXggdXNlclN0YXRlJzoge1xyXG4gICAgICAgIGlzTG9nZ2VkSW46IHVzZXJTdGF0ZT8uaXNMb2dnZWRJbixcclxuICAgICAgICBwaG9uZTogdXNlclN0YXRlPy5waG9uZSxcclxuICAgICAgICBwaG9uZUxlbmd0aDogdXNlclN0YXRlPy5waG9uZT8ubGVuZ3RoLFxyXG4gICAgICAgIHBob25lVHlwZTogdHlwZW9mIHVzZXJTdGF0ZT8ucGhvbmUsXHJcbiAgICAgICAgcm9sZUlkOiB1c2VyU3RhdGU/LnJvbGVJZCxcclxuICAgICAgICB1c2VySWQ6IHVzZXJTdGF0ZT8udXNlcklkLFxyXG4gICAgICAgIG5pY2tOYW1lOiB1c2VyU3RhdGU/Lm5pY2tOYW1lXHJcbiAgICAgIH0sXHJcbiAgICAgICdsb2NhbFN0b3JhZ2UgdXNlcic6IHtcclxuICAgICAgICBwaG9uZTogbG9jYWxVc2VyPy5waG9uZSxcclxuICAgICAgICBwaG9uZUxlbmd0aDogbG9jYWxVc2VyPy5waG9uZT8ubGVuZ3RoLFxyXG4gICAgICAgIHBob25lVHlwZTogdHlwZW9mIGxvY2FsVXNlcj8ucGhvbmUsXHJcbiAgICAgICAgdXNlcklkOiBsb2NhbFVzZXI/LnVzZXJJZCxcclxuICAgICAgICBuaWNrTmFtZTogbG9jYWxVc2VyPy5uaWNrTmFtZVxyXG4gICAgICB9LFxyXG4gICAgICAncGhvbmUgdmFsaWRhdGlvbic6IHtcclxuICAgICAgICAndXNlclN0YXRlLnBob25lIGV4aXN0cyc6ICEhdXNlclN0YXRlPy5waG9uZSxcclxuICAgICAgICAndXNlclN0YXRlLnBob25lIG5vdCBlbXB0eSc6IHVzZXJTdGF0ZT8ucGhvbmUgJiYgdXNlclN0YXRlLnBob25lLnRyaW0oKSAhPT0gJycsXHJcbiAgICAgICAgJ2xvY2FsVXNlci5waG9uZSBleGlzdHMnOiAhIWxvY2FsVXNlcj8ucGhvbmUsXHJcbiAgICAgICAgJ2xvY2FsVXNlci5waG9uZSBub3QgZW1wdHknOiBsb2NhbFVzZXI/LnBob25lICYmIGxvY2FsVXNlcj8ucGhvbmUudHJpbSgpICE9PSAnJ1xyXG4gICAgICB9XHJcbiAgICB9KTtcclxuXHJcbiAgICAvLyDkuLTml7bosIPnlKhBUEnojrflj5bmnIDmlrDnlKjmiLfkv6Hmga/ov5vooYzlr7nmr5RcclxuICAgIGlmICh1c2VyU3RhdGU/LnVzZXJJZCkge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdXNlckFwaS5nZXRVc2VySW5mbyh1c2VyU3RhdGUudXNlcklkKTtcclxuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIOS7jkFQSeiOt+WPlueahOacgOaWsOeUqOaIt+S/oeaBrzonLCB7XHJcbiAgICAgICAgICBjb2RlOiByZXNwb25zZS5jb2RlLFxyXG4gICAgICAgICAgcGhvbmU6IHJlc3BvbnNlLmRhdGE/LnBob25lLFxyXG4gICAgICAgICAgcGhvbmVMZW5ndGg6IHJlc3BvbnNlLmRhdGE/LnBob25lPy5sZW5ndGgsXHJcbiAgICAgICAgICBwaG9uZVR5cGU6IHR5cGVvZiByZXNwb25zZS5kYXRhPy5waG9uZSxcclxuICAgICAgICAgIG5pY2tOYW1lOiByZXNwb25zZS5kYXRhPy5uaWNrTmFtZSxcclxuICAgICAgICAgIHVzZXJJZDogcmVzcG9uc2UuZGF0YT8uaWRcclxuICAgICAgICB9KTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCfwn5SNIFtERUJVR10g6I635Y+W55So5oi35L+h5oGv5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOWmguaenOeUqOaIt+W3suaKpeWQje+8jOi3s+i9rOWIsOS7u+WKoemhtemdolxyXG4gICAgaWYgKGhhc1N1Ym1pdHRlZCkge1xyXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIOeUqOaIt+W3suaKpeWQje+8jOi3s+i9rOWIsOS7u+WKoemhtemdoicpO1xyXG4gICAgICBoYW5kbGVHb1RvVGFzaygpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgLy8g5qOA5p+l55So5oi35piv5ZCm55m75b2VXHJcbiAgICBpZiAoIXVzZXJTdGF0ZSB8fCAhdXNlclN0YXRlLmlzTG9nZ2VkSW4pIHtcclxuICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSDnlKjmiLfmnKrnmbvlvZXvvIzot7PovazliLDnmbvlvZXpobXpnaInKTtcclxuICAgICAgLy8g5pyq55m75b2V77yM5pi+56S655m75b2V5a+56K+d5qGGXHJcbiAgICAgIGNvbnN0IHJlZGlyZWN0VXJsID0gcGF0aG5hbWUgfHwgJy9ob21lJztcclxuICAgICAgcm91dGVyLnB1c2goYC9sb2dpbj9yZWRpcmVjdD0ke2VuY29kZVVSSUNvbXBvbmVudChyZWRpcmVjdFVybCl9YCk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICAvLyDmo4Dmn6XnlKjmiLfouqvku73vvJrlj6rmnInlrabnlJ/vvIhyb2xlSWTkuLox77yJ5omN6IO95oql5ZCNXHJcbiAgICBjb25zdCBpc1N0dWRlbnQgPSBjaGVja0lzU3R1ZGVudCgpO1xyXG4gICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSDmo4Dmn6XnlKjmiLfouqvku70nLCB7IGlzU3R1ZGVudCwgcm9sZUlkOiB1c2VyU3RhdGU/LnJvbGVJZCB9KTtcclxuICAgIGlmICghaXNTdHVkZW50KSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtERUJVR10g55So5oi35LiN5piv5a2m55Sf6Lqr5Lu977yM5pi+56S66Lqr5Lu96aqM6K+B5by556qXJyk7XHJcbiAgICAgIHNob3dTdHVkZW50T25seU1vZGFsKCk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICBjb25zb2xlLmxvZygn8J+UjSBbREVCVUddIOi6q+S7vemqjOivgemAmui/h++8jOW8gOWni+aKpeWQjea1geeoiycpO1xyXG4gICAgLy8g6Lqr5Lu96aqM6K+B6YCa6L+H77yM55u05o6l6L+b6KGM5oql5ZCNXHJcbiAgICBoYW5kbGVEaXJlY3RSZWdpc3RyYXRpb24oKTtcclxuICB9O1xyXG5cclxuICAvLyDlpITnkIbnmbvlvZXmiJDlip9cclxuICBjb25zdCBoYW5kbGVMb2dpblN1Y2Nlc3MgPSAoKSA9PiB7XHJcbiAgICBub3RpZmljYXRpb24uc3VjY2Vzcygn55m75b2V5oiQ5YqfJyk7XHJcblxyXG4gICAgLy8g55m75b2V5oiQ5Yqf5ZCO77yM6aG16Z2i55qEdXNlRWZmZWN05Lya6Ieq5Yqo5qOA5p+l5oql5ZCN54q25oCBXHJcbiAgICAvLyDkuI3pnIDopoHlnKjov5nph4zph43lpI3mo4Dmn6XvvIzorql1c2VFZmZlY3TlpITnkIbnirbmgIHmm7TmlrBcclxuICB9O1xyXG5cclxuICAvLyDlpITnkIbmiqXlkI3miJDlip9cclxuICBjb25zdCBoYW5kbGVTdWJtaXRTdWNjZXNzID0gKCkgPT4ge1xyXG4gICAgLy8g5pu05paw5o+Q5Lqk54q25oCBXHJcbiAgICBzZXRIYXNTdWJtaXR0ZWQodHJ1ZSk7XHJcbiAgICBzZXRSZWdpc3RyYXRpb25TdGF0dXMoMSk7IC8vIOiuvue9ruS4uuW3suaKpeWQjeeKtuaAgVxyXG4gICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+aKpeWQjeaIkOWKnycpO1xyXG5cclxuICAgIC8vIOmAmuefpemhtemdouWIt+aWsOWPgui1m+S9nOWTgeWIl+ihqFxyXG4gICAgaWYgKG9uUmVmcmVzaFdvcmtzKSB7XHJcbiAgICAgIG9uUmVmcmVzaFdvcmtzKCk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8g55u05o6l5oql5ZCN5aSE55CG5Ye95pWwXHJcbiAgY29uc3QgaGFuZGxlRGlyZWN0UmVnaXN0cmF0aW9uID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8g5qOA5p+l55So5oi35piv5ZCm57uR5a6a5LqG5omL5py65Y+3XHJcbiAgICAgIGlmICghdXNlclN0YXRlLnBob25lIHx8IHVzZXJTdGF0ZS5waG9uZS50cmltKCkgPT09ICcnKSB7XHJcbiAgICAgICAgLy8g5pi+56S656Gu6K6k5a+56K+d5qGGXHJcbiAgICAgICAgTW9kYWwuY29uZmlybSh7XHJcbiAgICAgICAgICB0aXRsZTogJ+mcgOimgee7keWumuaJi+acuuWPtycsXHJcbiAgICAgICAgICBjb250ZW50OiAn5oql5ZCN5rS75Yqo6ZyA6KaB57uR5a6a5omL5py65Y+377yM5piv5ZCm5YmN5b6A57uR5a6a77yfJyxcclxuICAgICAgICAgIG9rVGV4dDogJ+WJjeW+gOe7keWumicsXHJcbiAgICAgICAgICBjYW5jZWxUZXh0OiAn5Y+W5raIJyxcclxuICAgICAgICAgIG9uT2s6ICgpID0+IHtcclxuICAgICAgICAgICAgLy8g6Lez6L2s5Yiw55m75b2V6aG16Z2i77yM5bm25ZyoVVJM5Lit5re75Yqg5Y+C5pWw6KGo56S66ZyA6KaB57uR5a6a5omL5py65Y+3XHJcbiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRVcmwgPSB3aW5kb3cubG9jYXRpb24uaHJlZjtcclxuICAgICAgICAgICAgcm91dGVyLnB1c2goYC9sb2dpbj9uZWVkQmluZFBob25lPXRydWUmcmVkaXJlY3Q9JHtlbmNvZGVVUklDb21wb25lbnQoY3VycmVudFVybCl9YCk7XHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAgb25DYW5jZWw6ICgpID0+IHtcclxuICAgICAgICAgICAgbm90aWZpY2F0aW9uLmluZm8oJ+acque7keWumuaJi+acuuWPt++8jOaXoOazleaKpeWQjea0u+WKqCcpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RFQlVHXSDnlKjmiLflt7Lnu5HlrprmiYvmnLrlj7fvvIznu6fnu63miqXlkI3mtYHnqIsnKTtcclxuXHJcbiAgICAgIC8vIOaYvuekuuWKoOi9veaPkOekulxyXG4gICAgICBjb25zdCBsb2FkaW5nTWVzc2FnZSA9IG5vdGlmaWNhdGlvbi5sb2FkaW5nKCfmraPlnKjmj5DkuqTmiqXlkI0uLi4nKTtcclxuXHJcbiAgICAgIC8vIOaPkOS6pOaKpeWQjeaVsOaNru+8iOS9v+eUqOm7mOiupOWAvO+8jOi3s+i/h+WNj+iuruehruiupOatpemqpO+8iVxyXG4gICAgICBjb25zdCBzdWJtaXREYXRhID0ge1xyXG4gICAgICAgIGFjdGl2aXR5SWQ6IE51bWJlcihhY3Rpdml0eUlkKSxcclxuICAgICAgICBhZ3JlZW1lbnRBY2NlcHRlZDogdHJ1ZSwgLy8g6buY6K6k5ZCM5oSP5Y2P6K6uXHJcbiAgICAgICAgcGFyZW50Q29uc2VudEFjY2VwdGVkOiB0cnVlLCAvLyDpu5jorqTlkIzmhI/lrrbplb/nn6Xmg4XlkIzmhI/kuaZcclxuICAgICAgICBwYXJlbnRTaWduYXR1cmVQYXRoOiAnJywgLy8g56m6562+5ZCN6Lev5b6EXHJcbiAgICAgICAgc2lnbmF0dXJlVGltZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgICAgIHJlbWFyazogYOW/q+mAn+aKpeWQjeaXtumXtO+8miR7bmV3IERhdGUoKS50b0xvY2FsZVN0cmluZygnemgtQ04nKX1gXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFjdGl2aXR5UmVnaXN0cmF0aW9uQXBpLnN1Ym1pdFJlZ2lzdHJhdGlvbihzdWJtaXREYXRhKTtcclxuXHJcbiAgICAgIGlmIChyZXNwb25zZS5kYXRhPy5jb2RlID09PSAyMDApIHtcclxuICAgICAgICAvLyDmm7TmlrBVSeeKtuaAgVxyXG4gICAgICAgIHNldEhhc1N1Ym1pdHRlZCh0cnVlKTtcclxuICAgICAgICBzZXRSZWdpc3RyYXRpb25TdGF0dXMoMSk7XHJcbiAgICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+aKpeWQjeaIkOWKn++8geato+WcqOi3s+i9rOWIsOePree6p+epuumXtC4uLicpO1xyXG5cclxuICAgICAgICAvLyDpgJrnn6XpobXpnaLliLfmlrDlj4LotZvkvZzlk4HliJfooahcclxuICAgICAgICBpZiAob25SZWZyZXNoV29ya3MpIHtcclxuICAgICAgICAgIG9uUmVmcmVzaFdvcmtzKCk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyDmiqXlkI3miJDlip/lkI7vvIzojrflj5blr7nlupTnmoTotZvkuovku7vliqHlubbot7PovazliLDnj63nuqfnqbrpl7RcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgLy8g562J5b6F5LiA5bCP5q615pe26Ze056Gu5L+d5ZCO56uv5Yib5bu66LWb5LqL5Lu75Yqh5a6M5oiQXHJcbiAgICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwMCkpO1xyXG5cclxuICAgICAgICAgIC8vIOiOt+WPluW9k+WJjeeUqOaIt+eahOi1m+S6i+S7u+WKoeWIl+ihqFxyXG4gICAgICAgICAgY29uc3QgZXZlbnRzVGFza1Jlc3BvbnNlID0gYXdhaXQgZXZlbnRzVGFza0FwaS5nZXRNeVRhc2tzKCk7XHJcblxyXG4gICAgICAgICAgaWYgKGV2ZW50c1Rhc2tSZXNwb25zZS5kYXRhLmNvZGUgPT09IDIwMCkge1xyXG4gICAgICAgICAgICBjb25zdCBldmVudHNUYXNrcyA9IGV2ZW50c1Rhc2tSZXNwb25zZS5kYXRhLmRhdGE7XHJcbiAgICAgICAgICAgIC8vIOafpeaJvuS4juW9k+WJjea0u+WKqOebuOWFs+eahOi1m+S6i+S7u+WKoVxyXG4gICAgICAgICAgICBjb25zdCByZWxhdGVkVGFzayA9IGV2ZW50c1Rhc2tzLmZpbmQoKHRhc2s6IGFueSkgPT4gdGFzay5hY3Rpdml0eUlkID09PSBOdW1iZXIoYWN0aXZpdHlJZCkpO1xyXG5cclxuICAgICAgICAgICAgaWYgKHJlbGF0ZWRUYXNrKSB7XHJcbiAgICAgICAgICAgICAgLy8g5Zyo5paw5qCH562+6aG15Lit5omT5byA54+t57qn56m66Ze05bm26Ieq5Yqo5omT5byA5a+55bqU55qE6LWb5LqL5Lu75YqhXHJcbiAgICAgICAgICAgICAgd2luZG93Lm9wZW4oYC9jbGFzcy1zcGFjZT90YXNrSWQ9JHtyZWxhdGVkVGFzay5pZH1gLCAnX2JsYW5rJyk7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgLy8g5aaC5p6c5rKh5pyJ5om+5Yiw5a+55bqU5Lu75Yqh77yM5Zyo5paw5qCH562+6aG15Lit5omT5byA54+t57qn56m66Ze0XHJcbiAgICAgICAgICAgICAgd2luZG93Lm9wZW4oJy9jbGFzcy1zcGFjZScsICdfYmxhbmsnKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgLy8g5aaC5p6c6I635Y+W5Lu75Yqh5aSx6LSl77yM5Zyo5paw5qCH562+6aG15Lit5omT5byA54+t57qn56m66Ze0XHJcbiAgICAgICAgICAgIHdpbmRvdy5vcGVuKCcvY2xhc3Mtc3BhY2UnLCAnX2JsYW5rJyk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAodGFza0Vycm9yKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5botZvkuovku7vliqHlpLHotKU6JywgdGFza0Vycm9yKTtcclxuICAgICAgICAgIC8vIOWNs+S9v+iOt+WPluS7u+WKoeWksei0pe+8jOS5n+imgeWcqOaWsOagh+etvumhteS4reaJk+W8gOePree6p+epuumXtFxyXG4gICAgICAgICAgd2luZG93Lm9wZW4oJy9jbGFzcy1zcGFjZScsICdfYmxhbmsnKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLmRhdGE/Lm1lc3NhZ2UgfHwgJ+aKpeWQjeWksei0pScpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAobG9hZGluZ01lc3NhZ2UpIHtcclxuICAgICAgICBsb2FkaW5nTWVzc2FnZS5jbG9zZSgpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfmiqXlkI3lpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn5oql5ZCN5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VJyk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcblxyXG5cclxuXHJcblxyXG5cclxuXHJcblxyXG5cclxuICAvLyDlpITnkIbliIbkuqvmjInpkq7ngrnlh7vkuovku7ZcclxuICBjb25zdCBoYW5kbGVTaGFyZSA9ICgpID0+IHtcclxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAvLyDojrflj5blvZPliY3pobXpnaJVUkxcclxuICAgICAgY29uc3QgY3VycmVudFVybCA9IHdpbmRvdy5sb2NhdGlvbi5ocmVmO1xyXG5cclxuICAgICAgLy8g5aSN5Yi25Yiw5Ymq6LS05p2/XHJcbiAgICAgIG5hdmlnYXRvci5jbGlwYm9hcmQud3JpdGVUZXh0KGN1cnJlbnRVcmwpXHJcbiAgICAgICAgLnRoZW4oKCkgPT4ge1xyXG4gICAgICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+a0u+WKqOmTvuaOpeW3suWkjeWItu+8jOW/q+WOu+WIhuS6q+WQp++8gScpO1xyXG4gICAgICAgIH0pXHJcbiAgICAgICAgLmNhdGNoKGVyciA9PiB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCflpI3liLblpLHotKU6JywgZXJyKTtcclxuICAgICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign5aSN5Yi26ZO+5o6l5aSx6LSl77yM6K+35omL5Yqo5aSN5Yi2Jyk7XHJcbiAgICAgICAgfSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8g5aaC5p6c6L+Y5rKh5pyJ5Zyo5a6i5oi356uv5oyC6L2977yM6L+U5Zue5LiA5Liq566A5Y2V55qE5Y2g5L2N56ymXHJcbiAgaWYgKCFpc0NsaWVudCkge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhY3Rpdml0eS1oZWFkZXIgcmVsYXRpdmUgcm91bmRlZC0yeGwgb3ZlcmZsb3ctaGlkZGVuIHNoYWRvdy1tZCBib3JkZXIgYm9yZGVyLWdyYXktMTAwIGJnLXdoaXRlIGhvdmVyOnNoYWRvdy1sZyB0cmFuc2l0aW9uLXNoYWRvdyBkdXJhdGlvbi0zMDBcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGgtNjQgYmctZ3JheS0yMDAgYW5pbWF0ZS1wdWxzZVwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLXQgZnJvbS1ibGFjay82MCB0by10cmFuc3BhcmVudFwiIC8+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHAtNiBmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydCBtZDppdGVtcy1jZW50ZXIgaC1mdWxsXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTggYmctZ3JheS0zMDAgcm91bmRlZCBtYi0yIHctMy80XCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctZ3JheS0zMDAgcm91bmRlZCBtYi0xIHctMS8yXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctZ3JheS0zMDAgcm91bmRlZCB3LTEvM1wiPjwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPD5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhY3Rpdml0eS1oZWFkZXIgcmVsYXRpdmUgcm91bmRlZC0yeGwgb3ZlcmZsb3ctaGlkZGVuIHNoYWRvdy1tZCBib3JkZXIgYm9yZGVyLWdyYXktMTAwIGJnLXdoaXRlIGhvdmVyOnNoYWRvdy1sZyB0cmFuc2l0aW9uLXNoYWRvdyBkdXJhdGlvbi0zMDBcIj5cclxuXHJcblxyXG4gICAgICAgICAgey8qIOaoquW5heWbvueJhyAtIOWni+e7iOaYvuekuiAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy1mdWxsIGgtWzMwMHB4XSBvdmVyZmxvdy1oaWRkZW5cIj5cclxuICAgICAgICAgICAgey8qIEJhbm5lciBJbWFnZSAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLWIgZnJvbS10cmFuc3BhcmVudCB0by1ibGFjay8zMCB6LTEwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi0zMDAgJHtzaG93VUkgPyAnb3BhY2l0eS0xMDAnIDogJ29wYWNpdHktMCd9YH0gLz5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy1mdWxsIGgtZnVsbFwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgYWJzb2x1dGUgaW5zZXQtMCBiZy1ibHVlLTUwMC8xMCBhbmltYXRlLXB1bHNlIHotMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwICR7c2hvd1VJID8gJ29wYWNpdHktMTAwJyA6ICdvcGFjaXR5LTAnfWB9PjwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMFwiPlxyXG4gICAgICAgICAgICAgICAgPEltYWdlXHJcbiAgICAgICAgICAgICAgICAgIHNyYz17YmFubmVySW1hZ2V9XHJcbiAgICAgICAgICAgICAgICAgIGFsdD17dGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgIGZpbGxcclxuICAgICAgICAgICAgICAgICAgcHJpb3JpdHlcclxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgICAgICBvYmplY3RGaXQ6ICdjb3ZlcidcclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiei0xMCBob3ZlcjpzY2FsZS0xMDUgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tNzAwXCJcclxuICAgICAgICAgICAgICAgICAgb25FcnJvcj17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCB0YXJnZXQgPSBlLnRhcmdldCBhcyBIVE1MSW1hZ2VFbGVtZW50O1xyXG4gICAgICAgICAgICAgICAgICAgIHRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kID0gJ2xpbmVhci1ncmFkaWVudCgxMjBkZWcsICNmM2Y0ZjYsICNlNWU3ZWIpJztcclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgey8qIOa0u+WKqOS/oeaBr+WPoOWKoOWxgiAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BhYnNvbHV0ZSBib3R0b20tNCBsZWZ0LTQgcmlnaHQtNCB6LTIwIGZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwICR7c2hvd1VJID8gJ29wYWNpdHktMTAwJyA6ICdvcGFjaXR5LTAgcG9pbnRlci1ldmVudHMtbm9uZSd9YH0+XHJcbiAgICAgICAgICAgICAgey8qIOagh+etvuWKn+iDveaaguaXtuazqOmHiu+8jOS/neeVmeS7peS+v+WwhuadpeS9v+eUqCBcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTEuNSBiZy13aGl0ZS85MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtZnVsbCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS02MDAgc2hhZG93LXNtIGhvdmVyOnNoYWRvdy1tZCB0cmFuc2l0aW9uLXNoYWRvdyBkdXJhdGlvbi0zMDAgaG92ZXI6Ymctd2hpdGUgdHJhbnNmb3JtIGhvdmVyOi10cmFuc2xhdGUteS0xIGhvdmVyOnNjYWxlLTEwNSByZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICB7aXNFZGl0aW5nICYmIG5hdlRhZ3MubGVuZ3RoIDwgNSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b24gXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQWRkVGFnfVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtOCBsZWZ0LTAgZmxleCBpdGVtcy1jZW50ZXIgcHgtMiBweS0xIGJnLWJsdWUtNTAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyB0ZXh0LXhzIGhvdmVyOmJnLWJsdWUtNjAwIHRyYW5zaXRpb24tY29sb3JzXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cInctMyBoLTMgbXItMVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAg5re75Yqg5qCH562+XHJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICB7dGFncyAmJiB0YWdzLmxlbmd0aCA+IDAgPyAoXHJcbiAgICAgICAgICAgICAgICAgIHRhZ3MubWFwKCh0YWcsIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPFJlYWN0LkZyYWdtZW50IGtleT17dGFnLmlkfT5cclxuICAgICAgICAgICAgICAgICAgICAgIHtpbmRleCA+IDAgJiYgPHNwYW4gY2xhc3NOYW1lPVwibXgtMiB0ZXh0LWdyYXktMzAwXCI+fDwvc3Bhbj59XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMiBweS0wLjUgcm91bmRlZC1mdWxsIHRleHQteHNcIiBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogdGFnLmNvbG9yID8gYCR7dGFnLmNvbG9yfTMwYCA6ICcjZTVlN2ViJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiB0YWcuY29sb3IgfHwgJyM0YjU1NjMnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHt0YWcubmFtZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7aXNFZGl0aW5nICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXQgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWwtMSB3LTMgaC0zXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3RhZ0lkcy5pbmNsdWRlcyh0YWcuaWQpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChlLnRhcmdldC5jaGVja2VkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0VGFnSWRzKFsuLi50YWdJZHMsIHRhZy5pZF0pO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFRhZ0lkcyh0YWdJZHMuZmlsdGVyKGlkID0+IGlkICE9PSB0YWcuaWQpKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvUmVhY3QuRnJhZ21lbnQ+XHJcbiAgICAgICAgICAgICAgICAgICkpXHJcbiAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICBuYXZUYWdzLm1hcCgodGFnLCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxSZWFjdC5GcmFnbWVudCBrZXk9e3RhZy5pZH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7aW5kZXggPiAwICYmIDxzcGFuIGNsYXNzTmFtZT1cIm14LTIgdGV4dC1ncmF5LTMwMFwiPnw8L3NwYW4+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICB7aXNFZGl0aW5nICYmIG5hdlRhZ3MubGVuZ3RoID4gMSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGVUYWcodGFnLmlkKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtbC0xIHRleHQtcmVkLTUwMCBob3Zlcjp0ZXh0LXJlZC03MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwi5Yig6Zmk5qCH562+XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaCBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvUmVhY3QuRnJhZ21lbnQ+XHJcbiAgICAgICAgICAgICAgICAgICkpXHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICovfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiDnroDmtIHniYjlpLTpg6ggLSDlp4vnu4jmmL7npLogKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHJlbGF0aXZlIHAtNiBmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydCBtZDppdGVtcy1jZW50ZXIgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTMwMCAke3Nob3dVSSA/ICdvcGFjaXR5LTEwMCcgOiAnb3BhY2l0eS0wIHBvaW50ZXItZXZlbnRzLW5vbmUnfWB9PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxyXG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTgwMCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAge3RpdGxlfVxyXG4gICAgICAgICAgICAgICAgPFNwYXJrbGVzIGNsYXNzTmFtZT1cIm1sLTIgdy01IGgtNSB0ZXh0LXllbGxvdy00MDAgYW5pbWF0ZS1wdWxzZVwiIC8+XHJcbiAgICAgICAgICAgICAgPC9oMT5cclxuICAgICAgICAgICAgICB7b3JnYW5pemVyICYmIChcclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgdGV4dC1zbVwiPuS4u+WKnuaWuToge29yZ2FuaXplcn08L3A+XHJcbiAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCB0ZXh0LXNtIG10LTIgZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1pbmRpZ28tNTAwIGgtMS41IHctMS41IHJvdW5kZWQtZnVsbCBtci0yIGFuaW1hdGUtcHVsc2VcIj48L3NwYW4+XHJcbiAgICAgICAgICAgICAgICDmtLvliqjml7bpl7TvvJp7Zm9ybWF0RGF0ZShzdGFydFRpbWUpfSAtIHtmb3JtYXREYXRlKGVuZFRpbWUpfVxyXG4gICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0zIG10LTQgbWQ6bXQtMFwiPlxyXG4gICAgICAgICAgICAgIHsvKiA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRFeHBhbmRlZCghZXhwYW5kZWQpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcHgtNSBweS0yLjUgdGV4dC1zbSBmb250LW1lZGl1bSByb3VuZGVkLWZ1bGwgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCB0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktNTAgaG92ZXI6c2NhbGUtMTA1IGFjdGl2ZTpzY2FsZS05NSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtleHBhbmRlZCA/IChcclxuICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICDmlLbotbfor6bmg4VcclxuICAgICAgICAgICAgICAgICAgICA8Q2hldnJvblVwIGNsYXNzTmFtZT1cIm1sLTIgdy00IGgtNFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICDlsZXlvIDor6bmg4VcclxuICAgICAgICAgICAgICAgICAgICA8Q2hldnJvbkRvd24gY2xhc3NOYW1lPVwibWwtMiB3LTQgaC00XCIgLz5cclxuICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPiAqL31cclxuXHJcbiAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU3VibWl0Q2xpY2t9XHJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNDaGVja2luZ31cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHB4LTUgcHktMi41IHRleHQtc20gZm9udC1tZWRpdW0gcm91bmRlZC1mdWxsICR7aGFzU3VibWl0dGVkXHJcbiAgICAgICAgICAgICAgICAgID8gJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi01MDAgdG8tZW1lcmFsZC01MDAgdGV4dC13aGl0ZSBob3ZlcjpvcGFjaXR5LTkwIGhvdmVyOnNjYWxlLTEwNSBhY3RpdmU6c2NhbGUtOTUgc2hhZG93LW1kIGhvdmVyOnNoYWRvdy1sZydcclxuICAgICAgICAgICAgICAgICAgOiAnYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAwIHRvLWluZGlnby01MDAgdGV4dC13aGl0ZSBob3ZlcjpvcGFjaXR5LTkwIGhvdmVyOnNjYWxlLTEwNSBhY3RpdmU6c2NhbGUtOTUgc2hhZG93LW1kIGhvdmVyOnNoYWRvdy1sZydcclxuICAgICAgICAgICAgICAgICAgfSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDBgfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtpc0NoZWNraW5nID8gKFxyXG4gICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImxvYWRpbmctc3Bpbm5lciBtci0yXCI+PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIOajgOafpeeKtuaAgeS4rVxyXG4gICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICkgOiBoYXNTdWJtaXR0ZWQgPyAoXHJcbiAgICAgICAgICAgICAgICAgIDw+5bey5oql5ZCNIOWJjeW+gOS7u+WKoTwvPlxyXG4gICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgPD7nq4vljbPmiqXlkI08Lz5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgPC9idXR0b24+XHJcblxyXG4gICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNoYXJlfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcC0yLjUgdGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWJsdWUtNTAwIGhvdmVyOmJnLWdyYXktNTAgcm91bmRlZC1mdWxsIGhvdmVyOnNjYWxlLTExMCBhY3RpdmU6c2NhbGUtOTUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8U2hhcmUyIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIOWqkuS9k+i1hOa6kOWxleekuuWMuuWfnyAqL31cclxuICAgICAgeyhwcm9tb3Rpb25JbWFnZSB8fCBiYWNrZ3JvdW5kSW1hZ2UgfHwgZ2FsbGVyeUltYWdlcyB8fCBhdHRhY2htZW50RmlsZXMpICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTZcIj5cclxuICAgICAgICAgIDxBY3Rpdml0eU1lZGlhR2FsbGVyeVxyXG4gICAgICAgICAgICBwcm9tb3Rpb25JbWFnZT17cHJvbW90aW9uSW1hZ2V9XHJcbiAgICAgICAgICAgIGJhY2tncm91bmRJbWFnZT17YmFja2dyb3VuZEltYWdlfVxyXG4gICAgICAgICAgICBnYWxsZXJ5SW1hZ2VzPXtnYWxsZXJ5SW1hZ2VzfVxyXG4gICAgICAgICAgICBhdHRhY2htZW50RmlsZXM9e2F0dGFjaG1lbnRGaWxlc31cclxuICAgICAgICAgICAgYWN0aXZpdHlUaXRsZT17dGl0bGV9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApfVxyXG5cclxuICAgICAgey8qIOeZu+W9leWvueivneahhiAtIOWPquWcqOWuouaIt+err+a4suafkyAqL31cclxuICAgICAge2lzQ2xpZW50ICYmIChcclxuICAgICAgICA8TG9naW5EaWFsb2dcclxuICAgICAgICAgIGlzT3Blbj17c2hvd0xvZ2luRGlhbG9nfVxyXG4gICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd0xvZ2luRGlhbG9nKGZhbHNlKX1cclxuICAgICAgICAgIG9uU3VjY2Vzcz17aGFuZGxlTG9naW5TdWNjZXNzfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICl9XHJcbiAgICA8Lz5cclxuICApXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IEFjdGl2aXR5SGVhZGVyICJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiSW1hZ2UiLCJTaGFyZTIiLCJTcGFya2xlcyIsInNlbGVjdFVzZXJTdGF0ZSIsInNldFVzZXIiLCJ1c2VTZWxlY3RvciIsInVzZURpc3BhdGNoIiwiQWN0aXZpdHlNZWRpYUdhbGxlcnkiLCJNb2RhbCIsIkxvZ2luRGlhbG9nIiwiYWN0aXZpdHlSZWdpc3RyYXRpb25BcGkiLCJBY3Rpdml0eVR5cGUiLCJldmVudHNUYXNrQXBpIiwidXNlckFwaSIsIkdldE5vdGlmaWNhdGlvbiIsInVzZVJvdXRlciIsInVzZVBhdGhuYW1lIiwiZm9ybWF0RGF0ZSIsImRhdGVTdHJpbmciLCJkYXRlIiwiRGF0ZSIsImlzTmFOIiwiZ2V0VGltZSIsInllYXIiLCJnZXRGdWxsWWVhciIsIm1vbnRoIiwiU3RyaW5nIiwiZ2V0TW9udGgiLCJwYWRTdGFydCIsImRheSIsImdldERhdGUiLCJBY3Rpdml0eUhlYWRlciIsInRpdGxlIiwiaW5pdGlhbFRpdGxlIiwic3RhcnRUaW1lIiwiaW5pdGlhbFN0YXJ0VGltZSIsImVuZFRpbWUiLCJpbml0aWFsRW5kVGltZSIsImJhbm5lckltYWdlIiwiaW5pdGlhbEJhbm5lckltYWdlIiwiZXhwYW5kZWQiLCJzZXRFeHBhbmRlZCIsImFjdGl2aXR5SWQiLCJ0YWdzIiwib3JnYW5pemVyIiwiaW5pdGlhbE9yZ2FuaXplciIsImFjdGl2aXR5VHlwZSIsIldPUksiLCJvblJlZnJlc2hXb3JrcyIsInByb21vdGlvbkltYWdlIiwiYmFja2dyb3VuZEltYWdlIiwiZ2FsbGVyeUltYWdlcyIsImF0dGFjaG1lbnRGaWxlcyIsImNvbnNvbGUiLCJsb2ciLCJub3RpZmljYXRpb24iLCJyb3V0ZXIiLCJwYXRobmFtZSIsInVzZXJTdGF0ZSIsImRpc3BhdGNoIiwiaXNBZG1pbiIsInJvbGVJZCIsInN5bmNVc2VyU3RhdGUiLCJsb2NhbFVzZXIiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwidXNlckRhdGEiLCJKU09OIiwicGFyc2UiLCJpc0xvZ2dlZEluIiwidXNlcklkIiwicGhvbmUiLCJ0cmltIiwicmVzcG9uc2UiLCJnZXRVc2VySW5mbyIsImNvZGUiLCJkYXRhIiwidXBkYXRlZFVzZXJEYXRhIiwibmlja05hbWUiLCJhdmF0YXJVcmwiLCJnZW5kZXIiLCJvbGRQaG9uZSIsIm5ld1Bob25lIiwidXBkYXRlZCIsInNldEl0ZW0iLCJzdHJpbmdpZnkiLCJlcnJvciIsImhhc1Bob25lQ2hhbmdlZCIsImhhc1JvbGVDaGFuZ2VkIiwiaGFzTG9naW5TdGF0dXNDaGFuZ2VkIiwicGhvbmVDaGFuZ2VkIiwicm9sZUNoYW5nZWQiLCJsb2dpblN0YXR1c0NoYW5nZWQiLCJvbGRSb2xlIiwibmV3Um9sZSIsImNhdGNoIiwiaGFuZGxlVmlzaWJpbGl0eUNoYW5nZSIsImRvY3VtZW50IiwidmlzaWJpbGl0eVN0YXRlIiwiaGFuZGxlRm9jdXMiLCJhZGRFdmVudExpc3RlbmVyIiwid2luZG93IiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImNoZWNrSXNTdHVkZW50IiwidXNlclN0ciIsInVzZXJSb2xlSWQiLCJzaG93U3R1ZGVudE9ubHlNb2RhbCIsImNvbmZpcm0iLCJjb250ZW50Iiwib2tUZXh0IiwiY2FuY2VsVGV4dCIsIm9uQ2FuY2VsIiwiaW5mbyIsImdldFJlZ2lzdHJhdGlvblN0YXR1c1RleHQiLCJyZWdpc3RyYXRpb25TdGF0dXMiLCJuYXZUYWdzIiwic2V0TmF2VGFncyIsImlkIiwidGV4dCIsInRhZ0lkcyIsInNldFRhZ0lkcyIsImVkaXRpbmdUYWdJZCIsInNldEVkaXRpbmdUYWdJZCIsImVkaXRpbmdUYWdUZXh0Iiwic2V0RWRpdGluZ1RhZ1RleHQiLCJzaG93TG9naW5EaWFsb2ciLCJzZXRTaG93TG9naW5EaWFsb2ciLCJoYXNTdWJtaXR0ZWQiLCJzZXRIYXNTdWJtaXR0ZWQiLCJpc0NoZWNraW5nIiwic2V0SXNDaGVja2luZyIsInNldFJlZ2lzdHJhdGlvblN0YXR1cyIsInVwbG9hZExvYWRpbmciLCJzZXRVcGxvYWRMb2FkaW5nIiwiZmlsZUxpc3QiLCJzZXRGaWxlTGlzdCIsInNob3dVSSIsInNldFNob3dVSSIsImlzQ2xpZW50Iiwic2V0SXNDbGllbnQiLCJjaGVja1JlZ2lzdHJhdGlvblN0YXR1cyIsImlzU3R1ZGVudCIsImNoZWNrUmVnaXN0cmF0aW9uIiwiTnVtYmVyIiwic3VibWl0dGVkIiwic3VibWl0Iiwic3RhdHVzIiwidW5kZWZpbmVkIiwiY2hlY2tVc2VyU3VibWl0dGVkIiwiYWN0aXZpdHlBcGkiLCJmZXRjaEFjdGl2aXR5VGFncyIsImFjdGl2aXR5VGFnQXBpIiwiZ2V0QWN0aXZpdHlUYWdzIiwidWlkIiwibmFtZSIsInVybCIsImhhbmRsZUdvVG9UYXNrIiwibG9hZGluZ01lc3NhZ2UiLCJsb2FkaW5nIiwiZXZlbnRzVGFza1Jlc3BvbnNlIiwiZ2V0TXlUYXNrcyIsImV2ZW50c1Rhc2tzIiwicmVsYXRlZFRhc2siLCJmaW5kIiwidGFzayIsIm9wZW4iLCJzdWNjZXNzIiwiY2xvc2UiLCJoYW5kbGVTdWJtaXRDbGljayIsImxvY2FsVXNlclN0ciIsInBob25lTGVuZ3RoIiwibGVuZ3RoIiwicGhvbmVUeXBlIiwicmVkaXJlY3RVcmwiLCJwdXNoIiwiZW5jb2RlVVJJQ29tcG9uZW50IiwiaGFuZGxlRGlyZWN0UmVnaXN0cmF0aW9uIiwiaGFuZGxlTG9naW5TdWNjZXNzIiwiaGFuZGxlU3VibWl0U3VjY2VzcyIsIm9uT2siLCJjdXJyZW50VXJsIiwibG9jYXRpb24iLCJocmVmIiwic3VibWl0RGF0YSIsImFncmVlbWVudEFjY2VwdGVkIiwicGFyZW50Q29uc2VudEFjY2VwdGVkIiwicGFyZW50U2lnbmF0dXJlUGF0aCIsInNpZ25hdHVyZVRpbWUiLCJ0b0lTT1N0cmluZyIsInJlbWFyayIsInRvTG9jYWxlU3RyaW5nIiwic3VibWl0UmVnaXN0cmF0aW9uIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwidGFza0Vycm9yIiwiRXJyb3IiLCJtZXNzYWdlIiwiaGFuZGxlU2hhcmUiLCJuYXZpZ2F0b3IiLCJjbGlwYm9hcmQiLCJ3cml0ZVRleHQiLCJ0aGVuIiwiZXJyIiwiZGl2IiwiY2xhc3NOYW1lIiwic3JjIiwiYWx0IiwiZmlsbCIsInByaW9yaXR5Iiwic3R5bGUiLCJvYmplY3RGaXQiLCJvbkVycm9yIiwiZSIsInRhcmdldCIsImJhY2tncm91bmQiLCJoMSIsInAiLCJzcGFuIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwiYWN0aXZpdHlUaXRsZSIsImlzT3BlbiIsIm9uQ2xvc2UiLCJvblN1Y2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx\n"));

/***/ })

});