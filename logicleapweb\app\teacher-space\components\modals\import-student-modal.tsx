import { Modal, Upload } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import { DraggerProps } from 'antd/es/upload';

const { Dragger } = Upload;

interface ImportStudentModalProps {
  visible: boolean;
  onCancel: () => void;
  onImport: (file: File) => Promise<boolean>;
  classId: number;
}

export const ImportStudentModal: React.FC<ImportStudentModalProps> = ({
  visible,
  onCancel,
  onImport,
  classId
}) => {
  return (
    <Modal
      title="导入学生"
      open={visible}
      onCancel={onCancel}
      footer={null}
      centered
      style={{
        maxWidth: '90vw',
        top: 0,
        paddingBottom: 0
      }}
      modalRender={(modal) => (
        <div style={{
          margin: '5vh 0',
          display: 'flex',
          flexDirection: 'column',
          height: 'fit-content',
          maxHeight: '90vh'
        }}>
          {modal}
        </div>
      )}
      bodyStyle={{
        maxHeight: 'calc(90vh - 110px)',
        overflow: 'auto',
        padding: '24px'
      }}
    >
      <div className="space-y-6">
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-3">导入说明：</h4>
          <div className="space-y-2 text-gray-600 text-sm">
            <p>1. 请使用Excel文件（.xlsx或.xls格式）</p>
            <p>2. 表格内容格式：</p>
            <div className="bg-white p-3 rounded-lg shadow-sm">
              <table className="w-full text-xs">
                <thead>
                  <tr>
                    <th className="text-left px-2 py-1 text-gray-500">A列</th>
                    <th className="text-left px-2 py-1 text-gray-500">B列</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="px-2 py-1">学号</td>
                    <td className="px-2 py-1">姓名</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p>3. 学生初始密码：123456</p>
          </div>
        </div>
        
        <Dragger
          accept=".xlsx,.xls"
          beforeUpload={(file) => {
            onImport(file);
            return false;
          }}
          showUploadList={false}
          className="bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-100 hover:border-blue-200 transition-colors"
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined className="text-blue-500" />
          </p>
          <p className="ant-upload-text text-gray-700">点击或拖拽文件到此处上传</p>
          <p className="ant-upload-hint text-gray-500">
            支持 Excel 文件（.xlsx, .xls）
          </p>
        </Dragger>

        <div>
          <a 
            href="https://logicleap.oss-cn-guangzhou.aliyuncs.com/docs/%E5%AD%A6%E7%94%9F%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx" 
            download 
            className="text-blue-500 hover:text-blue-600 text-sm flex items-center gap-1 transition-colors"
          >
            <i className="ri-download-line" />
            下载导入模板
          </a>
        </div>
      </div>
    </Modal>
  );
}; 