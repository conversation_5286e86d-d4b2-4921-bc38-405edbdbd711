'use client'

import { Modal, Button, Tag, Radio, Upload, Empty, Carousel, Form } from 'antd';
import { InboxOutlined, FileTextOutlined, DeleteOutlined, InfoCircleOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { CustomArrow, ImageLabel } from './class-projects';
import { Task, TaskStatus } from '@/lib/api/task';
import { worksApi } from '@/lib/api/works';
import taskApi from '@/lib/api/task';
import { uploadApi } from '@/lib/api/upload';
import type { UploadFile } from 'antd/es/upload/interface';
import { useState, useEffect } from 'react';
import { ExtendedTask, TaskAssignment } from '../types';
import { addUserJoinRole, getUserRoleInfo } from '@/lib/api/role';
import { GetNotification } from 'logic-common/dist/components/Notification';
import { SelfAssessmentModal } from './self-assessment-modal';

type SubmissionType = 'work' | 'image' | 'files' | 'work_and_image';
type ViewType = 'works' | 'upload';

interface TaskSubmitModalProps {
  visible: boolean;
  task: ExtendedTask | null;
  onClose: () => void;
  onSubmitted: () => void;
}

export default function TaskSubmitModal({
  visible,
  task,
  onClose,
  onSubmitted
}: TaskSubmitModalProps) {
  const [form] = Form.useForm();
  const [selectedWorkId, setSelectedWorkId] = useState<number | null>(null);
  const [selectedImageId, setSelectedImageId] = useState<number | null>(null);
  const [submissionType, setSubmissionType] = useState<SubmissionType>('work');
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [activeView, setActiveView] = useState<ViewType>('works');
  const [submitting, setSubmitting] = useState(false);
  const [works, setWorks] = useState<any[]>([]);
  const [images, setImages] = useState<any[]>([]);
  const notification = GetNotification();
  const [isAssessmentModalVisible, setIsAssessmentModalVisible] = useState(false);
  const [detailedTask, setDetailedTask] = useState<ExtendedTask | null>(null);

  useEffect(() => {
    const fetchTaskDetails = async () => {
      if (task?.id) {
        try {
          const response = await taskApi.getTaskDetail(task.id);
          console.log('获取到的任务详情:', response.data.data);
          if (response.data.code === 200 && response.data.data) {
            setDetailedTask({
              ...response.data.data,
              onSubmitted: task.onSubmitted, // 保留回调
            });
          } else {
            setDetailedTask(task); // 失败时回退
            notification.error('获取任务详情失败。');
          }
        } catch (error) {
          console.error('获取任务详情失败:', error);
          setDetailedTask(task); // 异常时回退
          notification.error('获取任务详情失败，请检查网络。');
        }
      } else {
        setDetailedTask(task);
      }
    };
  
    if (visible && task) {
      fetchTaskDetails();
    } else {
      setDetailedTask(null); // 关闭时清空
    }
  }, [visible, task]);

  // 重置表单状态
  const resetForm = () => {
    setSelectedWorkId(null);
    setSelectedImageId(null);
    setUploadFiles([]);
    setSubmissionType('work');
    setActiveView('works');
    setImages([]);
  };

  // 获取图片列表
  const fetchImageList = async () => {
    try {
      const { data: response } = await worksApi.getAllWorks({
        page: 1,
        size: 100
      });

      if (response.code === 200) {
        setImages(response.data || []);
      }
    } catch (error) {
      console.error('获取图片列表失败:', error);
      notification.error('获取图片列表失败');
    }
  };

  // 获取作品列表和提交信息
  const fetchSubmissionInfo = async () => {
    if (!detailedTask?.assignments?.[0]?.id) return;

    try {
      // 获取当前用户ID
      const userData = localStorage.getItem('user');
      const currentUser = userData ? JSON.parse(userData) : null;
      const currentUserId = currentUser?.userId;

      // 确保当前用户是任务的分配者
      const userAssignment = detailedTask.assignments.find(assignment =>
        assignment.studentId === currentUserId
      );

      if (!userAssignment) {
        notification.error('无法获取任务提交信息：无权限');
        return;
      }

      const submissionResponse = await taskApi.getStudentWork(userAssignment.id);
      if (submissionResponse.data.code === 200) {
        const submission = submissionResponse.data.data;

        if (submission.hasWork) {
          setSubmissionType('work');
          setSelectedWorkId(submission.workId);
        } else if (submission.hasFiles) {
          setSubmissionType('image');
        }

        if (submission.hasFiles && submission.submissionFiles?.length) {
          const files = submission.submissionFiles.map((url: string) => ({
            uid: url,
            name: url.split('/').pop() || 'unknown',
            status: 'done' as const,
            url: url
          }));
          setUploadFiles(files);
        }
      }

      // 根据当前提交类型获取不同的列表
      if (submissionType === 'work') {
        const { data: response } = await worksApi.getList({
          type: 1,
          page: 1,
          size: 100
        });

        if (response.code === 200) {
          const worksList = response.data || [];

          // 获取每个作品的提交记录
          const worksWithSubmissions = await Promise.all(
            worksList.map(async (work: any) => {
              try {
                const submissionsResponse = await taskApi.getWorkSubmissions(work.id);
                if (submissionsResponse?.data?.code === 200) {
                  return {
                    ...work,
                    submissions: submissionsResponse.data.data || []
                  };
                }
                return work;
              } catch (error) {
                console.error('获取作品提交记录失败:', error);
                return work;
              }
            })
          );

          setWorks(worksWithSubmissions);
        }
      } else {
        await fetchImageList();
      }
    } catch (error) {
      console.error('获取提交信息失败:', error);
      notification.error('获取信息失败');
    }
  };

  useEffect(() => {
    if (visible && detailedTask) {
      resetForm();
      fetchSubmissionInfo();
    }
  }, [visible, detailedTask]);

  // 当切换提交类型时，重新获取对应的列表
  useEffect(() => {
    if (activeView === 'works' as ViewType) {
      if (submissionType === 'work') {
        fetchSubmissionInfo();
      } else {
        fetchImageList();
      }
    }
  }, [submissionType, activeView]);

  // 提交任务
  const handleSubmit = async () => {
    if (!detailedTask?.assignments?.[0]?.id) {
      notification.error('任务ID无效');
      return;
    }

    let submissionSuccessful = false;
    let submissionTypeForNotification: 'work' | 'image' | 'files' | 'work_and_image' | null = null;

    try {
      setSubmitting(true);
      // 获取当前用户ID
      const userData = localStorage.getItem('user');
      const currentUser = userData ? JSON.parse(userData) : null;
      const currentUserId = currentUser?.userId;

      // 确保当前用户是任务的分配者
      const userAssignment = detailedTask.assignments.find(assignment =>
        assignment.studentId === currentUserId
      );

      if (!userAssignment) {
        notification.error('无法提交任务：无权限');
        return;
      }

      // 在提交前获取用户当前的模板信息
      const roleInfo = await getUserRoleInfo(userAssignment.studentId);
      const originalTemplateId = roleInfo?.data?.data?.originalTemplateId;

      // 处理文件上传
      let allFileUrls: string[] | null = null;
      if (uploadFiles.length > 0) {
        const existingFiles = uploadFiles.filter(file => file.url);
        const newFiles = uploadFiles.filter(file => !file.url && file.originFileObj);

        allFileUrls = existingFiles.map(file => file.url || '');
        if (newFiles.length > 0) {
          const newFileUrls = await Promise.all(
            newFiles.map(file =>
              file.originFileObj ? uploadApi.uploadToOss(file.originFileObj) : Promise.reject('文件无效')
            )
          );
          allFileUrls = [...allFileUrls, ...newFileUrls];
        }
      }

      // 确定提交类型
      let submissionType: 'work' | 'image' | 'files' | 'work_and_image';
      if (selectedWorkId && selectedImageId) {
        submissionType = 'work_and_image';
      } else if (selectedWorkId) {
        submissionType = 'work';
      } else if (selectedImageId) {
        submissionType = 'image';
      } else {
        submissionType = 'files';
      }
      submissionTypeForNotification = submissionType;

      // 提交任务
      const response = await taskApi.submitTask({
        taskId: detailedTask.id,
        workId: selectedWorkId,
        imageId: selectedImageId,
        files: allFileUrls,
        submissionType
      });

      console.log('提交任务响应:', response);

      if (response.data?.code === 200) {
        submissionSuccessful = true;
        // 更新任务状态
        if (detailedTask?.assignments?.[0]) {
          // 更新本地状态
          detailedTask.assignments[0].taskStatus = TaskStatus.COMPLETED;
          detailedTask.assignments[0].taskScore = undefined;
          detailedTask.assignments[0].submitTime = new Date();
          detailedTask.assignments[0].workId = selectedWorkId || undefined;
          await fetchSubmissionInfo();
        }
      } else if (response.data === "") {
        notification.error("该作品已提交其他任务！");
      }

      // 提交成功后，恢复原始模板
      if (originalTemplateId) {
        try {
          await addUserJoinRole({
            userId: userAssignment.studentId,
            roleId: 1,
            templateId: originalTemplateId,
          });
        } catch (error) {
          console.error('恢复原始模板失败:', error);
        }
      }
    } catch (error: any) {
      console.error('提交失败:', error);
      notification.error(error.response?.data?.message || '提交失败');
    } finally {
      setSubmitting(false);
      if (submissionSuccessful) {
        console.log('准备检查自评项, 当前任务详情:', detailedTask);
        if (detailedTask.selfAssessmentItems && detailedTask.selfAssessmentItems.length > 0) {
          setIsAssessmentModalVisible(true);
        } else {
          let successMessage = '';
          switch (submissionTypeForNotification) {
            case 'work_and_image':
              successMessage = '作品和图片提交成功';
              break;
            case 'work':
              successMessage = '作品提交成功';
              break;
            case 'image':
              successMessage = '图片提交成功';
              break;
            case 'files':
              successMessage = '文件提交成功';
              break;
          }
          notification.success(successMessage);
          detailedTask?.onSubmitted?.();
          onSubmitted();
          onClose();
        }
      }
    }
  };

  if (!detailedTask) return null;

  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  const userAssignment = detailedTask.assignments.find(a => a.studentId === currentUser.userId);

  return (
    <>
      <Modal
        title={
          <div className="flex items-center justify-between">
            <span className="text-lg">📝 提交任务</span>
            {detailedTask?.assignments?.[0]?.taskStatus === TaskStatus.COMPLETED &&
              detailedTask.assignments[0].taskScore === null && (
                <Tag color="warning" className="rounded-full px-3">待评分</Tag>
              )}
          </div>
        }
        open={visible}
        onCancel={onClose}
        centered
        footer={[
          <Button key="cancel" onClick={onClose} className="rounded-full px-6 hover:bg-gray-50">
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={submitting}
            onClick={handleSubmit}
            className="rounded-full px-6"
            disabled={
              (activeView === 'works' as ViewType && !selectedWorkId && !selectedImageId) ||
              (activeView === 'upload' && uploadFiles.length === 0)
            }
          >
            提交
          </Button>
        ]}
        width={1200}
        className="task-submit-modal"
        modalRender={(modal) => (
          <div style={{
            margin: '5vh 0',
            display: 'flex',
            flexDirection: 'column',
            height: 'fit-content',
            maxHeight: '90vh'
          }}>
            {modal}
          </div>
        )}
        style={{
          maxWidth: '90vw',
          top: 0,
          paddingBottom: 0
        }}
        bodyStyle={{
          padding: '24px',
          maxHeight: 'calc(90vh - 110px)',
          overflow: 'hidden'
        }}
      >
        <div className="flex gap-8">
          {/* 左侧选项区域 */}
          <div className="w-[35%] bg-white rounded-xl overflow-y-auto" style={{ maxHeight: 'calc(90vh - 180px)' }}>
            <div className="space-y-3 p-4 lg:space-y-4 lg:p-6">
              {/* 提交方式选择 */}
              <div className="space-y-3 lg:space-y-4">
                {/* 父级选择 */}
                <div className="bg-white p-3 lg:p-4 rounded-2xl border-2 border-gray-100">
                  <div className="text-xs lg:text-sm text-gray-500 mb-2 lg:mb-3">选择提交方式：</div>
                  <div className="grid grid-cols-2 gap-2 lg:gap-3">
                    <div
                      className={`flex items-center gap-1.5 lg:gap-2 p-2 lg:p-3 rounded-xl cursor-pointer transition-all
                        ${activeView === 'works'
                          ? 'bg-blue-50 border-2 border-blue-200'
                          : 'bg-gray-50 border-2 border-gray-200 hover:border-blue-200'}`}
                      onClick={() => {
                        setActiveView('works');
                        setSubmissionType('work');
                      }}
                    >
                      <span className="text-base lg:text-xl">🎨</span>
                      <span className="text-xs lg:text-sm">从个人中心选中</span>
                    </div>
                    <div
                      className={`flex items-center gap-1.5 lg:gap-2 p-2 lg:p-3 rounded-xl cursor-pointer transition-all
                        ${activeView === 'upload'
                          ? 'bg-blue-50 border-2 border-blue-200'
                          : 'bg-gray-50 border-2 border-gray-200 hover:border-blue-200'}`}
                      onClick={() => {
                        setActiveView('upload');
                        setSubmissionType('files');
                      }}
                    >
                      <span className="text-base lg:text-xl">📁</span>
                      <span className="text-xs lg:text-sm">上传文件</span>
                    </div>
                  </div>
                </div>

                {/* 子级选择 */}
                {activeView === 'works' && (
                  <div className="bg-white p-3 lg:p-4 rounded-2xl border-2 border-gray-100">
                    <div className="text-xs lg:text-sm text-gray-500 mb-2 lg:mb-3">选择提交内容：</div>
                    <div className="grid grid-cols-2 gap-2 lg:gap-3">
                      <div
                        className={`flex items-center gap-1.5 lg:gap-2 p-2 lg:p-3 rounded-xl cursor-pointer transition-all
                          ${submissionType === 'work'
                            ? 'bg-blue-50 border-2 border-blue-200'
                            : 'bg-gray-50 border-2 border-gray-200 hover:border-blue-200'}`}
                        onClick={() => setSubmissionType('work')}
                      >
                        <span className="text-base lg:text-xl">🎨</span>
                        <span className="text-xs lg:text-sm">提交作品</span>
                      </div>
                      <div
                        className={`flex items-center gap-1.5 lg:gap-2 p-2 lg:p-3 rounded-xl cursor-pointer transition-all
                          ${submissionType === 'image'
                            ? 'bg-blue-50 border-2 border-blue-200'
                            : 'bg-gray-50 border-2 border-gray-200 hover:border-blue-200'}`}
                        onClick={() => setSubmissionType('image')}
                      >
                        <span className="text-base lg:text-xl">📸</span>
                        <span className="text-xs lg:text-sm">提交图片</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* 提交说明 */}
              <div className="bg-white p-3 lg:p-4 rounded-2xl border-2 border-gray-100">
                <div className="text-xs lg:text-sm text-gray-500 mb-2 lg:mb-3">提交说明：</div>
                <div className="space-y-1.5 lg:space-y-2">
                  <div className="flex items-start gap-1.5 lg:gap-2">
                    <div className="mt-1 text-xs lg:text-sm">•</div>
                    <div className="text-xs lg:text-sm text-gray-600">每次提交将覆盖之前的提交内容</div>
                  </div>
                  <div className="flex items-start gap-1.5 lg:gap-2">
                    <div className="mt-1 text-xs lg:text-sm">•</div>
                    <div className="text-xs lg:text-sm text-gray-600">提交后教师可以查看和评分</div>
                  </div>
                  <div className="flex items-start gap-1.5 lg:gap-2">
                    <div className="mt-1 text-xs lg:text-sm">•</div>
                    <div className="text-xs lg:text-sm text-gray-600">在截止日期前可多次提交</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧预览区域 */}
          <div className="w-[65%] rounded-xl overflow-y-auto" style={{ maxHeight: 'calc(90vh - 180px)' }}>
            {activeView === ('works' as ViewType) ? (
              <div>
                {submissionType === 'work' ? (
                  // 作品列表
                  <div className="works-list">
                    <div className="bg-blue-50 p-6 rounded-xl border-2 border-blue-100">
                      <div className="flex items-center gap-2 mb-4">
                        <span className="text-xl">🎯</span>
                        <p className="text-blue-600 font-medium">请选择要提交的作品：</p>
                      </div>
                      {works.length > 0 ? (
                        <div className="space-y-4 overflow-visible pr-2">
                          {works.map(work => {
                            const isSubmittedWork = detailedTask.assignments?.[0]?.workId === work.id;
                            const isSelected = selectedWorkId === work.id;
                            const isCancelled = isSubmittedWork && !isSelected;

                            return (
                              <div
                                key={work.id}
                                className={`p-3 rounded-lg border cursor-pointer transition-colors
                                  ${isCancelled ? 'border-red-200 bg-red-50' :
                                    isSubmittedWork && isSelected ? 'border-green-500 bg-green-50' :
                                      isSelected ? 'border-blue-500 bg-blue-50' :
                                        'border-gray-200 hover:border-blue-300'}`}
                                onClick={() => {
                                  if (selectedWorkId === work.id) {
                                    setSelectedWorkId(null);
                                  } else {
                                    setSelectedWorkId(work.id);
                                  }
                                }}
                              >
                                <div className="flex justify-between items-center mb-3">
                                  <div className="flex items-center gap-2">
                                    <div className={`w-4 h-4 rounded-full border transition-colors flex items-center justify-center
                                      ${isCancelled ? 'border-red-500 bg-white' :
                                        isSubmittedWork && isSelected ? 'border-green-500 bg-green-500' :
                                          isSelected ? 'border-blue-500 bg-blue-500' :
                                            'border-gray-300'
                                      }`}
                                    >
                                      {isSelected && (
                                        <CheckOutlined className="text-[10px] text-white" />
                                      )}
                                      {isCancelled && (
                                        <CloseOutlined className="text-[10px] text-red-500" />
                                      )}
                                    </div>
                                    <span className={`text-sm ${isCancelled ? 'text-red-600' : 'text-gray-600'}`}>
                                      {isCancelled ? '已取消提交' :
                                        isSubmittedWork && isSelected ? '保持提交' :
                                          isSelected ? '已选择' :
                                            isSubmittedWork ? '点击选择（当前已提交）' :
                                              '点击选择'}
                                    </span>
                                    {isSubmittedWork && (
                                      <Tag color={isSelected ? "success" : "error"} className="ml-2 rounded-full">
                                        {isSelected ? "当前提交" : "取消提交"}
                                      </Tag>
                                    )}
                                  </div>
                                </div>

                                {isCancelled && (
                                  <div className="mb-3 p-2 bg-red-50 rounded border border-red-200">
                                    <div className="flex items-center gap-2 text-red-600">
                                      <InfoCircleOutlined />
                                      <span className="text-sm">此作品将被取消提交</span>
                                    </div>
                                  </div>
                                )}

                                <div className="relative mb-3 h-[180px]">
                                  <Carousel
                                    dots={{ className: 'custom-dots !z-[1]' }}
                                    arrows
                                    prevArrow={<CustomArrow type="prev" />}
                                    nextArrow={<CustomArrow type="next" />}
                                  >
                                    {work.coverImage && (
                                      <div className="h-[180px] relative">
                                        <img
                                          src={work.coverImage}
                                          alt={`${work.title} 封面`}
                                          className="w-full h-full object-contain rounded-lg"
                                        />
                                        <ImageLabel text="作品封面" />
                                      </div>
                                    )}
                                    {work.screenShotImage && (
                                      <div className="h-[180px] relative">
                                        <img
                                          src={work.screenShotImage}
                                          alt={`${work.title} 截图`}
                                          className="w-full h-full object-contain rounded-lg"
                                        />
                                        <ImageLabel text="作品截图" />
                                      </div>
                                    )}
                                  </Carousel>
                                </div>

                                <div className="flex justify-between items-start">
                                  <h4 className="font-medium text-sm">{work.title}</h4>
                                  <Tag color={work.status === 0 ? 'default' : 'success'} className="rounded-full">
                                    {work.status === 0 ? '未发布' : '已发布'}
                                  </Tag>
                                </div>
                                <p className="text-xs text-gray-500 mt-1">{work.description || '暂无描述'}</p>
                                <div className="text-xs text-gray-400 mt-1">
                                  创建时间：{new Date(work.createTime).toLocaleString()}
                                </div>

                                {/* 添加已提交任务信息 */}
                                {work.submissions && work.submissions.length > 0 && (
                                  <div className="mt-3 pt-3 border-t border-gray-100">
                                    <div className="flex items-center gap-2 mb-2">
                                      <div className="text-xs text-gray-500">已提交到以下任务：</div>
                                      <div className="text-xs text-blue-500">({work.submissions.length}个)</div>
                                    </div>
                                    <div className="space-y-1.5">
                                      {work.submissions.map((submission: any) => (
                                        <div
                                          key={submission.taskId}
                                          className={`flex items-center justify-between p-1.5 rounded-md text-xs
                                            ${submission.taskId === detailedTask?.id
                                              ? 'bg-blue-50 border border-blue-100'
                                              : 'bg-gray-50 border border-gray-100'}`}
                                        >
                                          <div className="flex items-center gap-2">
                                            <div className={`w-1.5 h-1.5 rounded-full 
                                              ${submission.taskStatus === 2 ? 'bg-green-500' :
                                                submission.taskStatus === 4 ? 'bg-yellow-500' :
                                                  submission.taskStatus === 3 ? 'bg-red-500' :
                                                    'bg-blue-500'}`}
                                            />
                                            <span className={submission.taskId === detailedTask?.id ? 'text-blue-600 font-medium' : 'text-gray-600'}>
                                              {submission.taskName}
                                            </span>
                                          </div>
                                          <div className="flex items-center gap-3">
                                            {submission.taskScore !== undefined && submission.taskScore !== null && (
                                              <div className="flex items-center gap-1 text-blue-500">
                                                <svg className="w-3 h-3" viewBox="0 0 24 24" fill="currentColor">
                                                  <path d="M12 2L9.19 8.63 2 9.24l5.46 4.73L5.82 21 12 17.27 18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2z" />
                                                </svg>
                                                <span className="font-medium">{submission.taskScore}分</span>
                                              </div>
                                            )}
                                            {submission.submitTime && (
                                              <div className="text-gray-400">
                                                {new Date(submission.submitTime).toLocaleDateString()}
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      ) : (
                        <Empty description="暂无可提交的作品" />
                      )}
                    </div>
                  </div>
                ) : (
                  // 图片列表
                  <div className="images-list">
                    <div className="bg-blue-50 p-6 rounded-xl border-2 border-blue-100">
                      <div className="flex items-center gap-2 mb-4">
                        <span className="text-xl">🎯</span>
                        <p className="text-blue-600 font-medium">请选择要提交的图片：</p>
                      </div>
                      {images.length > 0 ? (
                        <div className="space-y-4 overflow-visible">
                          {images.map(image => {
                            const isSelected = selectedImageId === image.id;

                            return (
                              <div
                                key={image.id}
                                className={`p-3 rounded-lg border cursor-pointer transition-colors bg-white
                                  ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'}`}
                                onClick={() => {
                                  if (selectedImageId === image.id) {
                                    setSelectedImageId(null);
                                  } else {
                                    setSelectedImageId(image.id);
                                  }
                                }}
                              >
                                <div className="relative mb-3 h-[180px]">
                                  <img
                                    src={image.backupImagePath}
                                    alt={image.prompt || '图片'}
                                    className="w-full h-full object-contain rounded-lg"
                                  />
                                </div>
                                <div className="flex justify-between items-start">
                                  <div className="text-sm text-gray-500">
                                    {new Date(image.createTime).toLocaleString()}
                                  </div>
                                  {isSelected && (
                                    <Tag color="success">已选择</Tag>
                                  )}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      ) : (
                        <Empty description="暂无可提交的图片" />
                      )}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              // 文件上传区域
              <Upload.Dragger
                multiple
                fileList={uploadFiles}
                onChange={({ fileList }) => setUploadFiles(fileList)}
                beforeUpload={() => false}
                className="upload-area bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-100 rounded-xl"
                style={{ padding: '32px 16px' }}
              >
                <div className="space-y-4">
                  <div className="flex justify-center">
                    <InboxOutlined className="text-4xl text-yellow-500" />
                  </div>
                  <div>
                    <p className="text-base font-medium text-yellow-700">点击或拖拽文件到此区域上传</p>
                    <p className="text-sm text-yellow-600 mt-2">支持单个或批量上传</p>
                  </div>
                  <div className="flex justify-center gap-2">
                    <Tag color="orange" className="px-3 py-1 text-sm rounded-full">
                      <span className="mr-1">📄</span> 文档
                    </Tag>
                    <Tag color="blue" className="px-3 py-1 text-sm rounded-full">
                      <span className="mr-1">🖼️</span> 图片
                    </Tag>
                    <Tag color="purple" className="px-3 py-1 text-sm rounded-full">
                      <span className="mr-1">📊</span> 其他
                    </Tag>
                  </div>
                </div>
              </Upload.Dragger>
            )}
          </div>
        </div>
      </Modal>

      {detailedTask.selfAssessmentItems && userAssignment && (
        <SelfAssessmentModal
          visible={isAssessmentModalVisible}
          onClose={() => {
            setIsAssessmentModalVisible(false);
            notification.success('任务提交成功！');
            detailedTask?.onSubmitted?.();
            onSubmitted();
            onClose();
          }}
          items={detailedTask.selfAssessmentItems}
          assignmentId={userAssignment.id}
          studentId={userAssignment.studentId}
        />
      )}
    </>
  );
} 