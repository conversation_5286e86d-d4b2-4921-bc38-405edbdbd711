"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/ClassDetail.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/ClassDetail.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/blocks.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Blocks,ChevronDown,Download,Edit,Gift,Link,Plus,Search,Settings,Trash2,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _lib_api_student__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/api/student */ \"(app-pages-browser)/./lib/api/student.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/TemplateContext */ \"(app-pages-browser)/./app/workbench/contexts/TemplateContext.tsx\");\n/* harmony import */ var _AddStudentModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AddStudentModal */ \"(app-pages-browser)/./app/workbench/components/AddStudentModal.tsx\");\n/* harmony import */ var _teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../teacher-space/components/modals */ \"(app-pages-browser)/./app/teacher-space/components/modals/index.ts\");\n/* harmony import */ var _AssignBlocksModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AssignBlocksModal */ \"(app-pages-browser)/./app/workbench/components/AssignBlocksModal.tsx\");\n/* harmony import */ var _TransferClassModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TransferClassModal */ \"(app-pages-browser)/./app/workbench/components/TransferClassModal.tsx\");\n/* harmony import */ var _AssignPointsModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./AssignPointsModal */ \"(app-pages-browser)/./app/workbench/components/AssignPointsModal.tsx\");\n/* harmony import */ var _BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./BatchUseKeyPackageModal */ \"(app-pages-browser)/./app/workbench/components/BatchUseKeyPackageModal.tsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./app/workbench/components/StudentList/index.tsx\");\n/* harmony import */ var _ClassDetail_css__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ClassDetail.css */ \"(app-pages-browser)/./app/workbench/components/ClassDetail.css\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 类型定义已移至 ./types/index.ts\nconst ClassDetail = (param)=>{\n    let { classInfo: initialClassInfo, selectedSchool, onBack, onClassInfoUpdate, onClassDeleted } = param;\n    var _selectedStudent_nickName;\n    _s();\n    // 创建内部状态来管理班级信息，这样可以在编辑后更新显示\n    const [classInfo, setClassInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialClassInfo);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 同步外部传入的classInfo变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setClassInfo(initialClassInfo);\n    }, [\n        initialClassInfo\n    ]);\n    const [isAddStudentModalVisible, setIsAddStudentModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSettingsDropdownOpen, setIsSettingsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchActionsDropdownOpen, setIsBatchActionsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const settingsDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const batchActionsDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 模态框状态\n    const [isEditClassModalVisible, setIsEditClassModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isImportStudentModalVisible, setIsImportStudentModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTransferClassModalVisible, setIsTransferClassModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isInviteCodeModalVisible, setIsInviteCodeModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAssignBlocksModalVisible, setIsAssignBlocksModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [inviteCode, setInviteCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 新增功能模态框状态\n    const [isPublishTaskModalVisible, setIsPublishTaskModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResetPasswordModalVisible, setIsResetPasswordModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAssignPointsModalVisible, setIsAssignPointsModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchUseKeyPackageModalVisible, setIsBatchUseKeyPackageModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [isRedeemKeyModalVisible, setIsRedeemKeyModalVisible] = useState(false); // 未使用，已移除\n    // 转让管理相关状态\n    const [searchedTeacher, setSearchedTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [transferLoading, setTransferLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // PublishTaskModal 相关状态\n    const [fileList, setFileList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [officialTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 保留以避免引用错误\n    const [selectedTemplateId, setSelectedTemplateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // AssignBlocksModal 相关状态\n    const [loadingTemplates, setLoadingTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [studentTemplateUsage, setStudentTemplateUsage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [teacherTemplate, setTeacherTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedStudentId, setSelectedStudentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 添加userRoles状态，与teacher-space保持一致\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 多选状态\n    const [selectedStudentIds, setSelectedStudentIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSelectAll, setIsSelectAll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 使用全局模板状态\n    const { currentTemplate, globalTemplateChangeVersion, refreshCurrentTemplate } = (0,_contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__.useTemplate)();\n    // 全局当前模板信息（用于没有个人模板的学生）\n    const [globalCurrentTemplate, setGlobalCurrentTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 获取教师当前模板（从班级接口获取）\n    const fetchTeacherCurrentTemplate = async ()=>{\n        try {\n            if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id) || !(classInfo === null || classInfo === void 0 ? void 0 : classInfo.schoolId)) return null;\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.getTeacherClasses(classInfo.schoolId, userId);\n            if (response.data.code === 200 && response.data.data) {\n                // 找到当前班级的模板信息\n                const currentClass = response.data.data.find((cls)=>cls.id === classInfo.id);\n                if (currentClass && currentClass.templateName) {\n                    const templateInfo = {\n                        templateId: currentClass.templateId || 0,\n                        templateName: currentClass.templateName,\n                        isOfficial: currentClass.isOfficial || false\n                    };\n                    setGlobalCurrentTemplate(templateInfo);\n                    console.log(\"获取到教师当前模板:\", templateInfo);\n                    return templateInfo;\n                }\n            }\n        } catch (error) {\n            console.error(\"获取教师当前模板失败:\", error);\n        }\n        return null;\n    };\n    // 同步教师模板到学生（当教师模板更新时调用）\n    const syncTeacherTemplateToStudents = async ()=>{\n        try {\n            const newTemplate = await fetchTeacherCurrentTemplate();\n            if (newTemplate && students.length > 0) {\n                console.log(\"同步教师模板到学生:\", newTemplate);\n                // 使用 ref 获取最新的 personalTemplateAssignments\n                const currentPersonalAssignments = personalTemplateAssignmentsRef.current;\n                // 更新所有没有个人分配模板的学生\n                setStudents((prevStudents)=>prevStudents.map((student)=>{\n                        // 如果学生有个人分配的模板，保持不变\n                        if (currentPersonalAssignments.has(student.userId)) {\n                            console.log(\"保持学生 \".concat(student.nickName, \" 的个人模板 (syncTeacherTemplateToStudents)\"));\n                            return student;\n                        }\n                        // 否则更新为教师当前模板\n                        console.log(\"更新学生 \".concat(student.nickName, \" 为教师模板 (syncTeacherTemplateToStudents)\"));\n                        return {\n                            ...student,\n                            currentTemplate: newTemplate\n                        };\n                    }));\n            }\n        } catch (error) {\n            console.error(\"同步教师模板失败:\", error);\n        }\n    };\n    // 存储个人分配的模板信息，避免被fetchStudents覆盖\n    const [personalTemplateAssignments, setPersonalTemplateAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    // 添加强制重新渲染的状态\n    const [renderVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // setRenderVersion暂时未使用\n    // 获取用户信息\n    const userId = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector)((state)=>state.user.userState.userId);\n    const roleId = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector)((state)=>state.user.userState.roleId);\n    // 获取班级学生数据\n    const fetchStudents = async ()=>{\n        if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) return;\n        try {\n            setLoading(true);\n            setError(null);\n            // 并行获取班级学生基础数据和教师当前模板\n            const [response] = await Promise.all([\n                _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.getClassStudents(classInfo.id),\n                fetchTeacherCurrentTemplate()\n            ]);\n            if (response.data.code === 200) {\n                const studentsData = response.data.data || [];\n                if (studentsData.length === 0) {\n                    setStudents([]);\n                    setSelectedStudent(null);\n                    // 如果学生数量变为0，也要更新班级信息\n                    if (classInfo.studentCount !== 0) {\n                        const updatedClassInfo = {\n                            ...classInfo,\n                            studentCount: 0\n                        };\n                        setClassInfo(updatedClassInfo);\n                        // 通知父组件更新班级列表中的学生数\n                        onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n                    }\n                    return;\n                }\n                // 提取学生ID列表\n                const userIds = studentsData.map((s)=>s.userId);\n                // 批量获取学生详细信息（包括模板和能量信息）\n                const studentInfoMap = await (userIds.length > 20 ? Promise.all(Array.from({\n                    length: Math.ceil(userIds.length / 20)\n                }, (_, i)=>userIds.slice(i * 20, (i + 1) * 20)).map((batchId)=>_lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.getStudentsBatchInfo(batchId).then((param)=>{\n                        let { data: { data } } = param;\n                        return data;\n                    }).catch((error)=>{\n                        console.error(\"获取学生批量信息失败:\", error);\n                        return {};\n                    }))).then((results)=>Object.assign({}, ...results)) : _lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.getStudentsBatchInfo(userIds).then((param)=>{\n                    let { data: { data } } = param;\n                    return data;\n                }).catch((error)=>{\n                    console.error(\"获取学生批量信息失败:\", error);\n                    return {};\n                }));\n                // 合并学生数据\n                const completeStudents = studentsData.map((s)=>{\n                    const studentInfo = studentInfoMap[s.userId];\n                    // 检查是否有个人分配的模板\n                    const personalTemplate = personalTemplateAssignments.get(s.userId);\n                    return {\n                        ...s,\n                        ...studentInfo,\n                        id: s.userId,\n                        nickName: s.nickName || \"学生\".concat(s.studentNumber || s.userId),\n                        totalPoints: (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.totalPoints) || 0,\n                        availablePoints: (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.availablePoints) || 0,\n                        avatarUrl: s.avatarUrl || \"/default-avatar.png\",\n                        // 优先级：个人分配的模板 > 学生API返回的模板 > 教师当前模板 > null\n                        currentTemplate: (()=>{\n                            var _studentsData_;\n                            const result = personalTemplate || (studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.currentTemplate) || globalCurrentTemplate;\n                            // 调试信息\n                            if (s.userId === ((_studentsData_ = studentsData[0]) === null || _studentsData_ === void 0 ? void 0 : _studentsData_.userId)) {\n                                console.log(\"学生模板分配逻辑:\", {\n                                    studentId: s.userId,\n                                    studentName: s.nickName,\n                                    personalTemplate,\n                                    studentApiTemplate: studentInfo === null || studentInfo === void 0 ? void 0 : studentInfo.currentTemplate,\n                                    globalCurrentTemplate,\n                                    finalTemplate: result\n                                });\n                            }\n                            return result;\n                        })()\n                    };\n                });\n                setStudents(completeStudents);\n                // 如果学生数量发生变化，更新班级信息并通知父组件\n                if (completeStudents.length !== classInfo.studentCount) {\n                    const updatedClassInfo = {\n                        ...classInfo,\n                        studentCount: completeStudents.length\n                    };\n                    setClassInfo(updatedClassInfo);\n                    // 通知父组件更新班级列表中的学生数\n                    onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n                }\n                // 设置userRoles，与teacher-space保持一致\n                const newUserRoles = completeStudents.map((student)=>({\n                        userId: student.userId,\n                        roleId: 1 // 学生角色ID为1\n                    }));\n                // 添加教师自己的角色\n                newUserRoles.push({\n                    userId: userId,\n                    roleId: 2 // 教师角色ID为2\n                });\n                setUserRoles(newUserRoles);\n                // 默认选择第一个学生\n                if (completeStudents.length > 0) {\n                    setSelectedStudent(completeStudents[0]);\n                }\n            } else {\n                setError(response.data.message || \"获取学生列表失败\");\n            }\n        } catch (err) {\n            console.error(\"获取学生列表失败:\", err);\n            console.error(\"错误详情:\", {\n                message: err instanceof Error ? err.message : \"未知错误\",\n                stack: err instanceof Error ? err.stack : undefined,\n                classId: classInfo.id\n            });\n            setError(\"获取学生列表失败，请稍后重试\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 获取权限模板列表\n    const fetchTemplates = async ()=>{\n        setLoadingTemplates(true);\n        try {\n            const { getRoleTemplateList, getOfficialTemplates } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            // 同时获取教师自定义模板和官方模板\n            const [customResponse, officialResponse] = await Promise.all([\n                getRoleTemplateList(userId),\n                getOfficialTemplates()\n            ]);\n            if (customResponse.data.code === 200 && officialResponse.data.code === 200) {\n                const customTemplates = customResponse.data.data || [];\n                const officialTemplates = officialResponse.data.data || [];\n                // 为官方模板添加标记\n                const markedOfficialTemplates = officialTemplates.map((template)=>({\n                        ...template,\n                        isOfficial: true\n                    }));\n                // 合并所有模板\n                const allTemplates = [\n                    ...customTemplates,\n                    ...markedOfficialTemplates\n                ];\n                setTemplates(allTemplates);\n            }\n        } catch (error) {\n            console.error(\"获取权限模板列表失败:\", error);\n        } finally{\n            setLoadingTemplates(false);\n        }\n    };\n    // 获取模板使用情况\n    const fetchTemplateUsage = async ()=>{\n        try {\n            const { getUserCurrentTemplate, getStudentTemplates } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            // 获取教师使用的模板\n            const teacherResponse = await getUserCurrentTemplate(userId);\n            if (teacherResponse.data.code === 200) {\n                const teacherTemplateData = teacherResponse.data.data;\n                setTeacherTemplate(teacherTemplateData);\n            // 同时更新当前模板状态，确保与模板管理同步\n            // 暂时注释掉以停止循环\n            /*\r\n        if (teacherTemplateData) {\r\n          // 使用refreshCurrentTemplate来更新全局模板状态\r\n          await refreshCurrentTemplate();\r\n        }\r\n        */ }\n            // 获取学生使用的模板\n            const studentResponse = await getStudentTemplates({\n                teacherId: userId,\n                page: 1,\n                size: 200\n            });\n            if (studentResponse.data.code === 200) {\n                // 统计每个模板被使用的次数\n                const usage = {};\n                studentResponse.data.data.list.forEach((item)=>{\n                    if (item.templateId) {\n                        usage[item.templateId] = (usage[item.templateId] || 0) + 1;\n                    }\n                });\n                setStudentTemplateUsage(usage);\n            }\n        } catch (error) {\n            console.error(\"获取模板使用情况失败:\", error);\n        }\n    };\n    // 组件挂载时获取学生数据和模板\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStudents();\n        fetchTemplates();\n        fetchTemplateUsage();\n    }, [\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n        userId\n    ]);\n    // 当组件重新挂载或班级变化时，确保获取最新的当前模板\n    // 暂时注释掉以停止循环\n    /*\r\n  useEffect(() => {\r\n    if (classInfo?.id && userId) {\r\n      refreshCurrentTemplate();\r\n    }\r\n  }, [classInfo?.id, userId]);\r\n  */ // 移除这个useEffect，避免在currentTemplate变化时覆盖个人分配的模板\n    // 使用 useRef 来保存最新的 personalTemplateAssignments\n    const personalTemplateAssignmentsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(personalTemplateAssignments);\n    personalTemplateAssignmentsRef.current = personalTemplateAssignments;\n    // 监听全局模板变化，重新获取教师当前模板并更新学生数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (globalTemplateChangeVersion > 0 && (classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) {\n            console.log(\"检测到模板变化，重新获取教师当前模板\");\n            // 重新获取教师当前模板\n            fetchTeacherCurrentTemplate().then((newTemplate)=>{\n                if (newTemplate && students.length > 0) {\n                    // 不清除个人分配的模板记录，保留学生的个人模板数据\n                    // setPersonalTemplateAssignments(new Map()); // 注释掉这行\n                    // 使用 ref 获取最新的 personalTemplateAssignments\n                    const currentPersonalAssignments = personalTemplateAssignmentsRef.current;\n                    // 只更新没有个人模板的学生为新的教师当前模板\n                    console.log(\"全局模板变化，开始更新学生模板:\", {\n                        newTemplate,\n                        personalTemplateAssignments: Array.from(currentPersonalAssignments.entries()),\n                        studentsCount: students.length\n                    });\n                    setStudents((prevStudents)=>prevStudents.map((student)=>{\n                            const hasPersonalTemplate = currentPersonalAssignments.has(student.userId);\n                            console.log(\"学生 \".concat(student.nickName, \" (\").concat(student.userId, \"):\"), {\n                                hasPersonalTemplate,\n                                currentTemplate: student.currentTemplate,\n                                willUpdate: !hasPersonalTemplate\n                            });\n                            // 如果学生有个人分配的模板，保持不变\n                            if (hasPersonalTemplate) {\n                                console.log(\"保持学生 \".concat(student.nickName, \" 的个人模板\"));\n                                return student;\n                            }\n                            // 否则更新为新的教师当前模板\n                            console.log(\"更新学生 \".concat(student.nickName, \" 为教师模板\"));\n                            return {\n                                ...student,\n                                currentTemplate: newTemplate\n                            };\n                        }));\n                }\n            });\n        }\n    }, [\n        globalTemplateChangeVersion,\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id\n    ]);\n    // 定期检查教师模板更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(classInfo === null || classInfo === void 0 ? void 0 : classInfo.id)) return;\n        // 立即检查一次\n        syncTeacherTemplateToStudents();\n        // 设置定时器，每30秒检查一次模板更新\n        const interval = setInterval(()=>{\n            syncTeacherTemplateToStudents();\n        }, 30000); // 30秒\n        return ()=>clearInterval(interval);\n    }, [\n        classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n        students.length\n    ]);\n    // 点击外部关闭下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n            // setIsMoreActionsDropdownOpen(false); // 已移除\n            }\n            if (settingsDropdownRef.current && !settingsDropdownRef.current.contains(event.target)) {\n                setIsSettingsDropdownOpen(false);\n            }\n            if (batchActionsDropdownRef.current && !batchActionsDropdownRef.current.contains(event.target)) {\n                setIsBatchActionsDropdownOpen(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, []);\n    // 处理学生点击选择（用于右侧显示详情）\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n    };\n    // 处理删除学生\n    const handleDeleteStudent = async ()=>{\n        if (!selectedStudent) {\n            alert(\"请先选择要删除的学生\");\n            return;\n        }\n        // 显示确认对话框\n        const confirmed = window.confirm(\"确定要将 \".concat(selectedStudent.nickName, \" 移出班级吗？\\n\\n此操作不可恢复！\"));\n        if (!confirmed) {\n            return;\n        }\n        try {\n            console.log(\"删除学生:\", selectedStudent);\n            // 调用删除学生的API，传入学生的userId数组\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.removeStudentFromClass([\n                selectedStudent.userId\n            ]);\n            console.log(\"删除学生 API 响应:\", response.data);\n            // 检查响应状态\n            if (response.data.code === 200) {\n                console.log(\"删除学生成功\");\n                alert(\"学生已成功移出班级\");\n                // 清除选中的学生\n                setSelectedStudent(null);\n                // 重新获取学生列表\n                await fetchStudents();\n            } else {\n                console.error(\"删除学生失败:\", response.data.message);\n                alert(response.data.message || \"删除学生失败\");\n            }\n        } catch (error) {\n            console.error(\"删除学生失败:\", error);\n            alert(\"删除学生失败，请稍后重试\");\n        }\n    };\n    // 编辑班级\n    const handleEditClass = async (values)=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        try {\n            console.log(\"开始编辑班级:\", {\n                classId: classInfo.id,\n                values: values,\n                originalClassName: classInfo.className\n            });\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.updateClass(classInfo.id, {\n                className: values.className,\n                grade: classInfo.grade || \"\" // 确保传递grade字段，如果没有则使用空字符串\n            });\n            console.log(\"编辑班级API响应:\", response);\n            if (response.data.code === 200) {\n                console.log(\"编辑班级成功\");\n                notification.success(\"编辑班级成功\");\n                setIsEditClassModalVisible(false);\n                // 更新本地班级信息\n                const updatedClassInfo = {\n                    ...classInfo,\n                    className: values.className\n                };\n                setClassInfo(updatedClassInfo);\n                // 通知父组件更新班级列表中的班级信息\n                onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n            } else {\n                console.error(\"编辑班级失败 - API返回错误:\", {\n                    code: response.data.code,\n                    message: response.data.message,\n                    data: response.data\n                });\n                notification.error(response.data.message || \"编辑班级失败\");\n                throw new Error(response.data.message || \"编辑班级失败\"); // 抛出错误让模态框知道失败了\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"编辑班级失败 - 请求异常:\", {\n                error: error,\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // 只有在没有显示过错误消息的情况下才显示通用错误\n            if (!error.message || error.message === \"编辑班级失败\") {\n                var _error_response_data, _error_response2;\n                notification.error(((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"编辑班级失败，请稍后重试\");\n            }\n            throw error; // 重新抛出错误，让模态框保持打开状态\n        }\n    };\n    // 导入学生\n    const handleImportStudents = async (file)=>{\n        try {\n            console.log(\"导入学生文件:\", file);\n            // 这里需要实现文件解析和导入逻辑\n            alert(\"导入学生功能正在开发中\");\n            setIsImportStudentModalVisible(false);\n            return true;\n        } catch (error) {\n            console.error(\"导入学生失败:\", error);\n            alert(\"导入学生失败，请稍后重试\");\n            return false;\n        }\n    };\n    // 导出学生\n    const handleExportStudents = async ()=>{\n        try {\n            const response = await _lib_api_student__WEBPACK_IMPORTED_MODULE_3__.studentApi.exportStudents(classInfo.id);\n            console.log(\"导出学生成功:\", response);\n            alert(\"导出学生成功\");\n        } catch (error) {\n            console.error(\"导出学生失败:\", error);\n            alert(\"导出学生失败，请稍后重试\");\n        }\n    };\n    // 搜索教师\n    const handleSearchTeacher = async (phone)=>{\n        try {\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.searchTeacherByPhone(phone);\n            console.log(\"搜索教师响应:\", response);\n            if (response.data.code === 200) {\n                setSearchedTeacher(response.data.data);\n            } else {\n                const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                const notification = GetNotification();\n                notification.error(response.data.message || \"搜索教师失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"搜索教师失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"搜索教师失败\");\n        }\n    };\n    // 转让班级\n    const handleTransferClass = async (values)=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        setTransferLoading(true);\n        try {\n            let newTeacherId;\n            if (values.transferType === \"search\") {\n                if (!searchedTeacher) {\n                    notification.error(\"请先搜索并选择教师\");\n                    return;\n                }\n                newTeacherId = searchedTeacher.id;\n            } else {\n                // 检查是否有协助教师\n                if (!classInfo.assistantTeacherId) {\n                    notification.error(\"该班级没有协助教师\");\n                    return;\n                }\n                newTeacherId = classInfo.assistantTeacherId;\n            }\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.transferClass(classInfo.id, newTeacherId, values.transferType);\n            if (response.data.code === 200) {\n                notification.success(\"转让班级成功\");\n                setIsTransferClassModalVisible(false);\n                setSearchedTeacher(null);\n                // 转让成功后返回班级管理页面\n                onBack();\n            } else {\n                notification.error(response.data.message || \"转让班级失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"转让班级失败:\", error);\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"转让班级失败\");\n        } finally{\n            setTransferLoading(false);\n        }\n    };\n    // 移出协助教师\n    const handleRemoveAssistant = async ()=>{\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        try {\n            // 这里需要调用移出协助教师的API\n            // 暂时使用转让API，将assistantTeacherId设为0\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.updateClass(classInfo.id, {\n                assistantTeacherId: 0\n            });\n            if (response.data.code === 200) {\n                notification.success(\"移出协助教师成功\");\n                setIsTransferClassModalVisible(false);\n                // 更新本地班级信息\n                const updatedClassInfo = {\n                    ...classInfo,\n                    assistantTeacherId: 0\n                };\n                setClassInfo(updatedClassInfo);\n                onClassInfoUpdate === null || onClassInfoUpdate === void 0 ? void 0 : onClassInfoUpdate(updatedClassInfo);\n            } else {\n                notification.error(response.data.message || \"移出协助教师失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"移出协助教师失败:\", error);\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"移出协助教师失败\");\n        }\n    };\n    // 生成邀请码\n    const handleGenerateInviteCode = async ()=>{\n        try {\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.generateInviteCode(classInfo.id);\n            if (response.data.code === 200) {\n                setInviteCode(response.data.data.inviteCode);\n                setIsInviteCodeModalVisible(true);\n            } else {\n                console.error(\"生成邀请码失败:\", response.data.message);\n                alert(response.data.message || \"生成邀请码失败\");\n            }\n        } catch (error) {\n            console.error(\"生成邀请码失败:\", error);\n            alert(\"生成邀请码失败，请稍后重试\");\n        }\n    };\n    // 分配积木（批量或选中学生）\n    const handleAssignBlocks = async ()=>{\n        console.log(\"=== 分配积木开始 ===\");\n        console.log(\"selectedStudentIds:\", selectedStudentIds);\n        console.log(\"selectedStudentIds.length:\", selectedStudentIds.length);\n        // 如果有选中的学生，设置为单个学生分配模式\n        if (selectedStudentIds.length === 1) {\n            console.log(\"单个学生分配模式，studentId:\", selectedStudentIds[0]);\n            setSelectedStudentId(selectedStudentIds[0]);\n        } else {\n            console.log(\"批量分配模式，学生数量:\", selectedStudentIds.length);\n            setSelectedStudentId(null);\n        }\n        await fetchTemplates();\n        await fetchTemplateUsage();\n        setIsAssignBlocksModalVisible(true);\n    };\n    // 单独为学生分配积木\n    const handleIndividualAssignBlocks = async (studentId)=>{\n        console.log(\"=== 单独为学生分配积木 ===\");\n        console.log(\"studentId:\", studentId);\n        // 设置为单个学生分配模式\n        setSelectedStudentId(studentId);\n        setSelectedStudentIds([]); // 清空批量选择\n        await fetchTemplates();\n        await fetchTemplateUsage();\n        setIsAssignBlocksModalVisible(true);\n    };\n    // 删除班级\n    const handleDeleteClass = async ()=>{\n        const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n        const notification = GetNotification();\n        // 先检查是否有学生\n        if (students.length > 0) {\n            Modal.warning({\n                title: \"无法删除班级\",\n                centered: true,\n                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"班级中还有 \",\n                                students.length,\n                                \" 名学生，请先移除所有学生后再删除班级。\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 795,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-gray-500 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"删除步骤：\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    className: \"list-decimal ml-4 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"选择要移除的学生\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 799,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"使用批量操作移除所有学生\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 800,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"再次尝试删除班级\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 801,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 798,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 794,\n                    columnNumber: 11\n                }, undefined),\n                okText: \"知道了\"\n            });\n            return;\n        }\n        // 如果没有学生，显示删除确认对话框\n        Modal.confirm({\n            title: \"确认删除班级\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"您确定要删除 \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: classInfo.className\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 21\n                            }, undefined),\n                            \" 吗？\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 816,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-3 bg-red-50 rounded-lg border border-red-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 820,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 819,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"此操作不可恢复！\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 818,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-500\",\n                                children: \"删除班级将永久移除班级信息，包括班级设置、模板配置等数据。\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 824,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 817,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 815,\n                columnNumber: 9\n            }, undefined),\n            okText: \"确定删除\",\n            cancelText: \"取消\",\n            okButtonProps: {\n                danger: true\n            },\n            centered: true,\n            onOk: async ()=>{\n                try {\n                    const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.deleteClass(classInfo.id);\n                    if (response.data.code === 200) {\n                        notification.success(\"删除班级成功\");\n                        // 通知父组件班级已被删除\n                        onClassDeleted === null || onClassDeleted === void 0 ? void 0 : onClassDeleted(classInfo.id);\n                        // 返回到班级管理页面\n                        onBack();\n                    } else {\n                        notification.error(response.data.message || \"删除班级失败\");\n                    }\n                } catch (error) {\n                    var _error_response_data, _error_response;\n                    console.error(\"删除班级失败:\", error);\n                    if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                        notification.error(error.response.data.message);\n                    } else {\n                        notification.error(\"删除班级失败，请稍后重试\");\n                    }\n                }\n            }\n        });\n    };\n    // 下拉菜单项处理函数\n    const handleMenuItemClick = (action)=>{\n        // setIsMoreActionsDropdownOpen(false); // 已移除\n        switch(action){\n            case \"edit\":\n                setIsEditClassModalVisible(true);\n                break;\n            case \"addStudent\":\n                setIsAddStudentModalVisible(true);\n                break;\n            case \"importStudent\":\n                setIsImportStudentModalVisible(true);\n                break;\n            case \"exportStudent\":\n                handleExportStudents();\n                break;\n            case \"transfer\":\n                setIsTransferClassModalVisible(true);\n                break;\n            case \"invite\":\n                handleGenerateInviteCode();\n                break;\n            case \"batchRedeem\":\n                console.log(\"批量兑换密令\");\n                setIsBatchUseKeyPackageModalVisible(true);\n                break;\n            case \"assignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"deleteClass\":\n                handleDeleteClass();\n                break;\n            default:\n                break;\n        }\n    };\n    // 设置下拉菜单项处理函数\n    const handleSettingsMenuItemClick = (action)=>{\n        setIsSettingsDropdownOpen(false);\n        switch(action){\n            case \"edit\":\n                setIsEditClassModalVisible(true);\n                break;\n            case \"addStudent\":\n                setIsAddStudentModalVisible(true);\n                break;\n            case \"importStudent\":\n                setIsImportStudentModalVisible(true);\n                break;\n            case \"exportStudent\":\n                handleExportStudents();\n                break;\n            case \"transfer\":\n                setIsTransferClassModalVisible(true);\n                break;\n            case \"invite\":\n                handleGenerateInviteCode();\n                break;\n            case \"batchRedeem\":\n                console.log(\"批量兑换密令\");\n                setIsBatchUseKeyPackageModalVisible(true);\n                break;\n            case \"assignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"deleteClass\":\n                handleDeleteClass();\n                break;\n            default:\n                break;\n        }\n    };\n    // 确保选中状态同步\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const shouldBeSelectAll = selectedStudentIds.length > 0 && selectedStudentIds.length === students.length;\n        if (isSelectAll !== shouldBeSelectAll) {\n            console.log(\"修复选中状态同步:\", {\n                isSelectAll,\n                shouldBeSelectAll,\n                selectedStudentIds: selectedStudentIds.length,\n                totalStudents: students.length\n            });\n            setIsSelectAll(shouldBeSelectAll);\n        }\n    }, [\n        selectedStudentIds,\n        students.length,\n        isSelectAll\n    ]);\n    // 全选/取消全选处理函数\n    const handleSelectAll = ()=>{\n        if (isSelectAll) {\n            // 取消全选\n            setSelectedStudentIds([]);\n            setIsSelectAll(false);\n        } else {\n            // 全选\n            const allStudentIds = students.map((student)=>student.userId);\n            setSelectedStudentIds(allStudentIds);\n            setIsSelectAll(true);\n        }\n    };\n    // 单个学生选择处理函数\n    const handleStudentSelect = (studentId)=>{\n        if (selectedStudentIds.includes(studentId)) {\n            // 取消选择\n            const newSelectedIds = selectedStudentIds.filter((id)=>id !== studentId);\n            setSelectedStudentIds(newSelectedIds);\n            setIsSelectAll(false);\n        } else {\n            // 选择\n            const newSelectedIds = [\n                ...selectedStudentIds,\n                studentId\n            ];\n            setSelectedStudentIds(newSelectedIds);\n            // 检查是否全选\n            if (newSelectedIds.length === students.length) {\n                setIsSelectAll(true);\n            }\n        }\n    };\n    // 批量操作处理函数\n    const handleBatchAction = (action)=>{\n        setIsBatchActionsDropdownOpen(false);\n        if (selectedStudentIds.length === 0) {\n            Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                let { GetNotification } = param;\n                GetNotification().warning(\"请先选择要操作的学生\");\n            });\n            return;\n        }\n        switch(action){\n            case \"batchDelete\":\n                handleBatchRemoveStudents(selectedStudentIds);\n                break;\n            case \"batchAssignBlocks\":\n                handleAssignBlocks();\n                break;\n            case \"batchAssignPoints\":\n                // 清除单个学生选择，确保进入批量模式\n                setSelectedStudent(null);\n                setIsAssignPointsModalVisible(true);\n                break;\n            case \"batchUseKeyPackage\":\n                handleBatchUseKeyPackage();\n                break;\n            case \"batchExport\":\n                console.log(\"批量导出学生:\", selectedStudentIds);\n                Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                    let { GetNotification } = param;\n                    GetNotification().info(\"批量导出学生功能正在开发中\");\n                });\n                break;\n            default:\n                break;\n        }\n    };\n    // 批量移出班级的改进版本\n    const handleBatchRemoveStudents = async (studentIds)=>{\n        try {\n            // 获取选中的学生信息\n            const selectedStudentsInfo = students.filter((s)=>studentIds.includes(s.userId));\n            // 计算总可用积分\n            const totalAvailablePoints = selectedStudentsInfo.reduce((sum, student)=>sum + (student.availablePoints || 0), 0);\n            const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n            const { InfoCircleOutlined } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_ant-design_icons_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/index.js\"));\n            Modal.confirm({\n                title: \"确认批量移出班级\",\n                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"确定要将选中的 \",\n                                studentIds.length,\n                                \" 名学生移出班级吗？\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1037,\n                            columnNumber: 13\n                        }, undefined),\n                        totalAvailablePoints > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 p-3 bg-yellow-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-yellow-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoCircleOutlined, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1041,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"选中的学生共有 \",\n                                                totalAvailablePoints,\n                                                \" 点可用能量\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1042,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1040,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-sm text-yellow-500\",\n                                    children: \"移出班级后，可用能量将返还到各自的套餐积分中\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1044,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1039,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1036,\n                    columnNumber: 11\n                }, undefined),\n                okText: \"确定移出\",\n                cancelText: \"取消\",\n                centered: true,\n                okButtonProps: {\n                    danger: true\n                },\n                onOk: async ()=>{\n                    try {\n                        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                        const notification = GetNotification();\n                        const hideLoading = notification.loading(\"正在移出学生...\");\n                        const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.removeStudentFromClass(studentIds);\n                        if (hideLoading) {\n                            hideLoading.close();\n                        }\n                        if (response.data.code === 200) {\n                            notification.success(\"成功移出 \".concat(studentIds.length, \" 名学生\"));\n                            // 清除选择状态\n                            setSelectedStudentIds([]);\n                            setIsSelectAll(false);\n                            setSelectedStudent(null);\n                            // 重新获取学生列表\n                            await fetchStudents();\n                        } else {\n                            notification.error(response.data.message || \"批量移出学生失败\");\n                        }\n                    } catch (error) {\n                        var _error_response_data, _error_response;\n                        const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n                        const notification = GetNotification();\n                        notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"批量移出学生失败\");\n                        console.error(\"批量移出学生失败:\", error);\n                    }\n                }\n            });\n        } catch (error) {\n            console.error(\"批量移出学生失败:\", error);\n        }\n    };\n    // 处理添加学生\n    const handleAddStudent = async (values)=>{\n        try {\n            console.log(\"添加学生:\", values);\n            // 添加默认密码\n            const studentData = {\n                ...values,\n                password: \"123456\"\n            };\n            // 调用添加学生的API\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_2__.classApi.addStudentToClass(classInfo.id, studentData);\n            console.log(\"添加学生 API 响应:\", response.data);\n            // 检查响应状态\n            if (response.data.code === 200) {\n                console.log(\"添加学生成功\");\n                setIsAddStudentModalVisible(false);\n                // 重新获取学生列表\n                await fetchStudents();\n            } else {\n                console.error(\"添加学生失败:\", response.data.message);\n                alert(response.data.message || \"添加学生失败\");\n            }\n        } catch (error) {\n            console.error(\"添加学生失败:\", error);\n            alert(\"添加学生失败，请稍后重试\");\n        }\n    };\n    // 处理文件上传\n    const handleUpload = async (file)=>{\n        setUploading(true);\n        try {\n            // 这里可以添加文件上传逻辑\n            console.log(\"上传文件:\", file);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error(\"文件上传失败:\", error);\n            return {\n                success: false\n            };\n        } finally{\n            setUploading(false);\n        }\n    };\n    // 处理文件删除\n    const handleRemoveFile = async (file)=>{\n        try {\n            setFileList((prev)=>prev.filter((f)=>f.uid !== file.uid));\n            return true;\n        } catch (error) {\n            console.error(\"删除文件失败:\", error);\n            return false;\n        }\n    };\n    // 处理发布任务\n    const handlePublishTask = async (values)=>{\n        try {\n            console.log(\"发布任务:\", values);\n            // 导入taskApi\n            const { default: taskApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/task */ \"(app-pages-browser)/./lib/api/task.ts\"));\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 构建任务参数\n            const params = {\n                taskName: values.taskName,\n                taskDescription: values.taskDescription || \"\",\n                taskType: 1,\n                startDate: values.startDate ? new Date(values.startDate) : undefined,\n                endDate: values.endDate ? new Date(values.endDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n                taskContent: values.taskContent || \"\",\n                attachments: fileList.map((file)=>{\n                    var _file_response;\n                    return file.url || ((_file_response = file.response) === null || _file_response === void 0 ? void 0 : _file_response.url);\n                }).filter(Boolean),\n                teacherId: userId,\n                classId: classInfo === null || classInfo === void 0 ? void 0 : classInfo.id,\n                studentIds: selectedStudentIds.length > 0 ? selectedStudentIds : students.map((s)=>s.userId),\n                selfAssessmentItems: values.selfAssessmentItems || [],\n                priority: 1,\n                isPublic: 0,\n                allowLateSubmission: values.allowLateSubmission || false\n            };\n            console.log(\"发布任务参数:\", params);\n            // 调用发布任务API\n            const response = await taskApi.publishTask(params);\n            if (response.data.code === 200) {\n                // 显示详细的成功提示\n                const { Modal } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_antd_es_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! antd */ \"(app-pages-browser)/./node_modules/antd/es/index.js\"));\n                const studentCount = selectedStudentIds.length > 0 ? selectedStudentIds.length : students.length;\n                const className = (classInfo === null || classInfo === void 0 ? void 0 : classInfo.className) || \"当前班级\";\n                Modal.success({\n                    title: \"任务发布成功\",\n                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"任务 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: params.taskName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1194,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    \" 已成功发布到 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: className\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1194,\n                                        columnNumber: 63\n                                    }, undefined),\n                                    \"。\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1194,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"共有 \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: studentCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1195,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    \" 名学生将收到此任务。\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1195,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"学生可以在班级空间查看和提交任务。\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1196,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1193,\n                        columnNumber: 13\n                    }, undefined),\n                    okText: \"确定\",\n                    onOk: ()=>{\n                    // 可以在这里添加导航到任务管理页面的逻辑\n                    }\n                });\n                setIsPublishTaskModalVisible(false);\n                // 清理表单数据\n                setFileList([]);\n                setSelectedStudentIds([]);\n                setSelectedTemplateId(null);\n                // 刷新学生列表（如果需要显示任务相关信息）\n                await fetchStudents();\n            } else {\n                notification.error(response.data.message || \"任务发布失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"发布任务失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"发布任务失败，请稍后重试\");\n        }\n    };\n    // 处理重置密码\n    const handleResetPassword = async ()=>{\n        try {\n            if (!selectedStudent) {\n                alert(\"请先选择要重置密码的学生\");\n                return;\n            }\n            console.log(\"重置密码:\", selectedStudent);\n            // 这里可以添加重置密码的API调用\n            setIsResetPasswordModalVisible(false);\n            alert(\"密码重置成功，新密码为：123456\");\n        } catch (error) {\n            console.error(\"重置密码失败:\", error);\n            alert(\"重置密码失败，请稍后重试\");\n        }\n    };\n    // 处理选择模板\n    const handleSelectTemplate = async (templateId)=>{\n        try {\n            const { addUserJoinRole, batchAddUserJoinRole } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 确定要分配的学生\n            const targetStudents = selectedStudentId !== null ? [\n                selectedStudentId\n            ] : selectedStudentIds;\n            console.log(\"=== 模板分配详情 ===\");\n            console.log(\"selectedStudentId:\", selectedStudentId);\n            console.log(\"selectedStudentIds:\", selectedStudentIds);\n            console.log(\"targetStudents:\", targetStudents);\n            console.log(\"templateId:\", templateId);\n            if (targetStudents.length === 0) {\n                console.log(\"❌ 没有选中任何学生\");\n                notification.warning(\"请先选择学生\");\n                return;\n            }\n            const hideLoading = notification.loading(\"正在分配积木...\");\n            const userRolesMap = userRoles || [];\n            try {\n                // 准备用户数据 - 与teacher-space保持一致的逻辑\n                const usersData = targetStudents.map((userId)=>{\n                    const userInfo = userRolesMap.find((u)=>u.userId === userId);\n                    if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.roleId)) return null;\n                    return {\n                        userId: userId,\n                        roleId: userInfo.roleId,\n                        templateId: templateId,\n                        originalTemplateId: templateId\n                    };\n                }).filter((item)=>item !== null);\n                console.log(\"准备分配模板:\", {\n                    templateId,\n                    targetStudents,\n                    userRolesMap,\n                    usersData\n                });\n                if (usersData.length === 0) {\n                    notification.error(\"无有效用户可分配\");\n                    return;\n                }\n                // 分批并发处理\n                const results = await (usersData.length > 20 ? Promise.all(Array.from({\n                    length: Math.ceil(usersData.length / 20)\n                }, (_, i)=>usersData.slice(i * 20, (i + 1) * 20)).map((batchUsers)=>batchAddUserJoinRole({\n                        users: batchUsers\n                    }).then((param)=>{\n                        let { data } = param;\n                        return data;\n                    }).catch(()=>({\n                            code: 500,\n                            data: {\n                                successCount: 0,\n                                failCount: batchUsers.length\n                            }\n                        })))).then((results)=>({\n                        code: results.some((r)=>r.code !== 200) ? 500 : 200,\n                        data: {\n                            successCount: results.reduce((sum, r)=>{\n                                var _r_data;\n                                return sum + (((_r_data = r.data) === null || _r_data === void 0 ? void 0 : _r_data.successCount) || 0);\n                            }, 0),\n                            failCount: results.reduce((sum, r)=>{\n                                var _r_data;\n                                return sum + (((_r_data = r.data) === null || _r_data === void 0 ? void 0 : _r_data.failCount) || 0);\n                            }, 0)\n                        }\n                    })) : batchAddUserJoinRole({\n                    users: usersData\n                }).then((param)=>{\n                    let { data } = param;\n                    return data;\n                }).catch(()=>({\n                        code: 500,\n                        data: {\n                            successCount: 0,\n                            failCount: usersData.length\n                        }\n                    })));\n                if (hideLoading) {\n                    hideLoading.close();\n                }\n                // 显示结果\n                if (results.code === 200) {\n                    const { successCount = 0, failCount = 0 } = results.data || {};\n                    if (successCount > 0 && failCount === 0) {\n                        notification.success(\"成功为 \".concat(successCount, \" 名学生分配积木\"));\n                    } else if (successCount > 0 && failCount > 0) {\n                        notification.warning(\"成功为 \".concat(successCount, \" 名学生分配积木，\").concat(failCount, \" 名学生分配失败\"));\n                    } else {\n                        notification.error(\"积木分配失败\");\n                    }\n                } else {\n                    notification.error(\"积木分配失败\");\n                }\n                // 立即更新已分配学生的模板信息，无需等待API刷新\n                const selectedTemplate = templates.find((t)=>t.id === templateId);\n                if (selectedTemplate) {\n                    const templateData = {\n                        templateId: templateId,\n                        templateName: selectedTemplate.templateName || selectedTemplate.name,\n                        isOfficial: selectedTemplate.isOfficial || false\n                    };\n                    // 更新personalTemplateAssignments Map，确保数据持久化\n                    setPersonalTemplateAssignments((prev)=>{\n                        const newMap = new Map(prev);\n                        targetStudents.forEach((studentId)=>{\n                            newMap.set(studentId, templateData);\n                        });\n                        return newMap;\n                    });\n                    // 更新学生状态\n                    const updatedStudents = students.map((student)=>{\n                        if (targetStudents.includes(student.userId)) {\n                            // 被选中的学生：设置为新分配的模板\n                            return {\n                                ...student,\n                                currentTemplate: templateData\n                            };\n                        }\n                        // 未选中的学生：保持原有状态\n                        return student;\n                    });\n                    setStudents(updatedStudents);\n                    console.log(\"模板分配成功，已更新personalTemplateAssignments:\", {\n                        targetStudents,\n                        templateData,\n                        personalTemplateAssignmentsBefore: Array.from(personalTemplateAssignments.entries()),\n                        personalTemplateAssignmentsAfter: \"will be updated\"\n                    });\n                    // 延迟打印更新后的状态\n                    setTimeout(()=>{\n                        console.log(\"personalTemplateAssignments更新后:\", Array.from(personalTemplateAssignments.entries()));\n                    }, 100);\n                }\n                // 关闭弹窗并清理状态\n                setIsAssignBlocksModalVisible(false);\n                setSelectedStudentId(null);\n                setSelectedStudentIds([]); // 清空选中的学生\n                setIsSelectAll(false); // 取消全选状态\n                // 刷新相关数据\n                await fetchTemplateUsage(); // 刷新模板使用情况\n            // 当前模板信息由全局状态管理，无需手动刷新\n            } catch (error) {\n                if (hideLoading) {\n                    hideLoading.close();\n                }\n                throw error;\n            }\n        } catch (error) {\n            console.error(\"分配模板失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"分配模板失败，请稍后重试\");\n        }\n    };\n    // 处理模板使用情况点击\n    const handleTemplateUsageClick = (e, template)=>{\n        e.stopPropagation();\n        console.log(\"查看模板使用情况:\", template);\n    };\n    // 处理单个学生分配能量\n    const handleAssignPoints = async (values)=>{\n        var _values_studentExpiries;\n        if (!selectedStudent) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"请先选择学生\");\n            return;\n        }\n        // 从 studentExpiries 中提取单个学生的过期时间\n        const expireTime = (_values_studentExpiries = values.studentExpiries) === null || _values_studentExpiries === void 0 ? void 0 : _values_studentExpiries[selectedStudent.userId];\n        try {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            const hideLoading = notification.loading(\"正在分配能量...\");\n            const { pointsApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\"));\n            await pointsApi.assignPermission({\n                studentUserId: selectedStudent.userId,\n                availablePoints: values.availablePoints,\n                expireTime: expireTime,\n                remark: values.remark\n            });\n            if (hideLoading) {\n                hideLoading.close();\n            }\n            notification.success(\"分配能量成功\");\n            setIsAssignPointsModalVisible(false);\n            // 刷新学生列表\n            await refreshStudentList();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 增加更具体的错误提示\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"分配能量失败\");\n        }\n    };\n    // 刷新学生列表\n    const refreshStudentList = async ()=>{\n        await fetchStudents();\n    };\n    // 更新教师的当前模板（通过UserJoinRole表）\n    const updateClassCurrentTemplate = async (templateId, templateName, isOfficial)=>{\n        try {\n            console.log(\"更新教师当前模板:\", {\n                userId,\n                roleId,\n                templateId,\n                templateName,\n                isOfficial\n            });\n            // 使用addUserJoinRole API来更新教师的模板\n            const { addUserJoinRole } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\"));\n            const response = await addUserJoinRole({\n                userId: userId,\n                roleId: roleId || 2,\n                templateId: templateId\n            });\n            if (response.data.code === 200) {\n                // 更新本地的全局当前模板状态\n                const newTemplate = {\n                    templateId: templateId,\n                    templateName: templateName,\n                    isOfficial: isOfficial\n                };\n                setGlobalCurrentTemplate(newTemplate);\n                console.log(\"教师当前模板更新成功:\", newTemplate);\n            } else {\n                console.error(\"更新教师当前模板失败:\", response.data);\n            }\n        } catch (error) {\n            console.error(\"更新教师当前模板失败:\", error);\n        }\n    };\n    // 更新学生的当前模板信息\n    const handleUpdateStudentTemplate = (studentIds, templateInfo)=>{\n        const templateData = {\n            templateId: templateInfo.templateId,\n            templateName: templateInfo.templateName,\n            isOfficial: templateInfo.isOfficial || false\n        };\n        // 保存个人分配的模板信息\n        setPersonalTemplateAssignments((prev)=>{\n            const newMap = new Map(prev);\n            studentIds.forEach((studentId)=>{\n                newMap.set(studentId, templateData);\n            });\n            return newMap;\n        });\n        // 更新学生状态\n        setStudents((prevStudents)=>prevStudents.map((student)=>{\n                if (studentIds.includes(student.userId)) {\n                    return {\n                        ...student,\n                        currentTemplate: templateData\n                    };\n                }\n                return student;\n            }));\n    // 不再自动更新教师模板，只分配给学生\n    // updateClassCurrentTemplate(templateData.templateId, templateData.templateName, templateData.isOfficial);\n    };\n    // 处理批量/单个兑换密钥\n    const handleBatchUseKeyPackage = (studentId)=>{\n        if (studentId) {\n            // 单个学生触发\n            setSelectedStudentIds([\n                studentId\n            ]);\n            setIsBatchUseKeyPackageModalVisible(true);\n        } else {\n            // 批量操作触发\n            if (selectedStudentIds.length === 0) {\n                Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\")).then((param)=>{\n                    let { GetNotification } = param;\n                    GetNotification().warning(\"请先选择学生\");\n                });\n                return;\n            }\n            setIsBatchUseKeyPackageModalVisible(true);\n        }\n    };\n    // 处理兑换密令成功\n    const handleBatchUseKeyPackageSuccess = ()=>{\n        refreshStudentList();\n        setIsBatchUseKeyPackageModalVisible(false);\n    };\n    // 处理从兑换密令跳转到分配能量\n    const handleGoToAssignPointsFromRedeem = (studentIds)=>{\n        setIsBatchUseKeyPackageModalVisible(false);\n        // 设置选中的学生\n        setSelectedStudentIds(studentIds);\n        // 打开分配能量弹窗\n        setIsAssignPointsModalVisible(true);\n    };\n    // 批量分配能量处理函数\n    const handleBatchAssignPoints = async (values)=>{\n        if (selectedStudentIds.length === 0) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"请先选择学生\");\n            return;\n        }\n        if (!values.studentExpiries) {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            notification.error(\"未能获取学生过期时间信息\");\n            return;\n        }\n        try {\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            const hideLoading = notification.loading(\"正在批量分配能量...\");\n            // 调用新的批量分配 API\n            const { pointsApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../../../lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\"));\n            const response = await pointsApi.batchAssignPermission({\n                availablePoints: values.availablePoints,\n                studentExpiries: values.studentExpiries,\n                remark: values.remark\n            });\n            console.log(\"批量分配积分res\", response);\n            if (hideLoading) {\n                hideLoading.close();\n            }\n            if (response.data.code === 200) {\n                // 后端现在返回处理结果数组，可以根据需要处理\n                const results = response.data.data;\n                // 可以根据 results 中的信息给出更详细的成功/失败提示，\n                // 但为了简单起见，我们仍然使用之前的逻辑\n                notification.success(\"成功为 \".concat(results.success, \" 名学生分配能量\"));\n                setIsAssignPointsModalVisible(false);\n                // 刷新学生列表\n                await refreshStudentList(); // 确保使用 await\n                setSelectedStudentIds([]); // 清空选择\n                setIsSelectAll(false);\n            } else {\n                // 处理 API 返回的错误信息\n                notification.error(response.data.message || \"批量分配能量失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"批量分配能量失败:\", error);\n            const { GetNotification } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\"));\n            const notification = GetNotification();\n            // 处理请求级别的错误\n            notification.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"批量分配能量失败，请检查网络连接或稍后重试\");\n        }\n    };\n    if (!selectedSchool) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"class-detail-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-content\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"error-message\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"学校信息不存在\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1624,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onBack,\n                            className: \"back-button\",\n                            children: \"返回班级管理\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1625,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1623,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1622,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n            lineNumber: 1621,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 生成随机头像颜色\n    const getAvatarColor = (index)=>{\n        const colors = [\n            \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n            \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n            \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n            \"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)\",\n            \"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\",\n            \"linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)\"\n        ];\n        // 确保index是有效的正数\n        const safeIndex = Math.max(0, index);\n        return colors[safeIndex % colors.length];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"class-detail-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"back-button\",\n                    onClick: onBack,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 1657,\n                            columnNumber: 11\n                        }, undefined),\n                        \"返回班级管理\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 1653,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1652,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"class-detail-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"left-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"left-section-header\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"settings-container\",\n                                    ref: settingsDropdownRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"settings-btn \".concat(isSettingsDropdownOpen ? \"active\" : \"\"),\n                                            onClick: ()=>setIsSettingsDropdownOpen(!isSettingsDropdownOpen),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1672,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1668,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isSettingsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"dropdown-menu\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-menu-items\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"edit\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#8b5cf6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1683,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1682,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"编辑班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1685,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1678,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"addStudent\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#10b981\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1693,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1692,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"添加学生\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1695,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1688,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"importStudent\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#3b82f6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1703,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1702,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"导入学生\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1705,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1698,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"exportStudent\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#10b981\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1713,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1712,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"导出学生\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1715,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1708,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"transfer\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#f59e0b\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1723,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1722,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"转让管理\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1725,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1718,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"invite\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#3b82f6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1733,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1732,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"生成邀请码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1735,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1728,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"batchRedeem\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#f59e0b\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1743,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1742,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"批量兑换密令\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1745,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1738,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"assignBlocks\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    size: 16,\n                                                                    style: {\n                                                                        color: \"#3b82f6\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1753,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1752,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"分配积木\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1755,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1748,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-divider\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1758,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"dropdown-menu-item danger\",\n                                                        onClick: ()=>handleSettingsMenuItemClick(\"deleteClass\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"dropdown-menu-item-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 1765,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1764,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"删除班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1767,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1760,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1677,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1676,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1667,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1666,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"class-info-card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"class-card-header\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"class-card-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"class-card-icon\",\n                                                    children: \"\\uD83D\\uDCCB\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1779,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"class-card-info\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"class-card-title\",\n                                                            children: classInfo.className\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1783,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"class-card-subtitle\",\n                                                            children: selectedSchool.schoolName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1784,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 1782,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1778,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"class-card-badge\",\n                                            children: \"当前模板\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1787,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1777,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1776,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"search-section\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"search-box\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        size: 16,\n                                        className: \"search-icon\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1796,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 1795,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1794,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"students-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"students-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"学生\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1803,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"students-actions\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"add-student-btn\",\n                                                        onClick: ()=>setIsAddStudentModalVisible(true),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1809,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1805,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"select-all-btn \".concat(isSelectAll ? \"active\" : \"\"),\n                                                        onClick: ()=>{\n                                                            console.log(\"点击全选按钮，当前状态:\", {\n                                                                isSelectAll,\n                                                                selectedStudentIds: selectedStudentIds.length,\n                                                                totalStudents: students.length\n                                                            });\n                                                            handleSelectAll();\n                                                        },\n                                                        title: isSelectAll ? \"取消全选\" : \"全选\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: isSelectAll ? \"取消全选\" : \"全选\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 1823,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1811,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    selectedStudentIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"batch-actions-container\",\n                                                        ref: batchActionsDropdownRef,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"batch-actions-btn\",\n                                                                onClick: ()=>setIsBatchActionsDropdownOpen(!isBatchActionsDropdownOpen),\n                                                                title: \"批量操作\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"批量操作(\",\n                                                                            selectedStudentIds.length,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1834,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1835,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1829,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            isBatchActionsDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"batch-actions-dropdown\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchAssignBlocks\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1845,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量分配积木\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1846,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1841,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchAssignPoints\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1852,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量分配能量\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1853,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1848,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchUseKeyPackage\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1859,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量兑换密令\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1860,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1855,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchDelete\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1866,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量移出班级\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1867,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1862,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"batch-action-item\",\n                                                                        onClick: ()=>handleBatchAction(\"batchExport\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1873,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"批量导出\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                                lineNumber: 1874,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                        lineNumber: 1869,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1840,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1828,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1804,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1802,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"students-list\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            students: students,\n                                            loading: loading,\n                                            error: error,\n                                            selectedStudent: selectedStudent,\n                                            selectedStudentIds: selectedStudentIds,\n                                            currentTemplate: globalCurrentTemplate || currentTemplate,\n                                            renderVersion: renderVersion,\n                                            onStudentClick: handleStudentClick,\n                                            onStudentSelect: handleStudentSelect,\n                                            onRetry: fetchStudents,\n                                            onIndividualAssignBlocks: handleIndividualAssignBlocks,\n                                            onAssignPoints: (studentId)=>{\n                                                const student = students.find((s)=>s.userId === studentId);\n                                                if (student) {\n                                                    setSelectedStudent(student);\n                                                    setIsAssignPointsModalVisible(true);\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1884,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1883,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1801,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1664,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"right-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"student-info-header\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"student-avatar-large\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"student-avatar-circle\",\n                                            style: {\n                                                background: selectedStudent ? getAvatarColor(Math.max(0, students.findIndex((s)=>s.userId === selectedStudent.userId))) : getAvatarColor(0)\n                                            },\n                                            children: selectedStudent ? ((_selectedStudent_nickName = selectedStudent.nickName) === null || _selectedStudent_nickName === void 0 ? void 0 : _selectedStudent_nickName.charAt(0)) || \"S\" : \"S\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1913,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1912,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"student-details\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"student-name-large\",\n                                                children: selectedStudent ? selectedStudent.nickName || \"学生\".concat(selectedStudent.studentNumber || selectedStudent.userId) : \"请选择学生\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1925,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"student-id-large\",\n                                                children: selectedStudent ? selectedStudent.studentNumber || \"无学号\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1928,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1924,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"delete-student-btn\",\n                                        onClick: handleDeleteStudent,\n                                        disabled: !selectedStudent,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Blocks_ChevronDown_Download_Edit_Gift_Link_Plus_Search_Settings_Trash2_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 1937,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1932,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1911,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"functions-container\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"functions-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"更多功能\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1945,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"function-buttons\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn publish-task\",\n                                                        onClick: ()=>setIsPublishTaskModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83D\\uDCDD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1951,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"发布任务\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1952,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1947,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn distribute-blocks\",\n                                                        onClick: handleAssignBlocks,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83E\\uDDE9\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1958,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"分配积木\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1959,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1954,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn distribute-energy\",\n                                                        onClick: ()=>setIsAssignPointsModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"⚡\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1965,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"分配能量\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1966,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1961,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn exchange-tokens\",\n                                                        onClick: ()=>setIsBatchUseKeyPackageModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83C\\uDF81\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1972,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"兑换密令\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1973,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1968,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"function-btn reset-password\",\n                                                        onClick: ()=>setIsResetPasswordModalVisible(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"function-icon\",\n                                                                children: \"\\uD83D\\uDD11\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1979,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"重置密码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                lineNumber: 1980,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 1975,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1946,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1944,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"learning-status\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"课程学习情况\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1987,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"status-placeholder\",\n                                                children: \"该区域功能正在等待开放\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1988,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1986,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1942,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bottom-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"energy-progress-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"energy-progress-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"可用能量/总能量\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2000,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.availablePoints) || 0,\n                                                            \"/\",\n                                                            (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.totalPoints) || 0\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                        lineNumber: 2001,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 1999,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"energy-progress-bar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"energy-progress-fill\",\n                                                    style: {\n                                                        width: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.totalPoints) ? \"\".concat(Number(selectedStudent.availablePoints || 0) / Number(selectedStudent.totalPoints || 0) * 100, \"%\") : \"0%\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2006,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2005,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 1998,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"template-cards-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"template-card current-template\",\n                                                onClick: ()=>handleTemplateAssign(\"current\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"template-card-header\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"template-card-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-card-icon\",\n                                                                    children: \"\\uD83D\\uDCCB\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 2022,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-card-info\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"template-card-title\",\n                                                                            children: \"当前模板\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                            lineNumber: 2024,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"template-card-subtitle\",\n                                                                            children: (()=>{\n                                                                                if (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.currentTemplate) {\n                                                                                    return selectedStudent.currentTemplate.templateName;\n                                                                                } else if (globalCurrentTemplate) {\n                                                                                    return globalCurrentTemplate.templateName;\n                                                                                } else if (currentTemplate) {\n                                                                                    return currentTemplate.templateName;\n                                                                                } else if (selectedStudent) {\n                                                                                    return \"加载中...\";\n                                                                                } else {\n                                                                                    return \"请选择学生\";\n                                                                                }\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                            lineNumber: 2025,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 2023,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 2021,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"template-card-badge official\",\n                                                            children: \"官方\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 2042,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2020,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2019,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"template-card custom-template\",\n                                                onClick: ()=>handleTemplateAssign(\"custom\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"template-card-header\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"template-card-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-card-icon\",\n                                                                    children: \"\\uD83D\\uDD27\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 2050,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-card-info\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"template-card-title\",\n                                                                            children: \"当前模板\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                            lineNumber: 2052,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"template-card-subtitle\",\n                                                                            children: \"4444\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                            lineNumber: 2053,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                                    lineNumber: 2051,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 2049,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"template-card-badge custom\",\n                                                            children: \"自定义\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                            lineNumber: 2056,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2048,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                lineNumber: 2047,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2017,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 1997,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                        lineNumber: 1909,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 1662,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddStudentModal__WEBPACK_IMPORTED_MODULE_5__.AddStudentModal, {\n                visible: isAddStudentModalVisible,\n                onCancel: ()=>setIsAddStudentModalVisible(false),\n                onOk: handleAddStudent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2065,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.EditClassModal, {\n                visible: isEditClassModalVisible,\n                onCancel: ()=>setIsEditClassModalVisible(false),\n                onOk: handleEditClass,\n                initialValues: {\n                    className: classInfo.className\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2072,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.ImportStudentModal, {\n                visible: isImportStudentModalVisible,\n                onCancel: ()=>setIsImportStudentModalVisible(false),\n                onImport: handleImportStudents,\n                classId: classInfo.id\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2082,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransferClassModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                visible: isTransferClassModalVisible,\n                onCancel: ()=>{\n                    setIsTransferClassModalVisible(false);\n                    setSearchedTeacher(null);\n                },\n                onOk: handleTransferClass,\n                onSearchTeacher: handleSearchTeacher,\n                searchedTeacher: searchedTeacher,\n                hasAssistantTeacher: !!classInfo.assistantTeacherId,\n                onRemoveAssistant: handleRemoveAssistant,\n                loading: transferLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2090,\n                columnNumber: 7\n            }, undefined),\n            isInviteCodeModalVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold\",\n                                    children: \"邀请码生成成功\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2109,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-gray-500 hover:text-gray-700\",\n                                    onClick: ()=>setIsInviteCodeModalVisible(false),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-6 w-6\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2115,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                        lineNumber: 2114,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2110,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2108,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between bg-gray-50 p-3 rounded-lg mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-mono text-lg text-blue-600 mr-4\",\n                                            children: inviteCode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2121,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                            onClick: ()=>{\n                                                navigator.clipboard.writeText(inviteCode).then(()=>alert(\"邀请码已复制\")).catch(()=>alert(\"复制失败，请手动复制\"));\n                                            },\n                                            children: \"复制\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2122,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2120,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-sm space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"您可以:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2134,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-disc list-inside space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"将邀请码分享给学生，让他们加入班级\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2136,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"邀请其他老师作为协助教师加入班级\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                                    lineNumber: 2137,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2135,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-500\",\n                                            children: \"⏰ 邀请码有效期为24小时\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                            lineNumber: 2139,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                    lineNumber: 2133,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2119,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                onClick: ()=>setIsInviteCodeModalVisible(false),\n                                children: \"关闭\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                                lineNumber: 2145,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                            lineNumber: 2144,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                    lineNumber: 2107,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2106,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.PublishTaskModal, {\n                visible: isPublishTaskModalVisible,\n                onCancel: ()=>setIsPublishTaskModalVisible(false),\n                onOk: handlePublishTask,\n                students: students,\n                selectedStudents: selectedStudentIds,\n                setSelectedStudents: setSelectedStudentIds,\n                fileList: fileList,\n                uploading: uploading,\n                handleUpload: handleUpload,\n                handleRemoveFile: handleRemoveFile,\n                displayTemplates: templates,\n                officialTemplates: officialTemplates,\n                selectedTemplateId: selectedTemplateId,\n                setSelectedTemplateId: setSelectedTemplateId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2159,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals__WEBPACK_IMPORTED_MODULE_6__.ResetPasswordModal, {\n                visible: isResetPasswordModalVisible,\n                onCancel: ()=>setIsResetPasswordModalVisible(false),\n                onOk: handleResetPassword,\n                isBatch: false,\n                count: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2177,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AssignBlocksModal__WEBPACK_IMPORTED_MODULE_7__.AssignBlocksModal, {\n                visible: isAssignBlocksModalVisible,\n                onCancel: ()=>{\n                    setIsAssignBlocksModalVisible(false);\n                    setSelectedStudentId(null);\n                    setSelectedStudentIds([]);\n                },\n                isClassCardAssign: selectedStudentId !== null,\n                loadingTemplates: loadingTemplates,\n                templates: templates,\n                studentTemplateUsage: studentTemplateUsage,\n                teacherTemplate: teacherTemplate,\n                onSelectTemplate: handleSelectTemplate,\n                onTemplateUsageClick: handleTemplateUsageClick,\n                userId: userId,\n                onRefreshTemplates: fetchTemplates,\n                students: students,\n                selectedStudentIds: selectedStudentIds,\n                userRoles: userRoles,\n                onSuccess: ()=>{\n                    refreshStudentList();\n                    fetchTemplateUsage();\n                },\n                onUpdateStudentTemplate: handleUpdateStudentTemplate\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2186,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AssignPointsModal__WEBPACK_IMPORTED_MODULE_9__.AssignPointsModal, {\n                visible: isAssignPointsModalVisible,\n                onCancel: ()=>{\n                    setIsAssignPointsModalVisible(false);\n                    setSelectedStudent(null);\n                },\n                // 根据 selectedStudent 是否存在来决定调用哪个处理函数\n                onOk: selectedStudent ? handleAssignPoints : handleBatchAssignPoints,\n                studentName: selectedStudent ? \"\".concat(selectedStudent.nickName) : \"已选择 \".concat(selectedStudentIds.length, \" 名学生\"),\n                studentId: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.id) || 0,\n                userId: (selectedStudent === null || selectedStudent === void 0 ? void 0 : selectedStudent.userId) || 0,\n                student: selectedStudent,\n                isBatch: !selectedStudent,\n                selectedStudents: selectedStudentIds,\n                students: students,\n                onSuccess: ()=>{\n                // 移除这里的刷新，因为 onOk 内部已经处理了\n                },\n                refreshStudentList: refreshStudentList,\n                onGoToRedeemKey: (studentIds)=>{\n                    console.log(\"前往兑换密钥:\", studentIds);\n                    setIsBatchUseKeyPackageModalVisible(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2213,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_10__.BatchUseKeyPackageModal, {\n                open: isBatchUseKeyPackageModalVisible,\n                selectedStudentIds: selectedStudentIds,\n                students: students,\n                onClose: ()=>setIsBatchUseKeyPackageModalVisible(false),\n                onSuccess: handleBatchUseKeyPackageSuccess,\n                onGoToAssignPoints: handleGoToAssignPointsFromRedeem\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n                lineNumber: 2239,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassDetail.tsx\",\n        lineNumber: 1650,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClassDetail, \"rOoOqxdnOob2DP168DTJvyWgCZI=\", false, function() {\n    return [\n        _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_4__.useTemplate,\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector\n    ];\n});\n_c = ClassDetail;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClassDetail);\nvar _c;\n$RefreshReg$(_c, \"ClassDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/ClassDetail.tsx\n"));

/***/ })

});