'use client'

import { useState, useEffect } from 'react';
import { Card, Button, Modal, Form, Input, Select, Upload, Tag, Space } from 'antd';
import { PictureOutlined, UploadOutlined } from '@ant-design/icons';
import { carouselApi } from '@/lib/api/carousel';
import { uploadApi } from '@/lib/api/upload';
import { GetNotification } from 'logic-common/dist/components/Notification';

const { Option } = Select;

interface CarouselManagementProps {
  onCarouselCountChange?: (count: number) => void;
}

interface CarouselItem {
  id: number;
  type: number;
  displayType: number;
  largeTitle: string;
  mediumTitle: string;
  smallTitle: string;
  imageUrl: string;
  videoUrl: string;
  redirectUrl: string;
  sort: number;
  buttonStatus: number;
  buttonText: string;
  status: number;
}

export default function CarouselManagement({ onCarouselCountChange }: CarouselManagementProps) {
  const notification = GetNotification();
  const [isCarouselModalVisible, setIsCarouselModalVisible] = useState(false);
  const [isAddCarouselModalVisible, setIsAddCarouselModalVisible] = useState(false);
  const [carouselList, setCarouselList] = useState<CarouselItem[]>([]);
  const [carouselTotal, setCarouselTotal] = useState(0);
  const [addCarouselForm] = Form.useForm();
  const [editingCarousel, setEditingCarousel] = useState<CarouselItem | null>(null);
  const [uploadedImageUrl, setUploadedImageUrl] = useState('');

  // 获取轮播图列表
  const fetchCarouselList = async () => {
    try {
      const { data: res } = await carouselApi.getAllList();
      if (res.code === 200) {
        setCarouselList(res.data);
        setCarouselTotal(res.data.length);
        onCarouselCountChange?.(res.data.length);
      }
    } catch (error) {
      console.error('获取轮播图列表失败:', error);
      notification.error('获取轮播图列表失败');
    }
  };

  // 组件加载时获取轮播图列表
  useEffect(() => {
    fetchCarouselList();
  }, []);

  return (
    <div className="space-y-4">
      {/* 顶部统计和操作区 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-gray-500 mb-1">轮播图总数</div>
            <div className="text-2xl font-semibold text-blue-600">{carouselTotal}</div>
          </div>
          <PictureOutlined className="text-4xl text-blue-500" />
        </div>
        <Button
          type="primary"
          onClick={() => {
            setEditingCarousel(null);
            addCarouselForm.resetFields();
            setIsAddCarouselModalVisible(true);
          }}
        >
          添加轮播图
        </Button>
      </div>

      {/* 轮播图列表 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {carouselList.map((item) => (
          <div key={item.id} className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
            <div className="aspect-w-16 aspect-h-9 rounded-t-lg overflow-hidden">
              <img
                src={item.imageUrl}
                alt={item.largeTitle}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = 'https://logicleap.oss-cn-guangzhou.aliyuncs.com/base/c2b0f283138abaad873795e1dd8e18b_resized.png';
                }}
              />
            </div>
            <div className="p-4">
              <div className="flex items-center justify-between mb-2">
                <Tag color={item.type === 0 ? 'blue' : 'green'}>
                  {item.type === 0 ? '图片' : '视频'}
                </Tag>
                <Tag color={item.status === 1 ? 'success' : 'default'}>
                  {item.status === 1 ? '启用' : '禁用'}
                </Tag>
              </div>
              <h3 className="text-lg font-medium mb-2 truncate" title={item.largeTitle}>
                {item.largeTitle || '无标题'}
              </h3>
              {item.mediumTitle && (
                <p className="text-gray-500 text-sm mb-1 truncate" title={item.mediumTitle}>
                  {item.mediumTitle}
                </p>
              )}
              {item.smallTitle && (
                <p className="text-gray-400 text-sm mb-2 truncate" title={item.smallTitle}>
                  {item.smallTitle}
                </p>
              )}
              <div className="flex items-center justify-between mt-4">
                <span className="text-gray-500 text-sm">排序: {item.sort}</span>
                <Space>
                  <Button
                    type="link"
                    size="small"
                    onClick={() => {
                      setEditingCarousel(item);
                      addCarouselForm.setFieldsValue(item);
                      setUploadedImageUrl(item.imageUrl);
                      setIsAddCarouselModalVisible(true);
                    }}
                  >
                    编辑
                  </Button>
                  <Button
                    type="link"
                    size="small"
                    onClick={async () => {
                      try {
                        const { data: res } = await carouselApi.updateStatus(
                          item.id,
                          item.status === 1 ? 0 : 1
                        );
                        if (res.code === 200) {
                          notification.success(item.status === 1 ? '已禁用' : '已启用');
                          fetchCarouselList();
                        }
                      } catch (error) {
                        notification.error('操作失败');
                      }
                    }}
                  >
                    {item.status === 1 ? '禁用' : '启用'}
                  </Button>
                  <Button
                    type="link"
                    danger
                    size="small"
                    onClick={() => {
                      Modal.confirm({
                        title: '确认删除',
                        content: '确定要删除这个轮播图吗？',
                        onOk: async () => {
                          try {
                            const { data: res } = await carouselApi.delete(item.id);
                            if (res.code === 200) {
                              notification.success('删除成功');
                              fetchCarouselList();
                            }
                          } catch (error) {
                            notification.error('删除失败');
                          }
                        },
                      });
                    }}
                  >
                    删除
                  </Button>
                </Space>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 添加/编辑轮播图模态框 */}
      <Modal
        title={editingCarousel ? '编辑轮播图' : '添加轮播图'}
        open={isAddCarouselModalVisible}
        onCancel={() => {
          setIsAddCarouselModalVisible(false);
          setEditingCarousel(null);
          addCarouselForm.resetFields();
          setUploadedImageUrl('');
        }}
        footer={null}
        width={800}
      >
        <Form
          form={addCarouselForm}
          layout="vertical"
          onFinish={async (values) => {
            try {
              // 确保数字字段为正确的数字类型
              const formattedValues = {
                ...values,
                sort: parseInt(values.sort),
                type: parseInt(values.type || 0),
                displayType: parseInt(values.displayType || 2),
                status: parseInt(values.status || 1),
                buttonStatus: parseInt(values.buttonStatus || 1)
              };

              const api = editingCarousel ? carouselApi.update : carouselApi.add;
              const data = {
                ...formattedValues,
                imageUrl: uploadedImageUrl,
                ...(editingCarousel ? { id: editingCarousel.id } : {})
              };

              const { data: res } = await api(data);
              if (res.code === 200) {
                notification.success(editingCarousel ? '更新成功' : '添加成功');
                setIsAddCarouselModalVisible(false);
                setEditingCarousel(null);
                addCarouselForm.resetFields();
                setUploadedImageUrl('');
                fetchCarouselList();
              }
            } catch (error) {
              notification.error(editingCarousel ? '更新失败' : '添加失败');
            }
          }}
        >
          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="type"
              label="类型"
              initialValue={0}
              hidden
            >
              <Select>
                <Option value={0}>图片</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="displayType"
              label="显示方式"
              rules={[{ required: true, message: '请选择显示方式' }]}
            >
              <Select>
                <Option value={1}>全显示</Option>
                <Option value={2}>半显示</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="sort"
              label="排序"
              rules={[{ required: true, message: '请输入排序' }]}
            >
              <Input type="number" placeholder="数字越小越靠前" />
            </Form.Item>

            <Form.Item
              name="status"
              label="状态"
              initialValue={1}
            >
              <Select>
                <Option value={1}>启用</Option>
                <Option value={0}>禁用</Option>
              </Select>
            </Form.Item>
          </div>

          <Form.Item
            name="largeTitle"
            label="大标题"
            rules={[{ required: true, message: '请输入大标题' }]}
          >
            <Input placeholder="请输入大标题" />
          </Form.Item>

          <Form.Item name="mediumTitle" label="中标题">
            <Input placeholder="请输入中标题" />
          </Form.Item>

          <Form.Item name="smallTitle" label="小标题">
            <Input placeholder="请输入小标题" />
          </Form.Item>

          <Form.Item name="redirectUrl" label="跳转链接">
            <Input placeholder="请输入跳转链接" />
          </Form.Item>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item name="buttonStatus" label="按钮状态" initialValue={0}>
              <Select>
                <Option value={1}>显示</Option>
                <Option value={0}>隐藏</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="buttonText"
              label="按钮文字"
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (getFieldValue('buttonStatus') === 0) {
                      return Promise.resolve();
                    }
                    if (!value) {
                      return Promise.reject(new Error('请输入按钮文字'));
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <Input placeholder="请输入按钮文字" />
            </Form.Item>
          </div>

          <Form.Item label="轮播图图片" required>
            <div className="flex flex-col space-y-2">
              <Upload
                name="file"
                showUploadList={false}
                beforeUpload={async (file) => {
                  const isImage = file.type.startsWith('image/');
                  if (!isImage) {
                    notification.error('只能上传图片文件!');
                    return Upload.LIST_IGNORE;
                  }

                  try {
                    const formData = new FormData();
                    formData.append('file', file);

                    const res = await uploadApi.uploadToOss(file);
                    console.log('res', res);
                    // 如果res直接就是URL字符串
                    if (res && typeof res === 'string') {
                      setUploadedImageUrl(res);
                      notification.success('上传成功');
                    } else if (res && res.code === 200) {
                      // 兼容返回对象的情况
                      setUploadedImageUrl(res.data.url);
                      notification.success('上传成功');
                    } else {
                      notification.error((res && res.message) || '上传失败');
                    }
                  } catch (error) {
                    notification.error('上传失败');
                  }

                  return false;
                }}
              >
                <Button icon={<UploadOutlined />}>上传图片</Button>
              </Upload>

              {uploadedImageUrl && (
                <div className="mt-2">
                  <img
                    src={uploadedImageUrl}
                    alt="预览图"
                    className="max-w-full h-auto max-h-40 object-contain border rounded-md p-1"
                  />
                </div>
              )}
            </div>
          </Form.Item>

          <Form.Item className="mt-4">
            <div className="flex justify-end gap-2">
              <Button
                onClick={() => {
                  setIsAddCarouselModalVisible(false);
                  setEditingCarousel(null);
                  addCarouselForm.resetFields();
                  setUploadedImageUrl('');
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingCarousel ? '更新' : '添加'}
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
} 