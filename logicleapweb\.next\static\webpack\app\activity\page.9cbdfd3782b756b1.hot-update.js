"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/activity/page",{

/***/ "(app-pages-browser)/./app/activity/components/AddActivityModal.tsx":
/*!******************************************************!*\
  !*** ./app/activity/components/AddActivityModal.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,DatePicker,Form,Input,Modal,Tag,Upload!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,DatePicker,Form,Input,Modal,Tag,Upload!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,DatePicker,Form,Input,Modal,Tag,Upload!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,DatePicker,Form,Input,Modal,Tag,Upload!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,DatePicker,Form,Input,Modal,Tag,Upload!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,DatePicker,Form,Input,Modal,Tag,Upload!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/date-picker/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,DatePicker,Form,Input,Modal,Tag,Upload!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UploadOutlined.js\");\n/* harmony import */ var _lib_api_activity__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/activity */ \"(app-pages-browser)/./lib/api/activity/index.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst AddActivityModal = (param)=>{\n    let { visible, onCancel, onSuccess } = param;\n    _s();\n    const [form] = _barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fileList, setFileList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [promotionImage, setPromotionImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [attachmentFiles, setAttachmentFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [competitionGroups, setCompetitionGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newGroupInput, setNewGroupInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [dynamicConfigs, setDynamicConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newConfigName, setNewConfigName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 添加参赛组别\n    const addCompetitionGroup = ()=>{\n        const trimmedInput = newGroupInput.trim();\n        if (trimmedInput && !competitionGroups.includes(trimmedInput)) {\n            setCompetitionGroups([\n                ...competitionGroups,\n                trimmedInput\n            ]);\n            setNewGroupInput(\"\");\n        }\n    };\n    // 删除参赛组别\n    const removeCompetitionGroup = (groupToRemove)=>{\n        setCompetitionGroups(competitionGroups.filter((group)=>group !== groupToRemove));\n    };\n    // 处理回车键添加组别\n    const handleGroupInputKeyPress = (e)=>{\n        if (e.key === \"Enter\") {\n            e.preventDefault();\n            addCompetitionGroup();\n        }\n    };\n    // 动态配置项管理函数\n    const addDynamicConfig = ()=>{\n        if (!newConfigName.trim()) return;\n        const newConfig = {\n            id: Date.now().toString(),\n            name: newConfigName.trim(),\n            fileList: [],\n            exampleList: []\n        };\n        setDynamicConfigs((prev)=>[\n                ...prev,\n                newConfig\n            ]);\n        setNewConfigName(\"\");\n    };\n    const removeDynamicConfig = (configId)=>{\n        setDynamicConfigs((prev)=>prev.filter((config)=>config.id !== configId));\n    };\n    const updateConfigFileList = (configId, fileList)=>{\n        setDynamicConfigs((prev)=>prev.map((config)=>config.id === configId ? {\n                    ...config,\n                    fileList\n                } : config));\n    };\n    const updateConfigExampleList = (configId, exampleList)=>{\n        setDynamicConfigs((prev)=>prev.map((config)=>config.id === configId ? {\n                    ...config,\n                    exampleList\n                } : config));\n    };\n    const handleConfigInputKeyPress = (e)=>{\n        if (e.key === \"Enter\") {\n            e.preventDefault();\n            addDynamicConfig();\n        }\n    };\n    // 通用上传处理函数\n    const createUploadHandler = function(setFileListFunc) {\n        let maxCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        return async (options)=>{\n            const { file, onSuccess, onError } = options;\n            const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_4__.GetNotification)();\n            try {\n                const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.uploadToOss(file);\n                const newFile = {\n                    ...file,\n                    uid: file.uid,\n                    name: file.name,\n                    status: \"done\",\n                    url: url\n                };\n                if (maxCount === 1) {\n                    setFileListFunc([\n                        newFile\n                    ]);\n                } else {\n                    setFileListFunc((prev)=>[\n                            ...prev,\n                            newFile\n                        ]);\n                }\n                if (onSuccess) {\n                    onSuccess(null, file);\n                }\n                notification.success(\"\".concat(file.name, \" 上传成功\"));\n            } catch (err) {\n                console.error(\"上传失败:\", err);\n                notification.error(\"\".concat(file.name, \" 上传失败: \").concat(err.message || \"请稍后重试\"));\n                const errorFile = {\n                    ...file,\n                    uid: file.uid,\n                    name: file.name,\n                    status: \"error\",\n                    error: err\n                };\n                if (maxCount === 1) {\n                    setFileListFunc([\n                        errorFile\n                    ]);\n                } else {\n                    setFileListFunc((prev)=>[\n                            ...prev,\n                            errorFile\n                        ]);\n                }\n                if (onError) {\n                    onError(err);\n                }\n            }\n        };\n    };\n    // 封面图片上传处理\n    const handleCustomRequest = createUploadHandler(setFileList, 1);\n    // 通用删除处理函数\n    const createRemoveHandler = (setFileListFunc)=>{\n        return async (file)=>{\n            if (!file) {\n                console.warn(\"文件对象为空，跳过删除操作\");\n                return true;\n            }\n            const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_4__.GetNotification)();\n            if (file.url) {\n                try {\n                    await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.deleteFromOss(file.url);\n                    notification.success(\"\".concat(file.name || \"文件\", \" 已从服务器删除\"));\n                } catch (error) {\n                    console.error(\"从 OSS 删除文件失败:\", error);\n                    notification.error(\"\".concat(file.name || \"文件\", \" 服务器删除失败\"));\n                }\n            }\n            try {\n                setFileListFunc((prev)=>prev.filter((f)=>f.uid !== file.uid));\n            } catch (error) {\n                console.error(\"更新文件列表失败:\", error);\n            }\n            return true;\n        };\n    };\n    // 封面图片删除处理\n    const handleRemove = createRemoveHandler(setFileList);\n    const handleSubmit = async ()=>{\n        const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_4__.GetNotification)();\n        try {\n            var _promotionImage_find;\n            // 检查用户登录状态\n            const token = localStorage.getItem(\"token\");\n            const userInfo = localStorage.getItem(\"user\");\n            console.log(\"当前登录状态:\", {\n                token: !!token,\n                userInfo: !!userInfo\n            });\n            if (!token) {\n                notification.error(\"请先登录后再创建活动\");\n                return;\n            }\n            console.log(\"开始表单验证...\");\n            const values = await form.validateFields();\n            console.log(\"表单验证成功，获取到的值:\", values);\n            if (!fileList.length || fileList[0].status !== \"done\" || !fileList[0].url) {\n                var _fileList_, _fileList_1;\n                console.log(\"文件上传检查失败:\", {\n                    fileList,\n                    status: (_fileList_ = fileList[0]) === null || _fileList_ === void 0 ? void 0 : _fileList_.status,\n                    url: (_fileList_1 = fileList[0]) === null || _fileList_1 === void 0 ? void 0 : _fileList_1.url\n                });\n                notification.error(\"请上传封面图片\");\n                return;\n            }\n            console.log(\"文件上传检查通过\");\n            setLoading(true);\n            // 处理附件文件：格式为 \"文件名1,URL1,文件名2,URL2\"\n            const attachmentData = attachmentFiles.filter((f)=>f.status === \"done\" && f.url).map((f)=>\"\".concat(f.name, \",\").concat(f.url)).join(\",\");\n            // 处理日期时间：开始日期设为00:00:00，结束日期设为23:59:59\n            const startDate = dayjs__WEBPACK_IMPORTED_MODULE_5___default()(values.startTime).startOf(\"day\").toDate();\n            const endDate = dayjs__WEBPACK_IMPORTED_MODULE_5___default()(values.endTime).endOf(\"day\").toDate();\n            // 处理动态配置数据\n            let registrationFormData = \"\";\n            if (dynamicConfigs.length > 0) {\n                const configDataArray = dynamicConfigs.map((config)=>{\n                    var _config_fileList_find, _config_exampleList_find;\n                    const fileUrl = ((_config_fileList_find = config.fileList.find((f)=>f.status === \"done\" && f.url)) === null || _config_fileList_find === void 0 ? void 0 : _config_fileList_find.url) || \"\";\n                    const exampleUrl = ((_config_exampleList_find = config.exampleList.find((f)=>f.status === \"done\" && f.url)) === null || _config_exampleList_find === void 0 ? void 0 : _config_exampleList_find.url) || \"\";\n                    return \"\".concat(config.name, \",\").concat(fileUrl, \",\").concat(exampleUrl);\n                }).filter((data)=>{\n                    const parts = data.split(\",\");\n                    return parts.length === 3 && parts[0] && parts[1] && parts[2]; // 确保有完整的数据\n                });\n                if (configDataArray.length > 0) {\n                    registrationFormData = configDataArray.join(\"|\"); // 多个配置项用 | 分隔\n                }\n            }\n            const params = {\n                ...values,\n                startTime: startDate,\n                endTime: endDate,\n                coverImage: fileList[0].url,\n                promotionImage: ((_promotionImage_find = promotionImage.find((f)=>f.status === \"done\" && f.url)) === null || _promotionImage_find === void 0 ? void 0 : _promotionImage_find.url) || \"\",\n                attachmentFiles: attachmentData,\n                competitionGroups: competitionGroups.length > 0 ? competitionGroups.join(\",\") : undefined,\n                registrationForm: registrationFormData || undefined,\n                activityType: _lib_api_activity__WEBPACK_IMPORTED_MODULE_2__.ActivityType.WORK\n            };\n            console.log(\"创建活动的参数：\", params);\n            await _lib_api_activity__WEBPACK_IMPORTED_MODULE_2__.activityApi.create(params);\n            notification.success(\"活动创建成功\");\n            form.resetFields();\n            setFileList([]);\n            setPromotionImage([]);\n            setAttachmentFiles([]);\n            setCompetitionGroups([]);\n            setNewGroupInput(\"\");\n            setDynamicConfigs([]);\n            setNewConfigName(\"\");\n            onSuccess();\n            onCancel();\n        } catch (error) {\n            console.error(\"创建活动失败:\", error);\n            // 检查是否是表单验证错误\n            if (error.errorFields && Array.isArray(error.errorFields)) {\n                console.log(\"表单验证错误详情:\", error.errorFields);\n                const errorMessages = error.errorFields.map((field)=>\"\".concat(field.name.join(\".\"), \": \").concat(field.errors.join(\", \"))).join(\"; \");\n                notification.error(\"表单验证失败: \".concat(errorMessages));\n            } else {\n                notification.error(\"创建活动失败: \".concat(error.message || \"未知错误\"));\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCancel = async ()=>{\n        try {\n            var _fileList_, _promotionImage_;\n            // 清除封面图片\n            if (fileList.length && ((_fileList_ = fileList[0]) === null || _fileList_ === void 0 ? void 0 : _fileList_.url)) {\n                try {\n                    await handleRemove(fileList[0]);\n                } catch (error) {\n                    console.warn(\"清除封面图片失败:\", error);\n                }\n            }\n            // 清除宣传图片\n            if (promotionImage.length && ((_promotionImage_ = promotionImage[0]) === null || _promotionImage_ === void 0 ? void 0 : _promotionImage_.url)) {\n                try {\n                    await createRemoveHandler(setPromotionImage)(promotionImage[0]);\n                } catch (error) {\n                    console.warn(\"清除宣传图片失败:\", error);\n                }\n            }\n            // 清除附件文件\n            for (const file of attachmentFiles){\n                if (file === null || file === void 0 ? void 0 : file.url) {\n                    try {\n                        await createRemoveHandler(setAttachmentFiles)(file);\n                    } catch (error) {\n                        console.warn(\"清除附件文件失败:\", error);\n                    }\n                }\n            }\n            // 清除动态配置文件\n            for (const config of dynamicConfigs){\n                var _config_fileList, _config_fileList_, _config_exampleList, _config_exampleList_;\n                if (((_config_fileList = config.fileList) === null || _config_fileList === void 0 ? void 0 : _config_fileList.length) && ((_config_fileList_ = config.fileList[0]) === null || _config_fileList_ === void 0 ? void 0 : _config_fileList_.url)) {\n                    try {\n                        await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.deleteFromOss(config.fileList[0].url);\n                    } catch (error) {\n                        console.warn(\"清除报名表文件失败:\", error);\n                    }\n                }\n                if (((_config_exampleList = config.exampleList) === null || _config_exampleList === void 0 ? void 0 : _config_exampleList.length) && ((_config_exampleList_ = config.exampleList[0]) === null || _config_exampleList_ === void 0 ? void 0 : _config_exampleList_.url)) {\n                    try {\n                        await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.deleteFromOss(config.exampleList[0].url);\n                    } catch (error) {\n                        console.warn(\"清除示例图片失败:\", error);\n                    }\n                }\n            }\n        } catch (error) {\n            console.warn(\"清理文件时出现错误:\", error);\n        }\n        // 重置表单和状态\n        form.resetFields();\n        setFileList([]);\n        setPromotionImage([]);\n        setAttachmentFiles([]);\n        setCompetitionGroups([]);\n        setNewGroupInput(\"\");\n        setDynamicConfigs([]);\n        setNewConfigName(\"\");\n        onCancel();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        title: \"创建活动\",\n        open: visible,\n        onOk: handleSubmit,\n        onCancel: handleCancel,\n        confirmLoading: loading,\n        width: 800,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            form: form,\n            layout: \"vertical\",\n            initialValues: {\n                status: 0\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                    name: \"name\",\n                    label: \"活动名称\",\n                    rules: [\n                        {\n                            required: true,\n                            message: \"请输入活动名称\"\n                        }\n                    ],\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        placeholder: \"请输入活动名称\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                    name: \"organizer\",\n                    label: \"主办方\",\n                    rules: [\n                        {\n                            required: true,\n                            message: \"请输入主办方\"\n                        }\n                    ],\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        placeholder: \"请输入主办方\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                    lineNumber: 360,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                    label: \"参赛组别配置\",\n                    help: \"为活动配置可选的参赛组别，学生提交时可从中选择\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        placeholder: \"输入组别名称，如：小学组、初中组等\",\n                                        value: newGroupInput,\n                                        onChange: (e)=>setNewGroupInput(e.target.value),\n                                        onKeyDown: handleGroupInputKeyPress,\n                                        style: {\n                                            flex: 1\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        type: \"primary\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        onClick: addCompetitionGroup,\n                                        disabled: !newGroupInput.trim(),\n                                        children: \"添加\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, undefined),\n                            competitionGroups.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2 mt-2\",\n                                children: competitionGroups.map((group, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        closable: true,\n                                        onClose: ()=>removeCompetitionGroup(group),\n                                        color: \"blue\",\n                                        children: group\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 15\n                            }, undefined),\n                            competitionGroups.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"暂未配置参赛组别，学生提交时将无法选择组别\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                    name: \"startTime\",\n                    label: \"开始日期\",\n                    rules: [\n                        {\n                            required: true,\n                            message: \"请选择开始日期\"\n                        }\n                    ],\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        format: \"YYYY-MM-DD\",\n                        placeholder: \"请选择开始日期\",\n                        style: {\n                            width: \"100%\"\n                        },\n                        onChange: ()=>{\n                            // 当开始日期改变时，清除结束日期的验证错误\n                            form.validateFields([\n                                \"endTime\"\n                            ]);\n                        },\n                        disabledDate: (current)=>{\n                            // 禁用今天之前的日期\n                            return current && current < dayjs__WEBPACK_IMPORTED_MODULE_5___default()().startOf(\"day\");\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                    name: \"endTime\",\n                    label: \"结束日期\",\n                    rules: [\n                        {\n                            required: true,\n                            message: \"请选择结束日期\"\n                        },\n                        (param)=>{\n                            let { getFieldValue } = param;\n                            return {\n                                validator (_, value) {\n                                    const startTime = getFieldValue(\"startTime\");\n                                    if (!value || !startTime) {\n                                        return Promise.resolve();\n                                    }\n                                    if (dayjs__WEBPACK_IMPORTED_MODULE_5___default()(value).isBefore(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(startTime), \"day\")) {\n                                        return Promise.reject(new Error(\"结束日期不能早于开始日期\"));\n                                    }\n                                    return Promise.resolve();\n                                }\n                            };\n                        }\n                    ],\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        format: \"YYYY-MM-DD\",\n                        placeholder: \"请选择结束日期\",\n                        style: {\n                            width: \"100%\"\n                        },\n                        disabledDate: (current)=>{\n                            const startTime = form.getFieldValue(\"startTime\");\n                            if (!startTime) {\n                                // 如果没有选择开始日期，禁用今天之前的日期\n                                return current && current < dayjs__WEBPACK_IMPORTED_MODULE_5___default()().startOf(\"day\");\n                            }\n                            // 禁用开始日期之前的日期\n                            return current && current < dayjs__WEBPACK_IMPORTED_MODULE_5___default()(startTime).startOf(\"day\");\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                    lineNumber: 432,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                    label: \"封面图片\",\n                    required: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        listType: \"picture-card\",\n                        maxCount: 1,\n                        fileList: fileList,\n                        customRequest: handleCustomRequest,\n                        onRemove: handleRemove,\n                        children: fileList.length >= 1 ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: 8\n                                    },\n                                    children: \"上传\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                    label: \"宣传长图\",\n                    help: \"用于活动推广展示的长图\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        listType: \"picture-card\",\n                        maxCount: 1,\n                        fileList: promotionImage,\n                        customRequest: createUploadHandler(setPromotionImage, 1),\n                        onRemove: createRemoveHandler(setPromotionImage),\n                        accept: \"image/*\",\n                        children: promotionImage.length >= 1 ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: 8\n                                    },\n                                    children: \"上传宣传长图\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                            lineNumber: 500,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                    label: \"上传附件\",\n                    help: \"可上传活动相关的附件文件，如报名表、活动说明等\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        fileList: attachmentFiles,\n                        customRequest: createUploadHandler(setAttachmentFiles, 10),\n                        onRemove: createRemoveHandler(setAttachmentFiles),\n                        accept: \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar\",\n                        maxCount: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            style: {\n                                border: \"none\",\n                                background: \"none\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 15\n                                }, undefined),\n                                \" 选择附件\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                    lineNumber: 508,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                    label: \"报名表配置\",\n                    help: \"为活动配置需要的报名表文件，如参赛协议、家长同意书等\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        placeholder: \"输入配置项名称，如：参赛协议、家长同意书\",\n                                        value: newConfigName,\n                                        onChange: (e)=>setNewConfigName(e.target.value),\n                                        onKeyDown: handleConfigInputKeyPress,\n                                        style: {\n                                            flex: 1\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        type: \"primary\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        onClick: addDynamicConfig,\n                                        disabled: !newConfigName.trim(),\n                                        children: \"添加\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 13\n                            }, undefined),\n                            dynamicConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-gray-200 rounded-lg p-4 space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: config.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    type: \"text\",\n                                                    danger: true,\n                                                    size: \"small\",\n                                                    onClick: ()=>removeDynamicConfig(config.id),\n                                                    children: \"删除\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: [\n                                                                config.name,\n                                                                \"文件\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            fileList: config.fileList,\n                                                            customRequest: async (options)=>{\n                                                                const { file, onSuccess, onError } = options;\n                                                                const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_4__.GetNotification)();\n                                                                try {\n                                                                    const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.uploadToOss(file);\n                                                                    const newFile = {\n                                                                        ...file,\n                                                                        uid: file.uid,\n                                                                        name: file.name,\n                                                                        status: \"done\",\n                                                                        url: url\n                                                                    };\n                                                                    updateConfigFileList(config.id, [\n                                                                        newFile\n                                                                    ]);\n                                                                    if (onSuccess) {\n                                                                        onSuccess(null, file);\n                                                                    }\n                                                                    notification.success(\"\".concat(file.name, \" 上传成功\"));\n                                                                } catch (err) {\n                                                                    console.error(\"上传失败:\", err);\n                                                                    notification.error(\"\".concat(file.name, \" 上传失败: \").concat(err.message || \"请稍后重试\"));\n                                                                    const errorFile = {\n                                                                        ...file,\n                                                                        uid: file.uid,\n                                                                        name: file.name,\n                                                                        status: \"error\",\n                                                                        error: err\n                                                                    };\n                                                                    updateConfigFileList(config.id, [\n                                                                        errorFile\n                                                                    ]);\n                                                                    if (onError) {\n                                                                        onError(err);\n                                                                    }\n                                                                }\n                                                            },\n                                                            onRemove: async (file)=>{\n                                                                if (file === null || file === void 0 ? void 0 : file.url) {\n                                                                    try {\n                                                                        await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.deleteFromOss(file.url);\n                                                                    } catch (error) {\n                                                                        console.error(\"从 OSS 删除文件失败:\", error);\n                                                                    }\n                                                                }\n                                                                updateConfigFileList(config.id, []);\n                                                                return true;\n                                                            },\n                                                            accept: \".pdf,.doc,.docx,.xls,.xlsx\",\n                                                            maxCount: 1,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                style: {\n                                                                    border: \"none\",\n                                                                    background: \"none\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    \" 选择\",\n                                                                    config.name,\n                                                                    \"文件\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"示例图片\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            listType: \"picture-card\",\n                                                            maxCount: 1,\n                                                            fileList: config.exampleList,\n                                                            customRequest: async (options)=>{\n                                                                const { file, onSuccess, onError } = options;\n                                                                const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_4__.GetNotification)();\n                                                                try {\n                                                                    const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.uploadToOss(file);\n                                                                    const newFile = {\n                                                                        ...file,\n                                                                        uid: file.uid,\n                                                                        name: file.name,\n                                                                        status: \"done\",\n                                                                        url: url\n                                                                    };\n                                                                    updateConfigExampleList(config.id, [\n                                                                        newFile\n                                                                    ]);\n                                                                    if (onSuccess) {\n                                                                        onSuccess(null, file);\n                                                                    }\n                                                                    notification.success(\"\".concat(file.name, \" 上传成功\"));\n                                                                } catch (err) {\n                                                                    console.error(\"上传失败:\", err);\n                                                                    notification.error(\"\".concat(file.name, \" 上传失败: \").concat(err.message || \"请稍后重试\"));\n                                                                    const errorFile = {\n                                                                        ...file,\n                                                                        uid: file.uid,\n                                                                        name: file.name,\n                                                                        status: \"error\",\n                                                                        error: err\n                                                                    };\n                                                                    updateConfigExampleList(config.id, [\n                                                                        errorFile\n                                                                    ]);\n                                                                    if (onError) {\n                                                                        onError(err);\n                                                                    }\n                                                                }\n                                                            },\n                                                            onRemove: async (file)=>{\n                                                                if (file === null || file === void 0 ? void 0 : file.url) {\n                                                                    try {\n                                                                        await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.deleteFromOss(file.url);\n                                                                    } catch (error) {\n                                                                        console.error(\"从 OSS 删除文件失败:\", error);\n                                                                    }\n                                                                }\n                                                                updateConfigExampleList(config.id, []);\n                                                                return true;\n                                                            },\n                                                            accept: \"image/*\",\n                                                            children: config.exampleList.length >= 1 ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                                                        lineNumber: 654,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            marginTop: 8\n                                                                        },\n                                                                        children: \"上传示例图\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                                                        lineNumber: 655,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, config.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 15\n                                }, undefined)),\n                            dynamicConfigs.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-sm text-center py-4 border border-dashed border-gray-200 rounded-lg\",\n                                children: \"暂未配置报名表文件，学生提交时将无需上传相关文件\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                        lineNumber: 529,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                    lineNumber: 525,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                    name: \"status\",\n                    label: \"状态\",\n                    hidden: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        type: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                        lineNumber: 673,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n                    lineNumber: 672,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n            lineNumber: 345,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\components\\\\AddActivityModal.tsx\",\n        lineNumber: 337,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AddActivityModal, \"b2mExsPYPzJtv8NHRb8W5IlidMw=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_DatePicker_Form_Input_Modal_Tag_Upload_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm\n    ];\n});\n_c = AddActivityModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddActivityModal);\nvar _c;\n$RefreshReg$(_c, \"AddActivityModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/activity/components/AddActivityModal.tsx\n"));

/***/ })

});