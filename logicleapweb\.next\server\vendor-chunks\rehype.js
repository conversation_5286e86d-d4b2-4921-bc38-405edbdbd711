"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype";
exports.ids = ["vendor-chunks/rehype"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype/index.js":
/*!**************************************!*\
  !*** ./node_modules/rehype/index.js ***!
  \**************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rehype: () => (/* binding */ rehype)\n/* harmony export */ });\n/* harmony import */ var rehype_parse__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rehype-parse */ \"(ssr)/./node_modules/rehype-parse/lib/index.js\");\n/* harmony import */ var rehype_stringify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rehype-stringify */ \"(ssr)/./node_modules/rehype-stringify/lib/index.js\");\n/* harmony import */ var unified__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unified */ \"(ssr)/./node_modules/unified/lib/index.js\");\n// Note: types exposed from `index.d.ts`\n\n\n\n\n/**\n * Create a new unified processor that already uses `rehype-parse` and\n * `rehype-stringify`.\n */\nconst rehype = (0,unified__WEBPACK_IMPORTED_MODULE_0__.unified)().use(rehype_parse__WEBPACK_IMPORTED_MODULE_1__[\"default\"]).use(rehype_stringify__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).freeze()\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVoeXBlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNzQztBQUNRO0FBQ2Y7O0FBRS9CO0FBQ0E7QUFDQTtBQUNBO0FBQ08sZUFBZSxnREFBTyxPQUFPLG9EQUFXLE1BQU0sd0RBQWUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvcmVoeXBlL2luZGV4LmpzPzkxZDkiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gTm90ZTogdHlwZXMgZXhwb3NlZCBmcm9tIGBpbmRleC5kLnRzYFxuaW1wb3J0IHJlaHlwZVBhcnNlIGZyb20gJ3JlaHlwZS1wYXJzZSdcbmltcG9ydCByZWh5cGVTdHJpbmdpZnkgZnJvbSAncmVoeXBlLXN0cmluZ2lmeSdcbmltcG9ydCB7dW5pZmllZH0gZnJvbSAndW5pZmllZCdcblxuLyoqXG4gKiBDcmVhdGUgYSBuZXcgdW5pZmllZCBwcm9jZXNzb3IgdGhhdCBhbHJlYWR5IHVzZXMgYHJlaHlwZS1wYXJzZWAgYW5kXG4gKiBgcmVoeXBlLXN0cmluZ2lmeWAuXG4gKi9cbmV4cG9ydCBjb25zdCByZWh5cGUgPSB1bmlmaWVkKCkudXNlKHJlaHlwZVBhcnNlKS51c2UocmVoeXBlU3RyaW5naWZ5KS5mcmVlemUoKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype/index.js\n");

/***/ })

};
;