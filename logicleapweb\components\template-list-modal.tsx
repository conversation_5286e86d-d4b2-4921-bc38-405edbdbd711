/** @jsxImportSource react */
'use client'

import { Mo<PERSON>, Button, Tabs, Spin, Empty, Tag, Divider } from 'antd';
import { PlusOutlined, BlockOutlined, FolderOutlined, StarOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import PermissionTemplateModal from './permission-template-modal';
import PermissionModal from './permission-modal';
import { roleTemplateFolderApi } from '../lib/api/role-template-folder';
import { getOfficialTemplates, getTemplateInfo } from '../lib/api/role';

interface TemplateListModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectTemplate?: (templateId: number) => void;
  userId: number;
  roleId: number;
}

interface TemplateInfo {
  id: number;
  templateName: string;
  templateDescription: string;
  createTime: string;
  isDefault: boolean;
  lastModified?: string;
  isOfficial?: boolean;
}

const TemplateListModal: React.FC<TemplateListModalProps> = ({
  visible,
  onClose,
  onSelectTemplate,
  userId,
  roleId
}) => {
  const [activeTab, setActiveTab] = useState('my');
  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);
  const [isCreateTemplateModalVisible, setIsCreateTemplateModalVisible] = useState(false);
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(null);
  const [currentView, setCurrentView] = useState<'folders' | 'templates'>('folders');
  const [currentFolder, setCurrentFolder] = useState<any>(null);
  const [folders, setFolders] = useState<any[]>([]);
  const [myTemplates, setMyTemplates] = useState<TemplateInfo[]>([]);
  const [loadingFolders, setLoadingFolders] = useState(false);
  const [loadingMyTemplates, setLoadingMyTemplates] = useState(false);
  const [folderTemplates, setFolderTemplates] = useState<TemplateInfo[]>([]);
  const [loadingFolderTemplates, setLoadingFolderTemplates] = useState(false);
  const [specialTemplate, setSpecialTemplate] = useState<TemplateInfo | null>(null);
  const [loadingSpecialTemplate, setLoadingSpecialTemplate] = useState(false);

  // 获取特殊模板（系统默认推荐的模板）
  const fetchSpecialTemplate = async () => {
    if (activeTab !== 'official') return;

    setLoadingSpecialTemplate(true);
    try {
      const response = await getOfficialTemplates();
      if (response.data.code === 200) {
        // 查找系统推荐的模板
        const specialTemplateData = response.data.data.find((template: any) => template.userId === -1);

        if (specialTemplateData) {
          // 获取模板的详细信息
          const templateInfoResponse = await getTemplateInfo(specialTemplateData.id);
          if (templateInfoResponse.data.code === 200) {
            const templateData = templateInfoResponse.data.data;
            setSpecialTemplate({
              id: templateData.id,
              templateName: templateData.templateName,
              templateDescription: templateData.templateDescription || '',
              createTime: templateData.createTime,
              isDefault: false,
              isOfficial: true,
              lastModified: templateData.updateTime || templateData.createTime
            });
          }
        }
      }
    } catch (error) {
      console.error('获取特殊模板失败:', error);
    } finally {
      setLoadingSpecialTemplate(false);
    }
  };

  // 获取文件夹列表
  const fetchFolders = async () => {
    if (activeTab !== 'official') return;

    setLoadingFolders(true);
    try {
      const res = await roleTemplateFolderApi.getFolderList();
      if (res.data.code === 200) {
        // 获取每个文件夹的模板数量
        const foldersWithCount = await Promise.all(
          res.data.data.data.map(async (folder: any) => {
            const templatesRes = await roleTemplateFolderApi.getFolderTemplates(folder.id);
            return {
              ...folder,
              templateCount: templatesRes.data.code === 200 ? templatesRes.data.data.length : 0
            };
          })
        );
        setFolders(foldersWithCount);
      }
    } catch (error) {
      console.error('获取文件夹列表失败:', error);
    } finally {
      setLoadingFolders(false);
    }
  };

  // 获取个人模板列表
  const fetchMyTemplates = async () => {
    if (activeTab !== 'my') return;

    setLoadingMyTemplates(true);
    try {
      // 这里替换为实际的API调用
      // const response = await getUserTemplates(userId, roleId);
      // if (response.data.code === 200) {
      //   setMyTemplates(response.data.data);
      // }

      // 模拟数据
      setMyTemplates([
        {
          id: 1001,
          templateName: '我的自定义模板',
          templateDescription: '这是我创建的自定义模板',
          createTime: new Date().toISOString(),
          isDefault: false,
          lastModified: new Date().toISOString()
        }
      ]);
    } catch (error) {
      console.error('获取个人模板失败:', error);
    } finally {
      setLoadingMyTemplates(false);
    }
  };

  // 处理文件夹点击
  const handleFolderClick = async (folder: any) => {
    setCurrentFolder(folder);
    setCurrentView('templates');
    setFolderTemplates([]); // 先清空之前的数据
    setLoadingFolderTemplates(true); // 开始加载时显示加载动画

    try {
      const folderRes = await roleTemplateFolderApi.getFolderTemplates(folder.id);
      if (folderRes.data.code === 200) {
        const templatePromises = folderRes.data.data.data.map((item: { id: number }) =>
          roleTemplateFolderApi.getTemplateInfo(item.id)
        );
        const templateResults = await Promise.all(templatePromises);
        const templates = templateResults
          .filter(res => res.data.code === 200)
          .map(res => {
            const templateData = res.data.data;
            return {
              id: templateData.id,
              templateName: templateData.templateName,
              templateDescription: templateData.templateDescription || '',
              createTime: templateData.createTime,
              isDefault: false,
              isOfficial: true,
              lastModified: templateData.updateTime || templateData.createTime
            };
          });

        setFolderTemplates(templates);
      }
    } catch (error) {
      console.error('获取文件夹模板失败:', error);
    } finally {
      setLoadingFolderTemplates(false); // 无论成功失败都关闭加载动画
    }
  };

  // 返回文件夹列表
  const handleBackToFolders = () => {
    setCurrentView('folders');
    setCurrentFolder(null);
    setFolderTemplates([]); // 清空模板列表数据
  };

  // 处理模板点击，显示模板详情
  const handleTemplateClick = (templateId: number) => {
    setSelectedTemplateId(templateId);
    setIsPermissionModalVisible(true);
  };

  // 处理使用模板
  const handleUseTemplate = (templateId: number) => {
    if (onSelectTemplate) {
      onSelectTemplate(templateId);
    }
    onClose();
  };

  // 处理创建模板
  const handleCreateTemplate = () => {
    setIsCreateTemplateModalVisible(true);
  };

  // 处理创建模板成功
  const handleCreateTemplateSuccess = () => {
    if (activeTab === 'my') {
      fetchMyTemplates();
    } else if (activeTab === 'official' && currentView === 'templates' && currentFolder) {
      handleFolderClick(currentFolder);
    } else if (activeTab === 'official' && currentView === 'folders') {
      fetchFolders();
      fetchSpecialTemplate();
    }
    setIsCreateTemplateModalVisible(false);
  };

  // 当切换到官方模板标签时加载文件夹和特殊模板
  useEffect(() => {
    if (activeTab === 'official' && currentView === 'folders') {
      fetchFolders();
      fetchSpecialTemplate();
    } else if (activeTab === 'my') {
      fetchMyTemplates();
    }
  }, [activeTab, currentView]);

  // 监听模态框显示状态
  useEffect(() => {
    if (visible) {
      if (activeTab === 'official' && currentView === 'folders') {
        fetchFolders();
        fetchSpecialTemplate();
      } else if (activeTab === 'my') {
        fetchMyTemplates();
      }
    }
  }, [visible]);

  // 模板卡片组件
  const TemplateCard = ({ template, isOfficial = false }: { template: TemplateInfo, isOfficial?: boolean }) => (
    <div className="bg-white border border-gray-100 p-4 rounded-xl hover:shadow-md transition-all group relative mb-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <div className="w-10 h-10 rounded-lg bg-blue-50 group-hover:bg-blue-100 flex-shrink-0 flex items-center justify-center transition-colors">
            <BlockOutlined className="text-blue-500 text-lg" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors truncate max-w-[calc(100%-70px)]">
                {template.templateName}
              </span>
              {isOfficial && (
                <Tag color="gold" className="flex-shrink-0 rounded-full text-xs">
                  官方
                </Tag>
              )}
              {template.isDefault && (
                <Tag color="blue" className="flex-shrink-0 rounded-full text-xs">
                  默认
                </Tag>
              )}
            </div>
            <p className="text-xs text-gray-500 mt-1 truncate">
              {template.templateDescription || '暂无描述'}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2 flex-shrink-0">
          <Button
            type="default"
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              handleTemplateClick(template.id);
            }}
          >
            查看详情
          </Button>
          <Button
            type="primary"
            size="small"
            className="bg-blue-500"
            onClick={(e) => {
              e.stopPropagation();
              handleUseTemplate(template.id);
            }}
          >
            使用此模板
          </Button>
        </div>
      </div>
    </div>
  );

  // 文件夹列表组件
  const FolderList = () => (
    <div className="space-y-3">
      {/* 特殊模板（推荐模板）置顶显示 */}
      {specialTemplate && (
        <div className="bg-white border border-gray-100 p-4 rounded-xl hover:shadow-md transition-all group relative mb-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-3 flex-1 min-w-0">
              <div className="w-10 h-10 rounded-lg bg-amber-50 group-hover:bg-amber-100 flex-shrink-0 flex items-center justify-center transition-colors">
                <StarOutlined className="text-amber-500 text-lg" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors truncate max-w-[calc(100%-70px)]">
                    {specialTemplate.templateName}
                  </span>
                  <Tag color="gold" className="flex-shrink-0 rounded-full text-xs">
                    推荐
                  </Tag>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {specialTemplate.templateDescription || '推荐使用的模板'}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2 flex-shrink-0">
              <Button
                type="default"
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleTemplateClick(specialTemplate.id);
                }}
              >
                查看详情
              </Button>
              <Button
                type="primary"
                size="small"
                className="bg-blue-500"
                onClick={(e) => {
                  e.stopPropagation();
                  handleUseTemplate(specialTemplate.id);
                }}
              >
                使用此模板
              </Button>
            </div>
          </div>
        </div>
      )}

      <Divider orientation="left">模板分类</Divider>

      {/* 文件夹列表 */}
      {folders.map(folder => (
        <div
          key={folder.id}
          className="bg-white border border-gray-100 p-4 rounded-xl cursor-pointer hover:shadow-md transition-all group relative"
          onClick={() => handleFolderClick(folder)}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-blue-50 flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                <FolderOutlined className="text-blue-500 text-lg" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors">
                  {folder.folderName}
                  <Tag color="gold" className="ml-2 rounded-full text-xs">官方</Tag>
                </h3>
                <p className="text-xs text-gray-400 mt-0.5">
                  {folder.templateCount || 0} 个模板
                </p>
              </div>
            </div>
          </div>
        </div>
      ))}

      {/* 空状态 */}
      {folders.length === 0 && !specialTemplate && !loadingFolders && !loadingSpecialTemplate && (
        <Empty description="暂无模板分类" />
      )}
    </div>
  );

  // 模板列表组件
  const TemplatesList = ({ templates, isOfficial = false }: { templates: TemplateInfo[], isOfficial?: boolean }) => (
    <div className="space-y-3">
      {templates.map(template => (
        <TemplateCard key={template.id} template={template} isOfficial={isOfficial} />
      ))}

      {/* 空状态 */}
      {templates.length === 0 && (
        <Empty description="暂无模板" />
      )}
    </div>
  );

  return (
    <Modal
      title={
        <div className="flex items-center justify-between pr-8">
          <div className="flex items-center gap-4">
            {activeTab === 'official' && currentView === 'templates' && (
              <Button
                type="link"
                icon={<ArrowLeftOutlined />}
                onClick={handleBackToFolders}
                style={{ marginRight: '8px', padding: 0 }}
              />
            )}
            <span>
              {activeTab === 'official' && currentView === 'templates'
                ? `${currentFolder?.folderName || '模板分类'}`
                : '分配模板'}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Button
              type={activeTab === 'my' ? 'primary' : 'default'}
              onClick={() => setActiveTab('my')}
              className="rounded-full"
              size="small"
            >
              我的模板
            </Button>
            <Button
              type={activeTab === 'official' ? 'primary' : 'default'}
              onClick={() => {
                setActiveTab('official');
                setCurrentView('folders');
              }}
              className="rounded-full"
              size="small"
            >
              官方模板
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateTemplate}
              className="rounded-full ml-4"
              size="small"
            >
              创建模板
            </Button>
          </div>
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      centered
      style={{
        maxWidth: '90vw',
        top: 0
      }}
      bodyStyle={{
        padding: '24px',
        maxHeight: 'calc(90vh - 110px)',
        overflow: 'auto'
      }}
      className="custom-scrollbar-modal"
    >
      <style jsx global>{`
        .custom-scrollbar-modal .ant-modal-body {
          scrollbar-width: thin;
          scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
        }
        .custom-scrollbar-modal .ant-modal-body::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar-modal .ant-modal-body::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 0.2);
          border-radius: 3px;
          transition: background-color 0.3s;
        }
        .custom-scrollbar-modal .ant-modal-body::-webkit-scrollbar-thumb:hover {
          background-color: rgba(0, 0, 0, 0.3);
        }
        .custom-scrollbar-modal .ant-modal-body::-webkit-scrollbar-track {
          background-color: transparent;
        }
      `}</style>

      <div className="space-y-4">
        {/* 官方模板 - 文件夹视图 */}
        {activeTab === 'official' && currentView === 'folders' && (
          loadingFolders || loadingSpecialTemplate ? (
            <div className="flex justify-center items-center h-64">
              <Spin size="large" />
            </div>
          ) : (
            <FolderList />
          )
        )}

        {/* 官方模板 - 文件夹内模板列表 */}
        {activeTab === 'official' && currentView === 'templates' && (
          loadingFolderTemplates ? (
            <div className="flex justify-center items-center h-64">
              <Spin size="large" />
            </div>
          ) : (
            <TemplatesList templates={folderTemplates} isOfficial={true} />
          )
        )}

        {/* 我的模板 */}
        {activeTab === 'my' && (
          loadingMyTemplates ? (
            <div className="flex justify-center items-center h-64">
              <Spin size="large" />
            </div>
          ) : (
            <TemplatesList templates={myTemplates} />
          )
        )}
      </div>

      {/* 模板详情弹窗 */}
      {isPermissionModalVisible && selectedTemplateId && (
        <PermissionModal
          userId={0}
          visible={isPermissionModalVisible}
          onClose={() => {
            setIsPermissionModalVisible(false);
            setSelectedTemplateId(null);
          }}
          templateId={selectedTemplateId}
          readOnly={true}
          onUseTemplate={(templateId) => {
            handleUseTemplate(templateId);
            setIsPermissionModalVisible(false);
            setSelectedTemplateId(null);
          }}
        />
      )}

      {/* 创建模板弹窗 */}
      {isCreateTemplateModalVisible && (
        <PermissionTemplateModal
          visible={isCreateTemplateModalVisible}
          onClose={() => {
            setIsCreateTemplateModalVisible(false);
          }}
          onSuccess={handleCreateTemplateSuccess}
          roleId={roleId}
          userId={userId}
        />
      )}
    </Modal>
  );
};

export default TemplateListModal; 