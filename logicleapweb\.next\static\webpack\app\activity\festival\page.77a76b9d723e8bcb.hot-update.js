"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/activity/festival/page",{

/***/ "(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx":
/*!*************************************************************!*\
  !*** ./app/activity/festival/components/ActivityHeader.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Share2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Share2,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./lib/store.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ActivityMediaGallery */ \"(app-pages-browser)/./app/activity/festival/components/ActivityMediaGallery.tsx\");\n/* harmony import */ var _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Modal!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _components_login_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/login-dialog */ \"(app-pages-browser)/./components/login-dialog.tsx\");\n/* harmony import */ var _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/activity */ \"(app-pages-browser)/./lib/api/activity/index.ts\");\n/* harmony import */ var _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/activity/events-task */ \"(app-pages-browser)/./lib/api/activity/events-task.ts\");\n/* harmony import */ var _lib_api_user__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/user */ \"(app-pages-browser)/./lib/api/user.ts\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 临时实现formatDate函数，以解决模块导入问题\nconst formatDate = (dateString)=>{\n    // 在服务端渲染时，直接返回原始字符串，避免水合错误\n    if (false) {}\n    const date = new Date(dateString);\n    // 检查日期是否有效\n    if (isNaN(date.getTime())) {\n        return dateString; // 如果无效，返回原始字符串\n    }\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, \"0\");\n    const day = String(date.getDate()).padStart(2, \"0\");\n    return \"\".concat(year, \"-\").concat(month, \"-\").concat(day);\n};\nconst ActivityHeader = (param)=>{\n    let { title: initialTitle, startTime: initialStartTime, endTime: initialEndTime, bannerImage: initialBannerImage, activityId, organizer: initialOrganizer = \"\", activityType = _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.ActivityType.WORK, onRefreshWorks, // 媒体字段\n    promotionImage, backgroundImage, galleryImages, attachmentFiles } = param;\n    _s();\n    console.log(\"\\uD83D\\uDD0D [DEBUG] ActivityHeader 组件渲染\", {\n        activityId,\n        initialTitle\n    });\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_9__.GetNotification)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.usePathname)();\n    // 显示用的标题和时间\n    const title = initialTitle;\n    const startTime = initialStartTime;\n    const endTime = initialEndTime;\n    const bannerImage = initialBannerImage;\n    const organizer = initialOrganizer;\n    // 获取当前用户信息和dispatch\n    const userState = (0,react_redux__WEBPACK_IMPORTED_MODULE_11__.useSelector)(_lib_store__WEBPACK_IMPORTED_MODULE_3__.selectUserState);\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_11__.useDispatch)();\n    // 同步用户状态的函数 - 增强版，从API获取最新用户信息\n    const syncUserState = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 开始同步用户状态\");\n            const localUser = localStorage.getItem(\"user\");\n            if (localUser) {\n                const userData = JSON.parse(localUser);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中的用户数据\", userData);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] Redux 中的用户数据\", userState);\n                // 如果用户已登录但phone为空，尝试从API获取最新用户信息\n                if (userData.isLoggedIn && userData.userId && (!userData.phone || userData.phone.trim() === \"\")) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户phone为空，从API获取最新用户信息\");\n                    try {\n                        const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_8__.userApi.getUserInfo(userData.userId);\n                        if (response.code === 200 && response.data) {\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取到最新用户信息\", response.data);\n                            // 构建更新后的用户数据\n                            const updatedUserData = {\n                                ...userData,\n                                phone: response.data.phone || \"\",\n                                nickName: response.data.nickName || userData.nickName,\n                                avatarUrl: response.data.avatarUrl || userData.avatarUrl,\n                                gender: response.data.gender || userData.gender,\n                                roleId: response.data.roleId || userData.roleId\n                            };\n                            console.log(\"\\uD83D\\uDD0D [DEBUG] 更新用户数据\", {\n                                oldPhone: userData.phone,\n                                newPhone: updatedUserData.phone,\n                                updated: updatedUserData\n                            });\n                            // 更新localStorage和Redux\n                            localStorage.setItem(\"user\", JSON.stringify(updatedUserData));\n                            dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(updatedUserData));\n                            return;\n                        }\n                    } catch (error) {\n                        console.error(\"\\uD83D\\uDD0D [DEBUG] 从API获取用户信息失败:\", error);\n                    }\n                }\n                // 检查关键字段是否有变化\n                const hasPhoneChanged = userData.phone !== (userState === null || userState === void 0 ? void 0 : userState.phone);\n                const hasRoleChanged = userData.roleId !== (userState === null || userState === void 0 ? void 0 : userState.roleId);\n                const hasLoginStatusChanged = userData.isLoggedIn !== (userState === null || userState === void 0 ? void 0 : userState.isLoggedIn);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态变化检查\", {\n                    phoneChanged: hasPhoneChanged,\n                    roleChanged: hasRoleChanged,\n                    loginStatusChanged: hasLoginStatusChanged,\n                    oldPhone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                    newPhone: userData.phone,\n                    oldRole: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                    newRole: userData.roleId\n                });\n                // 如果localStorage中的用户状态与Redux中的不一致，说明需要更新\n                if (hasPhoneChanged || hasRoleChanged || hasLoginStatusChanged) {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 检测到用户状态变化，同步Redux状态\");\n                    dispatch((0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.setUser)(userData));\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 用户状态无变化，无需同步\");\n                }\n            } else {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] localStorage 中无用户数据\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDD0D [DEBUG] 检查用户状态失败:\", error);\n        }\n    };\n    // 监听页面焦点变化和路由变化，重新检查用户状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 页面加载时立即检查一次\n        syncUserState().catch(console.error);\n        const handleVisibilityChange = ()=>{\n            if (document.visibilityState === \"visible\") {\n                syncUserState().catch(console.error);\n            }\n        };\n        const handleFocus = ()=>{\n            syncUserState().catch(console.error);\n        };\n        // 添加多种事件监听确保状态同步\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n        window.addEventListener(\"focus\", handleFocus);\n        return ()=>{\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n            window.removeEventListener(\"focus\", handleFocus);\n        };\n    }, [\n        userState === null || userState === void 0 ? void 0 : userState.phone,\n        dispatch\n    ]);\n    // 工具函数：检查用户是否为学生身份\n    const checkIsStudent = ()=>{\n        try {\n            const userStr = localStorage.getItem(\"user\");\n            const localUser = userStr ? JSON.parse(userStr) : null;\n            const userRoleId = (localUser === null || localUser === void 0 ? void 0 : localUser.roleId) || (userState === null || userState === void 0 ? void 0 : userState.roleId);\n            return userRoleId === 1; // 学生身份的roleId为1\n        } catch (error) {\n            console.error(\"解析localStorage中的用户信息失败:\", error);\n            return false;\n        }\n    };\n    // 工具函数：显示身份验证失败提示\n    const showStudentOnlyModal = ()=>{\n        _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].confirm({\n            title: \"身份验证\",\n            content: \"抱歉，只有学生身份的用户才能参加此活动报名。如果您是学生，请联系管理员更新您的身份信息。\",\n            okText: \"我知道了\",\n            cancelText: \"联系管理员\",\n            onCancel: ()=>{\n                notification.info(\"如需帮助，请联系客服：400-123-4567\");\n            }\n        });\n    };\n    // 登录和报名相关状态\n    const [showLoginDialog, setShowLoginDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSubmitted, setHasSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [registrationStatus, setRegistrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 报名状态：0-已取消 1-已报名 2-已审核通过 3-已拒绝 4-评审中 5-已获奖 6-审核中\n    // 添加客户端检测来解决水合错误\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    // 页面加载时检查报名状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkRegistrationStatus = async ()=>{\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态 - useEffect 触发\", {\n                isLoggedIn: userState.isLoggedIn,\n                roleId: userState.roleId,\n                phone: userState.phone,\n                isClient,\n                activityId\n            });\n            // 只有在用户已登录且是学生身份时才检查报名状态\n            const isStudent = checkIsStudent();\n            if (!userState.isLoggedIn || !isStudent) {\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录或不是学生身份，重置报名状态\", {\n                    isLoggedIn: userState.isLoggedIn,\n                    isStudent\n                });\n                // 如果用户未登录或不是学生，重置状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n                return;\n            }\n            try {\n                var _response_data, _response_data1;\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 开始检查报名状态 API 调用\");\n                setIsChecking(true);\n                const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.checkRegistration(Number(activityId));\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 页面加载检查报名状态响应:\", response);\n                if ((response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200 && ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data)) {\n                    const { submitted, submit } = response.data.data;\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态检查结果\", {\n                        submitted,\n                        submit\n                    });\n                    setHasSubmitted(submitted);\n                    if (submit && submit.status !== undefined) {\n                        setRegistrationStatus(submit.status);\n                    } else {\n                        setRegistrationStatus(null);\n                    }\n                } else {\n                    console.log(\"\\uD83D\\uDD0D [DEBUG] 报名状态响应不符合预期，设置为未报名状态\");\n                    // 如果响应不符合预期，设置为未报名状态\n                    setHasSubmitted(false);\n                    setRegistrationStatus(null);\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 检查报名状态失败:\", error);\n                // 出错时设置为未报名状态\n                setHasSubmitted(false);\n                setRegistrationStatus(null);\n            } finally{\n                setIsChecking(false);\n            }\n        };\n        // 只在客户端执行，避免服务端渲染问题\n        if (isClient) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 客户端环境，执行报名状态检查\");\n            checkRegistrationStatus();\n        } else {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 非客户端环境，跳过报名状态检查\");\n        }\n    }, [\n        activityId,\n        userState.isLoggedIn,\n        userState.roleId,\n        userState.phone,\n        isClient\n    ]);\n    // 前往任务函数\n    const handleGoToTask = async ()=>{\n        try {\n            const loadingMessage = notification.loading(\"正在跳转到任务...\");\n            // 获取当前用户的赛事任务列表\n            const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n            if (eventsTaskResponse.data.code === 200) {\n                const eventsTasks = eventsTaskResponse.data.data;\n                // 查找与当前活动相关的赛事任务\n                const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                if (relatedTask) {\n                    // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                    window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                    notification.success(\"正在跳转到任务页面...\");\n                } else {\n                    // 如果没有找到对应任务，在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                    notification.info(\"未找到对应任务，已打开班级空间\");\n                }\n            } else {\n                // 如果获取任务失败，在新标签页中打开班级空间\n                window.open(\"/class-space\", \"_blank\");\n                notification.info(\"获取任务失败，已打开班级空间\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"跳转任务失败:\", error);\n            notification.error(\"跳转任务失败，请稍后重试\");\n        }\n    };\n    // 处理报名按钮点击\n    const handleSubmitClick = async ()=>{\n        var _userState_phone, _localUser_phone;\n        // 获取localStorage中的用户信息进行对比\n        const localUserStr = localStorage.getItem(\"user\");\n        const localUser = localUserStr ? JSON.parse(localUserStr) : null;\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 报名按钮点击 - 详细用户状态检查\", {\n            hasSubmitted,\n            \"Redux userState\": {\n                isLoggedIn: userState === null || userState === void 0 ? void 0 : userState.isLoggedIn,\n                phone: userState === null || userState === void 0 ? void 0 : userState.phone,\n                phoneLength: userState === null || userState === void 0 ? void 0 : (_userState_phone = userState.phone) === null || _userState_phone === void 0 ? void 0 : _userState_phone.length,\n                phoneType: typeof (userState === null || userState === void 0 ? void 0 : userState.phone),\n                roleId: userState === null || userState === void 0 ? void 0 : userState.roleId,\n                userId: userState === null || userState === void 0 ? void 0 : userState.userId,\n                nickName: userState === null || userState === void 0 ? void 0 : userState.nickName\n            },\n            \"localStorage user\": {\n                phone: localUser === null || localUser === void 0 ? void 0 : localUser.phone,\n                phoneLength: localUser === null || localUser === void 0 ? void 0 : (_localUser_phone = localUser.phone) === null || _localUser_phone === void 0 ? void 0 : _localUser_phone.length,\n                phoneType: typeof (localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                userId: localUser === null || localUser === void 0 ? void 0 : localUser.userId,\n                nickName: localUser === null || localUser === void 0 ? void 0 : localUser.nickName\n            },\n            \"phone validation\": {\n                \"userState.phone exists\": !!(userState === null || userState === void 0 ? void 0 : userState.phone),\n                \"userState.phone not empty\": (userState === null || userState === void 0 ? void 0 : userState.phone) && userState.phone.trim() !== \"\",\n                \"localUser.phone exists\": !!(localUser === null || localUser === void 0 ? void 0 : localUser.phone),\n                \"localUser.phone not empty\": (localUser === null || localUser === void 0 ? void 0 : localUser.phone) && (localUser === null || localUser === void 0 ? void 0 : localUser.phone.trim()) !== \"\"\n            }\n        });\n        // 临时调用API获取最新用户信息进行对比\n        if (userState === null || userState === void 0 ? void 0 : userState.userId) {\n            try {\n                var _response_data, _response_data_phone, _response_data1, _response_data2, _response_data3, _response_data4;\n                const response = await _lib_api_user__WEBPACK_IMPORTED_MODULE_8__.userApi.getUserInfo(userState.userId);\n                console.log(\"\\uD83D\\uDD0D [DEBUG] 从API获取的最新用户信息:\", {\n                    code: response.code,\n                    phone: (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.phone,\n                    phoneLength: (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_phone = _response_data1.phone) === null || _response_data_phone === void 0 ? void 0 : _response_data_phone.length,\n                    phoneType: typeof ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.phone),\n                    nickName: (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.nickName,\n                    userId: (_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : _response_data4.id\n                });\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [DEBUG] 获取用户信息失败:\", error);\n            }\n        }\n        // 如果用户已报名，跳转到任务页面\n        if (hasSubmitted) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已报名，跳转到任务页面\");\n            handleGoToTask();\n            return;\n        }\n        // 检查用户是否登录\n        if (!userState || !userState.isLoggedIn) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户未登录，跳转到登录页面\");\n            // 未登录，显示登录对话框\n            const redirectUrl = pathname || \"/home\";\n            router.push(\"/login?redirect=\".concat(encodeURIComponent(redirectUrl)));\n            return;\n        }\n        // 检查用户身份：只有学生（roleId为1）才能报名\n        const isStudent = checkIsStudent();\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 检查用户身份\", {\n            isStudent,\n            roleId: userState === null || userState === void 0 ? void 0 : userState.roleId\n        });\n        if (!isStudent) {\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户不是学生身份，显示身份验证弹窗\");\n            showStudentOnlyModal();\n            return;\n        }\n        console.log(\"\\uD83D\\uDD0D [DEBUG] 身份验证通过，开始报名流程\");\n        // 身份验证通过，直接进行报名\n        handleDirectRegistration();\n    };\n    // 处理登录成功\n    const handleLoginSuccess = ()=>{\n        notification.success(\"登录成功\");\n    // 登录成功后，页面的useEffect会自动检查报名状态\n    // 不需要在这里重复检查，让useEffect处理状态更新\n    };\n    // 直接报名处理函数\n    const handleDirectRegistration = async ()=>{\n        try {\n            var _response_data;\n            // 检查用户是否绑定了手机号\n            if (!userState.phone || userState.phone.trim() === \"\") {\n                // 显示确认对话框\n                _barrel_optimize_names_Modal_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].confirm({\n                    title: \"需要绑定手机号\",\n                    content: \"报名活动需要绑定手机号，是否前往绑定？\",\n                    okText: \"前往绑定\",\n                    cancelText: \"取消\",\n                    onOk: ()=>{\n                        // 跳转到登录页面，并在URL中添加参数表示需要绑定手机号\n                        const currentUrl = window.location.href;\n                        router.push(\"/login?needBindPhone=true&redirect=\".concat(encodeURIComponent(currentUrl)));\n                    },\n                    onCancel: ()=>{\n                        notification.info(\"未绑定手机号，无法报名活动\");\n                    }\n                });\n                return;\n            }\n            console.log(\"\\uD83D\\uDD0D [DEBUG] 用户已绑定手机号，继续报名流程\");\n            // 显示加载提示\n            const loadingMessage = notification.loading(\"正在提交报名...\");\n            // 提交报名数据（使用默认值，跳过协议确认步骤）\n            const submitData = {\n                activityId: Number(activityId),\n                agreementAccepted: true,\n                parentConsentAccepted: true,\n                parentSignaturePath: \"\",\n                signatureTime: new Date().toISOString(),\n                remark: \"快速报名时间：\".concat(new Date().toLocaleString(\"zh-CN\"))\n            };\n            const response = await _lib_api_activity__WEBPACK_IMPORTED_MODULE_6__.activityRegistrationApi.submitRegistration(submitData);\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                // 更新UI状态\n                setHasSubmitted(true);\n                setRegistrationStatus(1);\n                notification.success(\"报名成功！正在跳转到班级空间...\");\n                // 通知页面刷新参赛作品列表\n                if (onRefreshWorks) {\n                    onRefreshWorks();\n                }\n                // 报名成功后，获取对应的赛事任务并跳转到班级空间\n                try {\n                    // 等待一小段时间确保后端创建赛事任务完成\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    // 获取当前用户的赛事任务列表\n                    const eventsTaskResponse = await _lib_api_activity_events_task__WEBPACK_IMPORTED_MODULE_7__.eventsTaskApi.getMyTasks();\n                    if (eventsTaskResponse.data.code === 200) {\n                        const eventsTasks = eventsTaskResponse.data.data;\n                        // 查找与当前活动相关的赛事任务\n                        const relatedTask = eventsTasks.find((task)=>task.activityId === Number(activityId));\n                        if (relatedTask) {\n                            // 在新标签页中打开班级空间并自动打开对应的赛事任务\n                            window.open(\"/class-space?taskId=\".concat(relatedTask.id), \"_blank\");\n                        } else {\n                            // 如果没有找到对应任务，在新标签页中打开班级空间\n                            window.open(\"/class-space\", \"_blank\");\n                        }\n                    } else {\n                        // 如果获取任务失败，在新标签页中打开班级空间\n                        window.open(\"/class-space\", \"_blank\");\n                    }\n                } catch (taskError) {\n                    console.error(\"获取赛事任务失败:\", taskError);\n                    // 即使获取任务失败，也要在新标签页中打开班级空间\n                    window.open(\"/class-space\", \"_blank\");\n                }\n            } else {\n                var _response_data1;\n                throw new Error(((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.message) || \"报名失败\");\n            }\n            if (loadingMessage) {\n                loadingMessage.close();\n            }\n        } catch (error) {\n            console.error(\"报名失败:\", error);\n            notification.error(error instanceof Error ? error.message : \"报名失败，请稍后重试\");\n        }\n    };\n    // 处理分享按钮点击事件\n    const handleShare = ()=>{\n        if (true) {\n            // 获取当前页面URL\n            const currentUrl = window.location.href;\n            // 复制到剪贴板\n            navigator.clipboard.writeText(currentUrl).then(()=>{\n                notification.success(\"活动链接已复制，快去分享吧！\");\n            }).catch((err)=>{\n                console.error(\"复制失败:\", err);\n                notification.error(\"复制链接失败，请手动复制\");\n            });\n        }\n    };\n    // 如果还没有在客户端挂载，返回一个简单的占位符\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-64 bg-gray-200 animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 567,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-300 rounded mb-2 w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded mb-1 w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-300 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 566,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n            lineNumber: 565,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"activity-header relative rounded-2xl overflow-hidden shadow-md border border-gray-100 bg-white hover:shadow-lg transition-shadow duration-300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-[300px] overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-transparent to-black/30 z-10\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full h-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: bannerImage,\n                                        alt: title,\n                                        fill: true,\n                                        priority: true,\n                                        style: {\n                                            objectFit: \"contain\"\n                                        },\n                                        className: \"z-10 hover:scale-105 transition-transform duration-700\",\n                                        onError: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = \"linear-gradient(120deg, #f3f4f6, #e5e7eb)\";\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 590,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 586,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-6 flex flex-col md:flex-row justify-between items-start md:items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-800 flex items-center\",\n                                        children: [\n                                            title,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"ml-2 w-5 h-5 text-yellow-400 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    organizer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: [\n                                            \"主办方: \",\n                                            organizer\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-sm mt-2 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-blue-500 to-indigo-500 h-1.5 w-1.5 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"活动时间：\",\n                                            formatDate(startTime),\n                                            \" - \",\n                                            formatDate(endTime)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 612,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3 mt-4 md:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSubmitClick,\n                                        disabled: isChecking,\n                                        className: \"flex items-center px-5 py-2.5 text-sm font-medium rounded-full \".concat(hasSubmitted ? \"bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\" : \"bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:opacity-90 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\", \" transition-all duration-200\"),\n                                        children: isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"loading-spinner mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"检查状态中\"\n                                            ]\n                                        }, void 0, true) : hasSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"已报名 前往任务\"\n                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: \"立即报名\"\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShare,\n                                        className: \"flex items-center p-2.5 text-gray-500 hover:text-blue-500 hover:bg-gray-50 rounded-full hover:scale-110 active:scale-95 transition-all duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Share2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                            lineNumber: 669,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                                lineNumber: 627,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                        lineNumber: 611,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 582,\n                columnNumber: 7\n            }, undefined),\n            (promotionImage || backgroundImage || galleryImages || attachmentFiles) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ActivityMediaGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    promotionImage: promotionImage,\n                    backgroundImage: backgroundImage,\n                    galleryImages: galleryImages,\n                    attachmentFiles: attachmentFiles,\n                    activityTitle: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                    lineNumber: 678,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 677,\n                columnNumber: 9\n            }, undefined),\n            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_login_dialog__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showLoginDialog,\n                onClose: ()=>setShowLoginDialog(false),\n                onSuccess: handleLoginSuccess\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\activity\\\\festival\\\\components\\\\ActivityHeader.tsx\",\n                lineNumber: 690,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ActivityHeader, \"R7f7NXCQFizAAsWwTKWSkUI8xvI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.usePathname,\n        react_redux__WEBPACK_IMPORTED_MODULE_11__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_11__.useDispatch\n    ];\n});\n_c = ActivityHeader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ActivityHeader);\nvar _c;\n$RefreshReg$(_c, \"ActivityHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/activity/festival/components/ActivityHeader.tsx\n"));

/***/ })

});