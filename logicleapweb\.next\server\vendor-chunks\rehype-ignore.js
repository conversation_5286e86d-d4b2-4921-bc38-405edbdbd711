"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-ignore";
exports.ids = ["vendor-chunks/rehype-ignore"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-ignore/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/rehype-ignore/lib/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n\nconst rehypeIgnore = (options = {}) => {\n    const { openDelimiter = 'rehype:ignore:start', closeDelimiter = 'rehype:ignore:end' } = options;\n    return (tree) => {\n        (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(tree, (node, index, parent) => {\n            if (node.type === 'element' || node.type === 'root') {\n                // const start = node.children.findIndex((item) => item.type === 'comment' && item.value === openDelimiter);\n                // const end = node.children.findIndex((item) => item.type === 'comment' && item.value === closeDelimiter);\n                // if (start > -1 && end > -1) {\n                //   node.children = node.children.filter((_, idx) => idx < start || idx > end);\n                // }\n                let start = false;\n                node.children = node.children.filter((item) => {\n                    if (item.type === 'raw' || item.type === 'comment') {\n                        let str = (item.value || '').trim();\n                        str = str.replace(/^<!--(.*?)-->/, '$1');\n                        if (str === openDelimiter) {\n                            start = true;\n                            return false;\n                        }\n                        if (str === closeDelimiter) {\n                            start = false;\n                            return false;\n                        }\n                    }\n                    return !start;\n                });\n            }\n        });\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (rehypeIgnore);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-ignore/lib/index.js\n");

/***/ })

};
;