/** @jsxImportSource react */
'use client'

import { useState, useEffect } from 'react'
import { Dropdown, Menu } from 'antd'
import { UserSwitchOutlined } from '@ant-design/icons'
import userApi from '@/lib/api/user'
import { useDispatch, useSelector } from 'react-redux'
import { RootState, setUser } from '@/lib/store'
import { UserInfo } from '@/types/user'
import { GetNotification } from 'logic-common/dist/components/Notification';

interface RoleSwitcherProps {
  className?: string
}

export default function RoleSwitcher({ className = '' }: RoleSwitcherProps) {
  const [roles, setRoles] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const dispatch = useDispatch()
  const currentUser = useSelector((state: RootState) => state.user.userState)
  const notification = GetNotification();
  // 获取当前用户的所有角色
  useEffect(() => {
    if (currentUser.phone) {
      fetchUserRoles()
    }
  }, [currentUser.phone])

  const fetchUserRoles = async () => {
    try {
      setLoading(true)
      const response = await userApi.findAllBindByPhone(currentUser.phone)
      console.log('获取用户角色响应:', response)

      // 处理不同的响应格式
      if (response.data?.code === 200) {
        // 直接使用data字段
        if (Array.isArray(response.data.data)) {
          setRoles(response.data.data)
        }
        // 使用data.list字段
        else if (response.data.data?.list && Array.isArray(response.data.data.list)) {
          setRoles(response.data.data.list)
        }
        // 直接使用响应数据
        else if (Array.isArray(response.data)) {
          setRoles(response.data)
        }
      }
    } catch (error) {
      console.error('获取用户角色失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 切换角色
  const handleRoleSwitch = async (userId: number) => {
    try {
      setLoading(true)
      const response = await userApi.switchToUser(userId)
      console.log('角色切换响应:', response)

      // 处理不同的响应格式
      if (response.code === 200 || response.data?.code === 200) {
        // 获取用户数据和token
        const userData = response.data?.userInfo || response.data?.data?.userInfo || response.userInfo
        const token = response.data?.token || response.data?.data?.token || response.token

        if (userData && token) {
          localStorage.setItem('token', token)
          localStorage.setItem('userId', userData.id)

          // 更新Redux状态
          dispatch(setUser({
            userId: userData.id,
            nickName: userData.nickName,
            avatarUrl: userData.avatarUrl,
            gender: userData.gender,
            phone: userData.phone,
            isLoggedIn: true,
            roleId: userData.roleId
          }))

          notification.success('角色切换成功')

          // 刷新页面以应用新角色权限
          window.location.reload()
        } else {
          notification.error('角色切换成功，但未获取到用户信息')
        }
      } else {
        const errorMsg = response.message || response.data?.message || '角色切换失败'
        notification.error(errorMsg)
      }
    } catch (error) {
      console.error('角色切换失败:', error)
      notification.error('角色切换失败')
    } finally {
      setLoading(false)
    }
  }

  // 过滤掉当前角色
  const otherRoles = roles.filter(role => role.roleId !== currentUser.roleId)

  if (otherRoles.length === 0) {
    return null // 如果没有其他角色，不显示切换按钮
  }

  const menu = (
    <Menu>
      {otherRoles.map(role => (
        <Menu.Item
          key={role.id}
          onClick={() => handleRoleSwitch(role.roleId)}
          disabled={loading}
        >
          <div className="flex items-center">
            {role.avatarUrl && (
              <img
                src={role.avatarUrl}
                alt={role.nickName}
                className="w-6 h-6 rounded-full mr-2"
              />
            )}
            <div>
              <div>{role.nickName}</div>
              <div className="text-xs text-gray-500">{role.roleName}</div>
            </div>
          </div>
        </Menu.Item>
      ))}
    </Menu>
  )

  return (
    <Dropdown overlay={menu} placement="bottomRight" disabled={loading}>
      <div className={`cursor-pointer flex items-center ${className}`}>
        <UserSwitchOutlined className="mr-1" />
        <span>切换账号</span>
      </div>
    </Dropdown>
  )
} 